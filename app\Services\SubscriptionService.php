<?php

namespace App\Services;

use App\Models\User;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Cashier\Subscription;

class SubscriptionService
{
    /**
     * Get all active subscription plans
     */
    public function getActivePlans()
    {
        return SubscriptionPlan::active()->get();
    }

    /**
     * Get a specific plan by slug
     */
    public function getPlanBySlug(string $slug): ?SubscriptionPlan
    {
        return SubscriptionPlan::bySlug($slug)->first();
    }

    /**
     * Assign free plan to user
     */
    public function assignFreePlan(User $user): bool
    {
        $freePlan = SubscriptionPlan::where('price', 0)->first();
        
        if (!$freePlan) {
            Log::error('Free plan not found when trying to assign to user', ['user_id' => $user->id]);
            return false;
        }

        return $this->assignPlan($user, $freePlan);
    }

    /**
     * Assign a specific plan to user
     */
    public function assignPlan(User $user, SubscriptionPlan $plan): bool
    {
        try {
            DB::beginTransaction();

            $user->update([
                'subscription_plan_id' => $plan->id,
                'subscription_started_at' => now(),
                'subscription_ends_at' => $plan->isFree() ? null : now()->addMonth(),
                'subscription_status' => 'active',
                'usage_limits' => $plan->getRateLimits(),
                'current_usage' => [
                    'chat_messages_today' => 0,
                    'chat_messages_this_hour' => 0,
                    'appointments_this_month' => 0,
                    'api_requests_this_minute' => 0,
                    'api_requests_this_hour' => 0,
                ],
                'usage_reset_at' => now()->startOfDay()->addDay(),
            ]);

            DB::commit();
            
            Log::info('Plan assigned to user', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to assign plan to user', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if user can perform action based on their plan limits
     */
    public function canPerformAction(User $user, string $action): bool
    {
        // Reset usage counters if needed
        $user->resetUsageIfNeeded();

        // Check if user has exceeded limits
        return !$user->hasExceededLimit($action);
    }

    /**
     * Record usage for a user action
     */
    public function recordUsage(User $user, string $action): void
    {
        $user->incrementUsage($action);
    }

    /**
     * Get user's subscription status and details
     */
    public function getUserSubscriptionDetails(User $user): array
    {
        $plan = $user->subscriptionPlan;
        
        // Get Stripe subscription data if user has an active subscription
        $stripeData = null;
        if ($user->subscribed('default')) {
            $subscription = $user->subscription('default');
            if ($subscription) {
                $stripeSubscription = $subscription->asStripeSubscription();
                $stripeData = [
                    'id' => $stripeSubscription->id,
                    'status' => $stripeSubscription->status,
                    'current_period_start' => $stripeSubscription->current_period_start,
                    'current_period_end' => $stripeSubscription->current_period_end,
                    'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end,
                    'canceled_at' => $stripeSubscription->canceled_at,
                    'trial_end' => $stripeSubscription->trial_end,
                    'next_payment_date' => $stripeSubscription->current_period_end,
                ];
            }
        }
        
        return [
            'plan' => $plan ? [
                'id' => $plan->id,
                'name' => $plan->name,
                'slug' => $plan->slug,
                'price' => $plan->price,
                'formatted_price' => $plan->formatted_price,
                'currency' => $plan->currency,
                'interval' => $plan->interval,
                'description' => $plan->description,
                'features' => $plan->getFeaturesList(),
                'is_free' => $plan->isFree(),
            ] : null,
            'status' => $user->subscription_status,
            'started_at' => $user->subscription_started_at,
            'ends_at' => $user->subscription_ends_at,
            'is_active' => $user->hasActiveSubscription(),
            'usage_limits' => $user->getUsageLimits(),
            'current_usage' => $user->getCurrentUsage(),
            'usage_percentages' => $this->calculateUsagePercentages($user),
            'stripe_data' => $stripeData,
        ];
    }

    /**
     * Calculate usage percentages for display
     */
    private function calculateUsagePercentages(User $user): array
    {
        $limits = $user->getUsageLimits();
        $usage = $user->getCurrentUsage();
        
        $percentages = [];
        
        // Chat messages per day
        if (isset($limits['chat_messages_per_day']) && $limits['chat_messages_per_day'] > 0) {
            $percentages['chat_messages_day'] = min(100, 
                round(($usage['chat_messages_today'] ?? 0) / $limits['chat_messages_per_day'] * 100)
            );
        }
        
        // Appointments per month
        if (isset($limits['appointments_per_month']) && $limits['appointments_per_month'] > 0) {
            $percentages['appointments_month'] = min(100, 
                round(($usage['appointments_this_month'] ?? 0) / $limits['appointments_per_month'] * 100)
            );
        }
        
        return $percentages;
    }

    /**
     * Upgrade user to a paid plan
     */
    public function upgradeToPaidPlan(User $user, SubscriptionPlan $plan): array
    {
        try {
            if ($plan->isFree()) {
                return [
                    'success' => false,
                    'message' => 'Cannot upgrade to a free plan'
                ];
            }

            // This would integrate with Stripe for actual payment processing
            // For now, we'll just assign the plan
            $success = $this->assignPlan($user, $plan);

            return [
                'success' => $success,
                'message' => $success ? 'Successfully upgraded to ' . $plan->name : 'Failed to upgrade plan'
            ];
        } catch (\Exception $e) {
            Log::error('Failed to upgrade user plan', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while upgrading your plan'
            ];
        }
    }

    /**
     * Cancel user subscription
     */
    public function cancelSubscription(User $user): array
    {
        try {
            DB::beginTransaction();

            // Assign free plan
            $freePlan = SubscriptionPlan::where('price', 0)->first();
            if ($freePlan) {
                $this->assignPlan($user, $freePlan);
            } else {
                $user->update([
                    'subscription_status' => 'cancelled',
                    'subscription_ends_at' => now(),
                ]);
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Subscription cancelled successfully'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel subscription', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel subscription'
            ];
        }
    }

    /**
     * Reset all users' usage counters (for scheduled tasks)
     */
    public function resetAllUsageCounters(): void
    {
        User::whereNotNull('subscription_plan_id')->chunk(100, function ($users) {
            foreach ($users as $user) {
                $user->resetUsageIfNeeded();
            }
        });
    }
}
