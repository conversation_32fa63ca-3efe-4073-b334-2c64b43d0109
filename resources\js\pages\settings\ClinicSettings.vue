<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import axios from 'axios';

const breadcrumbs = [
    {
        title: 'Clinic Settings',
        href: '/settings/clinic',
    },
];

const page = usePage();
const user = page.props.auth.user;
const { showSuccess, showError } = useNotifications();

// State
const loading = ref(false);
const saving = ref(false);

// Clinic data
const clinic = ref({
    id: null,
    name: '',
    email: '',
    phone: '',
    website: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    description: '',
    license_number: '',
    tax_id: '',
    accepts_new_patients: true,
    telemedicine_enabled: false,
});

// Note: Users can only manage their own clinic through settings

// Methods
const fetchClinicData = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/settings/clinic/data');
        clinic.value = response.data.data;
    } catch (error) {
        console.error('Error fetching clinic data:', error);
        showError('Failed to load clinic information');
    } finally {
        loading.value = false;
    }
};

const saveClinicSettings = async () => {
    saving.value = true;
    try {
        const response = await axios.post('/settings/clinic/update', {
            name: clinic.value.name,
            email: clinic.value.email,
            phone: clinic.value.phone,
            website: clinic.value.website,
            address: clinic.value.address,
            city: clinic.value.city,
            state: clinic.value.state,
            postal_code: clinic.value.postal_code,
            country: clinic.value.country,
            description: clinic.value.description,
            license_number: clinic.value.license_number,
            tax_id: clinic.value.tax_id,
            accepts_new_patients: clinic.value.accepts_new_patients,
            telemedicine_enabled: clinic.value.telemedicine_enabled,
        });

        showSuccess('Clinic settings updated successfully');
        clinic.value = response.data.data;
    } catch (error) {
        console.error('Error saving clinic settings:', error);
        if (error.response?.data?.errors) {
            const errors = Object.values(error.response.data.errors).flat();
            showError(errors.join(', '));
        } else {
            showError('Failed to save clinic settings');
        }
    } finally {
        saving.value = false;
    }
};

onMounted(() => {
    fetchClinicData();
});
</script>

<template>
    <Head title="Clinic Settings" />

    <AppLayout>
        <SettingsLayout :breadcrumbs="breadcrumbs">
            <div class="space-y-6">
                <!-- Header -->
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Clinic Settings</h1>
                    <p class="text-sm text-gray-600 mt-1">Manage your clinic information and preferences</p>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>

                <!-- Clinic Settings Form -->
                <div v-else class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
                        
                        <form @submit.prevent="saveClinicSettings" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Clinic Name</label>
                                    <input 
                                        v-model="clinic.name"
                                        type="text" 
                                        placeholder="Enter clinic name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    >
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input 
                                        v-model="clinic.email"
                                        type="email" 
                                        placeholder="<EMAIL>"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    >
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input 
                                        v-model="clinic.phone"
                                        type="tel" 
                                        placeholder="+****************"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                                    <input 
                                        v-model="clinic.website"
                                        type="url" 
                                        placeholder="https://www.yourclinic.com"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea 
                                    v-model="clinic.description"
                                    rows="3"
                                    placeholder="Brief description of your clinic"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                ></textarea>
                            </div>

                            <!-- Address Information -->
                            <div class="pt-4 border-t border-gray-200">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                        <input 
                                            v-model="clinic.address"
                                            type="text" 
                                            placeholder="Street address"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                        <input 
                                            v-model="clinic.city"
                                            type="text" 
                                            placeholder="City"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                                        <input 
                                            v-model="clinic.state"
                                            type="text" 
                                            placeholder="State or Province"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                        <input 
                                            v-model="clinic.postal_code"
                                            type="text" 
                                            placeholder="Postal code"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                        <input 
                                            v-model="clinic.country"
                                            type="text" 
                                            placeholder="Country"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>
                                </div>
                            </div>

                            <!-- Legal Information -->
                            <div class="pt-4 border-t border-gray-200">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Legal Information</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">License Number</label>
                                        <input 
                                            v-model="clinic.license_number"
                                            type="text" 
                                            placeholder="Medical license number"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Tax ID</label>
                                        <input 
                                            v-model="clinic.tax_id"
                                            type="text" 
                                            placeholder="Tax identification number"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                    </div>
                                </div>
                            </div>

                            <!-- Preferences -->
                            <div class="pt-4 border-t border-gray-200">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Preferences</h4>
                                
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input 
                                            v-model="clinic.accepts_new_patients"
                                            type="checkbox" 
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">Accepting new patients</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input 
                                            v-model="clinic.telemedicine_enabled"
                                            type="checkbox" 
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">Telemedicine services enabled</span>
                                    </label>
                                </div>
                            </div>

                            <div class="flex justify-end pt-4">
                                <button 
                                    type="submit"
                                    :disabled="saving"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="saving">Saving...</span>
                                    <span v-else>Save Settings</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
