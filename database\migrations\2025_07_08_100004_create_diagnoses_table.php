<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('diagnoses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->string('diagnosis_code')->nullable(); // ICD-10, ICD-11, etc.
            $table->string('diagnosis_system')->nullable(); // ICD-10, ICD-11, SNOMED, etc.
            $table->string('diagnosis_name');
            $table->text('description')->nullable();
            $table->enum('type', ['primary', 'secondary', 'differential'])->default('primary');
            $table->enum('status', ['active', 'resolved', 'chronic', 'suspected'])->default('active');
            $table->date('onset_date')->nullable();
            $table->date('resolved_date')->nullable();
            $table->text('notes')->nullable();
            $table->integer('severity')->nullable(); // 1-10 scale
            $table->timestamps();

            // Indexes
            $table->index(['consultation_id', 'type']);
            $table->index('diagnosis_code');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('diagnoses');
    }
};
