<?php

namespace App\Repositories;

use App\Models\LabTestCatalog;
use App\Repositories\Interfaces\LabTestCatalogRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class LabTestCatalogRepository extends BaseRepository implements LabTestCatalogRepositoryInterface
{
    public function __construct(LabTestCatalog $model)
    {
        parent::__construct($model);
    }

    public function getAll(int $perPage = 20): LengthAwarePaginator
    {
        return $this->model->active()
            ->orderBy('category')
            ->orderBy('test_name')
            ->paginate($perPage);
    }

    public function getActive(): Collection
    {
        return $this->model->active()
            ->orderBy('category')
            ->orderBy('test_name')
            ->get();
    }

    public function findById(int $id): ?LabTestCatalog
    {
        return $this->model->find($id);
    }

    public function findByCode(string $testCode): ?LabTestCatalog
    {
        return $this->model->where('test_code', $testCode)->first();
    }

    public function findByCodes(array $testCodes): Collection
    {
        return $this->model->whereIn('test_code', $testCodes)->get();
    }

    public function getByCategory(string $category): Collection
    {
        return $this->model->active()
            ->byCategory($category)
            ->orderBy('test_name')
            ->get();
    }

    public function search(string $query, int $perPage = 20): LengthAwarePaginator
    {
        return $this->model->active()
            ->search($query)
            ->orderBy('test_name')
            ->paginate($perPage);
    }

    public function getCategories(): array
    {
        return $this->model::getCategories();
    }

    public function create(array $data): LabTestCatalog
    {
        return $this->model->create($data);
    }

    public function update(LabTestCatalog $test, array $data): LabTestCatalog
    {
        $test->update($data);
        return $test->fresh();
    }

    public function delete(LabTestCatalog $test): bool
    {
        return $test->delete();
    }
}
