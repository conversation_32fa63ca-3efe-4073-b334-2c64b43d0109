<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class UpdateUserRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->route('user');
        
        // Allow if user is admin, clinic admin, or updating their own profile
        return auth()->check() && (
            auth()->user()->hasRole(['admin', 'clinic_admin']) ||
            auth()->user()->id === $user->id
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        
        return [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $user->id,
            'role' => 'sometimes|required|in:patient,provider,admin,clinic_admin',
            'clinic_id' => 'nullable|exists:clinics,id',
            'phone_number' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'email.required' => 'Email is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email is already registered.',
            'role.required' => 'Role is required.',
            'role.in' => 'Role must be one of: patient, provider, admin, clinic_admin.',
            'clinic_id.exists' => 'The selected clinic is not valid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'phone_number' => 'phone number',
            'clinic_id' => 'clinic',
            'is_active' => 'active status',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // If role is clinic_admin, clinic_id is required
            if ($this->input('role') === 'clinic_admin' && !$this->input('clinic_id')) {
                $validator->errors()->add('clinic_id', 'Clinic is required for clinic admin role.');
            }
            
            // Only admins can change roles
            if ($this->has('role') && !auth()->user()->hasRole('admin')) {
                $validator->errors()->add('role', 'You are not authorized to change user roles.');
            }
        });
    }
}
