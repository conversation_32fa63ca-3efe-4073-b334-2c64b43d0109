<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserService
{
    protected UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Create a new user with business logic
     */
    public function createUser(array $data): User
    {
        return DB::transaction(function () use ($data) {
            // Business logic: Hash password, generate referral code, etc.
            if (isset($data['password'])) {
                $data['password'] = Hash::make($data['password']);
            }

            // Generate referral code if not provided
            if (empty($data['referral_code'])) {
                $data['referral_code'] = $this->generateReferralCode();
            }

            // Set default values
            $data['is_active'] = $data['is_active'] ?? true;
            $data['email_verified_at'] = $data['email_verified_at'] ?? null;

            // Extract provider data
            $providerData = $data['provider_data'] ?? [];
            unset($data['provider_data']);

            // Create user
            $user = $this->userRepository->create($data);

            // Assign role using Spatie
            if (!empty($data['role'])) {
                $this->ensureRoleExists($data['role']);
                $user->assignRole($data['role']);
            }

            // Create related profiles
            $this->createUserProfile($user, $data['role'], $providerData, $data['clinic_id']);

            // Load relationships
            $user->load('roles');
            if ($user->hasRole('patient')) {
                $user->load('patient');
            } elseif ($user->hasRole('provider')) {
                $user->load('provider');
            }

            return $user;
        });
    }

    /**
     * Update user with business logic
     */
    public function updateUser(int $userId, array $data): ?User
    {
        // Business logic: Don't allow certain fields to be updated directly
        unset($data['password'], $data['email_verified_at'], $data['referral_code']);

        return $this->userRepository->update($userId, $data);
    }

    /**
     * Change user password with validation
     */
    public function changePassword(int $userId, string $currentPassword, string $newPassword): bool
    {
        $user = $this->userRepository->find($userId);
        
        if (!$user || !Hash::check($currentPassword, $user->password)) {
            return false;
        }

        $updated = $this->userRepository->update($userId, [
            'password' => Hash::make($newPassword),
            'password_change_required' => false
        ]);

        return $updated !== null;
    }

    /**
     * Activate user account
     */
    public function activateUser(int $userId): bool
    {
        $updated = $this->userRepository->update($userId, ['is_active' => true]);
        
        // Business logic: Send welcome email, log activity, etc.
        if ($updated) {
            // Could trigger events, send notifications, etc.
        }

        return $updated !== null;
    }

    /**
     * Deactivate user account
     */
    public function deactivateUser(int $userId): bool
    {
        $updated = $this->userRepository->update($userId, ['is_active' => false]);
        
        // Business logic: Cancel appointments, notify related users, etc.
        if ($updated) {
            // Could trigger cleanup processes
        }

        return $updated !== null;
    }

    /**
     * Get users with business logic filtering
     */
    public function getActiveUsers(string $role = null): Collection
    {
        $users = $this->userRepository->findActive();

        if ($role) {
            $users = $users->where('role', $role);
        }

        return $users;
    }

    /**
     * Get users with filters and pagination
     */
    public function getUsersWithFilters(array $filters = [], int $perPage = 15): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $this->userRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Search users with business logic
     */
    public function searchUsers(string $search, array $filters = []): Collection
    {
        $users = $this->userRepository->search($search);
        
        // Business logic: Apply additional filters, permissions, etc.
        if (!empty($filters['clinic_id'])) {
            $users = $users->where('clinic_id', $filters['clinic_id']);
        }

        if (!empty($filters['role'])) {
            $users = $users->where('role', $filters['role']);
        }

        // Only return active users unless specifically requested
        if (!isset($filters['include_inactive']) || !$filters['include_inactive']) {
            $users = $users->where('is_active', true);
        }

        return $users;
    }

    /**
     * Get user dashboard data with business logic
     */
    public function getUserDashboard(int $userId): array
    {
        $user = $this->userRepository->find($userId);
        
        if (!$user) {
            return [];
        }

        // Business logic: Compile dashboard data based on user role
        $dashboardData = [
            'user' => $user,
            'notifications' => [],
            'recent_activity' => [],
        ];

        switch ($user->role) {
            case 'patient':
                $dashboardData = array_merge($dashboardData, $this->getPatientDashboardData($user));
                break;
            case 'provider':
                $dashboardData = array_merge($dashboardData, $this->getProviderDashboardData($user));
                break;
            case 'clinic_admin':
                $dashboardData = array_merge($dashboardData, $this->getClinicAdminDashboardData($user));
                break;
        }

        return $dashboardData;
    }

    /**
     * Update user activity tracking
     */
    public function updateActivity(int $userId): bool
    {
        return $this->userRepository->updateLastActivity($userId);
    }

    /**
     * Update login tracking
     */
    public function updateLogin(int $userId): bool
    {
        return $this->userRepository->updateLastLogin($userId);
    }

    /**
     * Generate unique referral code
     */
    private function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid()), 0, 8));
        } while ($this->userRepository->findOneBy(['referral_code' => $code]));

        return $code;
    }

    /**
     * Get patient-specific dashboard data
     */
    private function getPatientDashboardData(User $user): array
    {
        // Business logic for patient dashboard
        return [
            'upcoming_appointments' => [], // Would get from appointment service
            'recent_consultations' => [], // Would get from consultation service
            'health_metrics' => [], // Would get from health service
        ];
    }

    /**
     * Get provider-specific dashboard data
     */
    private function getProviderDashboardData(User $user): array
    {
        // Business logic for provider dashboard
        return [
            'todays_appointments' => [], // Would get from appointment service
            'pending_consultations' => [], // Would get from consultation service
            'patient_count' => 0, // Would get from patient service
        ];
    }

    /**
     * Get clinic admin-specific dashboard data
     */
    private function getClinicAdminDashboardData(User $user): array
    {
        // Business logic for clinic admin dashboard
        return [
            'clinic_stats' => [], // Would get from clinic service
            'provider_stats' => [], // Would get from provider service
            'appointment_stats' => [], // Would get from appointment service
        ];
    }

    /**
     * Ensure role exists in database
     */
    private function ensureRoleExists(string $roleName): void
    {
        if (!\Spatie\Permission\Models\Role::where('name', $roleName)->exists()) {
            \Spatie\Permission\Models\Role::create(['name' => $roleName]);
        }
    }

    /**
     * Create user profile based on role
     */
    private function createUserProfile(User $user, string $role, array $providerData, ?int $clinicId): void
    {
        if ($role === 'provider') {
            $provider = new \App\Models\Provider([
                'specialization' => $providerData['specialization'] ?? 'General Practice',
                'license_number' => $providerData['license_number'] ?? null,
                'verification_status' => 'pending',
                'clinic_id' => $clinicId,
            ]);
            $user->provider()->save($provider);
        } elseif ($role === 'patient') {
            // Patient record is created automatically in User model boot method
            // But ensure clinic_id is set
            if ($user->patient) {
                $user->patient->update(['clinic_id' => $clinicId]);
            }
        }
    }
}
