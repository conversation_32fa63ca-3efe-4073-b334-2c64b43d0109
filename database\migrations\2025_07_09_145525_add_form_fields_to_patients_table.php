<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Add fields that the form expects
            $table->text('emergency_contact')->nullable()->after('emergency_contact_relationship');
            $table->text('medical_history')->nullable()->after('medical_conditions');
            $table->text('current_medications')->nullable()->after('medications');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn(['emergency_contact', 'medical_history', 'current_medications']);
        });
    }
};
