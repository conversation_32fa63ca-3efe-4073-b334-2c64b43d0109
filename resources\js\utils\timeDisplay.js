/**
 * Simple time display utilities that don't modify stored appointment times
 * This approach keeps the stored times intact and only handles display formatting
 */

/**
 * Format time for display without timezone conversion
 * @param {string} timeString - Time in HH:mm format
 * @returns {string} Formatted time
 */
export function formatTime(timeString) {
    if (!timeString) return 'N/A';
    
    try {
        // Create a date object for today with the given time
        const today = new Date();
        const [hours, minutes] = timeString.split(':').map(Number);
        const timeDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
        
        return timeDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    } catch (error) {
        console.warn('Error formatting time:', error);
        return timeString;
    }
}

/**
 * Format date for display
 * @param {string} dateString - Date in YYYY-MM-DD format
 * @returns {string} Formatted date
 */
export function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
        const date = new Date(dateString + 'T12:00:00'); // Add time to avoid timezone issues
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } catch (error) {
        console.warn('Error formatting date:', error);
        return dateString;
    }
}

/**
 * Format appointment date and time for display
 * @param {Object} appointment - Appointment object
 * @returns {string} Formatted date and time
 */
export function formatAppointmentDateTime(appointment) {
    if (!appointment?.date || !appointment?.time_slot?.start_time) {
        return 'N/A';
    }
    
    const formattedDate = formatDate(appointment.date);
    const formattedTime = formatTime(appointment.time_slot.start_time);
    
    return `${formattedDate} at ${formattedTime}`;
}

/**
 * Get time range string for appointment
 * @param {Object} timeSlot - Time slot object with start_time and end_time
 * @returns {string} Time range (e.g., "9:20 AM - 9:40 AM")
 */
export function formatTimeRange(timeSlot) {
    if (!timeSlot?.start_time || !timeSlot?.end_time) {
        return formatTime(timeSlot?.start_time) || 'N/A';
    }
    
    const startTime = formatTime(timeSlot.start_time);
    const endTime = formatTime(timeSlot.end_time);
    
    return `${startTime} - ${endTime}`;
}