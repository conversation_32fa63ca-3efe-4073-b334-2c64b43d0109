<?php

namespace App\Repositories\Interfaces;

use App\Models\BillItem;
use Illuminate\Database\Eloquent\Collection;

interface BillItemRepositoryInterface
{
    public function getByBill(int $billId): Collection;
    public function create(array $data): BillItem;
    public function update(BillItem $billItem, array $data): BillItem;
    public function delete(BillItem $billItem): bool;
    public function findById(int $id): ?BillItem;
}
