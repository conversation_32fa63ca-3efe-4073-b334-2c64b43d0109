@extends('letterheads.common')

@section('document_content')
    <div class="document-info">
        <div class="left">
            <strong>{{ strtoupper($letter_title ?? 'MEDICAL LETTER') }}</strong><br>
            Ref: {{ $document_reference ?? 'ML-' . now()->format('Ymd-His') }}
        </div>
        <div class="right">
            {{ now()->format('d/m/Y') }}<br>
            {{ now()->format('g:i A') }}
        </div>
    </div>

    @if(isset($patient))
        <div class="patient-details">
            <h3>👤 Patient Details</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <p><strong>Name:</strong> {{ $patient->name }}</p>
                    <p><strong>Date of Birth:</strong> {{ $patient->date_of_birth ? \Carbon\Carbon::parse($patient->date_of_birth)->format('d/m/Y') : 'Not specified' }}</p>
                    @if($patient->nhs_number)
                        <p><strong>NHS Number:</strong> {{ $patient->nhs_number }}</p>
                    @endif
                    @if($patient->unique_id)
                        <p><strong>Patient ID:</strong> {{ $patient->unique_id }}</p>
                    @endif
                </div>
                <div>
                    @if($patient->address)
                        <p><strong>Address:</strong><br>
                        {{ $patient->address }}
                        @if($patient->city), {{ $patient->city }}@endif
                        @if($patient->postal_code)<br>{{ $patient->postal_code }}@endif
                        </p>
                    @endif
                    @if($patient->phone)
                        <p><strong>Phone:</strong> {{ $patient->phone }}</p>
                    @endif
                </div>
            </div>
        </div>
    @endif

    @if(isset($recipient_details))
        <div style="margin-bottom: 30px;">
            <h4>To:</h4>
            <div style="padding: 10px; border-left: 3px solid #007bff; background-color: #f8f9fa;">
                {!! nl2br(e($recipient_details)) !!}
            </div>
        </div>
    @endif

    <div style="margin-bottom: 30px;">
        <div style="line-height: 1.8; text-align: justify;">
            {!! $letter_content !!}
        </div>
    </div>

    @if(isset($consultation) && $consultation)
        <div style="margin-bottom: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h4 style="margin-top: 0; color: #333;">Consultation Summary</h4>
            <p><strong>Date:</strong> {{ \Carbon\Carbon::parse($consultation->consultation_date)->format('d/m/Y') }}</p>
            @if($consultation->chief_complaint)
                <p><strong>Chief Complaint:</strong> {{ $consultation->chief_complaint }}</p>
            @endif
            @if($consultation->diagnosis)
                <p><strong>Diagnosis:</strong> {{ $consultation->diagnosis }}</p>
            @endif
            @if($consultation->treatment_plan)
                <p><strong>Treatment Plan:</strong> {{ $consultation->treatment_plan }}</p>
            @endif
        </div>
    @endif

    @if(isset($attachments) && count($attachments) > 0)
        <div style="margin-bottom: 30px;">
            <h4>Attachments:</h4>
            <ul>
                @foreach($attachments as $attachment)
                    <li>{{ $attachment }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="provider-signature">
        <p>Yours sincerely,</p>
        <br>
        <p><strong>{{ $provider->name }}</strong></p>
        @if($provider->qualification)
            <p>{{ $provider->qualification }}</p>
        @else
            <p>General Practitioner</p>
        @endif
        @if($provider->registration_number)
            <p>Registration No: {{ $provider->registration_number }}</p>
        @endif
        @if($provider->signature_path)
            <img src="{{ asset('storage/' . $provider->signature_path) }}" alt="Digital Signature">
        @endif
        <p><small>Digitally signed on {{ now()->format('d/m/Y, g:i A') }}</small></p>
    </div>

    @if(isset($confidentiality_notice) && $confidentiality_notice)
        <div style="margin-top: 40px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; font-size: 10px; color: #856404;">
            <p style="margin: 0; text-align: center;"><strong>CONFIDENTIALITY NOTICE:</strong></p>
            <p style="margin: 5px 0 0 0; text-align: justify;">
                This document contains confidential medical information. It is intended solely for the use of the individual or entity to whom it is addressed. 
                If you are not the intended recipient, you are hereby notified that any disclosure, copying, distribution, or taking of any action in reliance 
                on the contents of this information is strictly prohibited.
            </p>
        </div>
    @endif
@endsection
