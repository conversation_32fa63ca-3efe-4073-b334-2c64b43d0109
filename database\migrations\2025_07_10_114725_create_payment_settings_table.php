<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->string('payment_provider'); // stripe, paypal, square, etc.
            $table->json('details'); // Provider-specific configuration (keys, secrets, etc.)
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index('clinic_id');
            $table->index('payment_provider');
            $table->index('is_active');

            // Unique constraint: one setting per provider per clinic
            $table->unique(['clinic_id', 'payment_provider']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_settings');
    }
};
