<?php

namespace Database\Factories;

use App\Models\Consultation;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use App\Models\Appointment;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConsultationFactory extends Factory
{
    protected $model = Consultation::class;

    public function definition(): array
    {
        return [
            'appointment_id' => null,
            'patient_id' => Patient::factory(),
            'provider_id' => Provider::factory(),
            'clinic_id' => Clinic::factory(),
            'consultation_type' => $this->faker->randomElement(['general', 'follow_up', 'emergency', 'specialist']),
            'status' => $this->faker->randomElement(['draft', 'in_progress', 'completed', 'cancelled']),
            'consultation_date' => $this->faker->dateTimeBetween('-1 month', '+1 week'),
            'duration_minutes' => $this->faker->numberBetween(15, 120),
            'chief_complaint' => $this->faker->sentence(),
            'history_of_present_illness' => $this->faker->paragraph(),
            'past_medical_history' => $this->faker->paragraph(),
            'medications' => $this->faker->sentence(),
            'allergies' => $this->faker->randomElement(['None known', 'Penicillin', 'Nuts', 'Shellfish']),
            'social_history' => $this->faker->paragraph(),
            'family_history' => $this->faker->paragraph(),
            'review_of_systems' => $this->faker->paragraph(),
            'physical_examination' => $this->faker->paragraph(),
            'vital_signs' => [
                'blood_pressure' => $this->faker->numberBetween(90, 180) . '/' . $this->faker->numberBetween(60, 120),
                'heart_rate' => $this->faker->numberBetween(60, 100),
                'temperature' => $this->faker->randomFloat(1, 36.0, 39.0),
                'respiratory_rate' => $this->faker->numberBetween(12, 20),
                'oxygen_saturation' => $this->faker->numberBetween(95, 100),
            ],
            'assessment' => $this->faker->paragraph(),
            'diagnosis' => $this->faker->sentence(),
            'differential_diagnosis' => $this->faker->sentence(),
            'treatment_plan' => $this->faker->paragraph(),
            'follow_up_instructions' => $this->faker->paragraph(),
            'patient_education' => $this->faker->paragraph(),
            'additional_notes' => $this->faker->paragraph(),
            'attachments' => null,
            'is_telemedicine' => $this->faker->boolean(30),
            'consultation_mode' => $this->faker->randomElement(['in_person', 'video', 'phone']),
            'started_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'completed_at' => function (array $attributes) {
                return $attributes['status'] === 'completed' ? $this->faker->dateTimeBetween($attributes['started_at'], 'now') : null;
            },
        ];
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'completed_at' => null,
        ]);
    }

    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'completed_at' => null,
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'completed_at' => $this->faker->dateTimeBetween($attributes['started_at'] ?? '-1 hour', 'now'),
        ]);
    }

    public function telemedicine(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_telemedicine' => true,
            'consultation_mode' => $this->faker->randomElement(['video', 'phone']),
        ]);
    }

    public function withAppointment(): static
    {
        return $this->state(fn (array $attributes) => [
            'appointment_id' => Appointment::factory(),
        ]);
    }
}
