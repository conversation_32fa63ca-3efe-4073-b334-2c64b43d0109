<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref, onMounted, watch } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import axios from 'axios';

const breadcrumbs = [
    {
        title: 'Letterhead Settings',
        href: '/settings/letterhead-settings',
    },
];

const page = usePage();
const user = page.props.auth.user;
const { showSuccess, showError } = useNotifications();

// State
const loading = ref(false);
const saving = ref(false);
const previewHtml = ref('');
const showTour = ref(false);
const tourStep = ref(0);
const dragOver = ref(false);
const uploadProgress = ref(0);
const showTemplateLibrary = ref(false);
const showMobilePreview = ref(false);

const settings = ref({
  clinic_id: null,
  header_html: '',
  footer_html: '',
  header_image_url: '',
  footer_image_url: '',
  header_file_id: null,
  footer_file_id: null,
  header_type: 'html',
  footer_type: 'html',
  header_content: null,
  footer_content: null,
  is_active: true,
});

// Template refs
const headerImageInput = ref(null);
const footerImageInput = ref(null);

// Template library data
const templates = ref([
  {
    id: 'modern',
    name: 'Modern Medical',
    description: 'Clean, contemporary design',
    preview: '/images/templates/modern-preview.png',
    header_html: '<div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px;"><h1 style="margin: 0; font-size: 28px;">{{clinic_name}}</h1><p style="margin: 10px 0 0 0; opacity: 0.9;">{{clinic_address}}</p></div>',
    footer_html: '<div style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 5px; font-size: 11px; color: #6c757d;">Generated on {{date}} | {{clinic_name}} | License: {{license}}</div>'
  },
  {
    id: 'classic',
    name: 'Classic Professional',
    description: 'Traditional medical letterhead',
    preview: '/images/templates/classic-preview.png',
    header_html: '<div style="text-align: center; border-bottom: 3px solid #2c3e50; padding-bottom: 20px;"><h1 style="margin: 0; color: #2c3e50; font-family: serif;">{{clinic_name}}</h1><div style="margin-top: 10px; font-size: 14px; color: #34495e;">{{clinic_address}}<br>{{clinic_contact}}</div></div>',
    footer_html: '<div style="text-align: center; border-top: 1px solid #bdc3c7; padding-top: 15px; font-size: 10px; color: #7f8c8d;">{{clinic_name}} | {{license}} | Generated: {{date}}</div>'
  },
  {
    id: 'pediatric',
    name: 'Pediatric Friendly',
    description: 'Colorful design for children\'s clinics',
    preview: '/images/templates/pediatric-preview.png',
    header_html: '<div style="text-align: center; padding: 20px; background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%); border-radius: 15px;"><h1 style="margin: 0; color: #2c3e50; font-family: Comic Sans MS, cursive;">{{clinic_name}} 🏥</h1><p style="margin: 10px 0 0 0; color: #34495e;">{{clinic_address}}</p></div>',
    footer_html: '<div style="text-align: center; padding: 10px; background-color: #fff3cd; border-radius: 10px; font-size: 11px; color: #856404;">Made with care at {{clinic_name}} 💙 | {{date}}</div>'
  }
]);

// Methods
const loadSettings = async () => {
  try {
    loading.value = true;
    const response = await axios.get('/letterhead/settings');
    
    if (response.data.success) {
      settings.value = { ...settings.value, ...response.data.data };
    }
  } catch (error) {
    console.error('Error loading letterhead settings:', error);
    showError('Failed to load letterhead settings');
  } finally {
    loading.value = false;
  }
};

const saveSettings = async () => {
  try {
    saving.value = true;
    
    const payload = {
      header_html: settings.value.header_html,
      footer_html: settings.value.footer_html,
      header_image_url: settings.value.header_image_url,
      footer_image_url: settings.value.footer_image_url,
      header_file_id: settings.value.header_file_id,
      footer_file_id: settings.value.footer_file_id,
      header_type: settings.value.header_type,
      footer_type: settings.value.footer_type,
      is_active: settings.value.is_active,
    };
    
    const response = await axios.post('/letterhead/settings', payload);
    
    if (response.data.success) {
      settings.value = { ...settings.value, ...response.data.data };
      showSuccess('Letterhead settings saved successfully');
    }
  } catch (error) {
    console.error('Error saving letterhead settings:', error);
    showError('Failed to save letterhead settings');
  } finally {
    saving.value = false;
  }
};

const uploadHeaderImage = async (event) => {
  const file = event.target.files?.[0] || event;
  if (!file) return;

  try {
    uploadProgress.value = 0;
    const formData = new FormData();
    formData.append('header_image', file);

    const response = await axios.post('/letterhead/upload-header-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      }
    });

    if (response.data.success) {
      settings.value.header_image_url = response.data.data.header_image_url;
      settings.value.header_file_id = response.data.data.header_file_id;
      settings.value.header_content = response.data.data.header_content;
      showSuccess('Header image uploaded successfully');
      generateLivePreview(); // Auto-update preview
    }
  } catch (error) {
    console.error('Error uploading header image:', error);
    showError('Failed to upload header image');
  } finally {
    uploadProgress.value = 0;
    dragOver.value = false;
  }

  // Reset file input
  if (headerImageInput.value) {
    headerImageInput.value.value = '';
  }
};

const uploadFooterImage = async (event) => {
  const file = event.target.files?.[0] || event;
  if (!file) return;

  try {
    uploadProgress.value = 0;
    const formData = new FormData();
    formData.append('footer_image', file);

    const response = await axios.post('/letterhead/upload-footer-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      }
    });

    if (response.data.success) {
      settings.value.footer_image_url = response.data.data.footer_image_url;
      settings.value.footer_file_id = response.data.data.footer_file_id;
      settings.value.footer_content = response.data.data.footer_content;
      showSuccess('Footer image uploaded successfully');
      generateLivePreview(); // Auto-update preview
    }
  } catch (error) {
    console.error('Error uploading footer image:', error);
    showError('Failed to upload footer image');
  } finally {
    uploadProgress.value = 0;
    dragOver.value = false;
  }

  // Reset file input
  if (footerImageInput.value) {
    footerImageInput.value.value = '';
  }
};

const removeHeaderImage = async () => {
  try {
    const response = await axios.delete('/letterhead/remove-header-image');
    
    if (response.data.success) {
      settings.value.header_image_url = '';
      settings.value.header_file_id = null;
      settings.value.header_content = null;
      settings.value.header_type = 'html';
      showSuccess('Header image removed successfully');
    }
  } catch (error) {
    console.error('Error removing header image:', error);
    showError('Failed to remove header image');
  }
};

const removeFooterImage = async () => {
  try {
    const response = await axios.delete('/letterhead/remove-footer-image');
    
    if (response.data.success) {
      settings.value.footer_image_url = '';
      settings.value.footer_file_id = null;
      settings.value.footer_content = null;
      settings.value.footer_type = 'html';
      showSuccess('Footer image removed successfully');
    }
  } catch (error) {
    console.error('Error removing footer image:', error);
    showError('Failed to remove footer image');
  }
};

const generatePreview = () => {
  // Generate a comprehensive preview HTML with professional styling
  previewHtml.value = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          line-height: 1.5;
          color: #333;
        }
        .header {
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
          margin-bottom: 30px;
          text-align: center;
        }
        .default-header {
          padding: 20px 0;
        }
        .clinic-name {
          font-size: 32px;
          font-weight: bold;
          color: #2c3e50;
          margin: 0 0 20px 0;
          letter-spacing: 0.5px;
        }
        .clinic-address {
          font-size: 14px;
          color: #34495e;
          margin-bottom: 15px;
        }
        .clinic-contact {
          font-size: 13px;
          color: #7f8c8d;
          display: flex;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;
        }
        .content {
          min-height: 300px;
          padding: 20px;
          background-color: #fafafa;
          border-radius: 5px;
          margin: 20px 0;
        }
        .footer {
          border-top: 1px solid #ccc;
          padding-top: 20px;
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
        }
        .default-footer {
          padding: 15px 0;
          background-color: #f8f9fa;
          border-radius: 5px;
          margin-top: 40px;
        }
        .footer-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 15px;
          font-size: 11px;
          color: #6c757d;
        }
      </style>
    </head>
    <body>
      <div class="header">
        ${settings.value.header_type === 'image' && settings.value.header_content
          ? `<img src="${settings.value.header_content}" style="max-height: 100px;">`
          : settings.value.header_html || `
            <div class="default-header">
              <h1 class="clinic-name">Your Clinic Name</h1>
              <div class="clinic-address">
                123 Medical Street, Healthcare City, HC 12345
              </div>
              <div class="clinic-contact">
                <span>📞 +1 (555) 123-4567</span>
                <span>✉️ <EMAIL></span>
                <span>🌐 www.yourclinic.com</span>
              </div>
            </div>
          `
        }
      </div>
      <div class="content">
        <h2>Sample Document Content</h2>
        <p>This is how your letterhead will appear on prescriptions and medical documents.</p>
        <p><strong>Professional Features:</strong></p>
        <ul>
          <li>Clean, professional layout</li>
          <li>Clinic branding and contact information</li>
          <li>Document reference numbers</li>
          <li>Generation timestamps</li>
          <li>Professional styling for medical documents</li>
        </ul>
      </div>
      <div class="footer">
        ${settings.value.footer_type === 'image' && settings.value.footer_content
          ? `<img src="${settings.value.footer_content}" style="max-height: 50px;">`
          : settings.value.footer_html || `
            <div class="default-footer">
              <div class="footer-content">
                <div style="font-style: italic;">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</div>
                <div style="font-weight: 500;">License: MED-12345</div>
                <div>Document ID: DOC-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(new Date().getHours()).padStart(2, '0')}${String(new Date().getMinutes()).padStart(2, '0')}</div>
              </div>
              <div style="margin-top: 10px; font-size: 10px; color: #adb5bd; font-style: italic;">
                This document was generated electronically and is valid without signature
              </div>
            </div>
          `
        }
      </div>
    </body>
    </html>
  `;
};

const resetSettings = () => {
  settings.value = {
    clinic_id: settings.value.clinic_id,
    header_html: '',
    footer_html: '',
    header_image_url: '',
    footer_image_url: '',
    header_file_id: null,
    footer_file_id: null,
    header_type: 'html',
    footer_type: 'html',
    header_content: null,
    footer_content: null,
    is_active: true,
  };
  previewHtml.value = '';
};

// Enhanced methods
const generateLivePreview = () => {
  // Auto-generate preview when settings change
  generatePreview();
};

const handleDragOver = (event) => {
  event.preventDefault();
  dragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  dragOver.value = false;
};

const handleDrop = (event, type = 'header') => {
  event.preventDefault();
  dragOver.value = false;

  const files = event.dataTransfer.files;
  if (files.length > 0) {
    const file = files[0];
    if (file.type.startsWith('image/')) {
      if (type === 'header') {
        uploadHeaderImage(file);
      } else {
        uploadFooterImage(file);
      }
    } else {
      showError('Please drop an image file');
    }
  }
};

const applyTemplate = (templateId) => {
  const template = templates.value.find(t => t.id === templateId);
  if (template) {
    settings.value.header_html = template.header_html;
    settings.value.footer_html = template.footer_html;
    settings.value.header_type = 'html';
    settings.value.footer_type = 'html';
    generateLivePreview();
    showSuccess(`${template.name} template applied successfully`);
    showTemplateLibrary.value = false;
  }
};

const startTour = () => {
  showTour.value = true;
  tourStep.value = 1;
};

const nextTourStep = () => {
  if (tourStep.value < 3) {
    tourStep.value++;
  } else {
    showTour.value = false;
    tourStep.value = 0;
  }
};

const skipTour = () => {
  showTour.value = false;
  tourStep.value = 0;
};

const autoSuggestContent = async () => {
  try {
    const response = await axios.get('/letterhead/suggestions');
    if (response.data.success) {
      const suggestions = response.data.data;
      settings.value.header_html = suggestions.header_html;
      settings.value.footer_html = suggestions.footer_html;
      generateLivePreview();
      showSuccess('Smart suggestions applied');
    }
  } catch (error) {
    console.error('Error getting suggestions:', error);
    showError('Failed to get suggestions');
  }
};

const checkForAutoUpdates = async () => {
  try {
    const response = await axios.get('/letterhead/auto-detect');
    if (response.data.success && response.data.data.has_changes) {
      const data = response.data.data;

      // Show notification about detected changes
      const shouldUpdate = confirm(
        `We detected updates to your clinic information. Would you like to update your letterhead?\n\n` +
        `Suggestions:\n${data.suggestions.join('\n')}`
      );

      if (shouldUpdate && data.updated_content) {
        settings.value.header_html = data.updated_content.header_html;
        settings.value.footer_html = data.updated_content.footer_html;
        generateLivePreview();
        showSuccess('Letterhead updated with latest clinic information');
      }
    }
  } catch (error) {
    console.error('Error checking for auto-updates:', error);
  }
};

const loadFieldSuggestions = async () => {
  try {
    const response = await axios.get('/letterhead/field-suggestions');
    if (response.data.success) {
      return response.data.data;
    }
  } catch (error) {
    console.error('Error loading field suggestions:', error);
  }
  return null;
};

// Watch for changes and auto-update preview
watch([
  () => settings.value.header_html,
  () => settings.value.footer_html,
  () => settings.value.header_type,
  () => settings.value.footer_type,
  () => settings.value.header_content,
  () => settings.value.footer_content
], () => {
  generateLivePreview();
}, { deep: true });

// Lifecycle
onMounted(async () => {
  await loadSettings();

  // Check if user is new and show tour
  const hasSeenTour = localStorage.getItem('letterhead_tour_seen');
  if (!hasSeenTour) {
    showTour.value = true;
    localStorage.setItem('letterhead_tour_seen', 'true');
  }

  // Check for auto-updates after loading settings
  setTimeout(() => {
    checkForAutoUpdates();
  }, 2000); // Delay to avoid overwhelming the user

  generateLivePreview();
});
</script>

<template>
  <Head title="Letterhead Settings" />

  <AppLayout>
    <SettingsLayout :breadcrumbs="breadcrumbs">
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-medium">Letterhead Settings</h3>
          <p class="text-sm text-muted-foreground">
            Customize your clinic's letterhead for prescriptions and medical documents
          </p>
          <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p class="text-sm text-blue-800">
              <strong>Default Behavior:</strong> When no custom letterhead is configured, documents will automatically use your clinic's name, address, and contact information in a professional format. You can customize the header and footer below to override this default appearance.
            </p>
          </div>
        </div>

        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>

        <div v-else class="space-y-6">
          <!-- Mobile Toggle for Preview -->
          <div class="lg:hidden">
            <button
              @click="showMobilePreview = !showMobilePreview"
              class="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100"
            >
              <span class="font-medium">{{ showMobilePreview ? 'Hide' : 'Show' }} Preview</span>
              <svg
                class="w-5 h-5 transform transition-transform"
                :class="{ 'rotate-180': showMobilePreview }"
                fill="none" stroke="currentColor" viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
          </div>

          <!-- Mobile Preview Panel -->
          <div v-if="showMobilePreview" class="lg:hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
              <div class="px-4 py-3 border-b border-gray-200">
                <h4 class="text-md font-medium">Live Preview</h4>
                <p class="text-sm text-muted-foreground">See how your letterhead will look</p>
              </div>

              <div class="p-4">
                <div v-if="previewHtml" class="border border-input rounded-lg overflow-hidden">
                  <iframe
                    :srcdoc="previewHtml"
                    class="w-full h-64 border-0"
                    sandbox="allow-same-origin"
                  ></iframe>
                </div>
                <div v-else class="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
                  <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">Click "Update Preview" to see your letterhead</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Settings Panel -->
            <div class="space-y-8">
            <!-- Quick Actions -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
              <button
                @click="showTemplateLibrary = true"
                type="button"
                class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-primary bg-primary/10 border border-primary/20 rounded-lg hover:bg-primary/20 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span class="hidden sm:inline">Template Library</span>
                <span class="sm:hidden">Templates</span>
              </button>
              <button
                @click="autoSuggestContent"
                type="button"
                class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span class="hidden sm:inline">Smart Suggestions</span>
                <span class="sm:hidden">Smart</span>
              </button>
              <button
                @click="generateLivePreview"
                type="button"
                class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span class="hidden sm:inline">Update Preview</span>
                <span class="sm:hidden">Preview</span>
              </button>
            </div>

            <!-- Header Settings -->
            <div class="space-y-4">
              <h4 class="text-md font-medium">Header Settings</h4>
            
            <!-- Header Type Selection -->
            <div class="space-y-3">
              <label class="text-sm font-medium">Header Type</label>
              <div class="flex space-x-4">
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="settings.header_type"
                    value="html"
                    class="h-4 w-4 text-primary border-input focus:ring-primary"
                  />
                  <span class="ml-2 text-sm">HTML Content</span>
                </label>
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="settings.header_type"
                    value="image"
                    class="h-4 w-4 text-primary border-input focus:ring-primary"
                  />
                  <span class="ml-2 text-sm">Image</span>
                </label>
              </div>
            </div>

            <!-- Header HTML Content -->
            <div v-if="settings.header_type === 'html'" class="space-y-2">
              <label class="block text-sm font-medium">Header HTML Content</label>
              <textarea
                v-model="settings.header_html"
                rows="6"
                class="w-full px-3 py-2 border border-input rounded-md shadow-sm focus:ring-primary focus:border-primary"
                placeholder="Enter custom HTML for header (leave empty to use default clinic information)"
              ></textarea>
              <p class="text-xs text-muted-foreground">
                You can use HTML tags for formatting. Leave empty to use default clinic header.
              </p>
            </div>

            <!-- Header Image Upload -->
            <div v-if="settings.header_type === 'image'" class="space-y-4">
              <div v-if="settings.header_content" class="space-y-2">
                <label class="block text-sm font-medium">Current Header Image</label>
                <div class="flex items-center space-x-4">
                  <img
                    :src="settings.header_content"
                    alt="Header Image"
                    class="h-20 w-auto border border-input rounded"
                  />
                  <button
                    @click="removeHeaderImage"
                    type="button"
                    class="px-3 py-1 text-sm text-destructive hover:text-destructive/80"
                  >
                    Remove
                  </button>
                </div>
              </div>

              <!-- Drag and Drop Zone -->
              <div
                class="relative border-2 border-dashed rounded-lg p-6 text-center transition-colors"
                :class="{
                  'border-primary bg-primary/5': dragOver,
                  'border-gray-300 hover:border-gray-400': !dragOver
                }"
                @dragover="handleDragOver"
                @dragleave="handleDragLeave"
                @drop="(e) => handleDrop(e, 'header')"
              >
                <div v-if="uploadProgress > 0" class="mb-4">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-primary h-2 rounded-full transition-all duration-300"
                      :style="{width: uploadProgress + '%'}"
                    ></div>
                  </div>
                  <p class="text-sm text-muted-foreground mt-2">Uploading... {{ uploadProgress }}%</p>
                </div>

                <div v-else>
                  <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <div class="mt-4">
                    <label for="header-file-upload" class="cursor-pointer">
                      <span class="mt-2 block text-sm font-medium text-gray-900">
                        {{ settings.header_content ? 'Replace Header Image' : 'Upload Header Image' }}
                      </span>
                      <span class="mt-2 block text-xs text-muted-foreground">
                        Drag and drop or click to upload
                      </span>
                    </label>
                    <input
                      id="header-file-upload"
                      ref="headerImageInput"
                      type="file"
                      accept="image/*"
                      @change="uploadHeaderImage"
                      class="sr-only"
                    />
                  </div>
                  <p class="text-xs text-muted-foreground mt-2">
                    Recommended: 800x200px • Max: 2MB • JPG, PNG, GIF
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer Settings -->
          <div class="space-y-4 border-t pt-8">
            <h4 class="text-md font-medium">Footer Settings</h4>
            
            <!-- Footer Type Selection -->
            <div class="space-y-3">
              <label class="text-sm font-medium">Footer Type</label>
              <div class="flex space-x-4">
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="settings.footer_type"
                    value="html"
                    class="h-4 w-4 text-primary border-input focus:ring-primary"
                  />
                  <span class="ml-2 text-sm">HTML Content</span>
                </label>
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="settings.footer_type"
                    value="image"
                    class="h-4 w-4 text-primary border-input focus:ring-primary"
                  />
                  <span class="ml-2 text-sm">Image</span>
                </label>
              </div>
            </div>

            <!-- Footer HTML Content -->
            <div v-if="settings.footer_type === 'html'" class="space-y-2">
              <label class="block text-sm font-medium">Footer HTML Content</label>
              <textarea
                v-model="settings.footer_html"
                rows="4"
                class="w-full px-3 py-2 border border-input rounded-md shadow-sm focus:ring-primary focus:border-primary"
                placeholder="Enter custom HTML for footer (leave empty to use default footer)"
              ></textarea>
              <p class="text-xs text-muted-foreground">
                You can use HTML tags for formatting. Leave empty to use default footer.
              </p>
            </div>

            <!-- Footer Image Upload -->
            <div v-if="settings.footer_type === 'image'" class="space-y-4">
              <div v-if="settings.footer_content" class="space-y-2">
                <label class="block text-sm font-medium">Current Footer Image</label>
                <div class="flex items-center space-x-4">
                  <img
                    :src="settings.footer_content"
                    alt="Footer Image"
                    class="h-16 w-auto border border-input rounded"
                  />
                  <button
                    @click="removeFooterImage"
                    type="button"
                    class="px-3 py-1 text-sm text-destructive hover:text-destructive/80"
                  >
                    Remove
                  </button>
                </div>
              </div>
              
              <div class="space-y-2">
                <label class="block text-sm font-medium">
                  {{ settings.footer_content ? 'Replace Footer Image' : 'Upload Footer Image' }}
                </label>
                <input
                  ref="footerImageInput"
                  type="file"
                  accept="image/*"
                  @change="uploadFooterImage"
                  class="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-muted file:text-muted-foreground hover:file:bg-muted/80"
                />
                <p class="text-xs text-muted-foreground">
                  Recommended size: 800x100px. Max file size: 2MB. Formats: JPG, PNG, GIF
                </p>
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div class="border-t pt-8">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-md font-medium">Preview</h4>
              <button
                @click="generatePreview"
                type="button"
                class="px-4 py-2 text-sm font-medium text-primary bg-primary/10 border border-primary/20 rounded-md hover:bg-primary/20"
              >
                Generate Preview
              </button>
            </div>
            
            <div v-if="previewHtml" class="border border-input rounded-lg p-4 bg-muted/50">
              <iframe
                :srcdoc="previewHtml"
                class="w-full h-96 border-0 rounded"
                sandbox="allow-same-origin"
              ></iframe>
            </div>
          </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 border-t pt-6">
              <button
                @click="resetSettings"
                type="button"
                class="px-4 py-2 text-sm font-medium border border-input rounded-md hover:bg-muted"
              >
                Reset
              </button>
              <button
                @click="saveSettings"
                :disabled="saving"
                type="button"
                class="px-4 py-2 text-sm font-medium text-primary-foreground bg-primary border border-transparent rounded-md hover:bg-primary/90 disabled:opacity-50"
              >
                {{ saving ? 'Saving...' : 'Save Settings' }}
              </button>
            </div>
          </div>
        </div>

          <!-- Live Preview Panel -->
          <div class="space-y-4">
            <div class="sticky top-4">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-4 py-3 border-b border-gray-200">
                  <h4 class="text-md font-medium">Live Preview</h4>
                  <p class="text-sm text-muted-foreground">See how your letterhead will look</p>
                </div>

                <div class="p-4">
                  <div v-if="previewHtml" class="border border-input rounded-lg overflow-hidden">
                    <iframe
                      :srcdoc="previewHtml"
                      class="w-full h-96 border-0"
                      sandbox="allow-same-origin"
                    ></iframe>
                  </div>
                  <div v-else class="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
                    <div class="text-center">
                      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <p class="mt-2 text-sm text-gray-500">Click "Update Preview" to see your letterhead</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Template Library Modal -->
      <div v-if="showTemplateLibrary" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
          <div class="flex items-center justify-between p-6 border-b">
            <h3 class="text-lg font-medium">Template Library</h3>
            <button @click="showTemplateLibrary = false" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <div class="p-6 overflow-auto max-h-[calc(90vh-120px)]">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="template in templates"
                :key="template.id"
                class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors"
                @click="applyTemplate(template.id)"
              >
                <div class="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                  <span class="text-2xl">📄</span>
                </div>
                <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-primary bg-primary/10 rounded-md hover:bg-primary/20">
                  Apply Template
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Onboarding Tour -->
      <div v-if="showTour" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
          <div class="p-6">
            <div v-if="tourStep === 1">
              <h3 class="text-lg font-medium mb-4">Welcome to Letterhead Settings! 🎉</h3>
              <p class="text-gray-600 mb-4">
                Let's set up your professional letterhead in just a few steps. Your documents will look amazing!
              </p>
              <div class="flex justify-between">
                <button @click="skipTour" class="text-gray-500 hover:text-gray-700">Skip Tour</button>
                <button @click="nextTourStep" class="px-4 py-2 bg-primary text-white rounded-md">Get Started</button>
              </div>
            </div>

            <div v-if="tourStep === 2">
              <h3 class="text-lg font-medium mb-4">Choose Your Style 🎨</h3>
              <p class="text-gray-600 mb-4">
                You can either use our beautiful templates from the Template Library, or create custom HTML content.
                For images, just drag and drop!
              </p>
              <div class="flex justify-between">
                <button @click="tourStep = 1" class="text-gray-500 hover:text-gray-700">Back</button>
                <button @click="nextTourStep" class="px-4 py-2 bg-primary text-white rounded-md">Next</button>
              </div>
            </div>

            <div v-if="tourStep === 3">
              <h3 class="text-lg font-medium mb-4">Live Preview 👁️</h3>
              <p class="text-gray-600 mb-4">
                Watch your changes come to life in real-time! The preview panel shows exactly how your letterhead will look on documents.
              </p>
              <div class="flex justify-between">
                <button @click="tourStep = 2" class="text-gray-500 hover:text-gray-700">Back</button>
                <button @click="nextTourStep" class="px-4 py-2 bg-primary text-white rounded-md">Finish</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SettingsLayout>
  </AppLayout>
</template>
