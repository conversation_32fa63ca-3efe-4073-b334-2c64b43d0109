<template>
  <Dialog :open="true" @update:open="$emit('close')">
    <DialogContent class="max-w-2xl">
      <DialogHeader>
        <DialogTitle class="flex items-center">
          <Pill class="w-5 h-5 mr-2" />
          Add Prescription
        </DialogTitle>
        <DialogDescription>
          Add a new medication prescription for this consultation.
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="savePrescription" class="space-y-6">
        <!-- Medication Name -->
        <div class="space-y-2">
          <Label for="medication_name">Medication Name *</Label>
          <div class="relative">
            <Input
              id="medication_name"
              v-model="form.medication_name"
              placeholder="Search for medication..."
              required
              @input="searchMedications"
              class="pr-10"
            />
            <Search class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <div v-if="searchingMedications" class="absolute right-10 top-1/2 transform -translate-y-1/2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            </div>
          </div>

          <!-- Medication Search Results -->
          <div v-if="medicationSuggestions.length" class="border rounded-md max-h-60 overflow-y-auto z-50 bg-white shadow-lg">
            <div
              v-for="medication in medicationSuggestions"
              :key="medication.rxcui || medication"
              @click="selectMedication(medication)"
              class="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
            >
              <div v-if="typeof medication === 'object'" class="space-y-1">
                <div class="font-medium">{{ medication.display_name || medication.name }}</div>
                <div class="text-sm text-gray-500">
                  RxCUI: {{ medication.rxcui }}
                  <span v-if="medication.score" class="ml-2 text-xs bg-blue-100 px-2 py-1 rounded">
                    {{ Math.round(medication.score) }}% match
                  </span>
                </div>
              </div>
              <div v-else>{{ medication }}</div>
            </div>
          </div>

          <!-- No Results / Suggestions -->
          <div v-if="showNoResults" class="border rounded-md bg-white shadow-sm p-3">
            <div class="text-sm text-gray-500 mb-2">No medications found. Did you mean:</div>
            <div v-if="spellingSuggestions.length > 0" class="space-y-1">
              <button
                v-for="suggestion in spellingSuggestions"
                :key="suggestion"
                type="button"
                @click="form.medication_name = suggestion; searchMedications()"
                class="text-blue-600 hover:text-blue-800 text-sm mr-2"
              >
                {{ suggestion }}
              </button>
            </div>
          </div>

          <!-- Selected Drug Details -->
          <div v-if="selectedDrugDetails" class="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div class="text-sm font-medium text-blue-900">Selected Medication Details</div>
            <div class="text-sm text-blue-700 mt-1">
              <div v-if="selectedDrugDetails.properties">
                <strong>Generic Name:</strong> {{ selectedDrugDetails.properties.name }}
              </div>
              <div v-if="selectedDrugDetails.drug_classes && selectedDrugDetails.drug_classes.length > 0">
                <strong>Drug Class:</strong> {{ selectedDrugDetails.drug_classes[0].className }}
              </div>
            </div>
          </div>
        </div>

        <!-- Dosage and Frequency Row -->
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="dosage">Dosage</Label>
            <Input
              id="dosage"
              v-model="form.dosage"
              placeholder="e.g., 500mg"
            />
          </div>
          <div class="space-y-2">
            <Label for="frequency">Frequency</Label>
            <Select v-model="form.frequency">
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="once_daily">Once daily</SelectItem>
                <SelectItem value="twice_daily">Twice daily</SelectItem>
                <SelectItem value="three_times_daily">Three times daily</SelectItem>
                <SelectItem value="four_times_daily">Four times daily</SelectItem>
                <SelectItem value="as_needed">As needed</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Duration -->
        <div class="space-y-2">
          <Label for="duration">Duration</Label>
          <div class="grid grid-cols-2 gap-4">
            <Input
              v-model="durationValue"
              type="number"
              min="1"
              placeholder="Duration"
            />
            <Select v-model="durationUnit">
              <SelectTrigger>
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="days">Days</SelectItem>
                <SelectItem value="weeks">Weeks</SelectItem>
                <SelectItem value="months">Months</SelectItem>
                <SelectItem value="ongoing">Ongoing</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Instructions -->
        <div class="space-y-2">
          <Label for="instructions">Instructions</Label>
          <Textarea
            id="instructions"
            v-model="form.instructions"
            placeholder="Special instructions for taking this medication..."
            rows="3"
          />
        </div>



        <!-- Status -->
        <div class="space-y-2">
          <Label for="status">Status</Label>
          <Select v-model="form.status">
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="discontinued">Discontinued</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" @click="$emit('close')">
            Cancel
          </Button>
          <Button type="submit" :disabled="saving">
            <Loader2 v-if="saving" class="w-4 h-4 mr-2 animate-spin" />
            Add Prescription
          </Button>
        </div>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Pill, Search, Loader2 } from 'lucide-vue-next'
import axios from 'axios'
import drugSearchService from '@/services/drugSearchService'

interface Props {
  consultationId: string | number
}

interface Emits {
  (e: 'close'): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form data
const form = ref({
  medication_name: '',
  dosage: '',
  frequency: '',
  duration: '',
  instructions: '',
  status: 'active'
})

const durationValue = ref('')
const durationUnit = ref('')
const saving = ref(false)
const medicationSuggestions = ref([])
const searchingMedications = ref(false)
const spellingSuggestions = ref([])
const selectedDrugDetails = ref(null)
const showNoResults = ref(false)
const selectedRxcui = ref('')

let searchTimeout: NodeJS.Timeout



// Watch duration inputs to combine them
watch([durationValue, durationUnit], ([value, unit]) => {
  if (value && unit) {
    if (unit === 'ongoing') {
      form.value.duration = 'Ongoing'
    } else {
      form.value.duration = `${value} ${unit}`
    }
  }
})

// Methods
const searchMedications = () => {
  const query = form.value.medication_name.trim()

  // Clear previous results
  medicationSuggestions.value = []
  spellingSuggestions.value = []
  showNoResults.value = false

  if (query.length < 2) {
    return
  }

  // Clear previous timeout
  clearTimeout(searchTimeout)

  // Debounce search
  searchTimeout = setTimeout(async () => {
    await performDrugSearch(query)
  }, 300)
}

const performDrugSearch = async (query: string) => {
  try {
    searchingMedications.value = true

    const data = await drugSearchService.searchDrugs(query, 10)

    if (data.success) {
      medicationSuggestions.value = data.data.drugs

      // If no results, get spelling suggestions
      if (data.data.drugs.length === 0 && data.data.has_suggestions) {
        spellingSuggestions.value = data.data.suggestions
        showNoResults.value = true
      }
    }
  } catch (error) {
    console.error('Error searching medications:', error)
    medicationSuggestions.value = []
  } finally {
    searchingMedications.value = false
  }
}

const selectMedication = async (medication: any) => {
  if (typeof medication === 'string') {
    form.value.medication_name = medication
  } else {
    form.value.medication_name = medication.display_name || medication.name
    selectedRxcui.value = medication.rxcui

    // Fetch detailed drug information
    if (medication.rxcui) {
      await fetchDrugDetails(medication.rxcui)
    }
  }

  medicationSuggestions.value = []
  showNoResults.value = false
}

const fetchDrugDetails = async (rxcui: string) => {
  try {
    const data = await drugSearchService.getDrugDetails(rxcui)

    if (data.success) {
      selectedDrugDetails.value = data.data
    }
  } catch (error) {
    console.error('Error fetching drug details:', error)
  }
}



const savePrescription = async () => {
  try {
    saving.value = true

    const payload = {
      consultation_id: props.consultationId,
      ...form.value,
      rxcui: selectedRxcui.value,
      drug_details: selectedDrugDetails.value
    }

    await axios.post('/consultation-prescriptions', payload)

    emit('saved')
    emit('close')
  } catch (error) {
    console.error('Error saving prescription:', error)
    alert('Failed to save prescription. Please try again.')
  } finally {
    saving.value = false
  }
}
</script>
