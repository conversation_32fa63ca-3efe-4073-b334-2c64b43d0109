<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

trait ClinicFilterable
{
    /**
     * Apply clinic filtering to a query based on the authenticated user's clinic association.
     */
    public function scopeForUserClinic(Builder $query, $user = null)
    {
        $user = $user ?: Auth::user();
        
        if (!$user) {
            return $query->whereRaw('1 = 0'); // Return no results if no user
        }

        // Admin users can see all data
        if ($user->hasRole('admin')) {
            return $query;
        }

        $clinicId = $user->clinic_id;
        
        if (!$clinicId) {
            return $query->whereRaw('1 = 0'); // Return no results if no clinic association
        }

        return $query->where('clinic_id', $clinicId);
    }

    /**
     * Get the clinic ID for the current user.
     */
    public static function getUserClinicId($user = null)
    {
        $user = $user ?: Auth::user();
        
        if (!$user || $user->hasRole('admin')) {
            return null;
        }

        return $user->clinic_id;
    }

    /**
     * Check if the current user can access data from a specific clinic.
     */
    public static function canAccessClinic($clinicId, $user = null)
    {
        $user = $user ?: Auth::user();
        
        if (!$user) {
            return false;
        }

        // Admin users can access any clinic
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->clinic_id == $clinicId;
    }

    /**
     * Apply clinic filtering to relationships.
     */
    public function scopeWithClinicFiltering(Builder $query, $relationshipName, $user = null)
    {
        $user = $user ?: Auth::user();

        if (!$user || $user->hasRole('admin')) {
            return $query;
        }

        $clinicId = $user->clinic_id;

        if (!$clinicId) {
            return $query->whereRaw('1 = 0');
        }

        return $query->whereHas($relationshipName, function ($q) use ($clinicId) {
            $q->where('clinic_id', $clinicId);
        });
    }

    /**
     * Apply direct clinic filtering using clinic_id column.
     * This is more efficient than relationship-based filtering.
     */
    public function scopeForClinic(Builder $query, $user = null)
    {
        $user = $user ?: Auth::user();

        if (!$user || $user->hasRole('admin')) {
            return $query;
        }

        $clinicId = $user->clinic_id;

        if (!$clinicId) {
            return $query->whereRaw('1 = 0');
        }

        return $query->where('clinic_id', $clinicId);
    }
}
