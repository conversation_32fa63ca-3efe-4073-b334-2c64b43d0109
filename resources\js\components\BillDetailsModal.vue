<!--
BillDetailsModal - Reusable component for displaying bill details in a modal

Usage:
<BillDetailsModal
    :isOpen="showModal"
    :billId="selectedBillId"
    @close="closeModal"
    @sent="onBillSent"
/>

Props:
- isOpen: Boolean - Controls modal visibility
- billId: Number/String - ID of the bill to display

Events:
- close: Emitted when modal should be closed
- sent: Emitted when bill is successfully sent to patient
-->
<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity" @click="closeModal"></div>

            <!-- Modal panel -->
            <div class="relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <!-- Header -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Bill Details</h3>
                        <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Loading state -->
                <div v-if="loading" class="px-6 py-8 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-2 text-gray-600">Loading bill details...</p>
                </div>

                <!-- Error state -->
                <div v-else-if="error" class="px-6 py-8 text-center">
                    <div class="text-red-600 mb-2">
                        <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600">{{ error }}</p>
                </div>

                <!-- Bill details content -->
                <div v-else-if="bill" class="px-6 py-4">
                    <!-- Bill Header -->
                    <div class="mb-6">
                        <h4 class="text-xl font-semibold text-gray-900">{{ bill.title || 'Dr. Tang Practice' }}</h4>
                        <p class="text-sm text-gray-600">Invoice ID: {{ bill.bill_number }}</p>
                        <p class="text-sm text-gray-600">Created on: {{ formatDate(bill.bill_date) }}</p>
                        <div class="mt-2">
                            <span :class="getStatusClass(bill.payment_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                {{ getStatusLabel(bill.payment_status) }}
                            </span>
                        </div>
                    </div>

                    <!-- Patient Details -->
                    <div class="mb-6">
                        <h5 class="text-lg font-medium text-gray-900 mb-3">Patient Details</h5>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                <p class="text-sm text-gray-900">{{ bill.patient?.user?.name || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Gender</label>
                                <p class="text-sm text-gray-900">{{ bill.patient?.gender || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                                <p class="text-sm text-gray-900">{{ formatDate(bill.patient?.date_of_birth) || 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email</label>
                                <p class="text-sm text-gray-900">{{ bill.patient?.user?.email || 'N/A' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Services -->
                    <div class="mb-6">
                        <h5 class="text-lg font-medium text-gray-900 mb-3">Services</h5>
                        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-if="bill.bill_items && bill.bill_items.length > 0" v-for="item in bill.bill_items" :key="item.id">
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ item.item_name }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">£{{ item.unit_price }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ item.quantity }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">£{{ item.total_price }}</td>
                                    </tr>
                                    <tr v-else>
                                        <td colspan="4" class="px-4 py-8 text-center text-sm text-gray-500">
                                            No services found for this bill
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Totals -->
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="font-medium">Total:</span>
                                <span>£{{ bill.total_amount }}</span>
                            </div>
                            <div v-if="bill.discount_amount && bill.discount_amount > 0" class="flex justify-between text-sm">
                                <span class="font-medium">Discount:</span>
                                <span>£{{ bill.discount_amount }}</span>
                            </div>
                            <div v-if="bill.paid_amount && bill.paid_amount > 0" class="flex justify-between text-sm">
                                <span class="font-medium">Paid Amount:</span>
                                <span class="text-green-600">£{{ bill.paid_amount }}</span>
                            </div>
                            <div v-if="shouldShowDueAmount(bill)" class="flex justify-between text-lg font-semibold border-t pt-2">
                                <span>Amount Due:</span>
                                <span class="text-red-600">£{{ calculateDueAmount(bill) }}</span>
                            </div>
                            <div v-else-if="bill.payment_status === 'paid'" class="flex justify-between text-lg font-semibold border-t pt-2">
                                <span>Status:</span>
                                <span class="text-green-600">Fully Paid</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div v-if="!loading && !error" class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                    <!-- Send to Patient - Only for unpaid/partially paid bills -->
                    <button v-if="canSendToPatient(bill)"
                            @click="sendToPatient"
                            :disabled="sending"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        <svg v-if="sending" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Send to Patient
                    </button>

                    <!-- Mark as Paid - Only for unpaid/partially paid bills -->
                    <button v-if="canMarkAsPaid(bill)"
                            @click="markAsPaid"
                            :disabled="marking"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
                        <svg v-if="marking" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Mark as Paid
                    </button>

                    <!-- Download Receipt - Only for paid bills -->
                    <button v-if="canDownloadReceipt(bill)"
                            @click="downloadReceipt"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        Download Receipt
                    </button>

                    <!-- View Invoice - Always available -->
                    <button @click="viewInvoice"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        View Invoice
                    </button>

                    <button @click="closeModal"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import axios from 'axios'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    billId: {
        type: [Number, String],
        default: null
    }
})

const emit = defineEmits(['close', 'sent'])

const bill = ref(null)
const loading = ref(false)
const error = ref(null)
const sending = ref(false)
const marking = ref(false)

// Watch for modal open and bill ID changes
watch([() => props.isOpen, () => props.billId], ([isOpen, billId]) => {
    if (isOpen && billId) {
        fetchBillDetails()
    } else {
        resetState()
    }
})

const fetchBillDetails = async () => {
    loading.value = true
    error.value = null

    try {
        const response = await axios.get(`/get-bill-details/${props.billId}`)

        if (response.data.success) {
            bill.value = response.data.data
        } else {
            error.value = response.data.message || 'Failed to load bill details'
        }
    } catch (err) {
        error.value = err.response?.data?.message || 'Failed to load bill details'
    } finally {
        loading.value = false
    }
}

const resetState = () => {
    bill.value = null
    error.value = null
    loading.value = false
    sending.value = false
    marking.value = false
}

const closeModal = () => {
    emit('close')
}

const sendToPatient = async () => {
    if (!bill.value) return

    sending.value = true

    try {
        const response = await axios.post(`/bills/${bill.value.id}/send-to-patient`)

        if (response.data.success) {
            // Show success notification
            if (window.toastify) {
                window.toastify('Bill sent to patient successfully!', 'success')
            }
            emit('sent', bill.value)
            closeModal()
        } else {
            error.value = response.data.message || 'Failed to send bill to patient'
        }
    } catch (err) {
        error.value = err.response?.data?.message || 'Failed to send bill to patient'
        // Show error notification
        if (window.toastify) {
            window.toastify(error.value, 'error')
        }
    } finally {
        sending.value = false
    }
}

const markAsPaid = async () => {
    if (!bill.value) return

    marking.value = true

    try {
        const response = await axios.post(`/bills/${bill.value.id}/mark-paid`)

        if (response.data.success) {
            // Show success notification
            if (window.toastify) {
                window.toastify('Bill marked as paid successfully!', 'success')
            }
            // Refresh bill data
            await fetchBillDetails()
            emit('sent', bill.value) // Notify parent to refresh
        } else {
            error.value = response.data.message || 'Failed to mark bill as paid'
        }
    } catch (err) {
        error.value = err.response?.data?.message || 'Failed to mark bill as paid'
        // Show error notification
        if (window.toastify) {
            window.toastify(error.value, 'error')
        }
    } finally {
        marking.value = false
    }
}

const downloadReceipt = async () => {
    if (!bill.value) return

    try {
        const response = await axios.get(`/bills/${bill.value.id}/download-receipt`, {
            responseType: 'blob'
        })

        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `receipt-${bill.value.bill_number}.pdf`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        if (window.toastify) {
            window.toastify('Receipt downloaded successfully!', 'success')
        }
    } catch (err) {
        const errorMessage = err.response?.data?.message || 'Failed to download receipt'
        if (window.toastify) {
            window.toastify(errorMessage, 'error')
        }
    }
}

const viewInvoice = () => {
    if (bill.value) {
        window.open(`/bills/${bill.value.id}/preview-invoice`, '_blank')
    }
}

// Business logic functions
const shouldShowDueAmount = (bill) => {
    if (!bill) return false
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const calculateDueAmount = (bill) => {
    if (!bill || bill.payment_status === 'paid') return 0

    // If there's a specific due amount, use it; otherwise use total amount
    const dueAmount = bill.due_amount || bill.total_amount
    const paidAmount = bill.paid_amount || 0

    return Math.max(0, dueAmount - paidAmount)
}

const canSendToPatient = (bill) => {
    if (!bill) return false
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const canMarkAsPaid = (bill) => {
    if (!bill) return false
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const canDownloadReceipt = (bill) => {
    if (!bill) return false
    return bill.payment_status === 'paid'
}

// Utility functions
const formatDate = (date) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleDateString('en-GB', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}

const getStatusClass = (status) => {
    const classes = {
        'paid': 'bg-green-100 text-green-800',
        'unpaid': 'bg-red-100 text-red-800',
        'partially_paid': 'bg-yellow-100 text-yellow-800',
        'sent_to_patient': 'bg-blue-100 text-blue-800'
    }
    return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status) => {
    const labels = {
        'paid': 'Paid',
        'unpaid': 'Unpaid',
        'partially_paid': 'Partially Paid',
        'sent_to_patient': 'Sent to Patient'
    }
    return labels[status] || status
}
</script>
