<template>
  <div class="flex items-center space-x-2">
    <!-- Toggle Switch -->
    <div class="relative">
      <input
        :id="`clinician-toggle-${user.id}`"
        type="checkbox"
        :checked="localIsClinicianState"
        @change="toggleAccess"
        :disabled="!canToggle || loading"
        class="sr-only"
      />
      <label
        :for="`clinician-toggle-${user.id}`"
        class="flex items-center cursor-pointer"
        :class="{ 'cursor-not-allowed opacity-50': !canToggle || loading }"
      >
        <div
          class="relative w-10 h-6 rounded-full transition-colors duration-200 ease-in-out"
          :class="localIsClinicianState ? 'bg-teal-600' : 'bg-gray-300'"
        >
          <div
            class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition-transform duration-200 ease-in-out"
            :class="localIsClinicianState ? 'transform translate-x-4' : ''"
          ></div>
        </div>
      </label>
    </div>

    <!-- Status Badge -->
    <span
      :class="localIsClinicianState ? 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'"
      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
    >
      {{ localIsClinicianState ? 'Clinician' : 'Standard' }}
    </span>

    <!-- Loading Indicator -->
    <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>

    <!-- Info Icon with Tooltip -->
    <div class="relative group" v-if="showInfo">
      <Icon name="info" class="w-4 h-4 text-gray-400 cursor-help" />
      <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
        {{ localIsClinicianState ? 'Can access clinical modules' : 'Cannot access clinical modules' }}
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useApi } from '@/composables/useApi'
import Icon from '../Icon.vue'

interface User {
  id: number
  name: string
  email: string
  is_clinician: boolean
  roles?: Array<{ id: number; name: string }>
}

interface Props {
  user: User
  showInfo?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'updated', user: User): void
  (e: 'error', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showInfo: true,
  disabled: false
})

const emit = defineEmits<Emits>()

const api = useApi()
const loading = ref(false)

// Local reactive state for immediate UI updates
const localIsClinicianState = ref(props.user.is_clinician)

const canToggle = computed(() => {
  if (props.disabled) return false

  // Don't allow toggling for patients (check both role field and roles array)
  if (props.user.role === 'patient' || props.user.roles?.some(role => role.name === 'patient')) {
    return false
  }

  // Don't allow toggling for admins (they shouldn't need clinical access)
  if (props.user.role === 'admin' || props.user.roles?.some(role => role.name === 'admin')) {
    return false
  }

  return true
})

const toggleAccess = async () => {
  if (!canToggle.value || loading.value) return

  const newStatus = !localIsClinicianState.value
  const action = newStatus ? 'enable' : 'disable'

  // Show confirmation for disabling
  if (!newStatus) {
    const confirmed = confirm(
      `Are you sure you want to disable clinician access for ${props.user.name}? ` +
      'They will no longer be able to access consultations, prescriptions, and other clinical features.'
    )
    if (!confirmed) return
  }

  try {
    loading.value = true

    // Update local state immediately for smooth UI
    localIsClinicianState.value = newStatus

    const response = await api.post(`/users/${props.user.id}/toggle-clinician`, {
      is_clinician: newStatus
    })

    if (response?.data) {
      // Emit the updated user for parent component
      const updatedUser = { ...props.user, is_clinician: newStatus }
      emit('updated', updatedUser)

      // Show success message
      const message = response.data.message || `Clinician access ${action}d successfully`

      console.log('Success:', message)
    } else {
      // Revert local state if API call failed
      localIsClinicianState.value = !newStatus
    }
  } catch (error: any) {
    console.error('Error toggling clinician access:', error)

    // Revert local state on error
    localIsClinicianState.value = !newStatus

    let errorMessage = 'Failed to update clinician access'

    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    }

    emit('error', errorMessage)

    // Show error message to user
    alert(errorMessage)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Additional styles for the toggle switch if needed */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
