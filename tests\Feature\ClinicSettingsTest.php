<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Clinic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class ClinicSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'clinic_admin']);
        Role::create(['name' => 'provider']);
        Role::create(['name' => 'patient']);
    }

    /** @test */
    public function clinic_admin_can_access_clinic_settings_page()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_access_clinic_settings_page()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('admin');

        $response = $this->actingAs($user)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function non_admin_users_cannot_access_clinic_settings_page()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('provider');

        $response = $this->actingAs($user)->get('/settings/clinic');

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_users_cannot_access_clinic_settings_page()
    {
        $response = $this->get('/settings/clinic');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function clinic_admin_can_get_their_clinic_data()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Test Clinic',
            'email' => '<EMAIL>',
            'phone' => '************'
        ]);
        
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $clinic->id,
                    'name' => 'Test Clinic',
                    'email' => '<EMAIL>',
                    'phone' => '************'
                ]
            ]);
    }

    /** @test */
    public function admin_can_get_clinic_data_if_they_have_clinic_association()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Admin Clinic',
            'email' => '<EMAIL>'
        ]);
        
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('admin');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $clinic->id,
                    'name' => 'Admin Clinic',
                    'email' => '<EMAIL>'
                ]
            ]);
    }

    /** @test */
    public function user_without_clinic_association_cannot_get_clinic_data()
    {
        $user = User::factory()->create(['clinic_id' => null]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'No clinic association found'
            ]);
    }

    /** @test */
    public function provider_cannot_get_clinic_data()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('provider');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(403);
    }

    /** @test */
    public function clinic_admin_can_update_their_clinic_settings()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>'
        ]);
        
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $updateData = [
            'name' => 'Updated Clinic Name',
            'email' => '<EMAIL>',
            'phone' => '************',
            'website' => 'https://updated-clinic.com',
            'address' => '123 Updated St',
            'city' => 'Updated City',
            'state' => 'Updated State',
            'postal_code' => '12345',
            'country' => 'Updated Country',
            'description' => 'Updated description',
            'license_number' => 'LIC123',
            'tax_id' => 'TAX456',
            'accepts_new_patients' => true,
            'telemedicine_enabled' => true
        ];

        $response = $this->actingAs($user)->post('/settings/clinic/update', $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Clinic settings updated successfully'
            ]);

        $this->assertDatabaseHas('clinics', [
            'id' => $clinic->id,
            'name' => 'Updated Clinic Name',
            'email' => '<EMAIL>',
            'phone' => '************'
        ]);
    }

    /** @test */
    public function user_cannot_update_other_clinic_settings()
    {
        $clinic1 = Clinic::factory()->create(['name' => 'Clinic 1']);
        $clinic2 = Clinic::factory()->create(['name' => 'Clinic 2']);
        
        $user = User::factory()->create(['clinic_id' => $clinic1->id]);
        $user->assignRole('clinic_admin');

        // Try to update clinic2 (should fail because user belongs to clinic1)
        $updateData = ['name' => 'Hacked Name'];

        $response = $this->actingAs($user)->post('/settings/clinic/update', $updateData);

        // Should update user's own clinic (clinic1), not clinic2
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('clinics', [
            'id' => $clinic1->id,
            'name' => 'Hacked Name' // User's own clinic gets updated
        ]);
        
        $this->assertDatabaseHas('clinics', [
            'id' => $clinic2->id,
            'name' => 'Clinic 2' // Other clinic remains unchanged
        ]);
    }

    /** @test */
    public function update_clinic_settings_validates_required_fields()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->post('/settings/clinic/update', [
            'name' => '', // Required field left empty
            'email' => 'invalid-email' // Invalid email format
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function provider_cannot_update_clinic_settings()
    {
        $clinic = Clinic::factory()->create(['name' => 'Original Name']);
        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('provider');

        $response = $this->actingAs($user)->post('/settings/clinic/update', [
            'name' => 'Hacked Name'
        ]);

        $response->assertStatus(403);
        
        $this->assertDatabaseHas('clinics', [
            'id' => $clinic->id,
            'name' => 'Original Name' // Name should remain unchanged
        ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_clinic_data()
    {
        $response = $this->get('/settings/clinic/data');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function unauthenticated_user_cannot_update_clinic_settings()
    {
        $response = $this->post('/settings/clinic/update', [
            'name' => 'Hacked Name'
        ]);

        $response->assertRedirect('/login');
    }

    /** @test */
    public function clinic_data_endpoint_returns_correct_structure()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Test Clinic',
            'email' => '<EMAIL>',
            'phone' => '************',
            'website' => 'https://testclinic.com',
            'address' => '123 Test St',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
            'description' => 'Test description',
            'license_number' => 'LIC123',
            'tax_id' => 'TAX456',
            'accepts_new_patients' => true,
            'telemedicine_enabled' => false
        ]);

        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'phone',
                    'website',
                    'address',
                    'city',
                    'state',
                    'postal_code',
                    'country',
                    'description',
                    'license_number',
                    'tax_id',
                    'accepts_new_patients',
                    'telemedicine_enabled',
                    'created_at',
                    'updated_at'
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'name' => 'Test Clinic',
                    'email' => '<EMAIL>',
                    'accepts_new_patients' => true,
                    'telemedicine_enabled' => false
                ]
            ]);
    }

    /** @test */
    public function update_clinic_settings_returns_updated_data()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>'
        ]);

        $user = User::factory()->create(['clinic_id' => $clinic->id]);
        $user->assignRole('clinic_admin');

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '************'
        ];

        $response = $this->actingAs($user)->post('/settings/clinic/update', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'phone',
                    'updated_at'
                ]
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Clinic settings updated successfully',
                'data' => [
                    'name' => 'Updated Name',
                    'email' => '<EMAIL>',
                    'phone' => '************'
                ]
            ]);
    }

    /** @test */
    public function clinic_not_found_returns_404()
    {
        // Create user with non-existent clinic_id
        $user = User::factory()->create(['clinic_id' => 99999]);
        $user->assignRole('clinic_admin');

        $response = $this->actingAs($user)->get('/settings/clinic/data');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Clinic not found'
            ]);
    }
}
