<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->string('patient_unique_id', 50)->nullable()->unique()->after('id');
            $table->string('first_name')->nullable()->after('user_id');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('nhs_number', 20)->nullable()->after('date_of_birth');
            $table->string('registered_gp_name')->nullable()->after('nhs_number');
            $table->text('registered_gp_address')->nullable()->after('registered_gp_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn([
                'patient_unique_id',
                'first_name',
                'last_name',
                'nhs_number',
                'registered_gp_name',
                'registered_gp_address'
            ]);
        });
    }
};
