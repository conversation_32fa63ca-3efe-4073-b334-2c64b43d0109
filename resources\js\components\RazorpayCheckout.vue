<template>
    <div class="razorpay-checkout">
        <!-- Payment Gateway Selection for Indian Users -->
        <div v-if="isIndianUser" class="mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-blue-900">Secure Payment with Razorpay</h3>
                        <p class="text-xs text-blue-700">Optimized for Indian customers with UPI, NetBanking, and more</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Amount Display -->
        <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Payment Amount</h3>
                    <p class="text-sm text-gray-600">{{ description || 'Payment for medical services' }}</p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600">{{ formatAmount(amount) }}</div>
                    <div class="text-sm text-gray-500">Indian Rupees</div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Preview -->
        <div v-if="paymentMethods" class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Available Payment Methods</h4>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                <div 
                    v-for="(method, key) in paymentMethods" 
                    :key="key"
                    class="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center"
                >
                    <div class="w-8 h-8 mx-auto mb-2 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <div class="text-xs font-medium text-gray-900">{{ method.name }}</div>
                    <div class="text-xs text-gray-500">{{ method.description }}</div>
                </div>
            </div>
        </div>

        <!-- Pay Button -->
        <button
            @click="handlePayment"
            :disabled="isLoading || !isAvailable"
            class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span v-if="isLoading">Processing...</span>
            <span v-else>Pay {{ formatAmount(amount) }}</span>
        </button>

        <!-- Error Display -->
        <div v-if="error" class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-red-800">Payment Error</span>
            </div>
            <p class="text-sm text-red-700 mt-1">{{ error }}</p>
        </div>

        <!-- Success Display -->
        <div v-if="paymentSuccess" class="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-green-800">Payment Successful</span>
            </div>
            <p class="text-sm text-green-700 mt-1">Your payment has been processed successfully!</p>
        </div>

        <!-- Test Mode Notice -->
        <div v-if="isTestMode" class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span class="text-sm font-medium text-yellow-800">Test Mode</span>
            </div>
            <p class="text-sm text-yellow-700 mt-1">
                This is a test payment. Use test cards or UPI ID: success@razorpay
            </p>
        </div>

        <!-- Not Available Notice -->
        <div v-if="!isAvailable" class="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Payment Not Available</span>
            </div>
            <p class="text-sm text-gray-700 mt-1">
                Razorpay payments are only available for Indian customers.
            </p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRazorpay } from '@/composables/useRazorpay'

// Props
const props = defineProps({
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'INR'
    },
    description: {
        type: String,
        default: 'Payment for medical services'
    },
    notes: {
        type: Object,
        default: () => ({})
    },
    autoOpen: {
        type: Boolean,
        default: false
    }
})

// Emits
const emit = defineEmits(['success', 'error', 'dismiss'])

// Razorpay composable
const {
    isLoading,
    error,
    isAvailable,
    isIndianUser,
    processPayment,
    getPaymentMethods,
    formatAmount
} = useRazorpay()

// Local state
const paymentSuccess = ref(false)
const paymentMethods = ref(null)
const isTestMode = computed(() => {
    return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
})

// Handle payment
const handlePayment = async () => {
    if (!isAvailable.value) {
        return
    }

    paymentSuccess.value = false
    
    const paymentData = {
        amount: props.amount,
        currency: props.currency,
        description: props.description,
        notes: props.notes
    }

    await processPayment(paymentData, {
        onSuccess: (result) => {
            paymentSuccess.value = true
            emit('success', result)
        },
        onError: (err) => {
            console.error('Payment failed:', err)
            emit('error', err)
        },
        onDismiss: () => {
            emit('dismiss')
        }
    })
}

// Load payment methods on mount
onMounted(async () => {
    if (isIndianUser.value) {
        try {
            const response = await getPaymentMethods()
            if (response.success) {
                paymentMethods.value = response.payment_methods
            }
        } catch (err) {
            console.error('Failed to load payment methods:', err)
        }
    }

    // Auto-open checkout if specified
    if (props.autoOpen && isAvailable.value) {
        handlePayment()
    }
})

// Watch for amount changes
watch(() => props.amount, () => {
    paymentSuccess.value = false
})
</script>

<style scoped>
.razorpay-checkout {
    max-width: 500px;
    margin: 0 auto;
}

/* Custom animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
