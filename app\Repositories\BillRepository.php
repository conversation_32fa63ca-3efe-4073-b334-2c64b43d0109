<?php

namespace App\Repositories;

use App\Models\Bill;
use App\Repositories\Interfaces\BillRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class BillRepository implements BillRepositoryInterface
{
    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator
    {
        return Bill::with(['patient.user', 'provider.user', 'billItems'])
                   ->where('clinic_id', $clinicId)
                   ->orderBy('created_at', 'desc')
                   ->paginate($perPage);
    }

    public function create(array $data): Bill
    {
        return Bill::create($data);
    }

    public function update(Bill $bill, array $data): Bill
    {
        $bill->update($data);
        return $bill->fresh();
    }

    public function delete(Bill $bill): bool
    {
        return $bill->delete();
    }

    public function findById(int $id): ?Bill
    {
        return Bill::find($id);
    }

    public function findByIdWithRelations(int $id, array $relations = []): ?Bill
    {
        $query = Bill::query();
        
        if (!empty($relations)) {
            $query->with($relations);
        }
        
        return $query->find($id);
    }

    public function getUnpaidByClinic(int $clinicId): Collection
    {
        return Bill::where('clinic_id', $clinicId)
                   ->where('payment_status', '!=', 'paid')
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    public function getByPatient(int $patientId): Collection
    {
        return Bill::where('patient_id', $patientId)
                   ->orderBy('created_at', 'desc')
                   ->get();
    }
}
