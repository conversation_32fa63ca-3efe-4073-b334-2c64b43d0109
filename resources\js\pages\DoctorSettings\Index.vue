<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { useNotifications } from '@/composables/useNotifications'

const { showSuccess, showError } = useNotifications()

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Doctor Settings', href: '/doctor-settings' },
]

const loading = ref(false)
const saving = ref(false)
const error = ref(null)

const form = ref({
    gmc_number: '',
    nmc_number: '',
    professional_title: '',
    qualifications: '',
    digital_signature: '',
    letterhead_template: '',
    clinic_address: '',
    contact_details: ''
})

// Load doctor settings data
const loadSettings = async () => {
    try {
        loading.value = true
        error.value = null

        const response = await axios.get('/doctor-settings-data')

        if (response.data && response.data.data) {
            const data = response.data.data
            form.value = {
                gmc_number: data.gmc_number || '',
                nmc_number: data.nmc_number || '',
                professional_title: data.professional_title || '',
                qualifications: data.qualifications || '',
                digital_signature: data.digital_signature || '',
                letterhead_template: data.letterhead_template || '',
                clinic_address: data.clinic_address || '',
                contact_details: data.contact_details || ''
            }
        }
    } catch (err: any) {
        console.error('Error loading settings:', err)
        error.value = err.response?.data?.message || 'Failed to load doctor settings'
        showError('Failed to load doctor settings')
    } finally {
        loading.value = false
    }
}

const saveSettings = async () => {
    try {
        saving.value = true

        const response = await axios.put('/doctor-settings', form.value)

        if (response.data && response.data.success) {
            showSuccess('Doctor settings saved successfully')
        }
    } catch (err: any) {
        console.error('Error saving settings:', err)
        const errorMessage = err.response?.data?.message || 'Failed to save doctor settings'
        showError(errorMessage)
    } finally {
        saving.value = false
    }
}

// Load data on component mount
onMounted(() => {
    loadSettings()
})
</script>

<template>
    <AppLayout>
        <Head title="Doctor Settings" />
        
        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 text-gray-900">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Doctor Settings</h2>
                            <p class="text-gray-600">Configure your professional settings and digital signature</p>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p class="mt-2 text-gray-600">Loading doctor settings...</p>
                    </div>
                </div>

                <!-- Error State -->
                <div v-if="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Error loading settings</h3>
                            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
                        </div>
                    </div>
                </div>

                <!-- Settings Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <form @submit.prevent="saveSettings" class="space-y-6">
                            <!-- Professional Details -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="professional_title" class="block text-sm font-medium text-gray-700 mb-2">
                                        Professional Title
                                    </label>
                                    <input
                                        v-model="form.professional_title"
                                        type="text"
                                        id="professional_title"
                                        placeholder="Dr., Prof., etc."
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>

                                <div>
                                    <label for="qualifications" class="block text-sm font-medium text-gray-700 mb-2">
                                        Qualifications
                                    </label>
                                    <input
                                        v-model="form.qualifications"
                                        type="text"
                                        id="qualifications"
                                        placeholder="MBBS, MD, etc."
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>

                                <div>
                                    <label for="gmc_number" class="block text-sm font-medium text-gray-700 mb-2">
                                        GMC Number
                                    </label>
                                    <input
                                        v-model="form.gmc_number"
                                        type="text"
                                        id="gmc_number"
                                        placeholder="GMC Registration Number"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>

                                <div>
                                    <label for="nmc_number" class="block text-sm font-medium text-gray-700 mb-2">
                                        NMC Number
                                    </label>
                                    <input
                                        v-model="form.nmc_number"
                                        type="text"
                                        id="nmc_number"
                                        placeholder="NMC Registration Number"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                            </div>

                            <!-- Contact Details -->
                            <div>
                                <label for="clinic_address" class="block text-sm font-medium text-gray-700 mb-2">
                                    Clinic Address
                                </label>
                                <textarea
                                    v-model="form.clinic_address"
                                    id="clinic_address"
                                    rows="3"
                                    placeholder="Full clinic address for letters"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                ></textarea>
                            </div>

                            <div>
                                <label for="contact_details" class="block text-sm font-medium text-gray-700 mb-2">
                                    Contact Details
                                </label>
                                <textarea
                                    v-model="form.contact_details"
                                    id="contact_details"
                                    rows="2"
                                    placeholder="Phone, email, etc."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                ></textarea>
                            </div>

                            <!-- Digital Signature -->
                            <div>
                                <label for="digital_signature" class="block text-sm font-medium text-gray-700 mb-2">
                                    Digital Signature
                                </label>
                                <textarea
                                    v-model="form.digital_signature"
                                    id="digital_signature"
                                    rows="4"
                                    placeholder="Your digital signature text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                ></textarea>
                                <p class="mt-1 text-sm text-gray-500">
                                    This will be used in medical letters and prescriptions
                                </p>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    :disabled="saving || loading"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-6 py-2 rounded-md text-sm font-medium flex items-center"
                                >
                                    <svg v-if="saving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span v-if="saving">Saving...</span>
                                    <span v-else>Save Settings</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
