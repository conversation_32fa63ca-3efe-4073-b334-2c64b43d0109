<?php

namespace App\Http\Requests\TreatmentPlan;

use App\Http\Requests\BaseFormRequest;

class UpdateTreatmentPlanRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $treatmentPlan = $this->route('treatmentPlan');
        
        // Only the provider who created the plan can update it
        return auth()->check() && 
               auth()->user()->hasRole('provider') && 
               $treatmentPlan->provider_id === auth()->user()->provider->id;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan_type' => 'sometimes|in:treatment,investigation,referral,lifestyle',
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'instructions' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'priority' => 'sometimes|in:low,medium,high,urgent',
            'status' => 'sometimes|in:planned,active,completed,cancelled,on_hold',
            'outcome' => 'nullable|string',
            'review_date' => 'nullable|date',
            'goals' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'plan_type.in' => 'Plan type must be one of: treatment, investigation, referral, lifestyle.',
            'end_date.after_or_equal' => 'End date must be on or after the start date.',
            'priority.in' => 'Priority must be one of: low, medium, high, urgent.',
            'status.in' => 'Status must be one of: planned, active, completed, cancelled, on_hold.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'plan_type' => 'plan type',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'review_date' => 'review date',
        ];
    }
}
