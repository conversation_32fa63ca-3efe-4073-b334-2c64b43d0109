<?php

namespace App\Services;

use App\Models\Bill;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Service;
use App\Repositories\Interfaces\BillRepositoryInterface;
use App\Repositories\Interfaces\BillItemRepositoryInterface;
use App\Repositories\Interfaces\BillingRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class BillService
{
    public function __construct(
        private BillRepositoryInterface $billRepository,
        private BillItemRepositoryInterface $billItemRepository,
        private BillingRepositoryInterface $billingRepository,
        private TaxService $taxService
    ) {}

    public function getAllByClinic(int $clinicId, array $params = []): LengthAwarePaginator
    {
        $perPage = $params['per_page'] ?? 20;
        return $this->billingRepository->getBillsWithDetailsForClinic($clinicId, $perPage, $params);
    }

    public function getBillCreationData(int $clinicId): array
    {
        $patients = Patient::with('user')
                          ->where('clinic_id', $clinicId)
                          ->get()
                          ->map(function ($patient) {
                              return [
                                  'id' => $patient->id,
                                  'name' => $patient->user->name,
                                  'email' => $patient->user->email,
                              ];
                          });

        $providers = Provider::with('user')
                           ->where('clinic_id', $clinicId)
                           ->get()
                           ->map(function ($provider) {
                               return [
                                   'id' => $provider->id,
                                   'name' => $provider->user->name,
                               ];
                           });

        $services = Service::where('clinic_id', $clinicId)
                          ->where('is_active', true)
                          ->get(['id', 'name', 'price']);

        $taxes = $this->taxService->getActiveByClinic($clinicId);

        return [
            'patients' => $patients,
            'providers' => $providers,
            'services' => $services,
            'taxes' => $taxes,
        ];
    }

    public function createBill(array $data, int $clinicId): Bill
    {
        DB::beginTransaction();

        try {
            // Create the bill
            $billData = [
                'clinic_id' => $clinicId,
                'patient_id' => $data['patient_id'],
                'provider_id' => $data['provider_id'],
                'consultation_id' => $data['consultation_id'] ?? null,
                'appointment_id' => $data['appointment_id'] ?? null,
                'title' => $data['title'] ?? null,
                'subtotal' => 0,
                'discount' => $data['discount'] ?? 0,
                'total_amount' => 0,
                'due_date' => $data['due_date'] ?? null,
                'notes' => $data['notes'] ?? null,
            ];

            $bill = $this->billRepository->create($billData);

            // Create bill items
            foreach ($data['items'] as $itemData) {
                $this->billItemRepository->create([
                    'bill_id' => $bill->id,
                    'service_id' => $itemData['service_id'] ?? null,
                    'item_name' => $itemData['item_name'],
                    'description' => $itemData['description'] ?? null,
                    'unit_price' => $itemData['unit_price'],
                    'quantity' => $itemData['quantity'],
                ]);
            }

            // Calculate totals with taxes
            $this->calculateAndUpdateTotals($bill);

            DB::commit();

            return $this->billRepository->findByIdWithRelations($bill->id, [
                'patient.user', 'provider.user', 'billItems'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getBillById(int $id, int $userClinicId): Bill
    {
        $bill = $this->billingRepository->getBillWithFullDetails($id);

        if (!$bill) {
            throw new \Exception('Bill not found', 404);
        }

        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        return $bill;
    }

    public function getBillByIdForAdmin(int $id): Bill
    {
        $bill = $this->billingRepository->getBillWithFullDetails($id);

        if (!$bill) {
            throw new \Exception('Bill not found', 404);
        }

        return $bill;
    }

    public function updateBillForAdmin(Bill $bill, array $data): Bill
    {
        return $this->billRepository->update($bill, $data);
    }

    public function deleteBillForAdmin(Bill $bill): bool
    {
        return $this->billRepository->delete($bill);
    }

    public function markBillAsPaidForAdmin(Bill $bill): Bill
    {
        $bill->update([
            'payment_status' => 'paid',
            'status' => 'paid'
        ]);

        return $bill->fresh();
    }

    public function updateBill(Bill $bill, array $data, int $userClinicId): Bill
    {
        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        $updatedBill = $this->billRepository->update($bill, $data);

        // Recalculate totals if discount changed
        if (isset($data['discount'])) {
            $this->calculateAndUpdateTotals($updatedBill);
        }

        return $this->billRepository->findByIdWithRelations($updatedBill->id, [
            'patient.user', 'provider.user', 'billItems'
        ]);
    }

    public function deleteBill(Bill $bill, int $userClinicId): bool
    {
        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        return $this->billRepository->delete($bill);
    }

    public function markBillAsPaid(Bill $bill, int $userClinicId): Bill
    {
        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        $bill->markAsPaid();
        return $bill;
    }

    public function sendBillToPatient(Bill $bill, int $userClinicId): Bill
    {
        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        $updatedBill = $this->billRepository->update($bill, [
            'payment_status' => 'sent_to_patient',
            'status' => 'sent',
        ]);

        // Generate payment link
        $paymentLink = $this->generatePaymentLink($bill);

        // Send email notification to patient with payment link
        try {
            if ($bill->patient && $bill->patient->user && $bill->patient->user->email) {
                \Mail::to($bill->patient->user->email)->send(new \App\Mail\BillPaymentNotification($bill, $paymentLink));
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send bill payment notification: ' . $e->getMessage());
        }

        return $updatedBill;
    }

    public function generatePaymentLink(Bill $bill): string
    {
        // Generate a secure payment link for the bill
        return route('bills.payment.page', [
            'bill' => $bill->id,
            'token' => encrypt([
                'bill_id' => $bill->id,
                'patient_id' => $bill->patient_id,
                'expires_at' => now()->addDays(30)->timestamp
            ])
        ]);
    }

    public function getUnpaidBills(int $clinicId): Collection
    {
        return $this->billingRepository->getUnpaidBillsWithPatientDetails($clinicId);
    }

    public function getBillsByPatient(int $patientId, int $userClinicId): Collection
    {
        $bills = $this->billingRepository->getBillsByPatientWithDetails($patientId);

        // Verify all bills belong to user's clinic
        foreach ($bills as $bill) {
            if ($bill->clinic_id !== $userClinicId) {
                throw new \Exception('Unauthorized access to patient bills', 403);
            }
        }

        return $bills;
    }

    public function getBillsByProvider(int $providerId, int $userClinicId): Collection
    {
        $bills = $this->billingRepository->getBillsByProviderWithDetails($providerId);

        // Verify all bills belong to user's clinic
        foreach ($bills as $bill) {
            if ($bill->clinic_id !== $userClinicId) {
                throw new \Exception('Unauthorized access to provider bills', 403);
            }
        }

        return $bills;
    }

    public function getBillingStats(int $clinicId): array
    {
        return $this->billingRepository->getBillingStatsForClinic($clinicId);
    }

    public function getBillingReport(int $clinicId, array $filters = []): Collection
    {
        return $this->billingRepository->getBillingReportData($clinicId, $filters);
    }

    private function calculateAndUpdateTotals(Bill $bill): void
    {
        $bill->calculateTotals();

        $taxCalculation = $this->taxService->calculateTaxesForAmount($bill->subtotal, $bill->clinic_id);

        $this->billRepository->update($bill, [
            'tax_data' => $taxCalculation['tax_data'],
            'tax_amount' => $taxCalculation['total_tax_amount'],
            'total_amount' => $bill->subtotal + $taxCalculation['total_tax_amount'] - $bill->discount,
        ]);
    }
}
