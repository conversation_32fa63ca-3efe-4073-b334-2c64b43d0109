<?php

namespace App\Mail;

use App\Models\Bill;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BillPaymentNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $bill;
    public $paymentLink;

    /**
     * Create a new message instance.
     */
    public function __construct(Bill $bill, string $paymentLink)
    {
        $this->bill = $bill;
        $this->paymentLink = $paymentLink;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Payment Required - Bill #' . $this->bill->bill_number,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.bill-payment-notification',
            with: [
                'bill' => $this->bill,
                'paymentLink' => $this->paymentLink,
                'patientName' => $this->bill->patient->user->name ?? 'Patient',
                'clinicName' => $this->bill->clinic->name ?? 'Clinic',
                'totalAmount' => $this->bill->total_amount,
                'dueDate' => $this->bill->due_date,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
