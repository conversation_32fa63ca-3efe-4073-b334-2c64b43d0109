<?php

namespace App\Services;

use Razorpay\Api\Api;
use Razorpay\Api\Errors\SignatureVerificationError;
use App\Models\Payment;
use App\Models\User;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Exception;

class RazorpayService
{
    private Api $api;
    private string $keyId;
    private string $keySecret;
    private string $webhookSecret;

    public function __construct()
    {
        $this->keyId = Config::get('services.razorpay.key');
        $this->keySecret = Config::get('services.razorpay.secret');
        $this->webhookSecret = Config::get('services.razorpay.webhook.secret');
        
        $this->api = new Api($this->keyId, $this->keySecret);
    }

    /**
     * Create a Razorpay order
     */
    public function createOrder(float $amount, string $currency = 'INR', array $options = []): array
    {
        try {
            // Convert amount to paise (smallest currency unit)
            $amountInPaise = $this->convertToSmallestUnit($amount, $currency);
            
            $orderData = [
                'amount' => $amountInPaise,
                'currency' => $currency,
                'receipt' => $options['receipt'] ?? 'order_' . time(),
                'notes' => $options['notes'] ?? [],
            ];

            // Add partial payment support if specified
            if (isset($options['partial_payment']) && $options['partial_payment']) {
                $orderData['partial_payment'] = true;
                if (isset($options['first_payment_min_amount'])) {
                    $orderData['first_payment_min_amount'] = $this->convertToSmallestUnit(
                        $options['first_payment_min_amount'], 
                        $currency
                    );
                }
            }

            $razorpayOrder = $this->api->order->create($orderData);
            
            Log::info('Razorpay order created', [
                'order_id' => $razorpayOrder['id'],
                'amount' => $amount,
                'currency' => $currency
            ]);

            return [
                'success' => true,
                'order_id' => $razorpayOrder['id'],
                'amount' => $razorpayOrder['amount'],
                'currency' => $razorpayOrder['currency'],
                'status' => $razorpayOrder['status'],
                'receipt' => $razorpayOrder['receipt'],
                'created_at' => $razorpayOrder['created_at'],
            ];
        } catch (Exception $e) {
            Log::error('Razorpay order creation failed', [
                'error' => $e->getMessage(),
                'amount' => $amount,
                'currency' => $currency
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment signature
     */
    public function verifyPaymentSignature(array $attributes): bool
    {
        try {
            $this->api->utility->verifyPaymentSignature($attributes);
            return true;
        } catch (SignatureVerificationError $e) {
            Log::error('Razorpay signature verification failed', [
                'error' => $e->getMessage(),
                'attributes' => $attributes
            ]);
            return false;
        }
    }

    /**
     * Fetch payment details
     */
    public function fetchPayment(string $paymentId): array
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);
            
            return [
                'success' => true,
                'payment' => [
                    'id' => $payment['id'],
                    'amount' => $payment['amount'],
                    'currency' => $payment['currency'],
                    'status' => $payment['status'],
                    'order_id' => $payment['order_id'],
                    'method' => $payment['method'],
                    'captured' => $payment['captured'],
                    'created_at' => $payment['created_at'],
                    'email' => $payment['email'] ?? null,
                    'contact' => $payment['contact'] ?? null,
                ]
            ];
        } catch (Exception $e) {
            Log::error('Razorpay payment fetch failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Capture payment
     */
    public function capturePayment(string $paymentId, float $amount, string $currency = 'INR'): array
    {
        try {
            $amountInPaise = $this->convertToSmallestUnit($amount, $currency);
            
            $payment = $this->api->payment->fetch($paymentId);
            $capturedPayment = $payment->capture(['amount' => $amountInPaise]);
            
            Log::info('Razorpay payment captured', [
                'payment_id' => $paymentId,
                'amount' => $amount,
                'currency' => $currency
            ]);

            return [
                'success' => true,
                'payment' => [
                    'id' => $capturedPayment['id'],
                    'amount' => $capturedPayment['amount'],
                    'status' => $capturedPayment['status'],
                    'captured' => $capturedPayment['captured'],
                ]
            ];
        } catch (Exception $e) {
            Log::error('Razorpay payment capture failed', [
                'payment_id' => $paymentId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create refund
     */
    public function createRefund(string $paymentId, float $amount = null, array $options = []): array
    {
        try {
            $refundData = ['payment_id' => $paymentId];
            
            if ($amount !== null) {
                $refundData['amount'] = $this->convertToSmallestUnit($amount, 'INR');
            }
            
            if (isset($options['notes'])) {
                $refundData['notes'] = $options['notes'];
            }
            
            if (isset($options['receipt'])) {
                $refundData['receipt'] = $options['receipt'];
            }

            $refund = $this->api->refund->create($refundData);
            
            Log::info('Razorpay refund created', [
                'refund_id' => $refund['id'],
                'payment_id' => $paymentId,
                'amount' => $amount
            ]);

            return [
                'success' => true,
                'refund' => [
                    'id' => $refund['id'],
                    'payment_id' => $refund['payment_id'],
                    'amount' => $refund['amount'],
                    'status' => $refund['status'],
                    'created_at' => $refund['created_at'],
                ]
            ];
        } catch (Exception $e) {
            Log::error('Razorpay refund creation failed', [
                'payment_id' => $paymentId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get checkout options for frontend
     */
    public function getCheckoutOptions(array $orderData, User $user, array $customOptions = []): array
    {
        $options = [
            'key' => $this->keyId,
            'amount' => $orderData['amount'],
            'currency' => $orderData['currency'],
            'name' => Config::get('app.name', 'Medroid'),
            'description' => $customOptions['description'] ?? 'Payment for medical services',
            'image' => $customOptions['image'] ?? asset('images/logo.png'),
            'order_id' => $orderData['order_id'],
            'prefill' => [
                'name' => $user->name,
                'email' => $user->email,
                'contact' => $customOptions['contact'] ?? '',
            ],
            'notes' => $customOptions['notes'] ?? [],
            'theme' => [
                'color' => $customOptions['theme_color'] ?? '#17C3B2', // Medroid teal
            ],
            'modal' => [
                'backdropclose' => false,
                'escape' => true,
                'handleback' => true,
                'confirm_close' => true,
            ],
            'retry' => [
                'enabled' => true,
                'max_count' => 4,
            ],
        ];

        // Add callback URL if provided
        if (isset($customOptions['callback_url'])) {
            $options['callback_url'] = $customOptions['callback_url'];
        }

        return $options;
    }

    /**
     * Convert amount to smallest currency unit (paise for INR)
     */
    private function convertToSmallestUnit(float $amount, string $currency): int
    {
        // For INR, multiply by 100 to convert to paise
        // For other currencies, check if they have decimal places
        $multipliers = [
            'INR' => 100,
            'USD' => 100,
            'EUR' => 100,
            'GBP' => 100,
            // Add more currencies as needed
        ];

        $multiplier = $multipliers[$currency] ?? 100;
        return (int) round($amount * $multiplier);
    }

    /**
     * Convert from smallest currency unit to main unit
     */
    public function convertFromSmallestUnit(int $amount, string $currency): float
    {
        $divisors = [
            'INR' => 100,
            'USD' => 100,
            'EUR' => 100,
            'GBP' => 100,
        ];

        $divisor = $divisors[$currency] ?? 100;
        return $amount / $divisor;
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            $this->api->utility->verifyWebhookSignature($payload, $signature, $this->webhookSecret);
            return true;
        } catch (SignatureVerificationError $e) {
            Log::error('Razorpay webhook signature verification failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get supported payment methods for Indian customers
     */
    public function getSupportedPaymentMethods(): array
    {
        return [
            'card' => [
                'name' => 'Credit/Debit Card',
                'description' => 'Visa, Mastercard, RuPay, American Express',
                'icon' => 'credit-card',
            ],
            'netbanking' => [
                'name' => 'Net Banking',
                'description' => 'All major Indian banks',
                'icon' => 'bank',
            ],
            'upi' => [
                'name' => 'UPI',
                'description' => 'Google Pay, PhonePe, Paytm, BHIM',
                'icon' => 'smartphone',
            ],
            'wallet' => [
                'name' => 'Wallets',
                'description' => 'Paytm, Mobikwik, Freecharge',
                'icon' => 'wallet',
            ],
            'emi' => [
                'name' => 'EMI',
                'description' => 'No Cost EMI available',
                'icon' => 'calendar',
            ],
        ];
    }
}
