<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Prescriptions</h1>
        <p class="text-gray-600">Manage patient prescriptions and medications</p>
      </div>
      <Button @click="createPrescription" class="bg-teal-600 hover:bg-teal-700">
        <Plus class="w-4 h-4 mr-2" />
        New Prescription
      </Button>
    </div>

    <!-- Filters -->
    <Card class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <Label for="status-filter">Status</Label>
          <Select v-model="filters.status" id="status-filter">
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="dispensed">Dispensed</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label for="type-filter">Type</Label>
          <Select v-model="filters.type" id="type-filter">
            <SelectTrigger>
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All types</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="repeat">Repeat</SelectItem>
              <SelectItem value="acute">Acute</SelectItem>
              <SelectItem value="chronic">Chronic</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label for="date-from">Date From</Label>
          <Input
            v-model="filters.date_from"
            type="date"
            id="date-from"
          />
        </div>

        <div>
          <Label for="search">Search Patient</Label>
          <Input
            v-model="filters.search"
            placeholder="Search by patient name or Rx number..."
            id="search"
          />
        </div>
      </div>
      
      <div class="flex justify-end mt-4 space-x-2">
        <Button variant="outline" @click="clearFilters">Clear Filters</Button>
        <Button @click="loadPrescriptions">Apply Filters</Button>
      </div>
    </Card>

    <!-- Prescriptions Table -->
    <Card>
      <div class="p-6">
        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
        </div>

        <div v-else-if="error" class="text-center py-8 text-red-600">
          {{ error }}
        </div>

        <div v-else-if="prescriptions.length === 0" class="text-center py-8 text-gray-500">
          No prescriptions found
        </div>

        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b">
                <th class="text-left py-3 px-4 font-medium">Rx Number</th>
                <th class="text-left py-3 px-4 font-medium">Patient</th>
                <th class="text-left py-3 px-4 font-medium">Date</th>
                <th class="text-left py-3 px-4 font-medium">Type</th>
                <th class="text-left py-3 px-4 font-medium">Status</th>
                <th class="text-left py-3 px-4 font-medium">Items</th>
                <th class="text-left py-3 px-4 font-medium">Total Cost</th>
                <th class="text-left py-3 px-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="prescription in prescriptions"
                :key="prescription.id"
                class="border-b hover:bg-gray-50 cursor-pointer"
                @click="viewPrescription(prescription.id)"
              >
                <td class="py-3 px-4">
                  <div class="font-mono text-sm">{{ prescription.prescription_number }}</div>
                </td>
                <td class="py-3 px-4">
                  <div class="font-medium">{{ prescription.patient?.user?.name || 'Unknown Patient' }}</div>
                  <div class="text-sm text-gray-500">{{ prescription.patient?.user?.email }}</div>
                </td>
                <td class="py-3 px-4">
                  <div class="font-medium">{{ formatDate(prescription.prescribed_date) }}</div>
                  <div class="text-sm text-gray-500" v-if="prescription.valid_until">
                    Valid until {{ formatDate(prescription.valid_until) }}
                  </div>
                </td>
                <td class="py-3 px-4">
                  <Badge variant="outline">{{ formatPrescriptionType(prescription.type) }}</Badge>
                </td>
                <td class="py-3 px-4">
                  <Badge :variant="getStatusVariant(prescription.status)">
                    {{ formatStatus(prescription.status) }}
                  </Badge>
                </td>
                <td class="py-3 px-4">
                  <div class="font-medium">{{ prescription.total_items }} item{{ prescription.total_items !== 1 ? 's' : '' }}</div>
                </td>
                <td class="py-3 px-4">
                  <div class="font-medium" v-if="prescription.total_cost">
                    £{{ prescription.total_cost.toFixed(2) }}
                  </div>
                  <div class="text-gray-500" v-else>-</div>
                </td>
                <td class="py-3 px-4">
                  <div class="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click.stop="viewPrescription(prescription.id)"
                    >
                      <Eye class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click.stop="editPrescription(prescription.id)"
                      v-if="prescription.status === 'draft'"
                    >
                      <Edit class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click.stop="cancelPrescription(prescription)"
                      v-if="['draft', 'active'].includes(prescription.status)"
                      class="text-red-600 hover:text-red-700"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click.stop="printPrescription(prescription.id)"
                      v-if="prescription.status !== 'draft'"
                    >
                      <Printer class="w-4 h-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </Card>

    <!-- Refill Requests Alert -->
    <Card v-if="pendingRefillCount > 0" class="border-orange-200 bg-orange-50">
      <CardContent class="p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <AlertCircle class="w-5 h-5 text-orange-600" />
            <div>
              <p class="font-medium text-orange-800">
                {{ pendingRefillCount }} pending refill request{{ pendingRefillCount !== 1 ? 's' : '' }}
              </p>
              <p class="text-sm text-orange-600">
                Patients are waiting for prescription refill approvals
              </p>
            </div>
          </div>
          <Button variant="outline" @click="viewRefillRequests" class="border-orange-300 text-orange-700 hover:bg-orange-100">
            Review Requests
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { usePrescriptionApi, type Prescription } from '@/composables/usePrescriptionApi'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Plus, Eye, Edit, X, Printer, AlertCircle } from 'lucide-vue-next'
import { useNotifications } from '@/composables/useNotifications'

const router = useRouter()
const { showPrompt, showSuccess, showError } = useNotifications()
const { 
  prescriptions, 
  loading, 
  error, 
  getPrescriptions, 
  cancelPrescription: apiCancelPrescription,
  getPendingRefillCount
} = usePrescriptionApi()

const pendingRefillCount = ref(0)

const filters = reactive({
  status: '',
  type: '',
  date_from: '',
  search: ''
})

// Watch filters and reload prescriptions
watch(filters, () => {
  loadPrescriptions()
}, { deep: true })

const loadPrescriptions = async () => {
  const params = Object.fromEntries(
    Object.entries(filters).filter(([_, value]) => value !== '')
  )
  await getPrescriptions(params)
}

const loadPendingRefillCount = async () => {
  try {
    const response = await getPendingRefillCount()
    if (response?.data) {
      pendingRefillCount.value = response.data.pending_count || 0
    }
  } catch (err) {
    console.error('Error loading refill count:', err)
  }
}

const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
}

const createPrescription = () => {
  router.push('/prescriptions/create')
}

const viewPrescription = (id: number) => {
  router.push(`/prescriptions/${id}`)
}

const editPrescription = (id: number) => {
  router.push(`/prescriptions/${id}/edit`)
}

const cancelPrescription = async (prescription: Prescription) => {
  try {
    const reason = await showPrompt(
      'Cancel Prescription',
      'Please provide a reason for cancelling this prescription:',
      'Enter cancellation reason...'
    )

    if (reason) {
      await apiCancelPrescription(prescription.id, reason)
      await loadPrescriptions()
      showSuccess('Prescription cancelled successfully')
    }
  } catch (error: any) {
    showError(error.message || 'Failed to cancel prescription')
  }
}

const printPrescription = (id: number) => {
  // This would open a print-friendly view or generate PDF
  window.open(`/prescriptions/${id}/print`, '_blank')
}

const viewRefillRequests = () => {
  router.push('/prescription-refills')
}

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatPrescriptionType = (type: string) => {
  return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status: string) => {
  return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'active': return 'default'
    case 'dispensed': return 'secondary'
    case 'completed': return 'outline'
    case 'draft': return 'outline'
    case 'cancelled': return 'destructive'
    case 'expired': return 'destructive'
    default: return 'outline'
  }
}

onMounted(() => {
  loadPrescriptions()
  loadPendingRefillCount()
})
</script>
