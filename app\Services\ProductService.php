<?php

namespace App\Services;

use App\Models\Product;
use App\Repositories\ProductRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class ProductService
{
    protected ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Create a new product with business logic
     */
    public function createProduct(array $data): Product
    {
        // Business logic: Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }
        
        // Business logic: Generate SKU if not provided
        if (empty($data['sku'])) {
            $data['sku'] = $this->generateUniqueSku();
        }
        
        // Business logic: Set defaults
        $data['approval_status'] = $data['approval_status'] ?? 'pending';
        $data['is_active'] = $data['is_active'] ?? true;
        $data['is_featured'] = $data['is_featured'] ?? false;
        $data['manage_stock'] = $data['manage_stock'] ?? false;
        $data['in_stock'] = $data['in_stock'] ?? true;
        $data['type'] = $data['type'] ?? 'simple';
        
        // Business logic: Set user_id if not provided
        if (empty($data['user_id'])) {
            $data['user_id'] = Auth::id();
        }

        return $this->productRepository->create($data);
    }

    /**
     * Update product with business logic
     */
    public function updateProduct(int $productId, array $data): ?Product
    {
        // Business logic: Update slug if name changed
        if (isset($data['name']) && !isset($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $productId);
        }

        // Business logic: Update stock status if quantity changed
        if (isset($data['stock_quantity']) && isset($data['manage_stock']) && $data['manage_stock']) {
            $data['in_stock'] = $data['stock_quantity'] > 0;
        }

        return $this->productRepository->update($productId, $data);
    }

    /**
     * Get product details
     */
    public function getProductDetails(int $productId): ?Product
    {
        $product = $this->productRepository->find($productId);
        
        if (!$product) {
            return null;
        }

        // Load relationships
        $product->load([
            'user',
            'category',
            'images',
            'reviews',
            'cartItems',
            'orderItems'
        ]);

        return $product;
    }

    /**
     * Get products by user
     */
    public function getUserProducts(int $userId): Collection
    {
        return $this->productRepository->findByUser($userId);
    }

    /**
     * Get products by category
     */
    public function getProductsByCategory(int $categoryId): Collection
    {
        return $this->productRepository->findByCategory($categoryId);
    }

    /**
     * Get active products
     */
    public function getActiveProducts(?int $userId = null): Collection
    {
        return $this->productRepository->findActive($userId);
    }

    /**
     * Get approved products
     */
    public function getApprovedProducts(?int $userId = null): Collection
    {
        return $this->productRepository->findApproved($userId);
    }

    /**
     * Get featured products
     */
    public function getFeaturedProducts(int $limit = 8): Collection
    {
        return $this->productRepository->findFeatured($limit);
    }

    /**
     * Get product by slug or ID
     */
    public function getProductBySlugOrId(string $slugOrId): ?Product
    {
        return $this->productRepository->findBySlugOrId($slugOrId);
    }

    /**
     * Get related products
     */
    public function getRelatedProducts(int $productId, int $categoryId, int $limit = 4): Collection
    {
        return $this->productRepository->findRelated($productId, $categoryId, $limit);
    }

    /**
     * Get products by type
     */
    public function getProductsByType(string $type, ?int $userId = null): Collection
    {
        return $this->productRepository->findByType($type, $userId);
    }

    /**
     * Get products on sale
     */
    public function getProductsOnSale(): Collection
    {
        return $this->productRepository->findOnSale();
    }

    /**
     * Get products in stock
     */
    public function getProductsInStock(): Collection
    {
        return $this->productRepository->findInStock();
    }

    /**
     * Search products
     */
    public function searchProducts(string $search, int $limit = 10, array $filters = []): Collection
    {
        $filters['limit'] = $limit;
        return $this->productRepository->search($search, $filters);
    }

    /**
     * Get products with filters and pagination
     */
    public function getProductsWithFilters(array $filters = [], int $perPage = 15, ?string $userCountryCode = null): LengthAwarePaginator
    {
        return $this->productRepository->getWithFilters($filters, $perPage, $userCountryCode);
    }

    /**
     * Get product by slug
     */
    public function getProductBySlug(string $slug): ?Product
    {
        return $this->productRepository->findBySlug($slug);
    }

    /**
     * Get product by SKU
     */
    public function getProductBySku(string $sku): ?Product
    {
        return $this->productRepository->findBySku($sku);
    }

    /**
     * Approve product
     */
    public function approveProduct(int $productId, ?string $reason = null): bool
    {
        return $this->productRepository->updateApprovalStatus(
            $productId, 
            'approved', 
            Auth::id(), 
            $reason
        );
    }

    /**
     * Reject product
     */
    public function rejectProduct(int $productId, string $reason): bool
    {
        return $this->productRepository->updateApprovalStatus(
            $productId, 
            'rejected', 
            Auth::id(), 
            $reason
        );
    }

    /**
     * Activate product
     */
    public function activateProduct(int $productId): bool
    {
        $product = $this->productRepository->update($productId, ['is_active' => true]);
        return $product !== null;
    }

    /**
     * Deactivate product
     */
    public function deactivateProduct(int $productId): bool
    {
        $product = $this->productRepository->update($productId, ['is_active' => false]);
        return $product !== null;
    }

    /**
     * Feature product
     */
    public function featureProduct(int $productId): bool
    {
        $product = $this->productRepository->update($productId, ['is_featured' => true]);
        return $product !== null;
    }

    /**
     * Unfeature product
     */
    public function unfeatureProduct(int $productId): bool
    {
        $product = $this->productRepository->update($productId, ['is_featured' => false]);
        return $product !== null;
    }

    /**
     * Update stock
     */
    public function updateStock(int $productId, int $quantity, string $operation = 'set'): bool
    {
        return $this->productRepository->updateStock($productId, $quantity, $operation);
    }

    /**
     * Calculate product price with sale
     */
    public function calculatePrice(int $productId): array
    {
        $product = $this->productRepository->find($productId);
        
        if (!$product) {
            return [];
        }

        $originalPrice = $product->price;
        $salePrice = $product->sale_price;
        $effectivePrice = $salePrice && $salePrice > 0 && $salePrice < $originalPrice ? $salePrice : $originalPrice;
        $savings = $originalPrice - $effectivePrice;
        $discountPercentage = $originalPrice > 0 ? round(($savings / $originalPrice) * 100, 2) : 0;

        return [
            'original_price' => $originalPrice,
            'sale_price' => $salePrice,
            'effective_price' => $effectivePrice,
            'savings' => $savings,
            'discount_percentage' => $discountPercentage,
            'is_on_sale' => $salePrice && $salePrice > 0 && $salePrice < $originalPrice,
        ];
    }

    /**
     * Check if product can be purchased
     */
    public function canPurchase(int $productId): bool
    {
        $product = $this->productRepository->find($productId);
        
        if (!$product) {
            return false;
        }

        // Business logic: Check various conditions
        if (!$product->is_active || $product->approval_status !== 'approved') {
            return false;
        }

        // Check stock if managed
        if ($product->manage_stock && (!$product->in_stock || $product->stock_quantity <= 0)) {
            return false;
        }

        return true;
    }

    /**
     * Generate unique slug
     */
    private function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate unique SKU
     */
    private function generateUniqueSku(): string
    {
        do {
            $sku = 'PRD' . date('Y') . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while ($this->productRepository->findBySku($sku));

        return $sku;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = $this->productRepository->model->newQuery();
        $query->where('slug', $slug);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }
}
