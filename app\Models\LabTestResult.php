<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\ClinicFilterable;

class LabTestResult extends Model
{
    use HasFactory, ClinicFilterable;

    protected $table = 'lab_test_results';

    protected $fillable = [
        'request_id',
        'clinic_id',
        'patient_id',
        'order_number',
        'lab_reference_id',
        'status',
        'result_data',
        'azure_file_path',
        'physician_notes',
        'reviewed_by',
        'received_at',
        'processed_at',
        'reviewed_at',
    ];

    protected $casts = [
        'result_data' => 'array',
        'received_at' => 'datetime',
        'processed_at' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    /**
     * Get the clinic that owns this result
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the patient for this result
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    /**
     * Get the lab request this result belongs to
     */
    public function request(): BelongsTo
    {
        return $this->belongsTo(LabTestRequest::class, 'request_id');
    }

    /**
     * Get the physician who reviewed this result
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by patient
     */
    public function scopeForPatient(Builder $query, int $patientId): Builder
    {
        return $query->where('patient_id', $patientId);
    }

    /**
     * Scope to get unreviewed results
     */
    public function scopeUnreviewed(Builder $query): Builder
    {
        return $query->where('status', '!=', 'reviewed');
    }

    /**
     * Scope to get reviewed results
     */
    public function scopeReviewed(Builder $query): Builder
    {
        return $query->where('status', 'reviewed');
    }

    /**
     * Check if result is received
     */
    public function isReceived(): bool
    {
        return $this->status === 'received';
    }

    /**
     * Check if result is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if result is reviewed
     */
    public function isReviewed(): bool
    {
        return $this->status === 'reviewed';
    }

    /**
     * Mark result as processed
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => 'completed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark result as reviewed
     */
    public function markAsReviewed(int $reviewerId, string $notes = null): void
    {
        $this->update([
            'status' => 'reviewed',
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'physician_notes' => $notes,
        ]);
    }

    /**
     * Get patient information from result data
     */
    public function getPatientInfo(): ?array
    {
        return $this->result_data['patient_info'] ?? null;
    }

    /**
     * Get test results from result data
     */
    public function getTestResults(): array
    {
        return $this->result_data['tests'] ?? [];
    }

    /**
     * Get report date from result data
     */
    public function getReportDate(): ?string
    {
        return $this->result_data['report_date'] ?? null;
    }

    /**
     * Get lab reference from result data
     */
    public function getLabReference(): ?string
    {
        return $this->result_data['lab_reference'] ?? $this->lab_reference_id;
    }

    /**
     * Get abnormal results count
     */
    public function getAbnormalResultsCount(): int
    {
        $count = 0;
        $tests = $this->getTestResults();
        
        foreach ($tests as $test) {
            $biomarkers = $test['biomarkers'] ?? [];
            foreach ($biomarkers as $biomarker) {
                if (($biomarker['abnormal_flag'] ?? 'N') !== 'N') {
                    $count++;
                }
            }
        }
        
        return $count;
    }

    /**
     * Check if result has abnormal values
     */
    public function hasAbnormalResults(): bool
    {
        return $this->getAbnormalResultsCount() > 0;
    }

    /**
     * Get status badge color
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'received' => 'blue',
            'processing' => 'yellow',
            'completed' => 'green',
            'reviewed' => 'purple',
            'archived' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get human readable status
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'received' => 'Received',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'reviewed' => 'Reviewed',
            'archived' => 'Archived',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get formatted result summary for display
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'lab_reference_id' => $this->getLabReference(),
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'status_color' => $this->getStatusColor(),
            'received_at' => $this->received_at?->format('Y-m-d H:i'),
            'processed_at' => $this->processed_at?->format('Y-m-d H:i'),
            'reviewed_at' => $this->reviewed_at?->format('Y-m-d H:i'),
            'has_abnormal_results' => $this->hasAbnormalResults(),
            'abnormal_count' => $this->getAbnormalResultsCount(),
            'test_count' => count($this->getTestResults()),
        ];
    }
}
