<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import Icon from '@/components/Icon.vue';

const props = defineProps({
    patientId: [String, Number]
});

const { showSuccess, showError } = useNotifications();

// Form data
const form = ref({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    date_of_birth: '',
    gender: '',
    nhs_number: '',
    registered_gp_name: '',
    registered_gp_address: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    medical_history: '',
    current_medications: '',
    clinic_id: ''
});

const loading = ref(false);
const saving = ref(false);
const errors = ref({});
const clinics = ref([]);

// Computed
const pageTitle = computed(() => `Edit Patient #${props.patientId}`);

// Methods
const fetchPatientData = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get(`/patients/${props.patientId}`);
        if (response.data.success) {
            const patient = response.data.data;
            form.value = {
                first_name: patient.first_name || '',
                last_name: patient.last_name || '',
                email: patient.email || '',
                phone_number: patient.phone_number || '',
                date_of_birth: patient.date_of_birth || '',
                gender: patient.gender || '',
                nhs_number: patient.nhs_number || '',
                registered_gp_name: patient.registered_gp_name || '',
                registered_gp_address: patient.registered_gp_address || '',
                emergency_contact_name: patient.emergency_contact_name || '',
                emergency_contact_phone: patient.emergency_contact_phone || '',
                emergency_contact_relationship: patient.emergency_contact_relationship || '',
                medical_history: patient.medical_history || '',
                current_medications: patient.current_medications || '',
                clinic_id: patient.clinic_id || ''
            };
        }
    } catch (error) {
        console.error('Error fetching patient data:', error);
        showError('Failed to load patient data');
    } finally {
        loading.value = false;
    }
};

const fetchClinics = async () => {
    try {
        const response = await window.axios.get('/clinics-list-dropdown');
        if (response.data.success) {
            clinics.value = response.data.data || [];
        }
    } catch (error) {
        console.error('Error fetching clinics:', error);
    }
};

const savePatient = async () => {
    saving.value = true;
    errors.value = {};
    
    try {
        const response = await window.axios.put(`/update-patient/${props.patientId}`, form.value);
        
        if (response.data.success || response.status === 200) {
            showSuccess('Patient updated successfully!');
            router.visit('/patients');
        }
    } catch (error) {
        console.error('Error saving patient:', error);
        
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            showError('Please fix the validation errors and try again.');
        } else {
            showError('Failed to update patient. Please try again.');
        }
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.visit('/patients');
};

// Lifecycle
onMounted(() => {
    fetchPatientData();
    fetchClinics();
});
</script>

<template>
    <Head :title="pageTitle" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button @click="cancel" class="text-gray-500 hover:text-gray-700">
                        <Icon name="arrow-left" class="w-5 h-5" />
                    </button>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800">
                        {{ pageTitle }}
                    </h2>
                </div>
                <div class="flex items-center space-x-3">
                    <button @click="cancel" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button @click="savePatient" :disabled="saving" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50">
                        <Icon v-if="saving" name="loader" class="w-4 h-4 mr-2 inline animate-spin" />
                        {{ saving ? 'Saving...' : 'Save Patient' }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                <div v-if="loading" class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3 text-gray-600">Loading patient data...</span>
                </div>

                <div v-else class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Patient Information</h3>
                        <p class="mt-1 text-sm text-gray-600">Update the patient's personal and medical information.</p>
                    </div>

                    <form @submit.prevent="savePatient" class="px-6 py-4 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700">First Name *</label>
                                <input
                                    v-model="form.first_name"
                                    type="text"
                                    id="first_name"
                                    required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.first_name }"
                                >
                                <p v-if="errors.first_name" class="mt-1 text-sm text-red-600">{{ errors.first_name[0] }}</p>
                            </div>

                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name *</label>
                                <input
                                    v-model="form.last_name"
                                    type="text"
                                    id="last_name"
                                    required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.last_name }"
                                >
                                <p v-if="errors.last_name" class="mt-1 text-sm text-red-600">{{ errors.last_name[0] }}</p>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                                <input
                                    v-model="form.email"
                                    type="email"
                                    id="email"
                                    required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.email }"
                                >
                                <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email[0] }}</p>
                            </div>

                            <div>
                                <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                                <input
                                    v-model="form.phone_number"
                                    type="tel"
                                    id="phone_number"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.phone_number }"
                                >
                                <p v-if="errors.phone_number" class="mt-1 text-sm text-red-600">{{ errors.phone_number[0] }}</p>
                            </div>

                            <div>
                                <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth *</label>
                                <input
                                    v-model="form.date_of_birth"
                                    type="date"
                                    id="date_of_birth"
                                    required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.date_of_birth }"
                                >
                                <p v-if="errors.date_of_birth" class="mt-1 text-sm text-red-600">{{ errors.date_of_birth[0] }}</p>
                            </div>

                            <div>
                                <label for="gender" class="block text-sm font-medium text-gray-700">Gender *</label>
                                <select
                                    v-model="form.gender"
                                    id="gender"
                                    required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-300': errors.gender }"
                                >
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                                <p v-if="errors.gender" class="mt-1 text-sm text-red-600">{{ errors.gender[0] }}</p>
                            </div>
                        </div>

                        <!-- Medical Information -->
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Medical Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="nhs_number" class="block text-sm font-medium text-gray-700">NHS Number</label>
                                    <input
                                        v-model="form.nhs_number"
                                        type="text"
                                        id="nhs_number"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        :class="{ 'border-red-300': errors.nhs_number }"
                                    >
                                    <p v-if="errors.nhs_number" class="mt-1 text-sm text-red-600">{{ errors.nhs_number[0] }}</p>
                                </div>

                                <div>
                                    <label for="clinic_id" class="block text-sm font-medium text-gray-700">Clinic</label>
                                    <select
                                        v-model="form.clinic_id"
                                        id="clinic_id"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        :class="{ 'border-red-300': errors.clinic_id }"
                                    >
                                        <option value="">Select Clinic</option>
                                        <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                                            {{ clinic.name }}
                                        </option>
                                    </select>
                                    <p v-if="errors.clinic_id" class="mt-1 text-sm text-red-600">{{ errors.clinic_id[0] }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="border-t border-gray-200 pt-6 flex justify-end space-x-3">
                            <button type="button" @click="cancel" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit" :disabled="saving" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50">
                                <Icon v-if="saving" name="loader" class="w-4 h-4 mr-2 inline animate-spin" />
                                {{ saving ? 'Saving...' : 'Save Patient' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
