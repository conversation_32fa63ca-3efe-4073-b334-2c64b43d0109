<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LetterheadSettings extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'header_html',
        'footer_html',
        'header_image_url',
        'footer_image_url',
        'header_file_id',
        'footer_file_id',
        'header_type',
        'footer_type',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the clinic that owns the letterhead settings.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the header file.
     */
    public function headerFile()
    {
        return $this->belongsTo(\App\Models\File::class, 'header_file_id');
    }

    /**
     * Get the footer file.
     */
    public function footerFile()
    {
        return $this->belongsTo(\App\Models\File::class, 'footer_file_id');
    }

    /**
     * Check if letterhead has custom header.
     */
    public function hasCustomHeader(): bool
    {
        return ($this->header_type === 'html' && !empty($this->header_html)) ||
               ($this->header_type === 'image' && (!empty($this->header_image_url) || !empty($this->header_file_id)));
    }

    /**
     * Check if letterhead has custom footer.
     */
    public function hasCustomFooter(): bool
    {
        return ($this->footer_type === 'html' && !empty($this->footer_html)) ||
               ($this->footer_type === 'image' && (!empty($this->footer_image_url) || !empty($this->footer_file_id)));
    }

    /**
     * Get header content (HTML or image URL).
     */
    public function getHeaderContent(): ?string
    {
        if ($this->header_type === 'html') {
            return $this->header_html;
        }

        if ($this->header_type === 'image') {
            // Priority: file from files table > direct URL
            if ($this->header_file_id && $this->headerFile) {
                return asset('storage/' . $this->headerFile->path);
            }
            return $this->header_image_url;
        }

        return null;
    }

    /**
     * Get footer content (HTML or image URL).
     */
    public function getFooterContent(): ?string
    {
        if ($this->footer_type === 'html') {
            return $this->footer_html;
        }

        if ($this->footer_type === 'image') {
            // Priority: file from files table > direct URL
            if ($this->footer_file_id && $this->footerFile) {
                return asset('storage/' . $this->footerFile->path);
            }
            return $this->footer_image_url;
        }

        return null;
    }

    /**
     * Get default letterhead settings for a clinic when none exist.
     */
    public static function getDefaultForClinic($clinic): array
    {
        return [
            'clinic_id' => $clinic->id,
            'header_html' => null,
            'footer_html' => null,
            'header_image_url' => null,
            'footer_image_url' => null,
            'header_file_id' => null,
            'footer_file_id' => null,
            'header_type' => 'html',
            'footer_type' => 'html',
            'header_content' => null,
            'footer_content' => null,
            'is_active' => true,
            'has_custom_header' => false,
            'has_custom_footer' => false,
        ];
    }

    /**
     * Check if clinic has any letterhead customization.
     */
    public static function clinicHasCustomization($clinicId): bool
    {
        return self::where('clinic_id', $clinicId)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNotNull('header_html')
                      ->orWhereNotNull('footer_html')
                      ->orWhereNotNull('header_image_url')
                      ->orWhereNotNull('footer_image_url')
                      ->orWhereNotNull('header_file_id')
                      ->orWhereNotNull('footer_file_id');
            })
            ->exists();
    }
}
