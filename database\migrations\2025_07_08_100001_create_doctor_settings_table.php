<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctor_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('professional_prefix')->nullable(); // Dr., Prof., Mr., Ms., etc.
            $table->string('registration_number')->nullable(); // GMC, NMC, etc.
            $table->string('registration_body')->nullable(); // GMC, NMC, RCGP, etc.
            $table->text('digital_signature')->nullable(); // Base64 encoded signature image
            $table->string('signature_format')->default('png'); // png, jpg, svg
            $table->json('letter_templates')->nullable(); // Custom letter templates
            $table->json('consultation_templates')->nullable(); // Consultation note templates
            $table->string('practice_name')->nullable();
            $table->text('practice_address')->nullable();
            $table->string('practice_phone')->nullable();
            $table->string('practice_email')->nullable();
            $table->boolean('include_signature_in_letters')->default(true);
            $table->boolean('include_registration_in_letters')->default(true);
            $table->json('default_letter_settings')->nullable(); // Font, size, margins, etc.
            $table->timestamps();

            // Ensure one settings record per user
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctor_settings');
    }
};
