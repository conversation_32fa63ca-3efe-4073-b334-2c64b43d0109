<script setup lang="ts">
import { Head, Link, usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import SubscriptionInfo from '@/components/SubscriptionInfo.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type BreadcrumbItem, type SharedData, type User } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Settings Overview',
        href: '/settings',
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;
const isPatient = computed(() => user.role === 'patient');

const quickActions = [
    {
        title: 'Profile',
        description: 'Update your personal information and contact details',
        href: '/settings/profile',
        icon: 'profile',
        color: 'blue'
    },
    {
        title: 'Password',
        description: 'Change your password and security settings',
        href: '/settings/password',
        icon: 'password',
        color: 'red'
    },
    {
        title: 'Subscription',
        description: 'Manage your subscription plan and billing',
        href: '/settings/subscription',
        icon: 'subscription',
        color: 'green'
    },
    {
        title: 'Appearance',
        description: 'Customize your theme and display preferences',
        href: '/settings/appearance',
        icon: 'appearance',
        color: 'purple'
    }
];

const patientActions = [
    {
        title: 'Medical Information',
        description: 'Update your medical history and current medications',
        href: '/settings/medical-info',
        icon: 'medical',
        color: 'teal'
    },
    {
        title: 'Insurance Information',
        description: 'Manage your insurance details and coverage',
        href: '/settings/insurance',
        icon: 'insurance',
        color: 'indigo'
    },
    {
        title: 'Privacy & Security',
        description: 'Control your privacy settings and data sharing',
        href: '/settings/privacy',
        icon: 'privacy',
        color: 'orange'
    }
];

const colorClasses = {
    blue: 'bg-medroid-orange/5 text-medroid-orange border-medroid-orange/20',
    red: 'bg-red-50 text-red-600 border-red-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    purple: 'bg-medroid-orange/5 text-medroid-orange border-medroid-orange/20',
    teal: 'bg-teal-50 text-teal-600 border-teal-200',
    indigo: 'bg-medroid-orange/5 text-medroid-orange border-medroid-orange/20',
    orange: 'bg-medroid-orange/5 text-medroid-orange border-medroid-orange/20'
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Settings" />

        <SettingsLayout>
            <!-- Welcome Section -->
            <div class="mb-6">
                <div class="bg-medroid-orange/5 rounded-xl p-5 border border-medroid-orange/20">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-medroid-orange rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-lg font-bold text-medroid-dark-gray">Welcome to Settings, {{ user.name }}!</h1>
                            <p class="text-medroid-slate">Manage your account preferences and customize your experience</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Info -->
            <SubscriptionInfo />

            <!-- Quick Actions -->
            <div class="space-y-5">
                <div>
                    <h2 class="text-base font-semibold text-medroid-dark-gray mb-3">Account Settings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <Link
                            v-for="action in quickActions"
                            :key="action.href"
                            :href="action.href"
                            class="group"
                        >
                            <Card class="h-full transition-all duration-200 hover:shadow-md hover:scale-[1.01] border-medroid-light-gray">
                                <CardHeader class="pb-3">
                                    <div class="flex items-center space-x-3">
                                        <div :class="['w-10 h-10 rounded-lg flex items-center justify-center border', colorClasses[action.color]]">
                                            <svg v-if="action.icon === 'profile'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            <svg v-else-if="action.icon === 'password'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                            <svg v-else-if="action.icon === 'subscription'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                            </svg>
                                            <svg v-else-if="action.icon === 'appearance'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <CardTitle class="text-base">{{ action.title }}</CardTitle>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent class="pt-0">
                                    <CardDescription class="text-sm">{{ action.description }}</CardDescription>
                                </CardContent>
                            </Card>
                        </Link>
                    </div>
                </div>

                <!-- Patient-specific settings -->
                <div v-if="isPatient">
                    <h2 class="text-base font-semibold text-medroid-dark-gray mb-3">Medical & Personal Settings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        <Link
                            v-for="action in patientActions"
                            :key="action.href"
                            :href="action.href"
                            class="group"
                        >
                            <Card class="h-full transition-all duration-200 hover:shadow-md hover:scale-[1.01] border-medroid-light-gray">
                                <CardHeader class="pb-3">
                                    <div class="flex items-center space-x-3">
                                        <div :class="['w-10 h-10 rounded-lg flex items-center justify-center border', colorClasses[action.color]]">
                                            <svg v-if="action.icon === 'medical'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <svg v-else-if="action.icon === 'insurance'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                            </svg>
                                            <svg v-else-if="action.icon === 'privacy'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <CardTitle class="text-base">{{ action.title }}</CardTitle>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent class="pt-0">
                                    <CardDescription class="text-sm">{{ action.description }}</CardDescription>
                                </CardContent>
                            </Card>
                        </Link>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>