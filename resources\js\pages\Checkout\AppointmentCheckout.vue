<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head :title="`Book Appointment - ${service?.name || 'Medical Consultation'}`" />

        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Book Your Appointment</h1>
                <p class="text-lg text-gray-600 mt-2">{{ service?.name || 'Medical Consultation' }}</p>
                
                <!-- Country & Flow Type Badge -->
                <div class="inline-flex items-center mt-4 px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {{ checkoutFlow.country_name }} - {{ formatFlowType(checkoutFlow.flow_type) }}
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Checkout Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <!-- Progress Steps -->
                        <div class="border-b border-gray-200 px-6 py-4">
                            <div class="flex items-center justify-between">
                                <div 
                                    v-for="(step, index) in checkoutFlow.steps" 
                                    :key="step"
                                    class="flex items-center"
                                    :class="{ 'opacity-50': index > currentStep }"
                                >
                                    <div 
                                        class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                                        :class="index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'"
                                    >
                                        {{ index + 1 }}
                                    </div>
                                    <span class="ml-2 text-sm font-medium text-gray-900 hidden sm:block">
                                        {{ formatStepName(step) }}
                                    </span>
                                    <svg 
                                        v-if="index < checkoutFlow.steps.length - 1"
                                        class="w-5 h-5 text-gray-400 mx-4"
                                        fill="none" 
                                        stroke="currentColor" 
                                        viewBox="0 0 24 24"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Step Content -->
                        <div class="p-6">
                            <!-- Patient Details Step -->
                            <div v-if="currentStepName === 'patient_details'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Patient Information</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                        <input
                                            v-model="formData.patient_details.full_name"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Enter patient's full name"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Age</label>
                                        <input
                                            v-model="formData.patient_details.age"
                                            type="number"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Enter age"
                                            min="1"
                                            max="120"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                                        <select
                                            v-model="formData.patient_details.gender"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            required
                                        >
                                            <option value="">Select gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                            <option value="prefer_not_to_say">Prefer not to say</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input
                                            v-model="formData.patient_details.phone"
                                            type="tel"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Enter phone number"
                                            required
                                        />
                                    </div>
                                </div>

                                <!-- Country-specific fields -->
                                <div v-if="checkoutFlow.flow_type === 'india_compliant'" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Emergency Contact</label>
                                        <input
                                            v-model="formData.patient_details.emergency_contact"
                                            type="tel"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Emergency contact number"
                                            required
                                        />
                                    </div>
                                </div>

                                <div v-if="checkoutFlow.flow_type === 'uk_compliant'" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">NHS Number (Optional)</label>
                                        <input
                                            v-model="formData.patient_details.nhs_number"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="NHS number if available"
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">GP Practice</label>
                                        <input
                                            v-model="formData.patient_details.gp_practice"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Your GP practice name"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Medical History Step -->
                            <div v-if="currentStepName === 'medical_history'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Medical History</h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Symptoms</label>
                                        <textarea
                                            v-model="formData.medical_history.symptoms"
                                            rows="4"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Describe your current symptoms..."
                                            required
                                        ></textarea>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Medications</label>
                                        <textarea
                                            v-model="formData.medical_history.medications"
                                            rows="3"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="List any medications you're currently taking..."
                                        ></textarea>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Allergies</label>
                                        <textarea
                                            v-model="formData.medical_history.allergies"
                                            rows="2"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="List any known allergies..."
                                        ></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Doctor Selection Step -->
                            <div v-if="currentStepName === 'doctor_selection'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Select Doctor & Time</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Choose Doctor</label>
                                        <select
                                            v-model="formData.provider_id"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            required
                                        >
                                            <option value="">Select a doctor</option>
                                            <option 
                                                v-for="provider in providers" 
                                                :key="provider.id" 
                                                :value="provider.id"
                                            >
                                                Dr. {{ provider.name }} - {{ provider.specialization }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Date</label>
                                        <input
                                            v-model="formData.appointment_date"
                                            type="date"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            :min="minDate"
                                            required
                                        />
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Time</label>
                                    <select
                                        v-model="formData.appointment_time"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    >
                                        <option value="">Select time</option>
                                        <option value="09:00">9:00 AM</option>
                                        <option value="10:00">10:00 AM</option>
                                        <option value="11:00">11:00 AM</option>
                                        <option value="14:00">2:00 PM</option>
                                        <option value="15:00">3:00 PM</option>
                                        <option value="16:00">4:00 PM</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Payment Step -->
                            <div v-if="currentStepName === 'payment_details'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Payment Information</h3>
                                
                                <!-- Payment Gateway Selection -->
                                <PaymentGatewaySelector 
                                    @gateway-selected="onGatewaySelected"
                                    @error="onPaymentError"
                                />

                                <!-- Razorpay Checkout -->
                                <div v-if="selectedGateway?.id === 'razorpay'">
                                    <RazorpayCheckout
                                        :amount="totalAmount"
                                        :currency="checkoutFlow.currency"
                                        :description="`Appointment: ${service?.name || 'Medical Consultation'}`"
                                        @success="onPaymentSuccess"
                                        @error="onPaymentError"
                                    />
                                </div>

                                <!-- Stripe Checkout -->
                                <div v-if="selectedGateway?.id === 'stripe'">
                                    <StripeCheckout
                                        :amount="totalAmount"
                                        :currency="checkoutFlow.currency"
                                        :description="`Appointment: ${service?.name || 'Medical Consultation'}`"
                                        @success="onPaymentSuccess"
                                        @error="onPaymentError"
                                    />
                                </div>
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                                <button
                                    v-if="currentStep > 0"
                                    @click="previousStep"
                                    class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                                >
                                    Previous
                                </button>
                                <div v-else></div>

                                <button
                                    v-if="currentStep < checkoutFlow.steps.length - 1"
                                    @click="nextStep"
                                    :disabled="!canProceedToNextStep"
                                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                                >
                                    Next
                                </button>
                                <button
                                    v-else
                                    @click="submitAppointment"
                                    :disabled="isSubmitting || !paymentCompleted"
                                    class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                                >
                                    <span v-if="isSubmitting">Processing...</span>
                                    <span v-else>Book Appointment</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Service Summary -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Appointment Summary</h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Service</span>
                                <span class="font-medium">{{ service?.name || 'Medical Consultation' }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Duration</span>
                                <span class="font-medium">{{ service?.duration || 30 }} minutes</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Fee</span>
                                <span class="font-medium">{{ formatAmount(service?.consultation_fee || 100) }}</span>
                            </div>
                            
                            <div v-if="checkoutFlow.regulations?.requires_gst" class="flex justify-between text-sm">
                                <span class="text-gray-600">GST (18%)</span>
                                <span class="font-medium">{{ formatAmount((service?.consultation_fee || 100) * 0.18) }}</span>
                            </div>
                            
                            <div class="border-t pt-3 flex justify-between font-semibold">
                                <span>Total</span>
                                <span>{{ formatAmount(totalAmount) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Country-specific Information -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-2">{{ checkoutFlow.country_name }} Regulations</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li v-if="checkoutFlow.regulations?.telemedicine_regulations">
                                ✓ Telemedicine compliant
                            </li>
                            <li v-if="checkoutFlow.regulations?.gdpr_compliance">
                                ✓ GDPR compliant
                            </li>
                            <li v-if="checkoutFlow.regulations?.hipaa_compliance">
                                ✓ HIPAA compliant
                            </li>
                            <li v-if="checkoutFlow.regulations?.data_localization">
                                ✓ Data stored locally
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import PaymentGatewaySelector from '@/components/PaymentGatewaySelector.vue'
import RazorpayCheckout from '@/components/RazorpayCheckout.vue'
import StripeCheckout from '@/components/StripeCheckout.vue'
import { useCountryManager } from '@/composables/useCountryManager'
import axios from 'axios'

// Props
const props = defineProps({
    checkoutFlow: Object,
    service: Object,
    providers: Array,
    user: Object,
    breadcrumbs: Array
})

// Country manager
const { formatAmount } = useCountryManager()

// State
const currentStep = ref(0)
const selectedGateway = ref(null)
const paymentCompleted = ref(false)
const isSubmitting = ref(false)

// Form data
const formData = ref({
    service_id: props.service?.id,
    patient_details: {
        full_name: props.user?.name || '',
        age: '',
        gender: '',
        phone: '',
        emergency_contact: '',
        nhs_number: '',
        gp_practice: ''
    },
    medical_history: {
        symptoms: '',
        medications: '',
        allergies: ''
    },
    provider_id: '',
    appointment_date: '',
    appointment_time: '',
    amount: props.service?.consultation_fee || 100
})

// Computed
const currentStepName = computed(() => props.checkoutFlow.steps[currentStep.value])

const totalAmount = computed(() => {
    const baseAmount = props.service?.consultation_fee || 100
    const gstAmount = props.checkoutFlow.regulations?.requires_gst ? baseAmount * 0.18 : 0
    return baseAmount + gstAmount
})

const minDate = computed(() => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return tomorrow.toISOString().split('T')[0]
})

const canProceedToNextStep = computed(() => {
    switch (currentStepName.value) {
        case 'patient_details':
            return formData.value.patient_details.full_name && 
                   formData.value.patient_details.age && 
                   formData.value.patient_details.gender &&
                   formData.value.patient_details.phone
        case 'medical_history':
            return formData.value.medical_history.symptoms
        case 'doctor_selection':
            return formData.value.provider_id && 
                   formData.value.appointment_date && 
                   formData.value.appointment_time
        case 'payment_details':
            return paymentCompleted.value
        default:
            return true
    }
})

// Methods
const formatStepName = (step) => {
    return step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatFlowType = (flowType) => {
    return flowType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const nextStep = () => {
    if (canProceedToNextStep.value && currentStep.value < props.checkoutFlow.steps.length - 1) {
        currentStep.value++
    }
}

const previousStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--
    }
}

const onGatewaySelected = (gateway) => {
    selectedGateway.value = gateway
    console.log('Gateway selected:', gateway)
}

const onPaymentSuccess = (result) => {
    paymentCompleted.value = true
    formData.value.payment_result = result
    console.log('Payment successful:', result)
}

const onPaymentError = (error) => {
    console.error('Payment error:', error)
    // Handle payment error
}

const submitAppointment = async () => {
    if (!paymentCompleted.value) return

    isSubmitting.value = true

    try {
        const response = await axios.post('/checkout/process/appointment', {
            ...formData.value,
            total_amount: totalAmount.value
        })

        if (response.data.success) {
            // Redirect to confirmation page
            router.visit(response.data.data.redirect_url)
        } else {
            throw new Error(response.data.message)
        }
    } catch (error) {
        console.error('Appointment booking failed:', error)
        alert('Failed to book appointment. Please try again.')
    } finally {
        isSubmitting.value = false
    }
}

onMounted(() => {
    console.log('Checkout flow:', props.checkoutFlow)
})
</script>
