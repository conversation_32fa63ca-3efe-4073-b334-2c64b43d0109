@extends('letterheads.common')

@section('document_content')
    <div class="document-info">
        <div class="left">
            <strong>{{ strtoupper($document_title ?? 'CONSULTATION SUMMARY') }}</strong><br>
            Ref: {{ $document_reference ?? 'CS-' . now()->format('Ymd-His') }}
        </div>
        <div class="right">
            {{ $generated_at->format('d/m/Y') }}<br>
            {{ $generated_at->format('g:i A') }}
        </div>
    </div>

    <div class="patient-details">
        <h3>👤 Patient Information</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <p><strong>Name:</strong> {{ $patient->user->name ?? 'N/A' }}</p>
                <p><strong>Date of Birth:</strong> {{ $patient->date_of_birth ? $patient->date_of_birth->format('d/m/Y') : 'N/A' }}</p>
                <p><strong>Email:</strong> {{ $patient->user->email ?? 'N/A' }}</p>
                @if($patient->nhs_number)
                    <p><strong>NHS Number:</strong> {{ $patient->nhs_number }}</p>
                @endif
                @if($patient->unique_id)
                    <p><strong>Patient ID:</strong> {{ $patient->unique_id }}</p>
                @endif
            </div>
            <div>
                @if($patient->phone)
                    <p><strong>Phone:</strong> {{ $patient->phone }}</p>
                @endif
                @if($patient->address)
                    <p><strong>Address:</strong><br>
                    {{ $patient->address }}
                    @if($patient->city), {{ $patient->city }}@endif
                    @if($patient->postal_code)<br>{{ $patient->postal_code }}@endif
                    </p>
                @endif
            </div>
        </div>
    </div>

    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            📋 Consultation Details
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <p><strong>Date:</strong> {{ $consultation->created_at->format('d/m/Y H:i') }}</p>
                <p><strong>Provider:</strong> {{ $provider->user->name ?? 'N/A' }}</p>
            </div>
            <div>
                <p><strong>Status:</strong> {{ ucfirst($consultation->status) }}</p>
                @if($consultation->duration)
                    <p><strong>Duration:</strong> {{ $consultation->duration }} minutes</p>
                @endif
            </div>
        </div>
    </div>

    @if($consultation->present_concerns)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            🩺 Present Concerns
        </h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
            {{ $consultation->present_concerns }}
        </div>
    </div>
    @endif

    @if($consultation->history)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            📖 History
        </h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
            {{ $consultation->history }}
        </div>
    </div>
    @endif

    @if($vitals && $vitals->count() > 0)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            📊 Vital Signs
        </h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            @foreach($vitals as $vital)
            <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; text-align: center;">
                <strong>{{ $vital->vital_name }}</strong><br>
                <span style="font-size: 18px; color: #1976d2;">{{ $vital->vital_value }} {{ $vital->unit ?? '' }}</span>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    @if($diagnoses && $diagnoses->count() > 0)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            🔍 Diagnoses
        </h3>
        @foreach($diagnoses as $diagnosis)
        <div style="background: #fff3cd; padding: 15px; margin-bottom: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
            <strong style="color: #856404;">{{ $diagnosis->diagnosis_name }}</strong>
            @if($diagnosis->icd10_code)
                <br><small style="color: #6c757d;">ICD-10: {{ $diagnosis->icd10_code }}</small>
            @endif
            @if($diagnosis->description)
                <br><span style="color: #495057;">{{ $diagnosis->description }}</span>
            @endif
        </div>
        @endforeach
    </div>
    @endif

    @if($prescriptions && $prescriptions->count() > 0)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            💊 Prescriptions
        </h3>
        @foreach($prescriptions as $prescription)
        <div style="background: #f0f8ff; padding: 15px; margin-bottom: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
            <strong style="color: #0056b3;">Prescription #{{ $prescription->id }}</strong>
            <br><small style="color: #6c757d;">Date: {{ $prescription->created_at->format('d/m/Y') }}</small>
            @if($prescription->items && $prescription->items->count() > 0)
                @foreach($prescription->items as $item)
                <div style="margin-top: 15px; padding: 10px; background: white; border-radius: 5px;">
                    <strong style="color: #495057;">{{ $item->medication->name ?? $item->medication_name }}</strong>
                    <div style="margin-top: 5px; font-size: 14px;">
                        <span style="display: inline-block; margin-right: 15px;"><strong>Dosage:</strong> {{ $item->dosage }}</span>
                        <span style="display: inline-block; margin-right: 15px;"><strong>Frequency:</strong> {{ $item->frequency }}</span>
                        <span style="display: inline-block;"><strong>Duration:</strong> {{ $item->duration }}</span>
                    </div>
                    @if($item->instructions)
                        <div style="margin-top: 8px; font-style: italic; color: #6c757d;">
                            <strong>Instructions:</strong> {{ $item->instructions }}
                        </div>
                    @endif
                </div>
                @endforeach
            @endif
        </div>
        @endforeach
    </div>
    @endif

    @if($notes && $notes->count() > 0)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            📝 Clinical Notes
        </h3>
        @foreach($notes as $note)
        <div style="background: #f0f9ff; padding: 15px; margin-bottom: 15px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <strong style="color: #0c4a6e;">{{ $note->title ?? 'Note' }}</strong>
            <br><small style="color: #6c757d;">{{ $note->created_at->format('d/m/Y H:i') }}</small>
            <div style="margin-top: 10px; color: #374151;">{{ $note->content }}</div>
        </div>
        @endforeach
    </div>
    @endif

    @if($treatmentPlans && $treatmentPlans->count() > 0)
    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            🎯 Treatment Plans
        </h3>
        @foreach($treatmentPlans as $plan)
        <div style="background: #f0fdf4; padding: 15px; margin-bottom: 15px; border-radius: 8px; border-left: 4px solid #10b981;">
            <strong style="color: #065f46;">{{ $plan->title }}</strong>
            <br><small style="color: #6c757d;">
                Start: {{ $plan->start_date ? $plan->start_date->format('d/m/Y') : 'N/A' }}
                @if($plan->end_date)
                    | End: {{ $plan->end_date->format('d/m/Y') }}
                @endif
            </small>
            <div style="margin-top: 10px; color: #374151;">{{ $plan->description }}</div>
        </div>
        @endforeach
    </div>
    @endif

@endsection
