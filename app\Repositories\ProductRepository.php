<?php

namespace App\Repositories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Product $model
     * @return void
     */
    public function __construct(Product $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find products by user.
     *
     * @param int $userId
     * @return Collection
     */
    public function findByUser(int $userId): Collection
    {
        return $this->findBy(['user_id' => $userId]);
    }
    
    /**
     * Find products by category.
     *
     * @param int $categoryId
     * @return Collection
     */
    public function findByCategory(int $categoryId): Collection
    {
        return $this->findBy(['category_id' => $categoryId]);
    }
    
    /**
     * Find active products.
     *
     * @param int|null $userId
     * @return Collection
     */
    public function findActive(?int $userId = null): Collection
    {
        $criteria = ['is_active' => true];
        
        if ($userId) {
            $criteria['user_id'] = $userId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find approved products.
     *
     * @param int|null $userId
     * @return Collection
     */
    public function findApproved(?int $userId = null): Collection
    {
        $criteria = ['approval_status' => 'approved'];
        
        if ($userId) {
            $criteria['user_id'] = $userId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find featured products.
     *
     * @param int $limit
     * @return Collection
     */
    public function findFeatured(int $limit = 8): Collection
    {
        return $this->model->newQuery()
            ->with(['category', 'images', 'user'])
            ->where('is_featured', true)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where('in_stock', true)
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->orderBy('sort_order')
            ->limit($limit)
            ->get();
    }

    /**
     * Find product by slug or ID.
     *
     * @param string $slugOrId
     * @return Product|null
     */
    public function findBySlugOrId(string $slugOrId): ?Product
    {
        return $this->model->newQuery()
            ->with(['category', 'images', 'user'])
            ->where(function($query) use ($slugOrId) {
                $query->where('slug', $slugOrId)
                      ->orWhere('id', $slugOrId);
            })
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->first();
    }

    /**
     * Find related products.
     *
     * @param int $productId
     * @param int $categoryId
     * @param int $limit
     * @return Collection
     */
    public function findRelated(int $productId, int $categoryId, int $limit = 4): Collection
    {
        return $this->model->newQuery()
            ->with(['category', 'images', 'user'])
            ->where('category_id', $categoryId)
            ->where('id', '!=', $productId)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where('in_stock', true)
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->limit($limit)
            ->get();
    }
    
    /**
     * Find products by type.
     *
     * @param string $type
     * @param int|null $userId
     * @return Collection
     */
    public function findByType(string $type, ?int $userId = null): Collection
    {
        $criteria = ['type' => $type];
        
        if ($userId) {
            $criteria['user_id'] = $userId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find products by approval status.
     *
     * @param string $status
     * @param int|null $userId
     * @return Collection
     */
    public function findByApprovalStatus(string $status, ?int $userId = null): Collection
    {
        $criteria = ['approval_status' => $status];
        
        if ($userId) {
            $criteria['user_id'] = $userId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find product by slug.
     *
     * @param string $slug
     * @return Product|null
     */
    public function findBySlug(string $slug): ?Product
    {
        return $this->findOneBy(['slug' => $slug]);
    }
    
    /**
     * Find product by SKU.
     *
     * @param string $sku
     * @return Product|null
     */
    public function findBySku(string $sku): ?Product
    {
        return $this->findOneBy(['sku' => $sku]);
    }
    
    /**
     * Find products on sale.
     *
     * @return Collection
     */
    public function findOnSale(): Collection
    {
        $query = $this->model->newQuery();
        $query->where('is_active', true)
              ->where('approval_status', 'approved')
              ->whereNotNull('sale_price')
              ->where('sale_price', '>', 0)
              ->where('sale_price', '<', $this->model->getTable() . '.price');
        
        return $query->get();
    }
    
    /**
     * Find products in stock.
     *
     * @return Collection
     */
    public function findInStock(): Collection
    {
        $query = $this->model->newQuery();
        $query->where('is_active', true)
              ->where('approval_status', 'approved')
              ->where(function ($q) {
                  $q->where('manage_stock', false)
                    ->orWhere(function ($subQ) {
                        $subQ->where('manage_stock', true)
                             ->where('in_stock', true)
                             ->where('stock_quantity', '>', 0);
                    });
              });
        
        return $query->get();
    }
    
    /**
     * Search products.
     *
     * @param string $search
     * @param array $filters
     * @return Collection
     */
    public function search(string $search, array $filters = []): Collection
    {
        $query = $this->model->newQuery();

        // Load relationships
        $query->with(['category', 'images', 'user']);

        // Apply search criteria
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('short_description', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%");
        });

        // Apply base filters for public search
        $query->where('is_active', true)
              ->where('approval_status', 'approved')
              ->whereHas('user', function ($q) {
                  $q->where('is_active', true);
              });

        // Apply additional filters
        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        // Apply ordering
        $query->orderBy('is_featured', 'desc')
              ->orderBy('name');

        // Apply limit if specified
        if (!empty($filters['limit'])) {
            $query->limit($filters['limit']);
        }

        return $query->get();
    }
    
    /**
     * Get products with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15, ?string $userCountryCode = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['user', 'category', 'images', 'countries', 'user.provider']);

        // Apply country filtering first if user country is available
        if ($userCountryCode) {
            \Log::info('Applying country filtering for user country: ' . $userCountryCode);
            $countryFilterService = new \App\Services\CountryFilterService();
            $query = $countryFilterService->filterProductsByCountry($query, $userCountryCode);
            \Log::info('Country filtering applied, query count: ' . $query->count());
        } else {
            \Log::info('No user country code provided, skipping country filtering');
            \Log::info('Query count without filtering: ' . $query->count());
        }

        // Apply filters
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }
        
        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }
        
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (!empty($filters['approval_status'])) {
            $query->where('approval_status', $filters['approval_status']);
        }
        
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }
        
        if (isset($filters['is_featured'])) {
            $query->where('is_featured', $filters['is_featured']);
        }
        
        if (isset($filters['in_stock'])) {
            if ($filters['in_stock']) {
                $query->where(function ($q) {
                    $q->where('manage_stock', false)
                      ->orWhere(function ($subQ) {
                          $subQ->where('manage_stock', true)
                               ->where('in_stock', true)
                               ->where('stock_quantity', '>', 0);
                      });
                });
            }
        }
        
        if (!empty($filters['min_price'])) {
            $query->where('price', '>=', $filters['min_price']);
        }
        
        if (!empty($filters['max_price'])) {
            $query->where('price', '<=', $filters['max_price']);
        }
        
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Update product approval status.
     *
     * @param int $productId
     * @param string $status
     * @param int|null $approvedBy
     * @param string|null $reason
     * @return bool
     */
    public function updateApprovalStatus(int $productId, string $status, ?int $approvedBy = null, ?string $reason = null): bool
    {
        $data = [
            'approval_status' => $status,
            'approved_by' => $approvedBy,
            'rejection_reason' => $reason
        ];
        
        if ($status === 'approved') {
            $data['approved_at'] = now();
        }
        
        $product = $this->update($productId, $data);
        return $product !== null;
    }
    
    /**
     * Update stock quantity.
     *
     * @param int $productId
     * @param int $quantity
     * @param string $operation
     * @return bool
     */
    public function updateStock(int $productId, int $quantity, string $operation = 'set'): bool
    {
        $product = $this->find($productId);
        
        if (!$product || !$product->manage_stock) {
            return false;
        }
        
        $newQuantity = match($operation) {
            'add' => $product->stock_quantity + $quantity,
            'subtract' => $product->stock_quantity - $quantity,
            'set' => $quantity,
            default => $product->stock_quantity
        };
        
        $data = [
            'stock_quantity' => max(0, $newQuantity),
            'in_stock' => $newQuantity > 0
        ];
        
        $updated = $this->update($productId, $data);
        return $updated !== null;
    }
}
