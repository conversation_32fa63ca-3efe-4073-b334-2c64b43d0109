<?php

namespace App\Services;

use App\Models\TreatmentPlan;
use App\Repositories\TreatmentPlanRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TreatmentPlanService
{
    protected TreatmentPlanRepository $treatmentPlanRepository;

    public function __construct(TreatmentPlanRepository $treatmentPlanRepository)
    {
        $this->treatmentPlanRepository = $treatmentPlanRepository;
    }

    /**
     * Create a new treatment plan with business logic
     */
    public function createTreatmentPlan(array $data): TreatmentPlan
    {
        // Business logic: Set defaults
        $data['status'] = $data['status'] ?? 'active';
        $data['start_date'] = $data['start_date'] ?? now();
        $data['created_by'] = $data['created_by'] ?? Auth::id();
        
        // Business logic: Calculate next review date if not provided
        if (empty($data['next_review_date']) && !empty($data['review_interval_days'])) {
            $data['next_review_date'] = Carbon::parse($data['start_date'])
                ->addDays($data['review_interval_days']);
        }
        
        // Business logic: Generate plan number
        if (empty($data['plan_number'])) {
            $data['plan_number'] = $this->generatePlanNumber();
        }

        return $this->treatmentPlanRepository->create($data);
    }

    /**
     * Update treatment plan with business logic
     */
    public function updateTreatmentPlan(int $planId, array $data): ?TreatmentPlan
    {
        // Business logic: Update next review date if interval changed
        if (isset($data['review_interval_days']) && !isset($data['next_review_date'])) {
            $plan = $this->treatmentPlanRepository->find($planId);
            if ($plan) {
                $data['next_review_date'] = $plan->start_date->addDays($data['review_interval_days']);
            }
        }

        return $this->treatmentPlanRepository->update($planId, $data);
    }

    /**
     * Get treatment plan details
     */
    public function getTreatmentPlanDetails(int $planId): ?TreatmentPlan
    {
        $plan = $this->treatmentPlanRepository->find($planId);
        
        if (!$plan) {
            return null;
        }

        // Load relationships
        $plan->load([
            'patient.user',
            'provider.user',
            'consultation',
            'goals',
            'tasks',
            'reviews',
            'prescriptions'
        ]);

        return $plan;
    }

    /**
     * Get patient treatment plans
     */
    public function getPatientTreatmentPlans(int $patientId): Collection
    {
        return $this->treatmentPlanRepository->findByPatient($patientId);
    }

    /**
     * Get active treatment plans for patient
     */
    public function getActivePatientTreatmentPlans(int $patientId): Collection
    {
        return $this->treatmentPlanRepository->findActive($patientId);
    }

    /**
     * Get provider treatment plans
     */
    public function getProviderTreatmentPlans(int $providerId): Collection
    {
        return $this->treatmentPlanRepository->findByProvider($providerId);
    }

    /**
     * Get treatment plans by consultation
     */
    public function getConsultationTreatmentPlans(int $consultationId): Collection
    {
        return $this->treatmentPlanRepository->findByConsultation($consultationId);
    }

    /**
     * Get treatment plans with filters and pagination
     */
    public function getTreatmentPlansWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->treatmentPlanRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Complete treatment plan
     */
    public function completeTreatmentPlan(int $planId, array $completionData = []): bool
    {
        $data = array_merge($completionData, [
            'status' => 'completed',
            'completed_at' => now(),
            'completed_by' => Auth::id()
        ]);

        $plan = $this->treatmentPlanRepository->update($planId, $data);
        
        // Business logic: Handle completion side effects
        if ($plan) {
            // Could send notifications, update patient records, etc.
        }

        return $plan !== null;
    }

    /**
     * Cancel treatment plan
     */
    public function cancelTreatmentPlan(int $planId, string $reason): bool
    {
        $data = [
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => Auth::id(),
            'cancellation_reason' => $reason
        ];

        $plan = $this->treatmentPlanRepository->update($planId, $data);
        
        // Business logic: Handle cancellation side effects
        if ($plan) {
            // Could send notifications, update related records, etc.
        }

        return $plan !== null;
    }

    /**
     * Review treatment plan
     */
    public function reviewTreatmentPlan(int $planId, array $reviewData): bool
    {
        $plan = $this->treatmentPlanRepository->find($planId);
        
        if (!$plan) {
            return false;
        }

        // Business logic: Create review record
        $reviewData['treatment_plan_id'] = $planId;
        $reviewData['reviewed_by'] = Auth::id();
        $reviewData['review_date'] = now();
        
        // This would create a review record in a separate table
        // $this->createReviewRecord($reviewData);
        
        // Business logic: Update next review date
        $nextReviewDate = null;
        if ($plan->review_interval_days) {
            $nextReviewDate = now()->addDays($plan->review_interval_days);
        }
        
        $updatedPlan = $this->treatmentPlanRepository->update($planId, [
            'last_reviewed_at' => now(),
            'next_review_date' => $nextReviewDate,
            'review_notes' => $reviewData['notes'] ?? null
        ]);

        return $updatedPlan !== null;
    }

    /**
     * Get treatment plans requiring review
     */
    public function getTreatmentPlansRequiringReview(?int $providerId = null): Collection
    {
        return $this->treatmentPlanRepository->findRequiringReview($providerId);
    }

    /**
     * Get treatment plans by type
     */
    public function getTreatmentPlansByType(string $type, ?int $patientId = null): Collection
    {
        return $this->treatmentPlanRepository->findByType($type, $patientId);
    }

    /**
     * Get treatment plan statistics
     */
    public function getTreatmentPlanStatistics(?int $providerId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        return $this->treatmentPlanRepository->getStatistics($providerId, $startDate, $endDate);
    }

    /**
     * Calculate treatment plan progress
     */
    public function calculateProgress(int $planId): array
    {
        $plan = $this->treatmentPlanRepository->find($planId);
        
        if (!$plan) {
            return [];
        }

        // Business logic: Calculate progress based on goals and tasks
        $totalGoals = $plan->goals()->count();
        $completedGoals = $plan->goals()->where('status', 'completed')->count();
        
        $totalTasks = $plan->tasks()->count();
        $completedTasks = $plan->tasks()->where('status', 'completed')->count();
        
        $goalProgress = $totalGoals > 0 ? ($completedGoals / $totalGoals) * 100 : 0;
        $taskProgress = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;
        
        $overallProgress = ($goalProgress + $taskProgress) / 2;

        return [
            'overall_progress' => round($overallProgress, 2),
            'goal_progress' => round($goalProgress, 2),
            'task_progress' => round($taskProgress, 2),
            'total_goals' => $totalGoals,
            'completed_goals' => $completedGoals,
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'days_active' => $plan->start_date->diffInDays(now()),
            'next_review_in_days' => $plan->next_review_date ? now()->diffInDays($plan->next_review_date, false) : null
        ];
    }

    /**
     * Generate unique plan number
     */
    private function generatePlanNumber(): string
    {
        do {
            $number = 'TP' . date('Y') . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while ($this->treatmentPlanRepository->findOneBy(['plan_number' => $number]));

        return $number;
    }
}
