<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            [
                'name' => 'United States',
                'code' => 'US',
                'code_3' => 'USA',
                'currency_code' => 'USD',
                'currency_symbol' => '$',
                'currency_name' => 'US Dollar',
                'phone_code' => '+1',
                'timezone' => 'America/New_York',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['en'],
                'tax_rate' => 8.25,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'United Kingdom',
                'code' => 'GB',
                'code_3' => 'GBR',
                'currency_code' => 'GBP',
                'currency_symbol' => '£',
                'currency_name' => 'British Pound',
                'phone_code' => '+44',
                'timezone' => 'Europe/London',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['en'],
                'tax_rate' => 20.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'India',
                'code' => 'IN',
                'code_3' => 'IND',
                'currency_code' => 'INR',
                'currency_symbol' => '₹',
                'currency_name' => 'Indian Rupee',
                'phone_code' => '+91',
                'timezone' => 'Asia/Kolkata',
                'supported_payment_gateways' => ['razorpay', 'stripe'],
                'supported_languages' => ['en', 'hi'],
                'tax_rate' => 18.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Canada',
                'code' => 'CA',
                'code_3' => 'CAN',
                'currency_code' => 'CAD',
                'currency_symbol' => 'C$',
                'currency_name' => 'Canadian Dollar',
                'phone_code' => '+1',
                'timezone' => 'America/Toronto',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['en', 'fr'],
                'tax_rate' => 13.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Australia',
                'code' => 'AU',
                'code_3' => 'AUS',
                'currency_code' => 'AUD',
                'currency_symbol' => 'A$',
                'currency_name' => 'Australian Dollar',
                'phone_code' => '+61',
                'timezone' => 'Australia/Sydney',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['en'],
                'tax_rate' => 10.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Germany',
                'code' => 'DE',
                'code_3' => 'DEU',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'currency_name' => 'Euro',
                'phone_code' => '+49',
                'timezone' => 'Europe/Berlin',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['de', 'en'],
                'tax_rate' => 19.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'France',
                'code' => 'FR',
                'code_3' => 'FRA',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'currency_name' => 'Euro',
                'phone_code' => '+33',
                'timezone' => 'Europe/Paris',
                'supported_payment_gateways' => ['stripe', 'paypal'],
                'supported_languages' => ['fr', 'en'],
                'tax_rate' => 20.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Nigeria',
                'code' => 'NG',
                'code_3' => 'NGA',
                'currency_code' => 'NGN',
                'currency_symbol' => '₦',
                'currency_name' => 'Nigerian Naira',
                'phone_code' => '+234',
                'timezone' => 'Africa/Lagos',
                'supported_payment_gateways' => ['paystack', 'flutterwave'],
                'supported_languages' => ['en'],
                'tax_rate' => 7.50,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'South Africa',
                'code' => 'ZA',
                'code_3' => 'ZAF',
                'currency_code' => 'ZAR',
                'currency_symbol' => 'R',
                'currency_name' => 'South African Rand',
                'phone_code' => '+27',
                'timezone' => 'Africa/Johannesburg',
                'supported_payment_gateways' => ['payfast', 'stripe'],
                'supported_languages' => ['en', 'af'],
                'tax_rate' => 15.00,
                'is_active' => true,
                'is_supported' => true,
                'sort_order' => 9,
            ],
        ];

        foreach ($countries as $country) {
            Country::updateOrCreate(
                ['code' => $country['code']],
                $country
            );
        }
    }
}
