<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->foreignId('created_by_provider_id')->nullable()->after('clinic_id')->constrained('providers')->onDelete('set null');
            $table->index('created_by_provider_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropForeign(['created_by_provider_id']);
            $table->dropIndex(['created_by_provider_id']);
            $table->dropColumn('created_by_provider_id');
        });
    }
};
