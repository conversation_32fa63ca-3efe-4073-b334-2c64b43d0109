<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class ClinicianMiddlewareTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the roles and permissions seeder
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
    }

    /** @test */
    public function non_authenticated_user_cannot_access_clinician_routes()
    {
        $response = $this->getJson('/api/doctor-settings');
        
        $response->assertStatus(401)
                ->assertJson(['message' => 'Unauthenticated.']);
    }

    /** @test */
    public function non_clinician_user_cannot_access_clinician_routes()
    {
        $user = User::factory()->create([
            'is_clinician' => false
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/doctor-settings');
        
        $response->assertStatus(403)
                ->assertJson(['message' => 'Access denied. This feature is only available to clinicians.']);
    }

    /** @test */
    public function clinician_user_can_access_clinician_routes()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create([
            'is_clinician' => true
        ]);
        
        // Create provider for the user
        Provider::factory()->create([
            'user_id' => $user->id,
            'clinic_id' => $clinic->id
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/doctor-settings');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function patient_user_cannot_access_consultation_routes()
    {
        $user = User::factory()->create([
            'is_clinician' => false
        ]);
        $user->assignRole('patient');
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/consultations');
        
        $response->assertStatus(403);
    }

    /** @test */
    public function provider_user_without_clinician_flag_cannot_access_consultation_routes()
    {
        $user = User::factory()->create([
            'is_clinician' => false
        ]);
        $user->assignRole('provider');
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/consultations');
        
        $response->assertStatus(403);
    }

    /** @test */
    public function provider_user_with_clinician_flag_can_access_consultation_routes()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create([
            'is_clinician' => true
        ]);
        $user->assignRole('provider');
        
        // Create provider for the user
        Provider::factory()->create([
            'user_id' => $user->id,
            'clinic_id' => $clinic->id
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/consultations');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_user_with_clinician_flag_can_access_consultation_routes()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create([
            'is_clinician' => true
        ]);
        $user->assignRole('admin');
        
        // Create provider for the user
        Provider::factory()->create([
            'user_id' => $user->id,
            'clinic_id' => $clinic->id
        ]);
        
        Sanctum::actingAs($user);
        
        $response = $this->getJson('/api/consultations');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function patients_can_create_prescription_refill_requests()
    {
        $user = User::factory()->create([
            'is_clinician' => false
        ]);
        $user->assignRole('patient');
        
        $patient = Patient::factory()->create([
            'user_id' => $user->id
        ]);
        
        Sanctum::actingAs($user);
        
        // This route should be accessible to patients
        $response = $this->postJson('/api/prescription-refills', [
            'original_prescription_id' => 999, // Will fail validation but should pass middleware
            'request_reason' => 'Running low on medication'
        ]);
        
        // Should not be blocked by clinician middleware
        $response->assertStatus(422); // Validation error, not 403 access denied
    }
}
