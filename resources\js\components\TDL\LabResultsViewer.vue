<template>
  <div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Lab Results</h2>
      <div class="flex space-x-3">
        <button
          @click="processResults"
          :disabled="processing"
          class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
        >
          <i class="fas fa-sync-alt mr-2" :class="{ 'fa-spin': processing }"></i>
          Process Results
        </button>
        <button
          @click="refreshResults"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <i class="fas fa-refresh mr-2"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select
            v-model="filters.status"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="received">Received</option>
            <option value="completed">Completed</option>
            <option value="reviewed">Reviewed</option>
            <option value="archived">Archived</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Patient</label>
          <select
            v-model="filters.patient_id"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Patients</option>
            <option v-for="patient in patients" :key="patient.id" :value="patient.id">
              {{ patient.first_name }} {{ patient.last_name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
          <input
            v-model="filters.date_from"
            type="date"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div class="flex items-end">
          <label class="flex items-center">
            <input
              v-model="filters.abnormal_only"
              type="checkbox"
              @change="applyFilters"
              class="rounded border-gray-300 text-red-600 focus:ring-red-500"
            />
            <span class="ml-2 text-sm text-gray-700">Abnormal results only</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Results List -->
    <div v-else class="space-y-4">
      <div
        v-for="result in results"
        :key="result.id"
        class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
      >
        <div class="p-4">
          <!-- Result Header -->
          <div class="flex justify-between items-start mb-4">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">
                {{ result.patient?.first_name }} {{ result.patient?.last_name }}
              </h3>
              <div class="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                <span>Order: {{ result.order_number }}</span>
                <span>Lab Ref: {{ result.lab_reference_id }}</span>
                <span>Received: {{ formatDate(result.received_at) }}</span>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <!-- Status Badge -->
              <span
                class="px-3 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(result.status)"
              >
                {{ result.status_label }}
              </span>
              
              <!-- Abnormal Flag -->
              <span
                v-if="result.has_abnormal_results"
                class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full"
              >
                <i class="fas fa-exclamation-triangle mr-1"></i>
                Abnormal
              </span>

              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  @click="viewResult(result)"
                  class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="View Details"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  v-if="result.status !== 'reviewed'"
                  @click="reviewResult(result)"
                  class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Mark as Reviewed"
                >
                  <i class="fas fa-check"></i>
                </button>
                <button
                  @click="downloadResult(result)"
                  class="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                  title="Download"
                >
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Test Summary -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-700">Tests:</span>
              <span class="ml-2">{{ result.test_count }} tests</span>
            </div>
            <div v-if="result.abnormal_count > 0">
              <span class="font-medium text-red-700">Abnormal:</span>
              <span class="ml-2 text-red-600">{{ result.abnormal_count }} values</span>
            </div>
            <div v-if="result.reviewed_at">
              <span class="font-medium text-gray-700">Reviewed by:</span>
              <span class="ml-2">{{ result.reviewer?.first_name }} {{ result.reviewer?.last_name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && results.length === 0" class="text-center py-12">
      <i class="fas fa-file-medical text-gray-400 text-4xl mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No results found</h3>
      <p class="text-gray-500">Try adjusting your filters or process new results.</p>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && pagination.last_page > 1" class="mt-6 flex justify-center">
      <nav class="flex space-x-2">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          class="px-3 py-2 text-sm rounded-lg transition-colors"
          :class="page === pagination.current_page 
            ? 'bg-blue-600 text-white' 
            : 'bg-white text-gray-700 hover:bg-gray-50 border'"
        >
          {{ page }}
        </button>
      </nav>
    </div>

    <!-- Result Detail Modal -->
    <div v-if="selectedResult" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="p-4 border-b flex justify-between items-center">
          <h3 class="text-lg font-semibold">Lab Result Details</h3>
          <button
            @click="selectedResult = null"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <LabResultDetail :result="selectedResult" @close="selectedResult = null" />
        </div>
      </div>
    </div>

    <!-- Review Modal -->
    <div v-if="reviewingResult" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-md w-full mx-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">Review Result</h3>
        </div>
        <div class="p-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Physician Notes</label>
            <textarea
              v-model="reviewNotes"
              rows="4"
              placeholder="Enter review notes (optional)..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <button
              @click="reviewingResult = null"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              @click="submitReview"
              :disabled="submittingReview"
              class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <i v-if="submittingReview" class="fas fa-spinner fa-spin mr-2"></i>
              Mark as Reviewed
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNotifications } from '@/composables/useNotifications'
import LabResultDetail from './LabResultDetail.vue'
import tdlLabService from '@/services/tdlLabService'
import type { LabTestResult, User, PaginationMeta } from '@/types/tdl'

const { showSuccess, showError } = useNotifications()

// Reactive data
interface Filters {
    status: string
    patient_id: string
    date_from: string
    abnormal_only: boolean
}

const results = ref<LabTestResult[]>([])
const patients = ref<User[]>([])
const selectedResult = ref<LabTestResult | null>(null)
const reviewingResult = ref<LabTestResult | null>(null)
const reviewNotes = ref<string>('')
const loading = ref<boolean>(false)
const processing = ref<boolean>(false)
const submittingReview = ref<boolean>(false)
const pagination = ref<PaginationMeta | null>(null)

const filters = ref<Filters>({
    status: '',
    patient_id: '',
    date_from: '',
    abnormal_only: false
})

// Computed
const visiblePages = computed(() => {
  if (!pagination.value) return []
  
  const current = pagination.value.current_page
  const last = pagination.value.last_page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const fetchResults = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: '10'
    })
    
    Object.entries(filters.value).forEach(([key, value]) => {
      if (value) {
        params.append(key, value.toString())
      }
    })
    
    const response = await fetch(`/lab/results?${params}`)
    const data = await response.json()
    
    if (data.success) {
      results.value = data.data.results.data
      pagination.value = {
        current_page: data.data.results.current_page,
        last_page: data.data.results.last_page,
        total: data.data.results.total
      }
    }
  } catch (error) {
    console.error('Error fetching results:', error)
    showError('Failed to load lab results')
  } finally {
    loading.value = false
  }
}

const fetchPatients = async () => {
  try {
    const response = await fetch('/patients')
    const data = await response.json()
    
    if (data.success) {
      patients.value = data.data.patients
    }
  } catch (error) {
    console.error('Error fetching patients:', error)
  }
}

const processResults = async () => {
  processing.value = true
  try {
    const response = await fetch('/lab/results/process', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    
    const data = await response.json()
    
    if (data.success) {
      showSuccess(`Processed ${data.data.processed_count} new results`)
      fetchResults()
    } else {
      showError(data.message || 'Failed to process results')
    }
  } catch (error) {
    console.error('Error processing results:', error)
    showError('Failed to process results')
  } finally {
    processing.value = false
  }
}

const refreshResults = () => {
  fetchResults()
}

const applyFilters = () => {
  fetchResults(1)
}

const goToPage = (page) => {
  fetchResults(page)
}

const viewResult = (result) => {
  selectedResult.value = result
}

const reviewResult = (result) => {
  reviewingResult.value = result
  reviewNotes.value = ''
}

const submitReview = async () => {
  if (!reviewingResult.value) return

  submittingReview.value = true
  try {
    const response = await fetch(`/lab/results/${reviewingResult.value.id}/review`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        notes: reviewNotes.value
      })
    })

    const data = await response.json()

    if (data.success) {
      showSuccess('Result marked as reviewed')
      reviewingResult.value = null
      fetchResults()
    } else {
      showError(data.message || 'Failed to review result')
    }
  } catch (error) {
    console.error('Error reviewing result:', error)
    showError('Failed to review result')
  } finally {
    submittingReview.value = false
  }
}

const downloadResult = async (result) => {
  try {
    const response = await fetch(`/lab/results/${result.id}/download`)
    const data = await response.json()
    
    if (data.success) {
      // Create and download file
      const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `lab-result-${result.order_number}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } else {
      showError('Failed to download result')
    }
  } catch (error) {
    console.error('Error downloading result:', error)
    showError('Failed to download result')
  }
}

const getStatusClass = (status) => {
  const classes = {
    received: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    reviewed: 'bg-purple-100 text-purple-800',
    archived: 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Lifecycle
onMounted(() => {
  fetchResults()
  fetchPatients()
})
</script>


