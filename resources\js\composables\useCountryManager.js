import { ref, computed, watch } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'

// Global state for country management
const currentTestCountry = ref(null)
const availableCountries = ref([])
const isLoading = ref(false)
const error = ref(null)

export function useCountryManager() {
    const page = usePage()
    
    // Get current user
    const currentUser = computed(() => page.props.auth?.user || null)
    
    // Environment detection
    const isTestingMode = computed(() => {
        const hostname = window.location.hostname
        const isLocal = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('local')
        const isStaging = hostname.includes('staging') || hostname.includes('test')
        const isDevelopment = import.meta.env.DEV || process.env.NODE_ENV === 'development'
        const isApiMedroid = hostname === 'api.medroid.ai' // Include api.medroid.ai as testing environment

        return isLocal || isStaging || isDevelopment || isApiMedroid
    })
    
    // Get effective country code (test country or user's actual country)
    const getEffectiveCountryCode = () => {
        if (isTestingMode.value && currentTestCountry.value) {
            return currentTestCountry.value.code
        }
        return currentUser.value?.country_code || null
    }
    
    // Get effective country info
    const getEffectiveCountryInfo = () => {
        const countryCode = getEffectiveCountryCode()
        const country = availableCountries.value.find(c => c.code === countryCode)
        
        if (country) {
            return country
        }
        
        // No fallback - return null if no country found
        return null
    }
    
    // Current effective country (reactive)
    const effectiveCountry = computed(() => getEffectiveCountryInfo())
    
    // Load available countries
    const loadCountries = async () => {
        if (availableCountries.value.length > 0) {
            return availableCountries.value
        }
        
        try {
            isLoading.value = true
            error.value = null
            
            const response = await axios.get('/api/countries')
            if (response.data.success) {
                availableCountries.value = response.data.data
                
                // Initialize test country from session storage
                initializeTestCountry()
            } else {
                throw new Error(response.data.message || 'Failed to load countries')
            }
        } catch (err) {
            error.value = err.response?.data?.message || err.message || 'Failed to load countries'
            console.error('Failed to load countries:', err)
        } finally {
            isLoading.value = false
        }
        
        return availableCountries.value
    }
    
    // Initialize test country from session storage
    const initializeTestCountry = () => {
        if (!isTestingMode.value) return
        
        const sessionCountryCode = sessionStorage.getItem('test_country_code')
        if (sessionCountryCode && availableCountries.value.length > 0) {
            const country = availableCountries.value.find(c => c.code === sessionCountryCode)
            if (country) {
                currentTestCountry.value = country
            }
        }
    }
    
    // Set test country
    const setTestCountry = (countryCode) => {
        if (!isTestingMode.value) {
            console.warn('Country switching is only available in testing mode')
            return false
        }
        
        const country = availableCountries.value.find(c => c.code === countryCode)
        if (!country) {
            console.error('Country not found:', countryCode)
            return false
        }
        
        currentTestCountry.value = country
        sessionStorage.setItem('test_country_code', countryCode)
        
        console.log('Test country set to:', country.name)
        return true
    }
    
    // Reset to user's actual country
    const resetToUserCountry = () => {
        if (!isTestingMode.value) return false
        
        currentTestCountry.value = null
        sessionStorage.removeItem('test_country_code')
        
        console.log('Reset to user country:', currentUser.value?.country_code || 'GB')
        return true
    }
    
    // Get payment gateway for current country
    const getPaymentGateway = async (countryCode = null) => {
        try {
            const code = countryCode || getEffectiveCountryCode()
            const response = await axios.get(`/api/payment-gateways/available?country_code=${code}`)
            
            if (response.data.success) {
                return {
                    success: true,
                    country: response.data.data.country_code,
                    currency: response.data.data.currency,
                    primaryGateway: response.data.data.primary_gateway,
                    availableGateways: response.data.data.available_gateways
                }
            } else {
                throw new Error(response.data.message || 'Failed to get payment gateway')
            }
        } catch (err) {
            console.error('Failed to get payment gateway:', err)
            return {
                success: false,
                error: err.response?.data?.message || err.message || 'Failed to get payment gateway'
            }
        }
    }
    
    // Get currency info for current country
    const getCurrencyInfo = async (countryCode = null) => {
        try {
            const code = countryCode || getEffectiveCountryCode()
            const response = await axios.get(`/api/currencies/country/${code}`)
            
            if (response.data.success) {
                return {
                    success: true,
                    ...response.data.data
                }
            } else {
                throw new Error(response.data.message || 'Failed to get currency info')
            }
        } catch (err) {
            console.error('Failed to get currency info:', err)
            return {
                success: false,
                error: err.response?.data?.message || err.message || 'Failed to get currency info'
            }
        }
    }
    
    // Check if a specific gateway is available for current country
    const isGatewayAvailable = async (gateway, countryCode = null) => {
        try {
            const code = countryCode || getEffectiveCountryCode()
            const response = await axios.get(`/api/payment-gateways/${gateway}/eligibility?country_code=${code}`)
            
            if (response.data.success) {
                return response.data.data.is_supported
            } else {
                return false
            }
        } catch (err) {
            console.error('Failed to check gateway availability:', err)
            return false
        }
    }
    
    // Get all supported countries for a gateway
    const getSupportedCountries = (gateway) => {
        return availableCountries.value.filter(country => {
            const gateways = country.supported_payment_gateways || []
            return gateways.includes(gateway)
        })
    }
    
    // Format amount with current country's currency
    const formatAmount = (amount, options = {}) => {
        const country = effectiveCountry.value
        const {
            showSymbol = true,
            decimals = 2
        } = options
        
        try {
            const formatter = new Intl.NumberFormat(getLocaleForCountry(country.code), {
                style: showSymbol ? 'currency' : 'decimal',
                currency: country.currency_code,
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            })
            return formatter.format(amount)
        } catch (error) {
            // Fallback formatting
            const formattedAmount = parseFloat(amount).toFixed(decimals)
            return showSymbol ? `${country.currency_symbol}${formattedAmount}` : formattedAmount
        }
    }
    
    // Get locale for country
    const getLocaleForCountry = (countryCode) => {
        const localeMap = {
            'US': 'en-US',
            'GB': 'en-GB',
            'IN': 'en-IN',
            'CA': 'en-CA',
            'AU': 'en-AU',
            'DE': 'de-DE',
            'FR': 'fr-FR',
            'NG': 'en-NG',
            'ZA': 'en-ZA'
        }
        return localeMap[countryCode] || 'en-GB'
    }
    
    // Get test mode status
    const getTestModeInfo = () => {
        return {
            isTestingMode: isTestingMode.value,
            hasTestCountry: !!currentTestCountry.value,
            testCountry: currentTestCountry.value,
            userCountry: currentUser.value?.country_code || 'GB',
            effectiveCountry: effectiveCountry.value.code,
            environment: import.meta.env.MODE || 'production'
        }
    }
    
    // Watch for test country changes and emit events
    watch(currentTestCountry, (newCountry, oldCountry) => {
        if (newCountry !== oldCountry) {
            // Emit custom event for other components to listen to
            window.dispatchEvent(new CustomEvent('country-changed', {
                detail: {
                    oldCountry,
                    newCountry,
                    effectiveCountry: effectiveCountry.value
                }
            }))
        }
    })
    
    return {
        // State
        isLoading,
        error,
        availableCountries,
        currentTestCountry,
        effectiveCountry,
        
        // Computed
        isTestingMode,
        currentUser,
        
        // Methods
        loadCountries,
        setTestCountry,
        resetToUserCountry,
        getEffectiveCountryCode,
        getEffectiveCountryInfo,
        getPaymentGateway,
        getCurrencyInfo,
        isGatewayAvailable,
        getSupportedCountries,
        formatAmount,
        getLocaleForCountry,
        getTestModeInfo
    }
}
