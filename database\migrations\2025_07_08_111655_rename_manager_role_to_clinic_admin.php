<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the role name from 'manager' to 'clinic_admin' in the roles table
        Role::where('name', 'manager')->update(['name' => 'clinic_admin']);

        // Update the role column in users table for any users with 'manager' role
        \DB::table('users')->where('role', 'manager')->update(['role' => 'clinic_admin']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the role name from 'clinic_admin' back to 'manager'
        Role::where('name', 'clinic_admin')->update(['name' => 'manager']);

        // Revert the role column in users table
        \DB::table('users')->where('role', 'clinic_admin')->update(['role' => 'manager']);
    }
};
