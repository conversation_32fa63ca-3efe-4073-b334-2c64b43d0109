<?php

namespace App\Services;

use App\Models\Service;
use App\Repositories\ServiceRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class ServiceManagementService
{
    protected ServiceRepository $serviceRepository;

    public function __construct(ServiceRepository $serviceRepository)
    {
        $this->serviceRepository = $serviceRepository;
    }

    /**
     * Create a new service with business logic
     */
    public function createService(array $data): Service
    {
        // Extract countries data before creating service
        $countriesData = $data['countries'] ?? [];
        unset($data['countries']);

        // Business logic: Set defaults
        $data['approval_status'] = $data['approval_status'] ?? 'pending';
        $data['active'] = $data['active'] ?? true;
        $data['internal'] = $data['internal'] ?? false;
        $data['is_telemedicine'] = $data['is_telemedicine'] ?? false;
        $data['discount_percentage'] = $data['discount_percentage'] ?? 0;

        // Business logic: Calculate discounted price if applicable
        if ($data['discount_percentage'] > 0) {
            $data['discounted_price'] = $data['price'] * (1 - $data['discount_percentage'] / 100);
        }

        $service = $this->serviceRepository->create($data);

        // Handle countries data
        if (!empty($countriesData)) {
            $this->syncServiceCountries($service, $countriesData);
        }

        return $service;
    }

    /**
     * Update service with business logic
     */
    public function updateService(int $serviceId, array $data): ?Service
    {
        // Extract countries data before updating service
        $countriesData = $data['countries'] ?? null;
        unset($data['countries']);

        // Business logic: Recalculate discounted price if price or discount changed
        if (isset($data['price']) || isset($data['discount_percentage'])) {
            $service = $this->serviceRepository->find($serviceId);
            if ($service) {
                $price = $data['price'] ?? $service->price;
                $discount = $data['discount_percentage'] ?? $service->discount_percentage;

                if ($discount > 0) {
                    $data['discounted_price'] = $price * (1 - $discount / 100);
                } else {
                    $data['discounted_price'] = null;
                }
            }
        }

        $service = $this->serviceRepository->update($serviceId, $data);

        // Handle countries data if provided
        if ($service && $countriesData !== null) {
            $this->syncServiceCountries($service, $countriesData);
        }

        return $service;
    }

    /**
     * Get service details
     */
    public function getServiceDetails(int $serviceId, array $filters = []): ?Service
    {
        $service = $this->serviceRepository->find($serviceId);

        if (!$service) {
            return null;
        }

        // Apply filters if provided
        if (!empty($filters)) {
            // Check clinic filter if provided
            if (isset($filters['clinic_id']) && $service->provider && $service->provider->clinic_id !== $filters['clinic_id']) {
                return null;
            }
        }

        // Load relationships
        $service->load(['provider.user', 'category', 'appointments', 'countries']);

        return $service;
    }

    /**
     * Get services by provider
     */
    public function getProviderServices(int $providerId): Collection
    {
        return $this->serviceRepository->findByProvider($providerId);
    }

    /**
     * Get active services
     */
    public function getActiveServices(?int $providerId = null): Collection
    {
        return $this->serviceRepository->findActive($providerId);
    }

    /**
     * Get services by category
     */
    public function getServicesByCategory(string $category, ?int $providerId = null): Collection
    {
        return $this->serviceRepository->findByCategory($category, $providerId);
    }

    /**
     * Get telemedicine services
     */
    public function getTelemedicineServices(?int $providerId = null): Collection
    {
        return $this->serviceRepository->findTelemedicine($providerId);
    }

    /**
     * Search services
     */
    public function searchServices(string $search, array $filters = []): Collection
    {
        return $this->serviceRepository->search($search, $filters['provider_id'] ?? null);
    }

    /**
     * Get services with filters and pagination
     */
    public function getServicesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->serviceRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Approve service
     */
    public function approveService(int $serviceId, ?string $reason = null): bool
    {
        return $this->serviceRepository->updateApprovalStatus(
            $serviceId, 
            'approved', 
            Auth::id(), 
            $reason
        );
    }

    /**
     * Reject service
     */
    public function rejectService(int $serviceId, string $reason): bool
    {
        return $this->serviceRepository->updateApprovalStatus(
            $serviceId, 
            'rejected', 
            Auth::id(), 
            $reason
        );
    }

    /**
     * Activate service
     */
    public function activateService(int $serviceId): bool
    {
        $service = $this->serviceRepository->update($serviceId, ['active' => true]);
        return $service !== null;
    }

    /**
     * Deactivate service
     */
    public function deactivateService(int $serviceId): bool
    {
        $service = $this->serviceRepository->update($serviceId, ['active' => false]);
        return $service !== null;
    }

    /**
     * Get service statistics
     */
    public function getServiceStatistics(?int $providerId = null): array
    {
        $query = $this->serviceRepository->model->newQuery();
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }

        $total = $query->count();
        $active = $query->where('active', true)->count();
        $approved = $query->where('approval_status', 'approved')->count();
        $pending = $query->where('approval_status', 'pending')->count();
        $telemedicine = $query->where('is_telemedicine', true)->count();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'approved' => $approved,
            'pending' => $pending,
            'rejected' => $total - $approved - $pending,
            'telemedicine' => $telemedicine,
            'in_person' => $total - $telemedicine,
        ];
    }

    /**
     * Get popular services
     */
    public function getPopularServices(int $limit = 10): Collection
    {
        // Business logic: Get services with most appointments
        $query = $this->serviceRepository->model->newQuery();
        $query->with(['provider.user'])
              ->where('active', true)
              ->where('approval_status', 'approved')
              ->withCount('appointments')
              ->orderBy('appointments_count', 'desc')
              ->limit($limit);

        return $query->get();
    }

    /**
     * Get services with discounts
     */
    public function getDiscountedServices(?int $providerId = null): Collection
    {
        return $this->serviceRepository->findWithDiscounts($providerId);
    }

    /**
     * Calculate service price with discount
     */
    public function calculateServicePrice(int $serviceId): array
    {
        $service = $this->serviceRepository->find($serviceId);
        
        if (!$service) {
            return [];
        }

        $originalPrice = $service->price;
        $discount = $service->discount_percentage ?? 0;
        $discountedPrice = $discount > 0 ? $originalPrice * (1 - $discount / 100) : $originalPrice;
        $savings = $originalPrice - $discountedPrice;

        return [
            'original_price' => $originalPrice,
            'discount_percentage' => $discount,
            'discounted_price' => $discountedPrice,
            'savings' => $savings,
            'has_discount' => $discount > 0,
            'discount_valid' => $service->discount_valid_until ? $service->discount_valid_until->isFuture() : true
        ];
    }

    /**
     * Sync service countries with pricing data
     */
    private function syncServiceCountries(Service $service, array $countriesData): void
    {
        $syncData = [];

        foreach ($countriesData as $countryData) {
            if (empty($countryData['country_code'])) {
                continue;
            }

            $syncData[$countryData['country_code']] = [
                'price_override' => $countryData['price'] ?? null,
                'is_available' => $countryData['is_available'] ?? true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Sync the countries with the service
        $service->countries()->sync($syncData);
    }
}
