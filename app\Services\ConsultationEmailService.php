<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\ConsultationDocument;
use App\Models\Prescription;
use App\Models\MedicalLetter;
use App\Models\Clinic;
use App\Services\DocumentGenerationService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ConsultationEmailService
{
    public function __construct(
        private BrevoService $brevoService
    ) {}

    /**
     * Share consultation document via email.
     */
    public function shareDocument(ConsultationDocument $document, array $emails, bool $encryptDocument = false): bool
    {
        try {
            $consultation = $document->consultation;
            $clinic = $consultation->clinic;

            // Get clinic email settings
            $emailSettings = $this->getClinicEmailSettings($clinic);

            // Prepare document for sharing
            $documentPath = $this->prepareDocumentForSharing($document, $encryptDocument);

            // Generate email content
            $subject = "Medical Document from {$clinic->name}";
            $content = $this->generateDocumentEmailContent($document, $consultation, $clinic);

            // Send to each email
            $successCount = 0;
            foreach ($emails as $email) {
                if ($this->sendDocumentEmail($email, $subject, $content, $documentPath, $emailSettings)) {
                    $successCount++;
                }
            }

            // Log the sharing activity
            Log::info('Document shared via email', [
                'document_id' => $document->id,
                'consultation_id' => $consultation->id,
                'emails_sent' => $successCount,
                'total_emails' => count($emails),
                'clinic_id' => $clinic->id,
            ]);

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('Failed to share document via email', [
                'document_id' => $document->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Share prescription via email.
     */
    public function sharePrescription(Prescription $prescription, array $emails, string $message = null): bool
    {
        try {
            $consultation = $prescription->consultation;
            $clinic = $prescription->clinic;

            // Get clinic email settings
            $emailSettings = $this->getClinicEmailSettings($clinic);

            // Generate prescription PDF
            $pdfPath = $this->generatePrescriptionPDF($prescription);

            // Generate email content
            $subject = "Prescription from {$clinic->name}";
            $content = $this->generatePrescriptionEmailContent($prescription, $consultation, $clinic, $message);

            // Send to each email
            $successCount = 0;
            foreach ($emails as $email) {
                if ($this->sendDocumentEmail($email, $subject, $content, $pdfPath, $emailSettings)) {
                    $successCount++;
                }
            }

            // Log the sharing activity
            Log::info('Prescription shared via email', [
                'prescription_id' => $prescription->id,
                'consultation_id' => $consultation->id,
                'emails_sent' => $successCount,
                'total_emails' => count($emails),
                'clinic_id' => $clinic->id,
            ]);

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('Failed to share prescription via email', [
                'prescription_id' => $prescription->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Share medical letter via email.
     */
    public function shareMedicalLetter(MedicalLetter $letter, array $emails): bool
    {
        try {
            $consultation = $letter->consultation;
            $clinic = $consultation->clinic;

            // Get clinic email settings
            $emailSettings = $this->getClinicEmailSettings($clinic);

            // Generate letter PDF
            $pdfPath = $this->generateMedicalLetterPDF($letter);

            // Generate email content
            $subject = "Medical Letter from {$clinic->name}";
            $content = $this->generateMedicalLetterEmailContent($letter, $consultation, $clinic);

            // Send to each email
            $successCount = 0;
            foreach ($emails as $email) {
                if ($this->sendDocumentEmail($email, $subject, $content, $pdfPath, $emailSettings)) {
                    $successCount++;
                }
            }

            // Log the sharing activity
            Log::info('Medical letter shared via email', [
                'letter_id' => $letter->id,
                'consultation_id' => $consultation->id,
                'emails_sent' => $successCount,
                'total_emails' => count($emails),
                'clinic_id' => $clinic->id,
            ]);

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('Failed to share medical letter via email', [
                'letter_id' => $letter->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Share complete consultation summary via email.
     */
    public function shareConsultationSummary(Consultation $consultation, array $emails, array $includeItems = []): bool
    {
        try {
            $clinic = $consultation->clinic;

            // Get clinic email settings
            $emailSettings = $this->getClinicEmailSettings($clinic);

            // Generate consultation summary PDF
            $pdfPath = $this->generateConsultationSummaryPDF($consultation, $includeItems);

            // Generate email content
            $subject = "Consultation Summary from {$clinic->name}";
            $content = $this->generateConsultationSummaryEmailContent($consultation, $clinic, $includeItems);

            // Send to each email
            $successCount = 0;
            foreach ($emails as $email) {
                if ($this->sendDocumentEmail($email, $subject, $content, $pdfPath, $emailSettings)) {
                    $successCount++;
                }
            }

            // Log the sharing activity
            Log::info('Consultation summary shared via email', [
                'consultation_id' => $consultation->id,
                'emails_sent' => $successCount,
                'total_emails' => count($emails),
                'include_items' => $includeItems,
                'clinic_id' => $clinic->id,
            ]);

            return $successCount > 0;
        } catch (\Exception $e) {
            Log::error('Failed to share consultation summary via email', [
                'consultation_id' => $consultation->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get clinic email settings.
     */
    private function getClinicEmailSettings(Clinic $clinic): array
    {
        $emailSettings = $clinic->emailSettings;
        
        if ($emailSettings && $emailSettings->details) {
            return [
                'sender_name' => $emailSettings->details['sender_name'] ?? $clinic->name,
                'sender_email' => $emailSettings->details['sender_email'] ?? config('mail.from.address'),
                'use_clinic_smtp' => true,
            ];
        }

        return [
            'sender_name' => $clinic->name,
            'sender_email' => config('mail.from.address'),
            'use_clinic_smtp' => false,
        ];
    }

    /**
     * Send document email using clinic settings.
     */
    private function sendDocumentEmail(string $email, string $subject, string $content, ?string $attachmentPath, array $emailSettings): bool
    {
        try {
            // Use Laravel Mail with proper attachment handling
            Mail::send([], [], function ($message) use ($email, $subject, $content, $attachmentPath, $emailSettings) {
                $message->to($email)
                    ->subject($subject)
                    ->from($emailSettings['sender_email'], $emailSettings['sender_name'])
                    ->html($content);

                // Attach the PDF if path is provided and file exists
                if ($attachmentPath && file_exists($attachmentPath)) {
                    $message->attach($attachmentPath, [
                        'as' => basename($attachmentPath),
                        'mime' => 'application/pdf',
                    ]);
                }
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send document email', [
                'email' => $email,
                'subject' => $subject,
                'attachment_path' => $attachmentPath,
                'attachment_exists' => $attachmentPath ? file_exists($attachmentPath) : false,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Prepare document for sharing (encryption, etc.).
     */
    private function prepareDocumentForSharing(ConsultationDocument $document, bool $encrypt = false): ?string
    {
        // For now, return the original document path
        // In the future, implement encryption logic here
        return $document->file_path;
    }

    /**
     * Generate prescription PDF.
     */
    private function generatePrescriptionPDF(Prescription $prescription): ?string
    {
        try {
            // Use DocumentGenerationService to generate the PDF
            $documentService = app(DocumentGenerationService::class);
            $result = $documentService->generatePrescriptionPDF($prescription);

            if ($result['success']) {
                return $result['file_path'];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to generate prescription PDF', [
                'prescription_id' => $prescription->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Generate medical letter PDF.
     */
    private function generateMedicalLetterPDF(MedicalLetter $letter): ?string
    {
        try {
            // Prepare letter data for PDF generation
            $letterData = [
                'letter_title' => $letter->title,
                'letter_content' => $letter->content,
                'patient_name' => $letter->consultation->patient->user->name ?? 'Patient',
                'provider_name' => $letter->consultation->provider->user->name ?? 'Provider',
                'clinic' => $letter->consultation->clinic,
                'consultation_id' => $letter->consultation_id,
                'letter_date' => $letter->created_at,
                'recipient_name' => $letter->recipient_name ?? '',
                'recipient_address' => $letter->recipient_address ?? '',
            ];

            // Use DocumentGenerationService to generate the PDF
            $documentService = app(DocumentGenerationService::class);
            $result = $documentService->generateMedicalLetterPDF($letterData);

            if ($result['success']) {
                return $result['file_path'];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to generate medical letter PDF', [
                'letter_id' => $letter->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Generate consultation summary PDF.
     */
    private function generateConsultationSummaryPDF(Consultation $consultation, array $includeItems): ?string
    {
        try {
            // Load consultation with all necessary relationships
            $consultation->load([
                'patient.user',
                'provider.user',
                'clinic',
                'prescriptions.items.medication',
                'diagnoses',
                'notes',
                'documents',
                'treatmentPlans',
                'vitals',
                'tabs'
            ]);

            // Use DocumentGenerationService to generate the PDF
            $documentService = app(DocumentGenerationService::class);
            $result = $documentService->generateConsultationSummaryPDF($consultation, $includeItems);

            return $result['file_path'] ?? null;
        } catch (\Exception $e) {
            Log::error('Failed to generate consultation summary PDF', [
                'consultation_id' => $consultation->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Generate document email content.
     */
    private function generateDocumentEmailContent(ConsultationDocument $document, Consultation $consultation, Clinic $clinic): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #2563eb; margin: 0;'>{$clinic->name}</h2>
                <p style='margin: 5px 0 0 0; color: #6b7280;'>Medical Document</p>
            </div>
            
            <div style='padding: 20px; background: white; border-radius: 8px; border: 1px solid #e5e7eb;'>
                <h3 style='color: #374151; margin-top: 0;'>Document Details</h3>
                
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280; width: 30%;'><strong>Document:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$document->title}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Patient:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->patient->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Provider:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->provider->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Date:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->consultation_date->format('d/m/Y')}</td>
                    </tr>
                </table>
                
                <div style='margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #3b82f6;'>
                    <p style='margin: 0; color: #1e40af; font-size: 14px;'>
                        <strong>Confidential Medical Information</strong><br>
                        This document contains confidential medical information. Please handle with appropriate care and in accordance with privacy regulations.
                    </p>
                </div>
            </div>
            
            <div style='margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 6px; text-align: center;'>
                <p style='margin: 0; color: #6b7280; font-size: 12px;'>
                    This document was shared from {$clinic->name}<br>
                    For questions, please contact: {$clinic->email}
                </p>
            </div>
        </div>";
    }

    /**
     * Generate prescription email content.
     */
    private function generatePrescriptionEmailContent(Prescription $prescription, Consultation $consultation, Clinic $clinic, ?string $message): string
    {
        $customMessage = $message ? "<p style='color: #374151; margin: 15px 0;'>{$message}</p>" : '';
        
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #059669; margin: 0;'>{$clinic->name}</h2>
                <p style='margin: 5px 0 0 0; color: #6b7280;'>Prescription</p>
            </div>
            
            <div style='padding: 20px; background: white; border-radius: 8px; border: 1px solid #e5e7eb;'>
                <h3 style='color: #374151; margin-top: 0;'>Prescription Details</h3>
                
                {$customMessage}
                
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280; width: 30%;'><strong>Patient:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->patient->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Prescribed by:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->provider->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Date:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$prescription->created_at->format('d/m/Y')}</td>
                    </tr>
                </table>
                
                <div style='margin-top: 20px; padding: 15px; background: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;'>
                    <p style='margin: 0; color: #92400e; font-size: 14px;'>
                        <strong>Important:</strong> Please follow the prescribed dosage and instructions. Consult your healthcare provider if you have any questions or concerns.
                    </p>
                </div>
            </div>
            
            <div style='margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 6px; text-align: center;'>
                <p style='margin: 0; color: #6b7280; font-size: 12px;'>
                    This prescription was issued by {$clinic->name}<br>
                    For questions, please contact: {$clinic->email}
                </p>
            </div>
        </div>";
    }

    /**
     * Generate medical letter email content.
     */
    private function generateMedicalLetterEmailContent(MedicalLetter $letter, Consultation $consultation, Clinic $clinic): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #7c3aed; margin: 0;'>{$clinic->name}</h2>
                <p style='margin: 5px 0 0 0; color: #6b7280;'>Medical Letter</p>
            </div>
            
            <div style='padding: 20px; background: white; border-radius: 8px; border: 1px solid #e5e7eb;'>
                <h3 style='color: #374151; margin-top: 0;'>Medical Letter</h3>
                
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280; width: 30%;'><strong>Subject:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$letter->subject}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Patient:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->patient->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Provider:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->provider->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Date:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$letter->created_at->format('d/m/Y')}</td>
                    </tr>
                </table>
                
                <div style='margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #3b82f6;'>
                    <p style='margin: 0; color: #1e40af; font-size: 14px;'>
                        <strong>Medical Correspondence</strong><br>
                        This letter contains medical information and recommendations from your healthcare provider.
                    </p>
                </div>
            </div>
            
            <div style='margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 6px; text-align: center;'>
                <p style='margin: 0; color: #6b7280; font-size: 12px;'>
                    This medical letter was issued by {$clinic->name}<br>
                    For questions, please contact: {$clinic->email}
                </p>
            </div>
        </div>";
    }

    /**
     * Generate consultation summary email content.
     */
    private function generateConsultationSummaryEmailContent(Consultation $consultation, Clinic $clinic, array $includeItems): string
    {
        $itemsList = '';
        if (!empty($includeItems)) {
            $itemsList = '<p style="color: #374151; margin: 10px 0;"><strong>Included:</strong> ' . implode(', ', $includeItems) . '</p>';
        }

        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #dc2626; margin: 0;'>{$clinic->name}</h2>
                <p style='margin: 5px 0 0 0; color: #6b7280;'>Consultation Summary</p>
            </div>
            
            <div style='padding: 20px; background: white; border-radius: 8px; border: 1px solid #e5e7eb;'>
                <h3 style='color: #374151; margin-top: 0;'>Consultation Summary</h3>
                
                {$itemsList}
                
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280; width: 30%;'><strong>Patient:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->patient->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Provider:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->provider->user->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Date:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>{$consultation->consultation_date->format('d/m/Y')}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6b7280;'><strong>Status:</strong></td>
                        <td style='padding: 8px 0; color: #374151;'>" . ucfirst($consultation->status) . "</td>
                    </tr>
                </table>
                
                <div style='margin-top: 20px; padding: 15px; background: #fef2f2; border-radius: 6px; border-left: 4px solid #ef4444;'>
                    <p style='margin: 0; color: #991b1b; font-size: 14px;'>
                        <strong>Comprehensive Medical Summary</strong><br>
                        This document contains a complete summary of the consultation including all relevant medical information.
                    </p>
                </div>
            </div>
            
            <div style='margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 6px; text-align: center;'>
                <p style='margin: 0; color: #6b7280; font-size: 12px;'>
                    This consultation summary was generated by {$clinic->name}<br>
                    For questions, please contact: {$clinic->email}
                </p>
            </div>
        </div>";
    }
}
