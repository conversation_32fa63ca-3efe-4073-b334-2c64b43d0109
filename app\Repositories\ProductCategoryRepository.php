<?php

namespace App\Repositories;

use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductCategoryRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param ProductCategory $model
     * @return void
     */
    public function __construct(ProductCategory $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find active categories.
     *
     * @return Collection
     */
    public function findActive(): Collection
    {
        return $this->findBy(['is_active' => true]);
    }
    
    /**
     * Find root categories (no parent).
     *
     * @return Collection
     */
    public function findRootCategories(): Collection
    {
        $query = $this->model->newQuery();
        $query->whereNull('parent_id')
              ->where('is_active', true)
              ->orderBy('sort_order')
              ->orderBy('name');
        
        return $query->get();
    }
    
    /**
     * Find categories by parent.
     *
     * @param int $parentId
     * @return Collection
     */
    public function findByParent(int $parentId): Collection
    {
        return $this->findBy(['parent_id' => $parentId]);
    }
    
    /**
     * Find category by slug.
     *
     * @param string $slug
     * @return ProductCategory|null
     */
    public function findBySlug(string $slug): ?ProductCategory
    {
        return $this->findOneBy(['slug' => $slug]);
    }
    
    /**
     * Find categories with product count.
     *
     * @return Collection
     */
    public function findWithProductCount(): Collection
    {
        $query = $this->model->newQuery();
        $query->active()
              ->withCount(['products' => function ($q) {
                  $q->where('is_active', true)
                    ->where('approval_status', 'approved');
              }])
              ->ordered();
        
        return $query->get();
    }
    
    /**
     * Search categories by name.
     *
     * @param string $search
     * @return Collection
     */
    public function search(string $search): Collection
    {
        $query = $this->model->newQuery();
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
        
        return $query->get();
    }
    
    /**
     * Get categories with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['parent', 'children']);
        
        // Apply filters
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }
        
        if (!empty($filters['parent_id'])) {
            $query->where('parent_id', $filters['parent_id']);
        } elseif (isset($filters['root_only']) && $filters['root_only']) {
            $query->whereNull('parent_id');
        }
        
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        return $query->orderBy('sort_order')->orderBy('name')->paginate($perPage);
    }
    
    /**
     * Get category hierarchy.
     *
     * @return Collection
     */
    public function getCategoryHierarchy(): Collection
    {
        $query = $this->model->newQuery();
        $query->with(['children' => function ($q) {
            $q->where('is_active', true)
              ->orderBy('sort_order')
              ->orderBy('name');
        }])
        ->whereNull('parent_id')
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->orderBy('name');
        
        return $query->get();
    }
    
    /**
     * Get next sort order for a parent category.
     *
     * @param int|null $parentId
     * @return int
     */
    public function getNextSortOrder(?int $parentId = null): int
    {
        $query = $this->model->newQuery();
        
        if ($parentId) {
            $query->where('parent_id', $parentId);
        } else {
            $query->whereNull('parent_id');
        }
        
        $maxOrder = $query->max('sort_order');
        
        return ($maxOrder ?? 0) + 1;
    }
    
    /**
     * Update sort orders.
     *
     * @param array $sortData
     * @return bool
     */
    public function updateSortOrders(array $sortData): bool
    {
        try {
            foreach ($sortData as $item) {
                $this->update($item['id'], ['sort_order' => $item['sort_order']]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
