<?php

namespace App\Repositories;

use App\Models\Bill;
use App\Repositories\Interfaces\BillingRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class BillingRepository implements BillingRepositoryInterface
{
    public function getBillsWithDetailsForClinic(int $clinicId, int $perPage = 20, array $params = []): LengthAwarePaginator
    {
        $query = Bill::with([
                'patient.user:id,name,email',
                'provider.user:id,name',
                'clinic:id,name',
                'billItems:id,bill_id,item_name,quantity,unit_price,total_price',
                'consultation:id,consultation_date',
                'appointment:id,date,time_slot',
                'payments:id,bill_id,amount'
            ])
            ->where('clinic_id', $clinicId);

        // Search functionality
        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->where(function ($q) use ($search) {
                $q->where('bill_number', 'like', "%{$search}%")
                  ->orWhereHas('patient.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('provider.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Doctor filter
        if (!empty($params['doctor'])) {
            $query->where('provider_id', $params['doctor']);
        }

        // Status filter
        if (!empty($params['status'])) {
            $query->where('payment_status', $params['status']);
        }

        // Date range filter
        if (!empty($params['date_from'])) {
            $query->whereDate('bill_date', '>=', $params['date_from']);
        }
        if (!empty($params['date_to'])) {
            $query->whereDate('bill_date', '<=', $params['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getBillWithFullDetails(int $billId): ?object
    {
        return Bill::with([
                'patient.user:id,name,email',
                'provider.user:id,name',
                'billItems.service:id,name',
                'consultation:id,consultation_date,notes',
                'appointment:id,date,time_slot,reason',
                'clinic:id,name,address,phone,email'
            ])
            ->find($billId);
    }

    public function getBillingReportData(int $clinicId, array $filters = []): Collection
    {
        $query = DB::table('bills')
            ->join('patients', 'bills.patient_id', '=', 'patients.id')
            ->join('users as patient_users', 'patients.user_id', '=', 'patient_users.id')
            ->join('providers', 'bills.provider_id', '=', 'providers.id')
            ->join('users as provider_users', 'providers.user_id', '=', 'provider_users.id')
            ->leftJoin('bill_items', 'bills.id', '=', 'bill_items.bill_id')
            ->where('bills.clinic_id', $clinicId)
            ->select([
                'bills.*',
                'patient_users.name as patient_name',
                'patient_users.email as patient_email',
                'provider_users.name as provider_name',
                DB::raw('COUNT(bill_items.id) as item_count'),
                DB::raw('SUM(bill_items.total_price) as items_total')
            ])
            ->groupBy('bills.id');

        // Apply filters
        if (!empty($filters['date_from'])) {
            $query->where('bills.bill_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->where('bills.bill_date', '<=', $filters['date_to']);
        }
        
        if (!empty($filters['payment_status'])) {
            $query->where('bills.payment_status', $filters['payment_status']);
        }
        
        if (!empty($filters['provider_id'])) {
            $query->where('bills.provider_id', $filters['provider_id']);
        }

        return $query->orderBy('bills.created_at', 'desc')->get();
    }

    public function getUnpaidBillsWithPatientDetails(int $clinicId): Collection
    {
        return Bill::with([
                'patient.user:id,name,email,phone',
                'provider.user:id,name'
            ])
            ->where('clinic_id', $clinicId)
            ->where('payment_status', '!=', 'paid')
            ->orderBy('due_date', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getBillsByPatientWithDetails(int $patientId): Collection
    {
        return Bill::with([
                'provider.user:id,name',
                'billItems:id,bill_id,item_name,quantity,unit_price,total_price',
                'consultation:id,consultation_date',
                'appointment:id,date,time_slot'
            ])
            ->where('patient_id', $patientId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getBillsByProviderWithDetails(int $providerId): Collection
    {
        return Bill::with([
                'patient.user:id,name,email',
                'billItems:id,bill_id,item_name,quantity,unit_price,total_price'
            ])
            ->where('provider_id', $providerId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getBillingStatsForClinic(int $clinicId): array
    {
        $stats = DB::table('bills')
            ->where('clinic_id', $clinicId)
            ->selectRaw('
                COUNT(*) as total_bills,
                SUM(CASE WHEN payment_status = "paid" THEN 1 ELSE 0 END) as paid_bills,
                SUM(CASE WHEN payment_status = "unpaid" THEN 1 ELSE 0 END) as unpaid_bills,
                SUM(CASE WHEN payment_status = "sent_to_patient" THEN 1 ELSE 0 END) as sent_bills,
                SUM(total_amount) as total_revenue,
                SUM(CASE WHEN payment_status = "paid" THEN total_amount ELSE 0 END) as paid_revenue,
                SUM(CASE WHEN payment_status != "paid" THEN total_amount ELSE 0 END) as outstanding_revenue,
                AVG(total_amount) as average_bill_amount
            ')
            ->first();

        return [
            'total_bills' => $stats->total_bills ?? 0,
            'paid_bills' => $stats->paid_bills ?? 0,
            'unpaid_bills' => $stats->unpaid_bills ?? 0,
            'sent_bills' => $stats->sent_bills ?? 0,
            'total_revenue' => round($stats->total_revenue ?? 0, 2),
            'paid_revenue' => round($stats->paid_revenue ?? 0, 2),
            'outstanding_revenue' => round($stats->outstanding_revenue ?? 0, 2),
            'average_bill_amount' => round($stats->average_bill_amount ?? 0, 2),
        ];
    }
}
