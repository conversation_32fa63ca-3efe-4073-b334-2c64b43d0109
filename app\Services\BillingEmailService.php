<?php

namespace App\Services;

use App\Mail\BillInvoiceNotification;
use App\Mail\PaymentConfirmationNotification;
use App\Models\Bill;
use App\Models\Payment;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class BillingEmailService
{
    public function __construct(
        private InvoiceService $invoiceService
    ) {}

    /**
     * Send invoice email to patient.
     */
    public function sendInvoiceEmail(Bill $bill, bool $attachPdf = true): bool
    {
        try {
            // Generate payment link
            $paymentLink = $this->generatePaymentLink($bill);

            // Generate invoice PDF if requested
            $invoicePath = null;
            if ($attachPdf) {
                $invoicePath = $this->invoiceService->generateInvoicePdf($bill);
            }

            // Send email
            Mail::to($bill->patient->user->email)
                ->send(new BillInvoiceNotification($bill, $paymentLink, $invoicePath));

            // Update bill status
            $bill->update([
                'status' => 'sent',
                'payment_status' => 'sent_to_patient',
                'email_sent_at' => now(),
            ]);

            // Log the email sending
            Log::info('Invoice email sent', [
                'bill_id' => $bill->id,
                'bill_number' => $bill->bill_number,
                'patient_email' => $bill->patient->user->email,
                'clinic_id' => $bill->clinic_id,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send invoice email', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage(),
                'patient_email' => $bill->patient->user->email,
            ]);

            return false;
        }
    }

    /**
     * Send payment confirmation email.
     */
    public function sendPaymentConfirmationEmail(Bill $bill, Payment $payment, bool $attachReceipt = true): bool
    {
        try {
            // Generate receipt PDF if requested
            $receiptPath = null;
            if ($attachReceipt) {
                $receiptPath = $this->invoiceService->generateReceiptPdf($bill);
            }

            // Send email
            Mail::to($bill->patient->user->email)
                ->send(new PaymentConfirmationNotification($bill, $payment, $receiptPath));

            // Log the email sending
            Log::info('Payment confirmation email sent', [
                'bill_id' => $bill->id,
                'payment_id' => $payment->id,
                'bill_number' => $bill->bill_number,
                'patient_email' => $bill->patient->user->email,
                'payment_amount' => $payment->amount,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation email', [
                'bill_id' => $bill->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'patient_email' => $bill->patient->user->email,
            ]);

            return false;
        }
    }

    /**
     * Send bill reminder email.
     */
    public function sendBillReminderEmail(Bill $bill): bool
    {
        try {
            // Only send reminders for unpaid bills
            if ($bill->isPaid()) {
                return false;
            }

            // Generate payment link
            $paymentLink = $this->generatePaymentLink($bill);

            // Send reminder email (reuse invoice template with reminder context)
            Mail::to($bill->patient->user->email)
                ->send(new BillInvoiceNotification($bill, $paymentLink));

            // Log the reminder
            Log::info('Bill reminder email sent', [
                'bill_id' => $bill->id,
                'bill_number' => $bill->bill_number,
                'patient_email' => $bill->patient->user->email,
                'days_overdue' => $bill->due_date ? now()->diffInDays($bill->due_date, false) : null,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send bill reminder email', [
                'bill_id' => $bill->id,
                'error' => $e->getMessage(),
                'patient_email' => $bill->patient->user->email,
            ]);

            return false;
        }
    }

    /**
     * Send bulk invoice emails.
     */
    public function sendBulkInvoiceEmails(array $billIds): array
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($billIds as $billId) {
            try {
                $bill = Bill::with(['patient.user', 'clinic'])->find($billId);
                
                if (!$bill) {
                    $results['failed']++;
                    $results['errors'][] = "Bill ID {$billId} not found";
                    continue;
                }

                if ($this->sendInvoiceEmail($bill)) {
                    $results['sent']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Failed to send email for bill {$bill->bill_number}";
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Error processing bill ID {$billId}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Send overdue bill notifications.
     */
    public function sendOverdueBillNotifications(int $clinicId): array
    {
        $overdueBills = Bill::where('clinic_id', $clinicId)
            ->where('payment_status', '!=', 'paid')
            ->where('due_date', '<', now())
            ->with(['patient.user', 'clinic'])
            ->get();

        $results = [
            'sent' => 0,
            'failed' => 0,
            'total_overdue' => $overdueBills->count(),
        ];

        foreach ($overdueBills as $bill) {
            if ($this->sendBillReminderEmail($bill)) {
                $results['sent']++;
            } else {
                $results['failed']++;
            }
        }

        return $results;
    }

    /**
     * Generate secure payment link for bill.
     */
    private function generatePaymentLink(Bill $bill): string
    {
        $token = encrypt([
            'bill_id' => $bill->id,
            'patient_id' => $bill->patient_id,
            'expires_at' => now()->addDays(30)->timestamp
        ]);

        return route('bills.payment.page', [
            'bill' => $bill->id,
            'token' => $token
        ]);
    }

    /**
     * Check if email can be sent to patient.
     */
    public function canSendEmail(Bill $bill): bool
    {
        // Check if patient has email
        if (!$bill->patient->user->email) {
            return false;
        }

        // Check if email is valid
        if (!filter_var($bill->patient->user->email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // Check if patient has opted out of emails (if you have this feature)
        // if ($bill->patient->email_opt_out) {
        //     return false;
        // }

        return true;
    }

    /**
     * Get email sending statistics for clinic.
     */
    public function getEmailStats(int $clinicId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalBills = Bill::where('clinic_id', $clinicId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $emailsSent = Bill::where('clinic_id', $clinicId)
            ->where('email_sent_at', '>=', $startDate)
            ->count();

        $paidAfterEmail = Bill::where('clinic_id', $clinicId)
            ->where('email_sent_at', '>=', $startDate)
            ->where('payment_status', 'paid')
            ->whereColumn('paid_at', '>', 'email_sent_at')
            ->count();

        return [
            'total_bills' => $totalBills,
            'emails_sent' => $emailsSent,
            'email_rate' => $totalBills > 0 ? round(($emailsSent / $totalBills) * 100, 2) : 0,
            'paid_after_email' => $paidAfterEmail,
            'conversion_rate' => $emailsSent > 0 ? round(($paidAfterEmail / $emailsSent) * 100, 2) : 0,
        ];
    }
}
