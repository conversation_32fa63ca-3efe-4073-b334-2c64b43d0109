<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Medication extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'brand_name',
        'active_ingredient',
        'strength',
        'form',
        'route',
        'description',
        'indications',
        'contraindications',
        'side_effects',
        'interactions',
        'dosage_guidelines',
        'drug_class',
        'controlled_substance_schedule',
        'requires_prescription',
        'is_active',
        'regulatory_code',
    ];

    protected $casts = [
        'indications' => 'array',
        'contraindications' => 'array',
        'side_effects' => 'array',
        'interactions' => 'array',
        'requires_prescription' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the prescription items for this medication.
     */
    public function prescriptionItems()
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    /**
     * Scope to get active medications.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search by name or brand name.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('brand_name', 'like', "%{$term}%")
              ->orWhere('active_ingredient', 'like', "%{$term}%");
        });
    }

    /**
     * Scope to filter by drug class.
     */
    public function scopeByDrugClass($query, $drugClass)
    {
        return $query->where('drug_class', $drugClass);
    }

    /**
     * Scope to filter by form.
     */
    public function scopeByForm($query, $form)
    {
        return $query->where('form', $form);
    }

    /**
     * Scope to filter by route.
     */
    public function scopeByRoute($query, $route)
    {
        return $query->where('route', $route);
    }

    /**
     * Scope to get controlled substances.
     */
    public function scopeControlledSubstances($query)
    {
        return $query->whereNotNull('controlled_substance_schedule');
    }

    /**
     * Check if medication is a controlled substance.
     */
    public function isControlledSubstance()
    {
        return !empty($this->controlled_substance_schedule);
    }

    /**
     * Check if medication requires prescription.
     */
    public function requiresPrescription()
    {
        return $this->requires_prescription;
    }

    /**
     * Get the display name (brand name if available, otherwise generic name).
     */
    public function getDisplayNameAttribute()
    {
        return $this->brand_name ?: $this->name;
    }

    /**
     * Get the full medication name with strength.
     */
    public function getFullNameAttribute()
    {
        $name = $this->display_name;
        if ($this->strength) {
            $name .= ' ' . $this->strength;
        }
        return $name;
    }

    /**
     * Get the form display name.
     */
    public function getFormDisplayAttribute()
    {
        $forms = [
            'tablet' => 'Tablet',
            'capsule' => 'Capsule',
            'liquid' => 'Liquid',
            'injection' => 'Injection',
            'cream' => 'Cream',
            'ointment' => 'Ointment',
            'drops' => 'Drops',
            'inhaler' => 'Inhaler',
            'patch' => 'Patch',
            'suppository' => 'Suppository',
        ];

        return $forms[$this->form] ?? ucfirst($this->form);
    }

    /**
     * Get the route display name.
     */
    public function getRouteDisplayAttribute()
    {
        $routes = [
            'oral' => 'Oral',
            'topical' => 'Topical',
            'injection' => 'Injection',
            'inhalation' => 'Inhalation',
            'rectal' => 'Rectal',
            'vaginal' => 'Vaginal',
            'sublingual' => 'Sublingual',
            'transdermal' => 'Transdermal',
        ];

        return $routes[$this->route] ?? ucfirst($this->route);
    }

    /**
     * Get the controlled substance schedule display.
     */
    public function getScheduleDisplayAttribute()
    {
        if (!$this->controlled_substance_schedule) {
            return null;
        }

        $schedules = [
            'I' => 'Schedule I',
            'II' => 'Schedule II',
            'III' => 'Schedule III',
            'IV' => 'Schedule IV',
            'V' => 'Schedule V',
        ];

        return $schedules[$this->controlled_substance_schedule] ?? 'Schedule ' . $this->controlled_substance_schedule;
    }

    /**
     * Check if medication has interactions with other medications.
     */
    public function hasInteractionsWith($medicationIds)
    {
        if (empty($this->interactions) || empty($medicationIds)) {
            return false;
        }

        // This is a simplified check - in a real system, you'd have a more sophisticated
        // drug interaction database
        $interactionMedications = collect($this->interactions)->pluck('medication_id');
        return $interactionMedications->intersect($medicationIds)->isNotEmpty();
    }
}
