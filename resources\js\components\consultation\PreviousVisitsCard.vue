<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Header -->
    <div class="bg-orange-50 px-4 py-3 border-b border-orange-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-7 h-7 bg-orange-500 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M8 2v4"/>
              <path d="M16 2v4"/>
              <rect width="18" height="18" x="3" y="4" rx="2"/>
              <path d="M3 10h18"/>
            </svg>
          </div>
          <h3 class="font-medium text-gray-900">Recent Consultations</h3>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4">
      <div v-if="loading" class="flex items-center justify-center py-6">
        <svg class="w-5 h-5 animate-spin text-gray-400" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <div v-else-if="visits.length" class="space-y-2">
        <div
          v-for="visit in visits"
          :key="visit.id"
          class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer transition-colors"
          @click="viewVisit(visit.id)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-2">
                <h4 class="font-medium text-sm text-gray-900">
                  {{ visit.consultation_type || 'General Visit' }}
                </h4>
                <span :class="getStatusClass(visit.status)" class="px-2 py-0.5 rounded-full text-xs font-medium">
                  {{ formatStatus(visit.status) }}
                </span>
              </div>

              <p class="text-xs text-gray-600 mt-1">
                {{ formatDate(visit.consultation_date) }}
              </p>

              <div v-if="visit.chief_complaint" class="mt-2">
                <p class="text-xs text-gray-500 line-clamp-2">
                  {{ visit.chief_complaint }}
                </p>
              </div>

              <div class="flex items-center justify-between mt-2">
                <p class="text-xs text-gray-500">
                  Dr. {{ visit.provider?.user?.name || 'Unknown' }}
                </p>
                <div class="flex items-center space-x-1">
                  <svg v-if="visit.documents_count" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                    <polyline points="14 2 14 8 20 8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                  </svg>
                  <svg v-if="visit.prescriptions_count" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                    <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
                  </svg>
                </div>
              </div>
            </div>

            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400 ml-2">
              <path d="m9 18 6-6-6-6"/>
            </svg>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-300">
          <path d="M8 2v4"/>
          <path d="M16 2v4"/>
          <rect width="18" height="18" x="3" y="4" rx="2"/>
          <path d="M3 10h18"/>
        </svg>
        <p class="text-sm">No completed consultations</p>
        <p class="text-xs text-gray-400 mt-1">
          No completed consultation history found for this patient
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { router } from '@inertiajs/vue3'
interface Visit {
  id: number
  consultation_type: string
  status: string
  consultation_date: string
  chief_complaint?: string
  provider?: {
    user?: {
      name: string
    }
  }
  documents_count?: number
  prescriptions_count?: number
}

interface Props {
  patientId?: string | number
}

const props = defineProps<Props>()

// Component state
const visits = ref<Visit[]>([])
const loading = ref(false)

// Methods
const loadPreviousVisits = async () => {
  if (!props.patientId) return

  try {
    loading.value = true

    // Load last 3 completed consultations from API
    const response = await fetch(`/patients/${props.patientId}/consultations?status=completed&limit=3`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    visits.value = data.data || []
  } catch (error) {
    console.error('Error loading previous visits:', error)
    visits.value = [] // Set empty array on error
  } finally {
    loading.value = false
  }
}

const viewVisit = (visitId: number) => {
  router.visit(`/consultation-dashboard/${props.patientId}?consultation_id=${visitId}`)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-700'
    case 'in_progress':
      return 'bg-blue-100 text-blue-700'
    case 'cancelled':
      return 'bg-red-100 text-red-700'
    default:
      return 'bg-gray-100 text-gray-700'
  }
}

// Watch for patient changes
watch(() => props.patientId, () => {
  if (props.patientId) {
    loadPreviousVisits()
  }
}, { immediate: true })

// Initialize
onMounted(() => {
  loadPreviousVisits()
})
</script>
