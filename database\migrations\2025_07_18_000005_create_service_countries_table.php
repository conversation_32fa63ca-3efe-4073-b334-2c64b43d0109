<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_countries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->string('country_code', 2);
            $table->decimal('price_override', 10, 2)->nullable(); // Country-specific pricing
            $table->boolean('is_available')->default(true);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('country_code')->references('code')->on('countries')->onDelete('cascade');
            
            // Unique constraint to prevent duplicates
            $table->unique(['service_id', 'country_code']);
            
            // Indexes
            $table->index(['service_id', 'is_available']);
            $table->index(['country_code', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_countries');
    }
};
