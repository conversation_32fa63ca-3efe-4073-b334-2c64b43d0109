<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-teal-600 to-teal-700 rounded-lg p-6 text-white">
      <h1 class="text-2xl font-bold mb-2">Welcome back, {{ doctorName }}</h1>
      <p class="text-teal-100">Here's your clinical overview for today</p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Today's Consultations</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.todayConsultations }}</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
              <Stethoscope class="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div class="mt-4 flex items-center text-sm">
            <TrendingUp class="w-4 h-4 text-green-500 mr-1" />
            <span class="text-green-600">{{ stats.consultationGrowth }}% from yesterday</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Active Prescriptions</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.activePrescriptions }}</p>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
              <Pill class="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div class="mt-4 flex items-center text-sm">
            <Clock class="w-4 h-4 text-gray-500 mr-1" />
            <span class="text-gray-600">{{ stats.prescriptionsThisWeek }} this week</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Pending Refills</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.pendingRefills }}</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-full">
              <RefreshCw class="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <div class="mt-4">
            <Button
              variant="outline"
              size="sm"
              @click="$router.push('/prescription-refills')"
              v-if="stats.pendingRefills > 0"
            >
              Review Requests
            </Button>
            <span v-else class="text-sm text-gray-500">All caught up!</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Letters Generated</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.lettersGenerated }}</p>
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
              <FileText class="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div class="mt-4 flex items-center text-sm">
            <Calendar class="w-4 h-4 text-gray-500 mr-1" />
            <span class="text-gray-600">{{ stats.lettersThisMonth }} this month</span>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Quick Actions -->
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            @click="$router.push('/consultations/create')"
            class="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <Plus class="w-6 h-6" />
            <span>New Consultation</span>
          </Button>
          
          <Button
            @click="$router.push('/prescriptions/create')"
            variant="outline"
            class="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <Pill class="w-6 h-6" />
            <span>New Prescription</span>
          </Button>
          
          <Button
            @click="$router.push('/medical-letters/create')"
            variant="outline"
            class="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <FileText class="w-6 h-6" />
            <span>Generate Letter</span>
          </Button>
          
          <Button
            @click="$router.push('/doctor-settings')"
            variant="outline"
            class="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <Settings class="w-6 h-6" />
            <span>Settings</span>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Consultations -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Recent Consultations</CardTitle>
            <Button variant="ghost" size="sm" @click="$router.push('/consultations')">
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="loadingConsultations" class="text-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600 mx-auto"></div>
          </div>
          
          <div v-else-if="recentConsultations.length === 0" class="text-center py-4 text-gray-500">
            No recent consultations
          </div>
          
          <div v-else class="space-y-3">
            <div
              v-for="consultation in recentConsultations"
              :key="consultation.id"
              class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="$router.push(`/consultations/${consultation.id}`)"
            >
              <div>
                <p class="font-medium">{{ consultation.patient?.user?.name }}</p>
                <p class="text-sm text-gray-500">{{ consultation.chief_complaint }}</p>
              </div>
              <div class="text-right">
                <Badge :variant="getStatusVariant(consultation.status)">
                  {{ formatStatus(consultation.status) }}
                </Badge>
                <p class="text-xs text-gray-500 mt-1">
                  {{ formatDate(consultation.consultation_date) }}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Recent Prescriptions -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Recent Prescriptions</CardTitle>
            <Button variant="ghost" size="sm" @click="$router.push('/prescriptions')">
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="loadingPrescriptions" class="text-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600 mx-auto"></div>
          </div>
          
          <div v-else-if="recentPrescriptions.length === 0" class="text-center py-4 text-gray-500">
            No recent prescriptions
          </div>
          
          <div v-else class="space-y-3">
            <div
              v-for="prescription in recentPrescriptions"
              :key="prescription.id"
              class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="$router.push(`/prescriptions/${prescription.id}`)"
            >
              <div>
                <p class="font-medium">{{ prescription.patient?.user?.name }}</p>
                <p class="text-sm text-gray-500">{{ prescription.total_items }} item(s)</p>
              </div>
              <div class="text-right">
                <Badge :variant="getPrescriptionStatusVariant(prescription.status)">
                  {{ formatStatus(prescription.status) }}
                </Badge>
                <p class="text-xs text-gray-500 mt-1">
                  {{ formatDate(prescription.prescribed_date) }}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useConsultationApi } from '@/composables/useConsultationApi'
import { usePrescriptionApi } from '@/composables/usePrescriptionApi'
import { useDoctorSettingsApi } from '@/composables/useDoctorSettingsApi'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Stethoscope, Pill, RefreshCw, FileText, Plus, Settings,
  TrendingUp, Clock, Calendar
} from 'lucide-vue-next'

const router = useRouter()
const { getConsultations, consultations } = useConsultationApi()
const { getPrescriptions, prescriptions, getPendingRefillCount } = usePrescriptionApi()
const { getFullProfessionalName } = useDoctorSettingsApi()

const loadingConsultations = ref(false)
const loadingPrescriptions = ref(false)
const recentConsultations = ref([])
const recentPrescriptions = ref([])
const doctorName = ref('Doctor')

const stats = reactive({
  todayConsultations: 0,
  consultationGrowth: 0,
  activePrescriptions: 0,
  prescriptionsThisWeek: 0,
  pendingRefills: 0,
  lettersGenerated: 0,
  lettersThisMonth: 0
})

const loadDashboardData = async () => {
  try {
    // Load recent consultations (only completed ones)
    loadingConsultations.value = true
    await getConsultations({ per_page: 5, status: 'completed' })
    recentConsultations.value = consultations.value.slice(0, 5)
    
    // Load recent prescriptions
    loadingPrescriptions.value = true
    await getPrescriptions({ per_page: 5 })
    recentPrescriptions.value = prescriptions.value.slice(0, 5)
    
    // Load pending refill count
    const refillResponse = await getPendingRefillCount()
    if (refillResponse?.data) {
      stats.pendingRefills = refillResponse.data.pending_count || 0
    }
    
    // Calculate stats (simplified for demo)
    const today = new Date().toISOString().split('T')[0]
    stats.todayConsultations = consultations.value.filter(c => 
      c.consultation_date.startsWith(today)
    ).length
    
    stats.activePrescriptions = prescriptions.value.filter(p => 
      p.status === 'active'
    ).length
    
    // Set doctor name
    doctorName.value = getFullProfessionalName() || 'Doctor'
    
  } catch (err) {
    console.error('Error loading dashboard data:', err)
  } finally {
    loadingConsultations.value = false
    loadingPrescriptions.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatStatus = (status: string) => {
  return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed': return 'default'
    case 'in_progress': return 'secondary'
    case 'draft': return 'outline'
    case 'cancelled': return 'destructive'
    default: return 'outline'
  }
}

const getPrescriptionStatusVariant = (status: string) => {
  switch (status) {
    case 'active': return 'default'
    case 'dispensed': return 'secondary'
    case 'completed': return 'outline'
    case 'draft': return 'outline'
    case 'cancelled': return 'destructive'
    case 'expired': return 'destructive'
    default: return 'outline'
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>
