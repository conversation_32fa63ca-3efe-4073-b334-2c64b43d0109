import { ref, computed } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'
import { useCountryManager } from './useCountryManager'

// Global Razorpay state
const isRazorpayLoaded = ref(false)
const isLoading = ref(false)
const error = ref(null)

export function useRazorpay() {
    const page = usePage()
    const { getEffectiveCountryCode, effectiveCountry, isTestingMode } = useCountryManager()

    // Get current user
    const currentUser = computed(() => page.props.auth?.user || null)
    const isIndianUser = computed(() => getEffectiveCountryCode() === 'IN')
    
    // Load Razorpay script
    const loadRazorpayScript = () => {
        return new Promise((resolve, reject) => {
            if (isRazorpayLoaded.value) {
                resolve(window.Razorpay)
                return
            }
            
            const script = document.createElement('script')
            script.src = 'https://checkout.razorpay.com/v1/checkout.js'
            script.onload = () => {
                isRazorpayLoaded.value = true
                resolve(window.Razorpay)
            }
            script.onerror = () => {
                reject(new Error('Failed to load Razorpay script'))
            }
            document.head.appendChild(script)
        })
    }
    
    // Create Razorpay order
    const createOrder = async (orderData) => {
        try {
            isLoading.value = true
            error.value = null
            
            const response = await axios.post('/api/razorpay/create-order', orderData)
            
            if (response.data.success) {
                return response.data
            } else {
                throw new Error(response.data.message || 'Failed to create order')
            }
        } catch (err) {
            error.value = err.response?.data?.message || err.message || 'Failed to create order'
            throw err
        } finally {
            isLoading.value = false
        }
    }
    
    // Verify payment
    const verifyPayment = async (paymentData) => {
        try {
            isLoading.value = true
            error.value = null
            
            const response = await axios.post('/api/razorpay/verify-payment', paymentData)
            
            if (response.data.success) {
                return response.data
            } else {
                throw new Error(response.data.message || 'Payment verification failed')
            }
        } catch (err) {
            error.value = err.response?.data?.message || err.message || 'Payment verification failed'
            throw err
        } finally {
            isLoading.value = false
        }
    }
    
    // Open Razorpay checkout
    const openCheckout = async (checkoutOptions, callbacks = {}) => {
        try {
            // Ensure Razorpay script is loaded
            const Razorpay = await loadRazorpayScript()
            
            const options = {
                ...checkoutOptions,
                handler: async (response) => {
                    try {
                        // Verify payment on success
                        const verificationResult = await verifyPayment({
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_signature: response.razorpay_signature,
                        })
                        
                        if (callbacks.onSuccess) {
                            callbacks.onSuccess(verificationResult)
                        }
                    } catch (err) {
                        console.error('Payment verification failed:', err)
                        if (callbacks.onError) {
                            callbacks.onError(err)
                        }
                    }
                },
                modal: {
                    ondismiss: () => {
                        if (callbacks.onDismiss) {
                            callbacks.onDismiss()
                        }
                    },
                    ...checkoutOptions.modal
                }
            }
            
            const rzp = new Razorpay(options)
            
            rzp.on('payment.failed', (response) => {
                console.error('Payment failed:', response.error)
                if (callbacks.onError) {
                    callbacks.onError(new Error(response.error.description || 'Payment failed'))
                }
            })
            
            rzp.open()
            
        } catch (err) {
            console.error('Failed to open Razorpay checkout:', err)
            if (callbacks.onError) {
                callbacks.onError(err)
            }
        }
    }
    
    // Get payment methods
    const getPaymentMethods = async () => {
        try {
            const response = await axios.get('/api/razorpay/payment-methods')
            return response.data
        } catch (err) {
            console.error('Failed to get payment methods:', err)
            throw err
        }
    }
    
    // Create refund
    const createRefund = async (refundData) => {
        try {
            isLoading.value = true
            error.value = null
            
            const response = await axios.post('/api/razorpay/refund', refundData)
            
            if (response.data.success) {
                return response.data
            } else {
                throw new Error(response.data.message || 'Failed to create refund')
            }
        } catch (err) {
            error.value = err.response?.data?.message || err.message || 'Failed to create refund'
            throw err
        } finally {
            isLoading.value = false
        }
    }
    
    // Process payment (complete flow)
    const processPayment = async (paymentData, callbacks = {}) => {
        try {
            if (!isIndianUser.value) {
                throw new Error('Razorpay is only available for Indian customers')
            }
            
            // Create order
            const orderResult = await createOrder(paymentData)
            
            // Open checkout
            await openCheckout(orderResult.checkout_options, {
                onSuccess: (verificationResult) => {
                    if (callbacks.onSuccess) {
                        callbacks.onSuccess({
                            order: orderResult.order,
                            payment: verificationResult.payment,
                            verification: verificationResult
                        })
                    }
                },
                onError: (err) => {
                    if (callbacks.onError) {
                        callbacks.onError(err)
                    }
                },
                onDismiss: () => {
                    if (callbacks.onDismiss) {
                        callbacks.onDismiss()
                    }
                }
            })
            
        } catch (err) {
            console.error('Payment processing failed:', err)
            if (callbacks.onError) {
                callbacks.onError(err)
            }
        }
    }
    
    // Format amount for display
    const formatAmount = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount)
    }
    
    // Get test payment credentials for development
    const getTestCredentials = () => {
        return {
            test_cards: [
                {
                    number: '****************',
                    name: 'Test Card',
                    expiry: '12/25',
                    cvv: '123',
                    type: 'Visa'
                },
                {
                    number: '****************',
                    name: 'Test Card',
                    expiry: '12/25',
                    cvv: '123',
                    type: 'Mastercard'
                }
            ],
            test_upi: [
                'success@razorpay',
                'failure@razorpay'
            ],
            test_netbanking: 'Any bank can be selected for testing'
        }
    }
    
    // Check if Razorpay is available for user
    const isAvailable = computed(() => {
        return isIndianUser.value
    })
    
    return {
        // State
        isRazorpayLoaded,
        isLoading,
        error,
        isAvailable,
        isIndianUser,
        
        // Methods
        loadRazorpayScript,
        createOrder,
        verifyPayment,
        openCheckout,
        processPayment,
        getPaymentMethods,
        createRefund,
        formatAmount,
        getTestCredentials,
        
        // Computed
        currentUser
    }
}
