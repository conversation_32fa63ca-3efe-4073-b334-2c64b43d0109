<?php

namespace App\Repositories;

use App\Models\ClinicTdlSetting;
use App\Repositories\Interfaces\ClinicTdlSettingRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ClinicTdlSettingRepository extends BaseRepository implements ClinicTdlSettingRepositoryInterface
{
    public function __construct(ClinicTdlSetting $model)
    {
        parent::__construct($model);
    }

    public function findByClinic(int $clinicId): ?ClinicTdlSetting
    {
        return $this->model->where('clinic_id', $clinicId)->first();
    }

    public function create(array $data): ClinicTdlSetting
    {
        return $this->model->create($data);
    }

    public function update(ClinicTdlSetting $setting, array $data): ClinicTdlSetting
    {
        $setting->update($data);
        return $setting->fresh();
    }

    public function delete(ClinicTdlSetting $setting): bool
    {
        return $setting->delete();
    }

    public function getActiveSettings(): Collection
    {
        return $this->model->where('is_active', true)
            ->whereNotNull('azure_connection_string')
            ->whereNotNull('tdl_account_id')
            ->get();
    }

    public function isConfiguredForClinic(int $clinicId): bool
    {
        $setting = $this->findByClinic($clinicId);
        return $setting && $setting->isConfigured();
    }
}
