<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultation_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->string('document_type'); // lab_report, image, pdf, prescription, letter, etc.
            $table->string('file_path');
            $table->string('file_name');
            $table->string('original_name');
            $table->integer('file_size')->nullable();
            $table->string('mime_type')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');

            // WordPress tracking fields
            $table->integer('wp_document_id')->nullable()->index();
            $table->integer('wp_attachment_id')->nullable()->index();
            $table->integer('wp_encounter_id')->nullable()->index();
            $table->datetime('wp_created_date')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['consultation_id', 'document_type']);
            $table->index('uploaded_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultation_documents');
    }
};
