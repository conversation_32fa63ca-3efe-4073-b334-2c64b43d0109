<?php

namespace App\Repositories\Interfaces;

use App\Models\ConsultationService;
use Illuminate\Database\Eloquent\Collection;

interface ConsultationServiceRepositoryInterface
{
    public function getByConsultation(int $consultationId): Collection;
    public function getBillableByConsultation(int $consultationId): Collection;
    public function getUnbilledByConsultation(int $consultationId): Collection;
    public function create(array $data): ConsultationService;
    public function update(ConsultationService $consultationService, array $data): ConsultationService;
    public function delete(ConsultationService $consultationService): bool;
    public function findById(int $id): ?ConsultationService;
    public function markAsBilled(int $consultationServiceId, int $billId): bool;
    public function getUnbilledByClinic(int $clinicId): Collection;
}
