<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Clinic;
use App\Models\EmailTemplate;
use App\Models\ClinicEmailTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class ClinicEmailTemplateControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $clinic;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a clinic
        $this->clinic = Clinic::factory()->create([
            'name' => 'Test Clinic',
        ]);

        // Create a clinic admin user
        $this->user = User::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);
        $this->user->assignRole('clinic_admin');

        // Create a test email template
        $this->template = EmailTemplate::create([
            'name' => 'Test Appointment Template',
            'slug' => 'appointment-booked-patient',
            'subject' => 'Your appointment with {{ $providerName }}',
            'content' => 'Hello {{ $patientName }}, your appointment is confirmed for {{ $appointmentDate }}.',
            'description' => 'Test template for appointments',
            'is_active' => true,
        ]);
    }

    #[Test]
    public function it_can_send_test_email_for_template()
    {
        Mail::fake();

        $response = $this->actingAs($this->user)
            ->postJson("/settings/email-templates/{$this->template->id}/test", [
                'test_email' => '<EMAIL>'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Test email sent successfully!',
                'success' => true
            ]);

        // Verify that an email was sent
        Mail::assertSent(function ($mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    #[Test]
    public function it_validates_email_address_when_sending_test()
    {
        $response = $this->actingAs($this->user)
            ->postJson("/settings/email-templates/{$this->template->id}/test", [
                'test_email' => 'invalid-email'
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['test_email']);
    }

    #[Test]
    public function it_requires_email_address_when_sending_test()
    {
        $response = $this->actingAs($this->user)
            ->postJson("/settings/email-templates/{$this->template->id}/test", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['test_email']);
    }

    #[Test]
    public function it_returns_404_for_non_existent_template()
    {
        $response = $this->actingAs($this->user)
            ->postJson("/settings/email-templates/999/test", [
                'test_email' => '<EMAIL>'
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Template not found'
            ]);
    }

    #[Test]
    public function it_requires_permission_to_send_test_emails()
    {
        // Create a user without email template permissions
        $unauthorizedUser = User::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);

        $response = $this->actingAs($unauthorizedUser)
            ->postJson("/settings/email-templates/{$this->template->id}/test", [
                'test_email' => '<EMAIL>'
            ]);

        $response->assertStatus(403);
    }

    #[Test]
    public function it_adds_test_prefix_to_email_subject()
    {
        Mail::fake();

        $this->actingAs($this->user)
            ->postJson("/settings/email-templates/{$this->template->id}/test", [
                'test_email' => '<EMAIL>'
            ]);

        Mail::assertSent(function ($mail) {
            // Check that the subject has [TEST] prefix
            return str_contains($mail->subject, '[TEST]');
        });
    }
}
