<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

// Repositories
use App\Repositories\UserRepository;
use App\Repositories\ClinicRepository;
use App\Repositories\PatientRepository;
use App\Repositories\ProviderRepository;
use App\Repositories\AppointmentRepository;
use App\Repositories\ConsultationRepository;
use App\Repositories\ConsultationNoteRepository;
use App\Repositories\DiagnosisRepository;
use App\Repositories\ConsultationDocumentRepository;
use App\Repositories\ServiceRepository;
use App\Repositories\PrescriptionRepository;
use App\Repositories\TreatmentPlanRepository;
use App\Repositories\PaymentRepository;
use App\Repositories\ProductCategoryRepository;
use App\Repositories\ProductRepository;
use App\Repositories\CategoryRepository;
use App\Repositories\ClinicTdlSettingRepository;
use App\Repositories\LabTestCatalogRepository;
use App\Repositories\LabTestRequestRepository;
use App\Repositories\LabTestResultRepository;

// Interfaces
use App\Repositories\Interfaces\ClinicTdlSettingRepositoryInterface;
use App\Repositories\Interfaces\LabTestCatalogRepositoryInterface;
use App\Repositories\Interfaces\LabTestRequestRepositoryInterface;
use App\Repositories\Interfaces\LabTestResultRepositoryInterface;

// Services
use App\Services\UserService;
use App\Services\ClinicService;
use App\Services\PatientService;
use App\Services\ProviderService;
use App\Services\AppointmentService;
use App\Services\ConsultationService;
use App\Services\ServiceManagementService;
use App\Services\PrescriptionService;
use App\Services\PaymentService;
use App\Services\TreatmentPlanService;
use App\Services\ProductCategoryService;
use App\Services\ProductService;
use App\Services\CategoryService;
use App\Services\TdlLabService;
use App\Services\TdlTestCatalogService;
use App\Services\HL7MessageService;
use App\Services\AzureBlobService;
use App\Services\RxNormService;

use App\Services\TemplateVariableService;

// Models
use App\Models\User;
use App\Models\Clinic;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Appointment;
use App\Models\Consultation;
use App\Models\ConsultationNote;
use App\Models\Diagnosis;
use App\Models\ConsultationDocument;
use App\Models\Service;
use App\Models\Prescription;
use App\Models\TreatmentPlan;
use App\Models\Payment;
use App\Models\ProductCategory;
use App\Models\Product;
use App\Models\Category;
use App\Models\ClinicTdlSetting;
use App\Models\LabTestCatalog;
use App\Models\LabTestRequest;
use App\Models\LabTestResult;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind Repositories (simple approach - just for data access)
        $this->app->bind(UserRepository::class, function ($app) {
            return new UserRepository(new User());
        });

        $this->app->bind(ClinicRepository::class, function ($app) {
            return new ClinicRepository(new Clinic());
        });

        $this->app->bind(PatientRepository::class, function ($app) {
            return new PatientRepository(new Patient());
        });

        $this->app->bind(ProviderRepository::class, function ($app) {
            return new ProviderRepository(new Provider());
        });

        $this->app->bind(AppointmentRepository::class, function ($app) {
            return new AppointmentRepository(new Appointment());
        });

        $this->app->bind(ConsultationRepository::class, function ($app) {
            return new ConsultationRepository(new Consultation());
        });

        $this->app->bind(ConsultationNoteRepository::class, function ($app) {
            return new ConsultationNoteRepository(new ConsultationNote());
        });

        $this->app->bind(DiagnosisRepository::class, function ($app) {
            return new DiagnosisRepository(new Diagnosis());
        });

        $this->app->bind(ConsultationDocumentRepository::class, function ($app) {
            return new ConsultationDocumentRepository(new ConsultationDocument());
        });

        $this->app->bind(ServiceRepository::class, function ($app) {
            return new ServiceRepository(new Service());
        });

        $this->app->bind(PrescriptionRepository::class, function ($app) {
            return new PrescriptionRepository(new Prescription());
        });

        $this->app->bind(TreatmentPlanRepository::class, function ($app) {
            return new TreatmentPlanRepository(new TreatmentPlan());
        });

        $this->app->bind(PaymentRepository::class, function ($app) {
            return new PaymentRepository(new Payment());
        });

        $this->app->bind(ProductCategoryRepository::class, function ($app) {
            return new ProductCategoryRepository(new ProductCategory());
        });

        $this->app->bind(ProductRepository::class, function ($app) {
            return new ProductRepository(new Product());
        });

        $this->app->bind(CategoryRepository::class, function ($app) {
            return new CategoryRepository(new Category());
        });

        // TDL Labs Repositories
        $this->app->bind(ClinicTdlSettingRepositoryInterface::class, function ($app) {
            return new ClinicTdlSettingRepository(new ClinicTdlSetting());
        });

        $this->app->bind(LabTestCatalogRepositoryInterface::class, function ($app) {
            return new LabTestCatalogRepository(new LabTestCatalog());
        });

        $this->app->bind(LabTestRequestRepositoryInterface::class, function ($app) {
            return new LabTestRequestRepository(new LabTestRequest());
        });

        $this->app->bind(LabTestResultRepositoryInterface::class, function ($app) {
            return new LabTestResultRepository(new LabTestResult());
        });



        // Bind Services (for business logic)
        $this->app->bind(UserService::class, function ($app) {
            return new UserService($app->make(UserRepository::class));
        });

        $this->app->bind(ClinicService::class, function ($app) {
            return new ClinicService($app->make(ClinicRepository::class));
        });

        $this->app->bind(DiagnosisRepository::class, function ($app) {
            return new DiagnosisRepository(new Diagnosis());
        });

        $this->app->bind(ConsultationDocumentRepository::class, function ($app) {
            return new ConsultationDocumentRepository(new ConsultationDocument());
        });

        // Bind Services (for business logic)
        $this->app->bind(UserService::class, function ($app) {
            return new UserService($app->make(UserRepository::class));
        });

        $this->app->bind(ClinicService::class, function ($app) {
            return new ClinicService($app->make(ClinicRepository::class));
        });

        $this->app->bind(PatientService::class, function ($app) {
            return new PatientService(
                $app->make(PatientRepository::class),
                $app->make(UserRepository::class)
            );
        });

        $this->app->bind(ProviderService::class, function ($app) {
            return new ProviderService(
                $app->make(ProviderRepository::class),
                $app->make(UserRepository::class)
            );
        });

        $this->app->bind(PaymentService::class, function ($app) {
            return new PaymentService($app->make(PaymentRepository::class));
        });

        $this->app->bind(AppointmentService::class, function ($app) {
            return new AppointmentService(
                $app->make(AppointmentRepository::class),
                $app->make(PaymentService::class)
            );
        });

        $this->app->bind(ConsultationService::class, function ($app) {
            return new ConsultationService(
                $app->make(ConsultationRepository::class),
                $app->make(ConsultationNoteRepository::class),
                $app->make(DiagnosisRepository::class),
                $app->make(ConsultationDocumentRepository::class)
            );
        });

        $this->app->bind(ServiceManagementService::class, function ($app) {
            return new ServiceManagementService($app->make(ServiceRepository::class));
        });

        $this->app->bind(PrescriptionService::class, function ($app) {
            return new PrescriptionService($app->make(PrescriptionRepository::class));
        });

        $this->app->bind(TreatmentPlanService::class, function ($app) {
            return new TreatmentPlanService($app->make(TreatmentPlanRepository::class));
        });

        $this->app->bind(ProductCategoryService::class, function ($app) {
            return new ProductCategoryService($app->make(ProductCategoryRepository::class));
        });

        $this->app->bind(ProductService::class, function ($app) {
            return new ProductService($app->make(ProductRepository::class));
        });

        $this->app->bind(CategoryService::class, function ($app) {
            return new CategoryService($app->make(CategoryRepository::class));
        });

        // TDL Labs Services
        $this->app->bind(TdlTestCatalogService::class, function ($app) {
            return new TdlTestCatalogService($app->make(LabTestCatalogRepositoryInterface::class));
        });

        $this->app->singleton(HL7MessageService::class);
        $this->app->singleton(AzureBlobService::class);
        $this->app->singleton(RxNormService::class);

        $this->app->bind(TdlLabService::class, function ($app) {
            return new TdlLabService(
                $app->make(ClinicTdlSettingRepositoryInterface::class),
                $app->make(LabTestRequestRepositoryInterface::class),
                $app->make(LabTestResultRepositoryInterface::class),
                $app->make(TdlTestCatalogService::class),
                $app->make(HL7MessageService::class),
                $app->make(AzureBlobService::class)
            );
        });

        $this->app->singleton(TemplateVariableService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
