<template>
    <div class="email-editor">
        <div class="mb-4">
            <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium text-gray-700">
                    Email Message
                </label>
                <div class="flex items-center space-x-2">
                    <button
                        type="button"
                        @click="toggleMode"
                        class="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    >
                        {{ isHtmlMode ? 'Visual Editor' : 'HTML Source' }}
                    </button>
                </div>
            </div>
            
            <!-- WYSIWYG Editor -->
            <div v-if="!isHtmlMode" class="border border-gray-300 rounded-lg overflow-hidden">
                <div ref="quillEditor" class="min-h-96"></div>
            </div>
            
            <!-- HTML Source Editor -->
            <div v-else>
                <textarea
                    v-model="htmlContent"
                    @input="updateFromHtml"
                    rows="12"
                    placeholder="Write your email message here. You can use HTML for formatting."
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                    required
                ></textarea>
            </div>
            
            <p class="text-xs text-gray-500 mt-1">
                Use the visual editor for easy formatting or switch to HTML source for advanced customization
            </p>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['update:modelValue']);

const quillEditor = ref(null);
const isHtmlMode = ref(false);
const htmlContent = ref('');
let quill = null;

// Initialize Quill editor
const initQuill = () => {
    if (!quillEditor.value) return;
    
    quill = new Quill(quillEditor.value, {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'align': [] }],
                ['link', 'image'],
                ['clean']
            ]
        },
        formats: [
            'header', 'bold', 'italic', 'underline', 'strike',
            'color', 'background', 'list', 'align',
            'link', 'image'
        ]
    });

    // Set initial content
    if (props.modelValue) {
        quill.root.innerHTML = props.modelValue;
    }

    // Listen for content changes
    quill.on('text-change', () => {
        const content = quill.root.innerHTML;
        emit('update:modelValue', content);
        htmlContent.value = content;
    });
};

// Toggle between visual and HTML mode
const toggleMode = async () => {
    if (isHtmlMode.value) {
        // Switching from HTML to visual
        isHtmlMode.value = false;
        await nextTick();
        initQuill();
        if (htmlContent.value) {
            quill.root.innerHTML = htmlContent.value;
        }
    } else {
        // Switching from visual to HTML
        if (quill) {
            htmlContent.value = quill.root.innerHTML;
        }
        isHtmlMode.value = true;
    }
};

// Update content from HTML textarea
const updateFromHtml = () => {
    emit('update:modelValue', htmlContent.value);
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
    htmlContent.value = newValue;
    if (quill && !isHtmlMode.value) {
        const currentContent = quill.root.innerHTML;
        if (currentContent !== newValue) {
            quill.root.innerHTML = newValue;
        }
    }
});

onMounted(() => {
    htmlContent.value = props.modelValue;
    initQuill();
});
</script>

<style scoped>
/* Custom Quill editor styles */
:deep(.ql-editor) {
    min-height: 300px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
}

:deep(.ql-toolbar) {
    border-top: 1px solid #e5e7eb;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    border-bottom: none;
    background-color: #f9fafb;
}

:deep(.ql-container) {
    border-bottom: 1px solid #e5e7eb;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    border-top: none;
}

:deep(.ql-editor.ql-blank::before) {
    color: #9ca3af;
    font-style: normal;
}

/* Email-specific styles for preview */
:deep(.ql-editor h1) {
    color: #1f2937;
    font-size: 28px;
    margin-bottom: 20px;
    font-weight: 700;
}

:deep(.ql-editor h2) {
    color: #374151;
    font-size: 22px;
    margin-bottom: 16px;
    font-weight: 600;
}

:deep(.ql-editor h3) {
    color: #4b5563;
    font-size: 18px;
    margin-bottom: 12px;
    font-weight: 600;
}

:deep(.ql-editor p) {
    margin-bottom: 16px;
    color: #4b5563;
}

:deep(.ql-editor a) {
    color: #2563eb;
    text-decoration: none;
}

:deep(.ql-editor a:hover) {
    text-decoration: underline;
}

:deep(.ql-editor .highlight) {
    background-color: #fef3c7;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

:deep(.ql-editor .alert) {
    padding: 16px;
    border-radius: 6px;
    margin: 20px 0;
}

:deep(.ql-editor .alert.success) {
    background-color: #dcfce7;
    border-left: 4px solid #22c55e;
    color: #166534;
}

:deep(.ql-editor .alert.warning) {
    background-color: #fef3c7;
    border-left: 4px solid #f59e0b;
    color: #92400e;
}

:deep(.ql-editor .alert.error) {
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
    color: #991b1b;
}

:deep(.ql-editor .btn) {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    margin: 10px 0;
}
</style>
