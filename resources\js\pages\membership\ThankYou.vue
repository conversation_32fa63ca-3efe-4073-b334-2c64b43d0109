<script setup lang="ts">
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface SubscriptionPlan {
  id: number;
  name: string;
  slug: string;
  price: number;
  formatted_price: string;
  currency: string;
  interval: string;
  description: string;
  features: {
    additional_features?: string[];
    chat_messages_per_hour?: number;
    chat_messages_per_day?: number;
    appointments_per_month?: number;
    api_requests_per_minute?: number;
    api_requests_per_hour?: number;
  };
  is_free: boolean;
}

interface Props {
  plan: SubscriptionPlan;
  isFreePlan: boolean;
}

const props = defineProps<Props>();

// Helper function to get features list
const getFeaturesList = (plan: SubscriptionPlan): string[] => {
  const features = plan.features || {};
  const featuresList: string[] = [];

  // Add rate limit features
  if (features.chat_messages_per_day) {
    featuresList.push(`${features.chat_messages_per_day} chat messages per day`);
  }

  if (features.appointments_per_month) {
    featuresList.push(`${features.appointments_per_month} appointments per month`);
  }

  // Add additional features
  if (features.additional_features && Array.isArray(features.additional_features)) {
    featuresList.push(...features.additional_features);
  }

  return featuresList;
};
</script>

<template>
  <AppLayout>
    <Head title="Welcome to Medroid!" />

    <div class="container mx-auto py-12 px-4 sm:px-6 lg:px-8 max-w-4xl">
      <div class="text-center">
        <!-- Success Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>

        <!-- Thank You Message -->
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          {{ isFreePlan ? 'Welcome to Medroid!' : 'Thank You for Subscribing!' }}
        </h1>
        
        <p class="text-xl text-gray-600 mb-8">
          {{ isFreePlan 
            ? 'You\'ve successfully joined our free plan and can start using Medroid right away.' 
            : `You've successfully subscribed to ${plan.name}. Welcome to the Medroid family!` 
          }}
        </p>

        <!-- Plan Details Card -->
        <Card class="max-w-md mx-auto mb-8">
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              {{ plan.name }}
              <span class="text-2xl font-bold">{{ plan.formatted_price }}</span>
            </CardTitle>
            <CardDescription>{{ plan.description }}</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="text-left">
              <h4 class="font-medium mb-3">Your plan includes:</h4>
              <ul class="space-y-2">
                <li v-for="(feature, index) in getFeaturesList(plan).slice(0, 5)" :key="index" class="flex items-center text-sm">
                  <svg class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  {{ feature }}
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <!-- Next Steps -->
        <div class="bg-gray-50 rounded-lg p-6 mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">What's Next?</h2>
          <div class="grid gap-4 md:grid-cols-3">
            <div class="text-center">
              <div class="bg-teal-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <svg class="h-6 w-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h3 class="font-medium">Complete Your Profile</h3>
              <p class="text-sm text-gray-600">Add your health information for personalized care</p>
            </div>
            
            <div class="text-center">
              <div class="bg-teal-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <svg class="h-6 w-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
              </div>
              <h3 class="font-medium">Start Chatting</h3>
              <p class="text-sm text-gray-600">Ask our AI health assistant any questions</p>
            </div>
            
            <div class="text-center">
              <div class="bg-teal-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <svg class="h-6 w-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-1h8v1z"></path>
                </svg>
              </div>
              <h3 class="font-medium">Book Appointment</h3>
              <p class="text-sm text-gray-600">Schedule with our healthcare providers</p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button as="a" :href="route('dashboard')" size="lg">
            Go to Dashboard
          </Button>
          
          <Button as="a" :href="route('settings.subscription')" variant="outline" size="lg">
            Manage Subscription
          </Button>
        </div>

        <!-- Support Information -->
        <div class="mt-12 text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Need Help Getting Started?</h3>
          <p class="text-gray-600 mb-4">
            Our support team is here to help you make the most of your Medroid experience.
          </p>
          <div class="flex justify-center space-x-6 text-sm">
            <a href="#" class="text-teal-600 hover:text-teal-500">
              📧 Email Support
            </a>
            <a href="#" class="text-teal-600 hover:text-teal-500">
              💬 Live Chat
            </a>
            <a href="#" class="text-teal-600 hover:text-teal-500">
              📚 Help Center
            </a>
          </div>
        </div>

        <!-- Confirmation Details -->
        <div v-if="!isFreePlan" class="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 class="text-lg font-medium text-blue-900 mb-2">Billing Information</h3>
          <p class="text-blue-700 text-sm">
            You'll be charged {{ plan.formatted_price }} every {{ plan.interval }}. 
            A confirmation email has been sent to your registered email address.
            You can cancel or modify your subscription anytime from your account settings.
          </p>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
