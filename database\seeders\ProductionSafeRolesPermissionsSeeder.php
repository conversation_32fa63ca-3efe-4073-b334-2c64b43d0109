<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ProductionSafeRolesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder is production-safe - it only creates missing permissions and roles.
     */
    public function run(): void
    {
        $this->command->info('Running production-safe roles and permissions seeder...');

        // Create permissions first
        $this->createPermissionsIfMissing();

        // Create roles and assign permissions
        $this->createRolesIfMissing();

        // Fix user role assignments
        $this->fixUserRoleAssignments();

        $this->command->info('Production-safe seeding completed!');
    }

    /**
     * Create permissions only if they don't exist
     */
    protected function createPermissionsIfMissing(): void
    {
        $permissionGroups = [
            'users' => ['view', 'create', 'edit', 'delete'],
            'providers' => ['view', 'create', 'edit', 'delete'],
            'patients' => ['view', 'create', 'edit', 'delete'],
            'clinics' => ['view', 'create', 'edit', 'delete', 'manage'],
            'categories' => ['view', 'create', 'edit', 'delete'],
            'appointments' => ['view', 'create', 'edit', 'delete'],
            'payments' => ['view', 'create', 'edit', 'delete'],
            'chats' => ['view', 'create', 'edit', 'delete'],
            'services' => ['view', 'create', 'edit', 'delete'],
            'products' => ['view', 'create', 'edit', 'delete', 'manage'],
            'orders' => ['view', 'create', 'edit', 'delete', 'manage'],
            'reports' => ['view', 'create', 'edit', 'delete', 'export'],
            'settings' => ['view', 'create', 'edit', 'delete'],
            'analytics' => ['view', 'export'],
            'permissions' => ['view', 'create', 'edit', 'delete', 'manage'],
            'diagnostic feedback' => ['view', 'create', 'edit'],
            'medical letters' => ['view', 'create', 'edit', 'delete', 'manage'],
            'doctor settings' => ['view', 'edit', 'manage'],
            'features' => ['view', 'create', 'edit', 'delete'],
            'email templates' => ['view', 'create', 'edit', 'delete', 'manage'],
            'notifications' => ['view', 'create', 'edit', 'delete', 'manage'],
            'referrals' => ['view', 'create', 'edit', 'delete', 'manage'],
            'credits' => ['view', 'create', 'edit', 'delete', 'manage'],
            'clubs' => ['view', 'create', 'edit', 'delete', 'manage'],
            'consultations' => ['view', 'create', 'edit', 'delete', 'manage'],
            'prescriptions' => ['view', 'create', 'edit', 'delete', 'manage'],
        ];

        $createdCount = 0;
        $existingCount = 0;

        foreach ($permissionGroups as $group => $actions) {
            foreach ($actions as $action) {
                $permissionName = "{$action} {$group}";

                $permission = Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => 'web',
                ]);

                if ($permission->wasRecentlyCreated) {
                    $createdCount++;
                } else {
                    $existingCount++;
                }
            }
        }

        $this->command->info("Permissions: {$createdCount} created, {$existingCount} already existed.");
    }

    /**
     * Create roles only if they don't exist and assign permissions
     */
    protected function createRolesIfMissing(): void
    {
        $roleDefinitions = [
            'admin' => [
                'description' => 'Full system access',
                'permissions' => 'all',
            ],
            'clinic_admin' => [
                'description' => 'Clinic administration access with user management capabilities within their clinic scope',
                'permissions' => [
                    'users' => ['view', 'create', 'edit'],
                    'providers' => ['view', 'create', 'edit'],
                    'patients' => ['view', 'create', 'edit'],
                    'clinics' => ['view', 'edit'], // Can only view/edit their own clinic
                    'categories' => ['view', 'create', 'edit'],
                    'appointments' => ['view', 'create', 'edit', 'delete'],
                    'payments' => ['view', 'edit'],
                    'chats' => ['view', 'edit'],
                    'services' => ['view', 'create', 'edit'],
                    'products' => ['view', 'create', 'edit'],
                    'orders' => ['view', 'edit'],
                    'reports' => ['view', 'create', 'export'],
                    'settings' => ['view', 'edit'],
                    'analytics' => ['view', 'export'],
                    'diagnostic feedback' => ['view'],
                    'permissions' => ['view'],
                    'medical letters' => ['view', 'create', 'edit'],
                    'doctor settings' => ['view', 'edit'],
                ],
            ],
            'provider' => [
                'description' => 'Healthcare provider access',
                'permissions' => [
                    'appointments' => ['view', 'create', 'edit'],
                    'patients' => ['view', 'create', 'edit'],
                    'payments' => ['view'],
                    'chats' => ['view', 'create'],
                    'categories' => ['view', 'create', 'edit', 'delete'],
                    'services' => ['view', 'edit'],
                    'products' => ['view', 'create', 'edit', 'delete'],
                    'reports' => ['view'],
                    'diagnostic feedback' => ['view', 'create', 'edit'],
                    'consultations' => ['view', 'create', 'edit', 'delete', 'manage'],
                    'prescriptions' => ['view', 'create', 'edit', 'delete', 'manage'],
                    'medical letters' => ['view', 'create', 'edit', 'delete', 'manage'],
                    'doctor settings' => ['view', 'edit', 'manage'],
                    'email templates' => ['view', 'edit'],
                ],
            ],
            'bot' => [
                'description' => 'Bot user access for content generation',
                'permissions' => [
                    // Minimal permissions for bot users
                ],
            ],
            'patient' => [
                'description' => 'Patient access',
                'permissions' => [
                    'appointments' => ['view', 'create'],
                    'providers' => ['view'],
                    'payments' => ['view', 'create'],
                    'chats' => ['view', 'create'],
                    'services' => ['view'],
                ],
            ],
        ];

        $allPermissions = Permission::all();

        foreach ($roleDefinitions as $roleName => $roleData) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);

            $wasCreated = $role->wasRecentlyCreated;

            // Assign permissions to the role
            if ($roleData['permissions'] === 'all') {
                $role->syncPermissions($allPermissions);
            } else {
                $rolePermissions = [];
                foreach ($roleData['permissions'] as $group => $actions) {
                    foreach ($actions as $action) {
                        $rolePermissions[] = "{$action} {$group}";
                    }
                }
                $role->syncPermissions($rolePermissions);
            }

            $status = $wasCreated ? 'Created' : 'Updated';
            $this->command->info("{$status} role '{$roleName}' with " . count($role->permissions) . " permissions.");
        }
    }

    /**
     * Fix user role assignments - assign Spatie roles based on database role column
     */
    protected function fixUserRoleAssignments(): void
    {
        $this->command->info('Fixing user role assignments...');

        // Get all users that have a role in the database but may not have Spatie roles assigned
        $users = \App\Models\User::whereNotNull('role')->get();
        
        $fixedCount = 0;
        $skippedCount = 0;

        foreach ($users as $user) {
            if (empty($user->role)) {
                $skippedCount++;
                continue;
            }

            // Check if the role exists in Spatie system
            $roleExists = Role::where('name', $user->role)->exists();
            if (!$roleExists) {
                $this->command->warn("Role '{$user->role}' does not exist in Spatie roles system for user {$user->email}");
                $skippedCount++;
                continue;
            }

            // Check if user already has the correct role assigned
            if (!$user->hasRole($user->role)) {
                try {
                    $user->assignRole($user->role);
                    $this->command->info("✅ Assigned '{$user->role}' role to {$user->email}");
                    $fixedCount++;
                } catch (\Exception $e) {
                    $this->command->error("❌ Failed to assign role to {$user->email}: " . $e->getMessage());
                    $skippedCount++;
                }
            } else {
                $skippedCount++;
            }
        }

        $this->command->info("User role assignments: {$fixedCount} fixed, {$skippedCount} skipped.");
        
        // Clear permission cache after role assignments
        try {
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
            $this->command->info('Permission cache cleared.');
        } catch (\Exception $e) {
            $this->command->warn('Could not clear permission cache: ' . $e->getMessage());
        }
    }
}
