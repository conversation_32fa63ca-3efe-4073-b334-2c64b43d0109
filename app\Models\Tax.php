<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tax extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'name',
        'type',
        'rate',
        'description',
        'is_active',
        'is_inclusive',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rate' => 'decimal:4',
        'is_active' => 'boolean',
        'is_inclusive' => 'boolean',
    ];

    /**
     * Get the clinic that owns the tax.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Calculate tax amount for a given subtotal.
     *
     * @param float $subtotal
     * @return float
     */
    public function calculateTaxAmount(float $subtotal): float
    {
        if (!$this->is_active) {
            return 0;
        }

        if ($this->type === 'percentage') {
            return ($subtotal * $this->rate) / 100;
        }

        // Fixed amount tax
        return (float) $this->rate;
    }

    /**
     * Scope to get active taxes for a clinic.
     */
    public function scopeActiveForClinic($query, $clinicId)
    {
        return $query->where('clinic_id', $clinicId)
                    ->where('is_active', true);
    }
}
