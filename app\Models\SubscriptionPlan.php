<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'stripe_product_id',
        'stripe_price_id',
        'price',
        'currency',
        'interval',
        'interval_count',
        'description',
        'features',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'is_active' => 'boolean',
        'interval_count' => 'integer',
        'sort_order' => 'integer',
    ];

    protected $appends = [
        'formatted_price',
        'is_free',
    ];

    /**
     * Get all active subscription plans ordered by sort_order
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get the plan by slug
     */
    public function scopeBySlug($query, $slug)
    {
        return $query->where('slug', $slug);
    }

    /**
     * Check if this is a free plan
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Check if this is a paid plan
     */
    public function isPaid(): bool
    {
        return $this->price > 0;
    }

    /**
     * Get is_free attribute
     */
    public function getIsFreeAttribute(): bool
    {
        return $this->isFree();
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }

        $symbol = $this->currency === 'gbp' ? '£' : '$';
        return $symbol . number_format($this->price, 2);
    }

    /**
     * Get rate limits for this plan
     */
    public function getRateLimits(): array
    {
        $features = $this->features ?? [];
        
        return [
            'chat_messages_per_hour' => $features['chat_messages_per_hour'] ?? 10,
            'chat_messages_per_day' => $features['chat_messages_per_day'] ?? 50,
            'appointments_per_month' => $features['appointments_per_month'] ?? 2,
            'api_requests_per_minute' => $features['api_requests_per_minute'] ?? 60,
            'api_requests_per_hour' => $features['api_requests_per_hour'] ?? 1000,
        ];
    }

    /**
     * Get plan features as a formatted list
     */
    public function getFeaturesList(): array
    {
        $features = $this->features ?? [];
        $limits = $this->getRateLimits();
        
        $featuresList = [];
        
        // Chat features
        if (isset($limits['chat_messages_per_day'])) {
            $featuresList[] = $limits['chat_messages_per_day'] . ' chat messages per day';
        }
        
        // Appointment features
        if (isset($limits['appointments_per_month'])) {
            $featuresList[] = $limits['appointments_per_month'] . ' appointments per month';
        }
        
        // Additional features from the features array
        if (isset($features['additional_features']) && is_array($features['additional_features'])) {
            $featuresList = array_merge($featuresList, $features['additional_features']);
        }
        
        return $featuresList;
    }

    /**
     * Get users subscribed to this plan
     */
    public function users()
    {
        return $this->hasMany(User::class, 'subscription_plan_id');
    }
}
