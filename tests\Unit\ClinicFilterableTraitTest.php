<?php

use App\Models\User;
use App\Models\Clinic;
use App\Models\Patient;
use App\Traits\ClinicFilterable;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'clinic_admin']);
    Role::create(['name' => 'provider']);
    Role::create(['name' => 'patient']);
});

it('allows admin users to see all data', function () {
    // Create clinics
    $clinic1 = Clinic::factory()->create();
    $clinic2 = Clinic::factory()->create();

    // Create patients for different clinics
    $patient1 = Patient::factory()->create(['clinic_id' => $clinic1->id]);
    $patient2 = Patient::factory()->create(['clinic_id' => $clinic2->id]);

    // Create admin user
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    // Test that admin can see all patients
    $this->actingAs($admin);
    $patients = Patient::forUserClinic()->get();

    expect($patients)->toHaveCount(2);
    expect($patients->contains($patient1))->toBeTrue();
    expect($patients->contains($patient2))->toBeTrue();
});

it('allows non-admin users to only see their clinic data', function () {
    // Create clinics
    $clinic1 = Clinic::factory()->create();
    $clinic2 = Clinic::factory()->create();

    // Create patients for different clinics
    $patient1 = Patient::factory()->create(['clinic_id' => $clinic1->id]);
    $patient2 = Patient::factory()->create(['clinic_id' => $clinic2->id]);

    // Create clinic admin user for clinic1
    $clinicAdmin = User::factory()->create(['clinic_id' => $clinic1->id]);
    $clinicAdmin->assignRole('clinic_admin');

    // Test that clinic admin only sees their clinic's patients
    $this->actingAs($clinicAdmin);
    $patients = Patient::forUserClinic()->get();

    expect($patients)->toHaveCount(1);
    expect($patients->contains($patient1))->toBeTrue();
    expect($patients->contains($patient2))->toBeFalse();
});

it('shows no data for users without clinic association', function () {
    // Create clinic and patient
    $clinic = Clinic::factory()->create();
    $patient = Patient::factory()->create(['clinic_id' => $clinic->id]);

    // Create user without clinic association
    $user = User::factory()->create(['clinic_id' => null]);
    $user->assignRole('provider');

    // Test that user sees no patients
    $this->actingAs($user);
    $patients = Patient::forUserClinic()->get();

    expect($patients)->toHaveCount(0);
});

it('shows no data for unauthenticated users', function () {
    // Create clinic and patient
    $clinic = Clinic::factory()->create();
    Patient::factory()->create(['clinic_id' => $clinic->id]);

    // Test without authentication
    $patients = Patient::forUserClinic()->get();

    expect($patients)->toHaveCount(0);
});

it('can filter by specific user', function () {
    // Create clinics
    $clinic1 = Clinic::factory()->create();
    $clinic2 = Clinic::factory()->create();

    // Create patients
    $patient1 = Patient::factory()->create(['clinic_id' => $clinic1->id]);
    $patient2 = Patient::factory()->create(['clinic_id' => $clinic2->id]);

    // Create users
    $user1 = User::factory()->create(['clinic_id' => $clinic1->id]);
    $user1->assignRole('clinic_admin');

    $user2 = User::factory()->create(['clinic_id' => $clinic2->id]);
    $user2->assignRole('clinic_admin');

    // Test filtering by specific user
    $patients = Patient::forUserClinic($user1)->get();
    expect($patients)->toHaveCount(1);
    expect($patients->contains($patient1))->toBeTrue();

    $patients = Patient::forUserClinic($user2)->get();
    expect($patients)->toHaveCount(1);
    expect($patients->contains($patient2))->toBeTrue();
});

it('works with query builder methods', function () {
    // Create clinic
    $clinic = Clinic::factory()->create();

    // Create patients with different statuses
    $activePatient = Patient::factory()->create([
        'clinic_id' => $clinic->id,
        'is_active' => true
    ]);
    $inactivePatient = Patient::factory()->create([
        'clinic_id' => $clinic->id,
        'is_active' => false
    ]);

    // Create clinic admin
    $user = User::factory()->create(['clinic_id' => $clinic->id]);
    $user->assignRole('clinic_admin');

    $this->actingAs($user);

    // Test combining with other query methods
    $activePatients = Patient::forUserClinic()
        ->where('is_active', true)
        ->get();

    expect($activePatients)->toHaveCount(1);
    expect($activePatients->contains($activePatient))->toBeTrue();
    expect($activePatients->contains($inactivePatient))->toBeFalse();
});

it('returns correct clinic id for different roles', function () {
    $clinic = Clinic::factory()->create();

    // Test admin user (should return null - no specific clinic)
    $admin = User::factory()->create();
    $admin->assignRole('admin');
    $this->actingAs($admin);

    $trait = new class {
        use ClinicFilterable;
    };

    expect($trait->getUserClinicId())->toBeNull();

    // Test clinic admin user
    $clinicAdmin = User::factory()->create(['clinic_id' => $clinic->id]);
    $clinicAdmin->assignRole('clinic_admin');
    $this->actingAs($clinicAdmin);

    expect($trait->getUserClinicId())->toBe($clinic->id);

    // Test provider user
    $provider = User::factory()->create(['clinic_id' => $clinic->id]);
    $provider->assignRole('provider');
    $this->actingAs($provider);

    expect($trait->getUserClinicId())->toBe($clinic->id);
});
