<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\ConsultationService;
use App\Models\Service;
use App\Repositories\Interfaces\ConsultationServiceRepositoryInterface;
use App\Services\BillService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ConsultationBillingService
{
    public function __construct(
        private ConsultationServiceRepositoryInterface $consultationServiceRepository,
        private BillService $billService
    ) {}

    /**
     * Add a service to a consultation.
     */
    public function addServiceToConsultation(int $consultationId, array $serviceData, int $userClinicId): ConsultationService
    {
        $consultation = Consultation::find($consultationId);
        
        if (!$consultation) {
            throw new \Exception('Consultation not found', 404);
        }

        if ($consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation', 403);
        }

        // If service_id is provided, get service details
        if (!empty($serviceData['service_id'])) {
            $service = Service::find($serviceData['service_id']);
            if ($service) {
                $serviceData['service_name'] = $service->name;
                $serviceData['unit_price'] = $serviceData['unit_price'] ?? $service->price;
                $serviceData['description'] = $serviceData['description'] ?? $service->description;
            }
        }

        $serviceData['consultation_id'] = $consultationId;
        $serviceData['quantity'] = $serviceData['quantity'] ?? 1;
        $serviceData['is_billable'] = $serviceData['is_billable'] ?? true;

        return $this->consultationServiceRepository->create($serviceData);
    }

    /**
     * Update a consultation service.
     */
    public function updateConsultationService(ConsultationService $consultationService, array $data, int $userClinicId): ConsultationService
    {
        if ($consultationService->consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation service', 403);
        }

        if ($consultationService->is_billed) {
            throw new \Exception('Cannot update service that has already been billed', 400);
        }

        return $this->consultationServiceRepository->update($consultationService, $data);
    }

    /**
     * Delete a consultation service.
     */
    public function deleteConsultationService(ConsultationService $consultationService, int $userClinicId): bool
    {
        if ($consultationService->consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation service', 403);
        }

        if ($consultationService->is_billed) {
            throw new \Exception('Cannot delete service that has already been billed', 400);
        }

        return $this->consultationServiceRepository->delete($consultationService);
    }

    /**
     * Get consultation services.
     */
    public function getConsultationServices(int $consultationId, int $userClinicId): Collection
    {
        $consultation = Consultation::find($consultationId);
        
        if (!$consultation) {
            throw new \Exception('Consultation not found', 404);
        }

        if ($consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation', 403);
        }

        return $this->consultationServiceRepository->getByConsultation($consultationId);
    }

    /**
     * Get unbilled services for a consultation.
     */
    public function getUnbilledServices(int $consultationId, int $userClinicId): Collection
    {
        $consultation = Consultation::find($consultationId);
        
        if (!$consultation) {
            throw new \Exception('Consultation not found', 404);
        }

        if ($consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation', 403);
        }

        return $this->consultationServiceRepository->getUnbilledByConsultation($consultationId);
    }

    /**
     * Generate bill from consultation.
     */
    public function generateBillFromConsultation(int $consultationId, array $billData, int $userClinicId): array
    {
        $consultation = Consultation::find($consultationId);
        
        if (!$consultation) {
            throw new \Exception('Consultation not found', 404);
        }

        if ($consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation', 403);
        }

        $unbilledServices = $this->consultationServiceRepository->getUnbilledByConsultation($consultationId);

        if ($unbilledServices->isEmpty()) {
            throw new \Exception('No unbilled services found for this consultation', 400);
        }

        return DB::transaction(function () use ($consultation, $unbilledServices, $billData, $userClinicId) {
            // Prepare bill data
            $billData = array_merge([
                'patient_id' => $consultation->patient_id,
                'provider_id' => $consultation->provider_id,
                'consultation_id' => $consultation->id,
                'appointment_id' => $consultation->appointment_id,
                'title' => $billData['title'] ?? "Bill for Consultation #{$consultation->id}",
                'due_date' => $billData['due_date'] ?? now()->addDays(30),
                'notes' => $billData['notes'] ?? "Generated from consultation on " . $consultation->consultation_date->format('Y-m-d'),
                'discount' => $billData['discount'] ?? 0,
            ], $billData);

            // Convert consultation services to bill items
            $billData['items'] = $unbilledServices->map(function ($service) {
                return [
                    'service_id' => $service->service_id,
                    'item_name' => $service->service_name,
                    'description' => $service->description,
                    'unit_price' => $service->unit_price,
                    'quantity' => $service->quantity,
                ];
            })->toArray();

            // Create the bill
            $bill = $this->billService->createBill($billData, $userClinicId);

            // Mark consultation services as billed
            foreach ($unbilledServices as $service) {
                $this->consultationServiceRepository->markAsBilled($service->id, $bill->id);
            }

            return [
                'bill' => $bill,
                'billed_services' => $unbilledServices->count(),
                'total_amount' => $bill->total_amount,
            ];
        });
    }

    /**
     * Get all unbilled services for a clinic.
     */
    public function getUnbilledServicesByClinic(int $clinicId): Collection
    {
        return $this->consultationServiceRepository->getUnbilledByClinic($clinicId);
    }

    /**
     * Add default consultation service (consultation fee).
     */
    public function addConsultationFee(int $consultationId, float $amount, int $userClinicId): ConsultationService
    {
        return $this->addServiceToConsultation($consultationId, [
            'service_name' => 'Consultation Fee',
            'description' => 'Standard consultation fee',
            'unit_price' => $amount,
            'quantity' => 1,
            'is_billable' => true,
        ], $userClinicId);
    }

    /**
     * Bulk add services to consultation.
     */
    public function bulkAddServices(int $consultationId, array $services, int $userClinicId): Collection
    {
        $consultation = Consultation::find($consultationId);
        
        if (!$consultation) {
            throw new \Exception('Consultation not found', 404);
        }

        if ($consultation->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to consultation', 403);
        }

        $addedServices = collect();

        DB::transaction(function () use ($consultationId, $services, $userClinicId, &$addedServices) {
            foreach ($services as $serviceData) {
                $addedServices->push(
                    $this->addServiceToConsultation($consultationId, $serviceData, $userClinicId)
                );
            }
        });

        return $addedServices;
    }
}
