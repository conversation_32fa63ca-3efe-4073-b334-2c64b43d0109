<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('letterhead_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->longText('header_html')->nullable(); // HTML content for header
            $table->longText('footer_html')->nullable(); // HTML content for footer
            $table->string('header_image_url')->nullable(); // Full URL for header image
            $table->string('footer_image_url')->nullable(); // Full URL for footer image
            $table->foreignId('header_file_id')->nullable()->constrained('files')->onDelete('set null'); // Reference to files table
            $table->foreignId('footer_file_id')->nullable()->constrained('files')->onDelete('set null'); // Reference to files table
            $table->enum('header_type', ['html', 'image'])->default('html'); // Either HTML content or image
            $table->enum('footer_type', ['html', 'image'])->default('html'); // Either HTML content or image
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index('clinic_id');
            $table->index('is_active');
            $table->index('header_file_id');
            $table->index('footer_file_id');

            // Unique constraint: one letterhead setting per clinic
            $table->unique('clinic_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('letterhead_settings');
    }
};
