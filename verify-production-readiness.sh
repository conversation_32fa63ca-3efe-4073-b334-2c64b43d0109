#!/bin/bash

# Medroid Production Readiness Verification Script
# Run this before deploying to app.medroid.ai

echo "🏥 Medroid Production Readiness Check"
echo "====================================="
echo "Target: app.medroid.ai"
echo "Date: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_status "error" "artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

ISSUES=0

# 1. Code Readiness Check
print_status "info" "1. Code Readiness Check"
echo "======================="

# Check if production assets are built
if [ -d "public/build" ] && [ -f "public/build/manifest.json" ]; then
    print_status "success" "Production assets built"
else
    print_status "error" "Production assets not built. Run: npm run build"
    ((ISSUES++))
fi

# Check if vendor directory exists
if [ -d "vendor" ]; then
    print_status "success" "Composer dependencies installed"
else
    print_status "error" "Composer dependencies missing. Run: composer install --no-dev --optimize-autoloader"
    ((ISSUES++))
fi

# Check for production environment file
if [ -f ".env.production" ]; then
    print_status "success" "Production environment template exists"
else
    print_status "warning" "No .env.production template found"
fi

echo ""

# 2. Configuration Check
print_status "info" "2. Configuration Check"
echo "======================"

# Check Laravel caches
if [ -f "bootstrap/cache/config.php" ]; then
    print_status "success" "Configuration cached"
else
    print_status "warning" "Configuration not cached. Run: php artisan config:cache"
fi

if [ -f "bootstrap/cache/routes-v7.php" ]; then
    print_status "success" "Routes cached"
else
    print_status "warning" "Routes not cached. Run: php artisan route:cache"
fi

if [ -f "bootstrap/cache/compiled.php" ]; then
    print_status "success" "Views cached"
else
    print_status "warning" "Views not cached. Run: php artisan view:cache"
fi

echo ""

# 3. Security Check
print_status "info" "3. Security Check"
echo "================="

# Check file permissions
if [ -f ".env" ]; then
    ENV_PERMS=$(stat -c %a .env 2>/dev/null || stat -f %A .env 2>/dev/null)
    if [ "$ENV_PERMS" = "600" ]; then
        print_status "success" ".env file permissions secure (600)"
    else
        print_status "warning" ".env file permissions should be 600. Current: $ENV_PERMS"
    fi
else
    print_status "info" "No .env file found (will be created on server)"
fi

# Check for sensitive files that shouldn't be in production
SENSITIVE_FILES=(".env.local" ".env.development" "composer.phar" "phpunit.xml.dist")
for file in "${SENSITIVE_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "warning" "Remove $file before production deployment"
    fi
done

echo ""

# 4. Database Migration Check
print_status "info" "4. Database Migration Check"
echo "==========================="

# Check for pending migrations
if php artisan migrate:status --no-interaction 2>/dev/null | grep -q "Pending"; then
    print_status "warning" "Pending database migrations found"
    echo "   Run: php artisan migrate --force (on production server)"
else
    print_status "success" "No pending migrations"
fi

echo ""

# 5. Storage Check
print_status "info" "5. Storage Check"
echo "================"

# Check storage directories
STORAGE_DIRS=("storage/app" "storage/framework" "storage/logs" "bootstrap/cache")
for dir in "${STORAGE_DIRS[@]}"; do
    if [ -d "$dir" ] && [ -w "$dir" ]; then
        print_status "success" "$dir exists and writable"
    else
        print_status "error" "$dir missing or not writable"
        ((ISSUES++))
    fi
done

echo ""

# 6. Production Files Check
print_status "info" "6. Production Files Check"
echo "========================="

PROD_FILES=("nginx-production.conf" "supervisor-medroid-worker.conf" "production-health-check.sh")
for file in "${PROD_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "success" "$file ready for deployment"
    else
        print_status "error" "$file missing"
        ((ISSUES++))
    fi
done

echo ""

# 7. Bot System Check
print_status "info" "7. Bot System Check"
echo "==================="

# Check if bot commands exist
if php artisan list | grep -q "bot:"; then
    print_status "success" "Bot system commands available"
else
    print_status "warning" "Bot system commands not found"
fi

# Check for bot storage directories
BOT_DIRS=("storage/app/public/b_images")
for dir in "${BOT_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        print_status "success" "$dir exists"
    else
        print_status "warning" "$dir will be created during deployment"
    fi
done

echo ""

# 8. API Configuration Check
print_status "info" "8. API Configuration Check"
echo "=========================="

# Check route files
ROUTE_FILES=("routes/api.php" "routes/web.php" "routes/cron.php")
for file in "${ROUTE_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "success" "$file exists"
    else
        print_status "error" "$file missing"
        ((ISSUES++))
    fi
done

echo ""

# Summary
print_status "info" "🎯 PRODUCTION READINESS SUMMARY"
echo "==============================="

if [ "$ISSUES" -eq 0 ]; then
    print_status "success" "All critical checks passed! 🎉"
    echo ""
    echo "✅ Code: Ready for deployment"
    echo "✅ Assets: Built and optimized"
    echo "✅ Security: Configured"
    echo "✅ Storage: Prepared"
    echo "✅ Configuration: Cached"
    echo ""
    echo "🚀 Ready to deploy to app.medroid.ai!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Upload code to production server"
    echo "2. Copy .env.production to .env and configure"
    echo "3. Run: php artisan migrate --force"
    echo "4. Set up queue workers and cron jobs"
    echo "5. Configure Nginx and SSL"
    echo "6. Run: ./production-health-check.sh"
else
    print_status "warning" "$ISSUES critical issues found"
    echo ""
    echo "⚠️  Please resolve the issues above before deployment"
    echo "📋 Common fixes:"
    echo "• Run: npm run build"
    echo "• Run: composer install --no-dev --optimize-autoloader"
    echo "• Run: php artisan optimize"
    echo "• Fix file permissions"
fi

echo ""
echo "📞 Support: Check PRODUCTION_DEPLOYMENT_GUIDE.md for detailed instructions"
