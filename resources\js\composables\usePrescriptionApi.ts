/**
 * Prescription API Composable
 * 
 * This composable provides methods for managing prescriptions, medications,
 * and prescription refill requests.
 */

import { ref, type Ref } from 'vue'
import { useApi } from './useApi'

export interface Medication {
  id: number
  name: string
  brand_name?: string
  active_ingredient: string
  strength?: string
  form: string
  route?: string
  description?: string
  indications?: string[]
  contraindications?: string[]
  side_effects?: string[]
  interactions?: any[]
  dosage_guidelines?: string
  drug_class?: string
  controlled_substance_schedule?: string
  requires_prescription: boolean
  is_active: boolean
  regulatory_code?: string
  created_at: string
  updated_at: string
  display_name?: string
  full_name?: string
}

export interface PrescriptionItem {
  id?: number
  prescription_id?: number
  medication_id: number
  medication_name: string
  strength: string
  form: string
  dosage: string
  frequency: string
  route: string
  quantity: number
  quantity_unit: string
  duration_days?: number
  directions_for_use: string
  additional_instructions?: string
  take_with_food?: boolean
  avoid_alcohol?: boolean
  warnings?: string
  unit_cost?: number
  total_cost?: number
  status?: 'pending' | 'dispensed' | 'partially_dispensed' | 'cancelled'
  quantity_dispensed?: number
  dispensed_at?: string
  dispensing_notes?: string
  is_repeat_eligible?: boolean
  repeats_allowed?: number
  repeats_used?: number
  medication?: Medication
}

export interface Prescription {
  id: number
  prescription_number: string
  consultation_id?: number
  patient_id: number
  prescriber_id: number
  clinic_id?: number
  status: 'draft' | 'active' | 'dispensed' | 'completed' | 'cancelled' | 'expired'
  type: 'new' | 'repeat' | 'acute' | 'chronic'
  prescribed_date: string
  valid_until?: string
  clinical_indication?: string
  additional_instructions?: string
  warnings?: string
  is_private: boolean
  is_electronic: boolean
  pharmacy_name?: string
  pharmacy_address?: string
  total_items: number
  total_cost?: number
  dispensed_at?: string
  dispensed_by?: string
  dispensing_notes?: string
  attachments?: string[]
  created_at: string
  updated_at: string
  patient?: any
  prescriber?: any
  consultation?: any
  clinic?: any
  items?: PrescriptionItem[]
  refill_requests?: PrescriptionRefillRequest[]
}

export interface PrescriptionRefillRequest {
  id: number
  original_prescription_id: number
  patient_id: number
  requested_by: number
  reviewed_by?: number
  new_prescription_id?: number
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  request_reason?: string
  patient_notes?: string
  requested_items?: any[]
  requested_at: string
  reviewed_at?: string
  review_notes?: string
  rejection_reason?: string
  requires_consultation: boolean
  expires_at?: string
  created_at: string
  updated_at: string
  original_prescription?: Prescription
  patient?: any
  requester?: any
  reviewer?: any
  new_prescription?: Prescription
}

export function usePrescriptionApi() {
  const api = useApi()
  const prescriptions: Ref<Prescription[]> = ref([])
  const currentPrescription: Ref<Prescription | null> = ref(null)
  const medications: Ref<Medication[]> = ref([])
  const refillRequests: Ref<PrescriptionRefillRequest[]> = ref([])

  // Prescription methods
  const getPrescriptions = async (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    const response = await api.get(`/prescriptions-list${queryString}`)
    if (response?.data) {
      prescriptions.value = response.data.data || response.data
    }
    return response
  }

  const getPrescription = async (id: number) => {
    const response = await api.get(`/prescriptions/${id}`)
    if (response?.data) {
      currentPrescription.value = response.data
    }
    return response
  }

  const createPrescription = async (data: Partial<Prescription>) => {
    const response = await api.post('/prescriptions', data)
    if (response?.data) {
      prescriptions.value.unshift(response.data)
      currentPrescription.value = response.data
    }
    return response
  }

  const updatePrescription = async (id: number, data: Partial<Prescription>) => {
    const response = await api.put(`/prescriptions/${id}`, data)
    if (response?.data) {
      const index = prescriptions.value.findIndex(p => p.id === id)
      if (index !== -1) {
        prescriptions.value[index] = response.data
      }
      if (currentPrescription.value?.id === id) {
        currentPrescription.value = response.data
      }
    }
    return response
  }

  const cancelPrescription = async (id: number, reason?: string) => {
    const response = await api.post(`/api/prescriptions/${id}/cancel`, { reason })
    if (response?.data) {
      const index = prescriptions.value.findIndex(p => p.id === id)
      if (index !== -1) {
        prescriptions.value[index] = response.data
      }
      if (currentPrescription.value?.id === id) {
        currentPrescription.value = response.data
      }
    }
    return response
  }

  const getPrescriptionHistory = async (patientId: number, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    return await api.get(`/api/prescriptions/patient/${patientId}/history${queryString}`)
  }

  // Medication methods
  const getMedications = async (params?: Record<string, any>) => {
    let queryString = ''
    if (params) {
      const filteredParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          filteredParams.append(key, value.toString())
        }
      })
      if (filteredParams.toString()) {
        queryString = '?' + filteredParams.toString()
      }
    }
    const response = await api.get(`/medications${queryString}`)
    if (response?.data) {
      medications.value = response.data.data || response.data
    }
    return response
  }

  const getMedication = async (id: number) => {
    return await api.get(`/medications/${id}`)
  }

  const searchMedications = async (query: string, limit?: number) => {
    const params = new URLSearchParams()
    if (query && query.trim()) {
      params.append('q', query.trim())
    }
    if (limit && limit > 0) {
      params.append('limit', limit.toString())
    }
    return await api.get(`/medications/search?${params.toString()}`)
  }

  const getDrugClasses = async () => {
    return await api.get('/medications/drug-classes')
  }

  const getMedicationForms = async () => {
    return await api.get('/medications/forms')
  }

  const getMedicationRoutes = async () => {
    return await api.get('/medications/routes')
  }

  const checkDrugInteractions = async (medicationIds: number[]) => {
    return await api.post('/medications/check-interactions', { medication_ids: medicationIds })
  }

  // Refill request methods
  const getRefillRequests = async (params?: Record<string, any>) => {
    let queryString = ''
    if (params) {
      const filteredParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          filteredParams.append(key, value.toString())
        }
      })
      if (filteredParams.toString()) {
        queryString = '?' + filteredParams.toString()
      }
    }
    const response = await api.get(`/prescription-refills${queryString}`)
    if (response?.data) {
      refillRequests.value = response.data.data || response.data
    }
    return response
  }

  const getRefillRequest = async (id: number) => {
    return await api.get(`/api/prescription-refills/${id}`)
  }

  const createRefillRequest = async (data: Partial<PrescriptionRefillRequest>) => {
    const response = await api.post('/api/prescription-refills', data)
    if (response?.data) {
      refillRequests.value.unshift(response.data)
    }
    return response
  }

  const approveRefillRequest = async (id: number, data: { review_notes?: string; create_new_prescription?: boolean; modifications?: any }) => {
    const response = await api.post(`/api/prescription-refills/${id}/approve`, data)
    if (response?.data) {
      const index = refillRequests.value.findIndex(r => r.id === id)
      if (index !== -1) {
        refillRequests.value[index] = response.data
      }
    }
    return response
  }

  const rejectRefillRequest = async (id: number, data: { rejection_reason: string; review_notes?: string }) => {
    const response = await api.post(`/api/prescription-refills/${id}/reject`, data)
    if (response?.data) {
      const index = refillRequests.value.findIndex(r => r.id === id)
      if (index !== -1) {
        refillRequests.value[index] = response.data
      }
    }
    return response
  }

  const getPendingRefillCount = async () => {
    return await api.get('/api/prescription-refills/pending/count')
  }

  return {
    // State
    prescriptions,
    currentPrescription,
    medications,
    refillRequests,
    loading: api.loading,
    error: api.error,

    // Prescription methods
    getPrescriptions,
    getPrescription,
    createPrescription,
    updatePrescription,
    cancelPrescription,
    getPrescriptionHistory,

    // Medication methods
    getMedications,
    getMedication,
    searchMedications,
    getDrugClasses,
    getMedicationForms,
    getMedicationRoutes,
    checkDrugInteractions,

    // Refill request methods
    getRefillRequests,
    getRefillRequest,
    createRefillRequest,
    approveRefillRequest,
    rejectRefillRequest,
    getPendingRefillCount,

    // Utility methods
    reset: api.reset,
    clearError: api.clearError
  }
}
