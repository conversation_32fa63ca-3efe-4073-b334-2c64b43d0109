import { ref, computed } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'
import { useCountryManager } from './useCountryManager'

// Global currency state
const countries = ref([])
const userCountry = ref(null)
const isLoading = ref(false)

export function useCurrency() {
    const page = usePage()
    const { getEffectiveCountryCode, effectiveCountry, isTestingMode } = useCountryManager()

    // Get current user's country from auth data (with test country override)
    const currentUser = computed(() => page.props.auth?.user || null)
    const currentUserCountryCode = computed(() => getEffectiveCountryCode())
    
    // Currency symbols mapping
    const currencySymbols = {
        'USD': '$',
        'GBP': '£',
        'EUR': '€',
        'INR': '₹',
        'CAD': 'C$',
        'AUD': 'A$',
        'NGN': '₦',
        'ZAR': 'R'
    }
    
    // Get country data by country code
    const getCountryByCode = (countryCode) => {
        return countries.value.find(country => country.code === countryCode) || null
    }
    
    // Get current user's country data
    const getCurrentUserCountry = computed(() => {
        if (!currentUserCountryCode.value) return null
        return getCountryByCode(currentUserCountryCode.value)
    })
    
    // Get currency symbol for a country code
    const getCurrencySymbol = (countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        
        if (country && country.currency_symbol) {
            return country.currency_symbol
        }
        
        // Fallback to mapping
        const currencyCode = country?.currency_code || 'GBP'
        return currencySymbols[currencyCode] || '£'
    }
    
    // Get currency code for a country code
    const getCurrencyCode = (countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        return country?.currency_code || 'GBP'
    }
    
    // Format amount with currency symbol
    const formatCurrency = (amount, countryCode = null, options = {}) => {
        const {
            showSymbol = true,
            decimals = 2,
            useIntl = true
        } = options
        
        const numAmount = parseFloat(amount) || 0
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        const currencyCode = country?.currency_code || 'GBP'
        const symbol = country?.currency_symbol || getCurrencySymbol(code)
        
        if (useIntl && typeof Intl !== 'undefined') {
            try {
                // Use Intl.NumberFormat for proper localization
                const locale = getLocaleForCountry(code)
                const formatter = new Intl.NumberFormat(locale, {
                    style: showSymbol ? 'currency' : 'decimal',
                    currency: currencyCode,
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                })
                return formatter.format(numAmount)
            } catch (error) {
                console.warn('Intl.NumberFormat failed, falling back to manual formatting:', error)
            }
        }
        
        // Fallback manual formatting
        const formattedAmount = numAmount.toFixed(decimals)
        return showSymbol ? `${symbol}${formattedAmount}` : formattedAmount
    }
    
    // Get locale for country code
    const getLocaleForCountry = (countryCode) => {
        const localeMap = {
            'US': 'en-US',
            'GB': 'en-GB',
            'IN': 'en-IN',
            'CA': 'en-CA',
            'AU': 'en-AU',
            'DE': 'de-DE',
            'FR': 'fr-FR',
            'NG': 'en-NG',
            'ZA': 'en-ZA'
        }
        return localeMap[countryCode] || 'en-GB'
    }

    // Format amount for Razorpay (in paise for INR)
    const formatForRazorpay = (amount, countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        const currencyCode = country?.currency_code || 'GBP'

        // For INR, convert to paise (multiply by 100)
        if (currencyCode === 'INR') {
            return Math.round(amount * 100)
        }

        // For other currencies, convert to smallest unit
        return Math.round(amount * 100)
    }

    // Convert from Razorpay format (paise to rupees for INR)
    const convertFromRazorpay = (amount, countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        const currencyCode = country?.currency_code || 'GBP'

        // For INR, convert from paise (divide by 100)
        if (currencyCode === 'INR') {
            return amount / 100
        }

        // For other currencies, convert from smallest unit
        return amount / 100
    }
    
    // Format price for display (commonly used function)
    const formatPrice = (price, countryCode = null) => {
        return formatCurrency(price, countryCode, { showSymbol: true, decimals: 2 })
    }
    
    // Format amount without currency symbol
    const formatAmount = (amount, countryCode = null, decimals = 2) => {
        return formatCurrency(amount, countryCode, { showSymbol: false, decimals })
    }
    
    // Load countries data
    const loadCountries = async () => {
        if (countries.value.length > 0) return countries.value
        
        isLoading.value = true
        try {
            const response = await axios.get('/api/countries')
            if (response.data.success) {
                countries.value = response.data.data
            }
        } catch (error) {
            console.error('Failed to load countries:', error)
        } finally {
            isLoading.value = false
        }
        
        return countries.value
    }
    
    // Load user's current country info (only for authenticated users)
    const loadUserCountry = async () => {
        // Check if user is authenticated before making the call
        if (!currentUser.value) {
            console.log('Skipping user country load - user not authenticated')
            return null
        }

        try {
            const response = await axios.get('/api/user/country')
            if (response.data.success) {
                userCountry.value = response.data.data.country
                return response.data.data
            }
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('User country requires authentication - user not logged in')
            } else {
                console.error('Failed to load user country:', error)
            }
        }
        return null
    }
    
    // Get user's country information
    const getUserCountryInfo = (countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        
        if (!country) {
            return {
                name: 'United Kingdom',
                code: 'GB',
                currency_code: 'GBP',
                currency_symbol: '£',
                currency_name: 'British Pound'
            }
        }
        
        return {
            name: country.name,
            code: country.code,
            currency_code: country.currency_code,
            currency_symbol: country.currency_symbol,
            currency_name: country.currency_name,
            phone_code: country.phone_code,
            timezone: country.timezone
        }
    }
    
    // Check if user is in a specific country
    const isUserInCountry = (countryCode) => {
        return currentUserCountryCode.value === countryCode
    }
    
    // Get supported payment gateways for user's country
    const getSupportedPaymentGateways = (countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        return country?.supported_payment_gateways || ['stripe']
    }
    
    // Get tax rate for user's country
    const getTaxRate = (countryCode = null) => {
        const code = countryCode || currentUserCountryCode.value
        const country = getCountryByCode(code)
        return parseFloat(country?.tax_rate || '0')
    }
    
    // Calculate price with tax
    const calculatePriceWithTax = (basePrice, countryCode = null) => {
        const taxRate = getTaxRate(countryCode)
        const numPrice = parseFloat(basePrice) || 0
        return numPrice + (numPrice * taxRate / 100)
    }
    
    return {
        // State
        countries,
        userCountry,
        isLoading,
        
        // Computed
        currentUser,
        currentUserCountryCode,
        getCurrentUserCountry,
        
        // Methods
        loadCountries,
        loadUserCountry,
        getCountryByCode,
        getCurrencySymbol,
        getCurrencyCode,
        formatCurrency,
        formatPrice,
        formatAmount,
        getUserCountryInfo,
        isUserInCountry,
        getSupportedPaymentGateways,
        getTaxRate,
        calculatePriceWithTax,
        getLocaleForCountry,
        formatForRazorpay,
        convertFromRazorpay
    }
}
