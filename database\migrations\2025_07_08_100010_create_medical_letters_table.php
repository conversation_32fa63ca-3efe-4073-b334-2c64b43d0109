<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medical_letters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('clinic_id')->nullable()->constrained()->onDelete('set null');
            $table->string('letter_type'); // referral, discharge, sick_note, medical_report, etc.
            $table->string('template_name')->nullable(); // Template used
            $table->string('recipient_name')->nullable(); // Who the letter is addressed to
            $table->string('recipient_title')->nullable(); // Dr., Mr., Ms., etc.
            $table->text('recipient_address')->nullable();
            $table->string('subject');
            $table->text('content'); // Main letter content
            $table->text('additional_notes')->nullable();
            $table->json('letter_data')->nullable(); // Structured data for template
            $table->enum('status', ['draft', 'final', 'sent', 'archived'])->default('draft');
            $table->date('letter_date');
            $table->date('sent_date')->nullable();
            $table->string('sent_method')->nullable(); // email, post, fax, etc.
            $table->boolean('include_signature')->default(true);
            $table->boolean('include_letterhead')->default(true);
            $table->string('file_path')->nullable(); // Generated PDF path
            $table->json('attachments')->nullable(); // Additional attachments
            $table->timestamps();

            // Indexes
            $table->index(['patient_id', 'letter_date']);
            $table->index(['created_by', 'letter_date']);
            $table->index('letter_type');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medical_letters');
    }
};
