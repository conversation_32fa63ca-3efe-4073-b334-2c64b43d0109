<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900">Multi-Country Pricing</h3>
      <button
        @click="addCountry"
        type="button"
        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Country
      </button>
    </div>

    <div v-if="localCountries.length === 0" class="text-center py-8 text-gray-500">
      <p class="text-sm">No country-specific pricing added yet. Click "Add Country" to set different prices for different countries.</p>
    </div>

    <div v-else class="space-y-4">
      <div 
        v-for="(country, index) in localCountries" 
        :key="index"
        class="border border-gray-200 rounded-lg p-4 bg-gray-50"
      >
        <div class="flex items-start justify-between mb-4">
          <h4 class="text-md font-medium text-gray-900">Country Pricing #{{ index + 1 }}</h4>
          <button
            @click="removeCountry(index)"
            type="button"
            class="text-red-600 hover:text-red-800"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Country Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
            <select
              v-model="country.country_code"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm appearance-none bg-no-repeat bg-right pr-10"
              style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
            >
              <option value="">Select Country</option>
              <option v-for="countryOption in availableCountries" :key="countryOption.code" :value="countryOption.code">
                {{ countryOption.name }} ({{ countryOption.code }})
              </option>
            </select>
          </div>

          <!-- Price -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
            <div class="relative">
              <span class="absolute left-3 top-2 text-gray-500 text-sm">{{ getCurrencySymbol(country.country_code) }}</span>
              <input
                v-model="country.price"
                type="number"
                step="0.01"
                min="0"
                :placeholder="defaultPrice || '0.00'"
                class="block w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>

          <!-- Sale Price -->
          <div v-if="showSalePrice">
            <label class="block text-sm font-medium text-gray-700 mb-1">Sale Price</label>
            <div class="relative">
              <span class="absolute left-3 top-2 text-gray-500 text-sm">{{ getCurrencySymbol(country.country_code) }}</span>
              <input
                v-model="country.sale_price"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                class="block w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>
        </div>

        <!-- Currency and Tax Info -->
        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
            <input
              v-model="country.currency"
              type="text"
              :placeholder="getDefaultCurrency(country.country_code)"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Tax Rate (%)</label>
            <input
              v-model="country.tax_rate"
              type="number"
              step="0.01"
              min="0"
              max="100"
              placeholder="0.00"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  defaultPrice: {
    type: [String, Number],
    default: null
  },
  showSalePrice: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const localCountries = ref([...props.modelValue])

// Common countries with their currencies
const availableCountries = ref([
  { code: 'US', name: 'United States', currency: 'USD', symbol: '$' },
  { code: 'GB', name: 'United Kingdom', currency: 'GBP', symbol: '£' },
  { code: 'IN', name: 'India', currency: 'INR', symbol: '₹' },
  { code: 'CA', name: 'Canada', currency: 'CAD', symbol: 'C$' },
  { code: 'AU', name: 'Australia', currency: 'AUD', symbol: 'A$' },
  { code: 'DE', name: 'Germany', currency: 'EUR', symbol: '€' },
  { code: 'FR', name: 'France', currency: 'EUR', symbol: '€' },
  { code: 'JP', name: 'Japan', currency: 'JPY', symbol: '¥' },
  { code: 'CN', name: 'China', currency: 'CNY', symbol: '¥' },
  { code: 'SG', name: 'Singapore', currency: 'SGD', symbol: 'S$' },
  { code: 'AE', name: 'United Arab Emirates', currency: 'AED', symbol: 'د.إ' },
  { code: 'SA', name: 'Saudi Arabia', currency: 'SAR', symbol: 'ر.س' },
])

const addCountry = () => {
  localCountries.value.push({
    country_code: '',
    price: '',
    sale_price: '',
    currency: '',
    tax_rate: ''
  })
  emit('update:modelValue', localCountries.value)
}

const removeCountry = (index) => {
  localCountries.value.splice(index, 1)
  emit('update:modelValue', localCountries.value)
}

const getCurrencySymbol = (countryCode) => {
  const country = availableCountries.value.find(c => c.code === countryCode)
  return country ? country.symbol : '$'
}

const getDefaultCurrency = (countryCode) => {
  const country = availableCountries.value.find(c => c.code === countryCode)
  return country ? country.currency : 'USD'
}

// Watch for changes in localCountries and emit updates
watch(localCountries, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// Watch for changes in modelValue from parent
watch(() => props.modelValue, (newValue) => {
  localCountries.value = [...newValue]
}, { deep: true })
</script>