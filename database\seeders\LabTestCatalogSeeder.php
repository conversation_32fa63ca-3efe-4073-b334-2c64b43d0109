<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LabTestCatalogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tests = [
            // Hematology Tests
            [
                'test_code' => 'FBC001',
                'test_name' => 'Full Blood Count',
                'category' => 'Hematology',
                'price' => 25.00,
                'description' => 'Complete blood count including red cells, white cells, and platelets',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],
            [
                'test_code' => 'ESR001',
                'test_name' => 'Erythrocyte Sedimentation Rate',
                'category' => 'Hematology',
                'price' => 15.00,
                'description' => 'Measures inflammation in the body',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],

            // Biochemistry Tests
            [
                'test_code' => 'LFT001',
                'test_name' => 'Liver Function Tests',
                'category' => 'Biochemistry',
                'price' => 35.00,
                'description' => 'Comprehensive liver function assessment',
                'requirements' => json_encode([
                    'fasting_required' => true,
                    'fasting_hours' => 12,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24-48 hours',
                    'special_instructions' => 'Fast for 12 hours before test'
                ]),
                'is_active' => true,
            ],
            [
                'test_code' => 'RFT001',
                'test_name' => 'Renal Function Tests',
                'category' => 'Biochemistry',
                'price' => 30.00,
                'description' => 'Kidney function assessment including creatinine and urea',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],
            [
                'test_code' => 'GLUC001',
                'test_name' => 'Fasting Glucose',
                'category' => 'Biochemistry',
                'price' => 20.00,
                'description' => 'Blood glucose level after fasting',
                'requirements' => json_encode([
                    'fasting_required' => true,
                    'fasting_hours' => 8,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24 hours',
                    'special_instructions' => 'Fast for 8-12 hours before test'
                ]),
                'is_active' => true,
            ],

            // Lipid Profile
            [
                'test_code' => 'LIPID001',
                'test_name' => 'Lipid Profile',
                'category' => 'Biochemistry',
                'price' => 40.00,
                'description' => 'Cholesterol, triglycerides, HDL, and LDL levels',
                'requirements' => json_encode([
                    'fasting_required' => true,
                    'fasting_hours' => 12,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24-48 hours',
                    'special_instructions' => 'Fast for 12 hours, avoid alcohol 24 hours before test'
                ]),
                'is_active' => true,
            ],

            // Thyroid Tests
            [
                'test_code' => 'TSH001',
                'test_name' => 'Thyroid Stimulating Hormone',
                'category' => 'Endocrinology',
                'price' => 25.00,
                'description' => 'TSH level to assess thyroid function',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24-48 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],
            [
                'test_code' => 'T4001',
                'test_name' => 'Free T4',
                'category' => 'Endocrinology',
                'price' => 30.00,
                'description' => 'Free thyroxine level',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24-48 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],

            // Cardiac Markers
            [
                'test_code' => 'TROP001',
                'test_name' => 'Troponin I',
                'category' => 'Cardiology',
                'price' => 45.00,
                'description' => 'Cardiac troponin for heart attack diagnosis',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '2-4 hours',
                    'special_instructions' => 'Urgent test - results available within 4 hours'
                ]),
                'is_active' => true,
            ],

            // Inflammatory Markers
            [
                'test_code' => 'CRP001',
                'test_name' => 'C-Reactive Protein',
                'category' => 'Immunology',
                'price' => 20.00,
                'description' => 'Inflammatory marker',
                'requirements' => json_encode([
                    'fasting_required' => false,
                    'sample_type' => 'blood',
                    'turnaround_time' => '24 hours',
                    'special_instructions' => 'No special preparation required'
                ]),
                'is_active' => true,
            ],
        ];

        foreach ($tests as $test) {
            DB::table('lab_test_catalog')->insert(array_merge($test, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
