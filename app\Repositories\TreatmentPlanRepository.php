<?php

namespace App\Repositories;

use App\Models\TreatmentPlan;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class TreatmentPlanRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param TreatmentPlan $model
     * @return void
     */
    public function __construct(TreatmentPlan $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find treatment plans by patient.
     *
     * @param int $patientId
     * @return Collection
     */
    public function findByPatient(int $patientId): Collection
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Find treatment plans by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->findBy(['provider_id' => $providerId]);
    }
    
    /**
     * Find treatment plans by consultation.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findByConsultation(int $consultationId): Collection
    {
        return $this->findBy(['consultation_id' => $consultationId]);
    }
    
    /**
     * Find active treatment plans.
     *
     * @param int|null $patientId
     * @return Collection
     */
    public function findActive(?int $patientId = null): Collection
    {
        $criteria = ['status' => 'active'];
        
        if ($patientId) {
            $criteria['patient_id'] = $patientId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find treatment plans by status.
     *
     * @param string $status
     * @param int|null $patientId
     * @return Collection
     */
    public function findByStatus(string $status, ?int $patientId = null): Collection
    {
        $criteria = ['status' => $status];
        
        if ($patientId) {
            $criteria['patient_id'] = $patientId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find treatment plans by type.
     *
     * @param string $type
     * @param int|null $patientId
     * @return Collection
     */
    public function findByType(string $type, ?int $patientId = null): Collection
    {
        $criteria = ['plan_type' => $type];
        
        if ($patientId) {
            $criteria['patient_id'] = $patientId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find treatment plans requiring review.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findRequiringReview(?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', 'active')
              ->where('next_review_date', '<=', now());
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->get();
    }
    
    /**
     * Get treatment plans with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['patient.user', 'provider.user', 'consultation']);
        
        // Apply filters
        if (!empty($filters['patient_id'])) {
            $query->where('patient_id', $filters['patient_id']);
        }
        
        if (!empty($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }
        
        if (!empty($filters['consultation_id'])) {
            $query->where('consultation_id', $filters['consultation_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['plan_type'])) {
            $query->where('plan_type', $filters['plan_type']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->whereDate('start_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('start_date', '<=', $filters['date_to']);
        }
        
        if (isset($filters['requires_review'])) {
            if ($filters['requires_review']) {
                $query->where('next_review_date', '<=', now());
            } else {
                $query->where('next_review_date', '>', now());
            }
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Get treatment plan statistics.
     *
     * @param int|null $providerId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getStatistics(?int $providerId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = $this->model->newQuery();
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        
        $total = $query->count();
        $active = $query->where('status', 'active')->count();
        $completed = $query->where('status', 'completed')->count();
        $cancelled = $query->where('status', 'cancelled')->count();
        $requiresReview = $query->where('status', 'active')
                               ->where('next_review_date', '<=', now())
                               ->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'completed' => $completed,
            'cancelled' => $cancelled,
            'requires_review' => $requiresReview,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }
}
