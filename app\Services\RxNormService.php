<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class RxNormService
{
    private string $baseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->baseUrl = config('services.rxnorm.api_url');
        $this->timeout = config('services.rxnorm.timeout');
    }

    /**
     * Search for drugs using approximate term matching
     */
    public function searchDrugs(string $query, int $maxEntries = 20): array
    {
        if (strlen($query) < 2) {
            return [];
        }

        $cacheKey = "rxnorm_search_" . md5($query . $maxEntries);
        
        return Cache::remember($cacheKey, 3600, function () use ($query, $maxEntries) {
            try {
                $response = Http::timeout($this->timeout)
                    ->get("{$this->baseUrl}/approximateTerm.json", [
                        'term' => $query,
                        'maxEntries' => $maxEntries
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    $candidates = $data['approximateGroup']['candidate'] ?? [];
                    
                    return $this->formatSearchResults($candidates);
                }

                return [];

            } catch (\Exception $e) {
                return [];
            }
        });
    }

    /**
     * Get drug details by RxCUI
     */
    public function getDrugDetails(string $rxcui): ?array
    {
        $cacheKey = "rxnorm_details_" . $rxcui;
        
        return Cache::remember($cacheKey, 7200, function () use ($rxcui) {
            try {
                // Get drug properties
                $propertiesResponse = Http::timeout($this->timeout)
                    ->get("{$this->baseUrl}/rxcui/{$rxcui}/properties.json");

                // Get related drugs
                $relatedResponse = Http::timeout($this->timeout)
                    ->get("{$this->baseUrl}/rxcui/{$rxcui}/related.json", [
                        'tty' => 'SCD+SBD+GPCK+BPCK'
                    ]);

                $details = [];

                if ($propertiesResponse->successful()) {
                    $properties = $propertiesResponse->json();
                    $details['properties'] = $properties['properties'] ?? null;
                }

                if ($relatedResponse->successful()) {
                    $related = $relatedResponse->json();
                    $details['related'] = $related['relatedGroup'] ?? null;
                }

                return $details;

            } catch (\Exception $e) {
                return null;
            }
        });
    }

    /**
     * Get drug interactions for multiple RxCUIs
     */
    public function getDrugInteractions(array $rxcuis): array
    {
        if (empty($rxcuis)) {
            return [];
        }

        $cacheKey = "rxnorm_interactions_" . md5(implode(',', $rxcuis));
        
        return Cache::remember($cacheKey, 3600, function () use ($rxcuis) {
            try {
                $rxcuiList = implode('+', $rxcuis);
                
                $response = Http::timeout($this->timeout)
                    ->get("{$this->baseUrl}/interaction/list.json", [
                        'rxcuis' => $rxcuiList
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['fullInteractionTypeGroup'] ?? [];
                }

                return [];

            } catch (\Exception $e) {
                return [];
            }
        });
    }

    /**
     * Get drug spelling suggestions
     */
    public function getSpellingSuggestions(string $query): array
    {
        if (strlen($query) < 2) {
            return [];
        }

        try {
            $response = Http::timeout($this->timeout)
                ->get("{$this->baseUrl}/spellingsuggestions.json", [
                    'name' => $query
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['suggestionGroup']['suggestionList']['suggestion'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Format search results for consistent output
     */
    private function formatSearchResults(array $candidates): array
    {
        $results = [];
        $seen = [];

        foreach ($candidates as $candidate) {
            $rxcui = $candidate['rxcui'] ?? '';
            $name = $candidate['candidate'] ?? '';
            $score = $candidate['score'] ?? 0;

            // Skip duplicates
            if (empty($rxcui) || empty($name) || isset($seen[$rxcui])) {
                continue;
            }

            $seen[$rxcui] = true;

            $results[] = [
                'rxcui' => $rxcui,
                'name' => $name,
                'score' => $score,
                'display_name' => $this->formatDisplayName($name)
            ];
        }

        // Sort by score (highest first)
        usort($results, function ($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        return $results;
    }

    /**
     * Format display name for better readability
     */
    private function formatDisplayName(string $name): string
    {
        return $name;
    }

    /**
     * Get drug classes for a given RxCUI
     */
    public function getDrugClasses(string $rxcui): array
    {
        $cacheKey = "rxnorm_classes_" . $rxcui;
        
        return Cache::remember($cacheKey, 7200, function () use ($rxcui) {
            try {
                $response = Http::timeout($this->timeout)
                    ->get("{$this->baseUrl}/rxclass/class/byRxcui.json", [
                        'rxcui' => $rxcui,
                        'relaSource' => 'ATC'
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['rxclassDrugInfoList']['rxclassDrugInfo'] ?? [];
                }

                return [];

            } catch (\Exception $e) {
                return [];
            }
        });
    }

    /**
     * Get NDC codes for a given RxCUI
     */
    public function getNDCCodes(string $rxcui): array
    {
        try {
            $response = Http::timeout($this->timeout)
                ->get("{$this->baseUrl}/rxcui/{$rxcui}/ndcs.json");

            if ($response->successful()) {
                $data = $response->json();
                return $data['ndcGroup']['ndcList']['ndc'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            return [];
        }
    }
}
