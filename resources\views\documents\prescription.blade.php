@extends('letterheads.common')

@section('document_content')
    <div class="document-info">
        <div class="left">
            <strong>PRESCRIPTION</strong><br>
            Ref: RX-{{ str_pad($prescription->id, 6, '0', STR_PAD_LEFT) }}
        </div>
        <div class="right">
            {{ now()->format('d/m/Y') }}<br>
            {{ now()->format('g:i A') }}
        </div>
    </div>

    <div class="patient-details">
        <h3>👤 Patient Details</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <p><strong>Name:</strong> {{ $prescription->patient->name }}</p>
                <p><strong>Date of Birth:</strong> {{ $prescription->patient->date_of_birth ? \Carbon\Carbon::parse($prescription->patient->date_of_birth)->format('d/m/Y') : 'Not specified' }}</p>
                @if($prescription->patient->nhs_number)
                    <p><strong>NHS Number:</strong> {{ $prescription->patient->nhs_number }}</p>
                @endif
                @if($prescription->patient->unique_id)
                    <p><strong>Patient ID:</strong> {{ $prescription->patient->unique_id }}</p>
                @endif
            </div>
            <div>
                @if($prescription->patient->address)
                    <p><strong>Address:</strong><br>
                    {{ $prescription->patient->address }}
                    @if($prescription->patient->city), {{ $prescription->patient->city }}@endif
                    @if($prescription->patient->postal_code)<br>{{ $prescription->patient->postal_code }}@endif
                    </p>
                @endif
                @if($prescription->patient->phone)
                    <p><strong>Phone:</strong> {{ $prescription->patient->phone }}</p>
                @endif
            </div>
        </div>
    </div>

    <div style="margin-bottom: 30px;">
        <h3 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">
            💊 Prescribed Medications
        </h3>
        
        @forelse($prescription->items as $index => $item)
            <div style="margin-bottom: 25px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #fafafa;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: #007bff; font-size: 14px;">
                        {{ $index + 1 }}. {{ $item->medication_name }}
                        @if($item->form)
                            <span style="color: #666; font-weight: normal;">({{ $item->form }})</span>
                        @endif
                    </h4>
                    @if($item->route)
                        <span style="background-color: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px;">
                            {{ strtoupper($item->route) }}
                        </span>
                    @endif
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 10px;">
                    @if($item->strength)
                        <p style="margin: 0;"><strong>Dose:</strong> {{ $item->strength }}</p>
                    @endif
                    @if($item->frequency)
                        <p style="margin: 0;"><strong>Frequency:</strong> {{ $item->frequency }}</p>
                    @endif
                    @if($item->duration_days)
                        <p style="margin: 0;"><strong>Duration:</strong> {{ $item->duration_days }} days</p>
                    @endif
                    @if($item->quantity)
                        <p style="margin: 0;"><strong>Quantity:</strong> {{ $item->quantity }} {{ $item->quantity_unit ?? 'units' }}</p>
                    @endif
                </div>
                
                @if($item->directions_for_use)
                    <p style="margin: 10px 0 0 0;"><strong>Directions:</strong> {{ $item->directions_for_use }}</p>
                @endif
                
                @if($item->additional_instructions)
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 8px; margin-top: 10px;">
                        <strong style="color: #856404;">Special Instructions:</strong> {{ $item->additional_instructions }}
                    </div>
                @endif
                
                @if($item->warnings)
                    <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 8px; margin-top: 10px;">
                        <strong style="color: #721c24;">⚠️ Warnings:</strong> {{ $item->warnings }}
                    </div>
                @endif
            </div>
        @empty
            <p style="text-align: center; color: #666; font-style: italic;">No medications prescribed.</p>
        @endforelse
    </div>

    @if($prescription->clinical_indication)
        <div style="margin-bottom: 30px; padding: 15px; background-color: #e7f3ff; border-left: 4px solid #007bff;">
            <h4 style="margin-top: 0; color: #333;">Clinical Indication</h4>
            <p style="margin-bottom: 0;">{{ $prescription->clinical_indication }}</p>
        </div>
    @endif

    @if($prescription->additional_instructions)
        <div style="margin-bottom: 30px; padding: 15px; background-color: #fff3cd; border-left: 4px solid #ffc107;">
            <h4 style="margin-top: 0; color: #333;">Additional Instructions</h4>
            <p style="margin-bottom: 0;">{{ $prescription->additional_instructions }}</p>
        </div>
    @endif

    <div class="provider-signature">
        <p><strong>{{ $prescription->provider->name }}</strong></p>
        @if($prescription->provider->qualification)
            <p>{{ $prescription->provider->qualification }}</p>
        @else
            <p>General Practitioner</p>
        @endif
        @if($prescription->provider->registration_number)
            <p>Registration No: {{ $prescription->provider->registration_number }}</p>
        @endif
        @if($prescription->provider->signature_path)
            <img src="{{ asset('storage/' . $prescription->provider->signature_path) }}" alt="Digital Signature">
        @endif
        <p><small>Digitally signed on {{ now()->format('d/m/Y, g:i A') }}</small></p>
    </div>

    <div style="margin-top: 40px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; text-align: center; font-size: 11px; color: #666;">
        <p style="margin: 0;"><strong>Important:</strong> In case of emergency, contact NHS 111 or 999 or your local emergency services</p>
        @if($prescription->valid_until)
            <p style="margin: 5px 0 0 0;">This prescription is valid until: {{ \Carbon\Carbon::parse($prescription->valid_until)->format('d/m/Y') }}</p>
        @endif
    </div>
@endsection
