<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClinicEmailTemplate extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'system_template_id',
        'subject',
        'content',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'is_active' => true,
    ];

    /**
     * Get the clinic that owns this template customization.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the system template this customization is based on.
     */
    public function systemTemplate()
    {
        return $this->belongsTo(EmailTemplate::class, 'system_template_id');
    }

    /**
     * Scope to filter active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter templates by clinic.
     */
    public function scopeForClinic($query, $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }

    /**
     * Get clinic template by clinic and system template slug.
     */
    public static function getByClinicAndSlug($clinicId, $slug)
    {
        return static::whereHas('systemTemplate', function ($query) use ($slug) {
            $query->where('slug', $slug);
        })
        ->where('clinic_id', $clinicId)
        ->where('is_active', true)
        ->first();
    }

    /**
     * Create or update clinic template customization.
     */
    public static function createOrUpdateCustomization($clinicId, $systemTemplateId, $data)
    {
        return static::updateOrCreate(
            [
                'clinic_id' => $clinicId,
                'system_template_id' => $systemTemplateId,
            ],
            [
                'subject' => $data['subject'],
                'content' => $data['content'],
                'is_active' => $data['is_active'] ?? true,
            ]
        );
    }
}
