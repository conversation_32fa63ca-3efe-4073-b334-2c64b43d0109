<template>
  <!-- Clean Prescription Component -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Header -->
    <div class="bg-green-50 px-4 sm:px-6 py-4 border-b border-green-100">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
            </svg>
          </div>
          <div class="flex items-center gap-3">
            <h2 class="text-lg font-semibold text-gray-900">Prescriptions</h2>
          </div>
        </div>
        <div class="flex gap-2 w-full sm:w-auto">
          <button @click="viewAllPrescriptions"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm flex-1 sm:flex-initial"
            :disabled="prescriptions.length === 0">
            <span class="hidden sm:inline">View Prescriptions</span>
            <span class="sm:hidden">View</span>
          </button>
          <button @click="handleAddPrescriptionForm"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm flex-1 sm:flex-initial">
            <span class="hidden sm:inline">Add Medication</span>
            <span class="sm:hidden">Add</span>
          </button>
        </div>
      </div>
    </div>
    <!-- Prescription List -->
    <div class="p-4 sm:p-6">
      <div v-if="prescriptions.length === 0" class="text-center py-8 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-3 text-gray-300">
          <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
        </svg>
        <p class="text-sm">No prescriptions added yet</p>
      </div>

      <div v-else class="space-y-3">
        <div v-for="prescription in prescriptions" :key="prescription.id"
          class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ prescription.medication_name }}</h4>
              <div class="flex items-center gap-4 mt-1 text-sm text-gray-600">
                <span>{{ prescription.frequency }}</span>
                <span>•</span>
                <span>{{ formatDuration(prescription.duration) }}</span>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <!-- View Button -->
              <button
                @click="viewPrescription(prescription.id)"
                class="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                title="View with Letterhead"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
              </button>

              <!-- Quick Actions for Desktop -->
              <div class="hidden sm:flex items-center gap-1">
                <!-- PDF Button -->
                <button
                  @click="quickGeneratePDF(prescription.id)"
                  :disabled="pdfLoading[prescription.id]"
                  class="p-2 text-green-500 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50"
                  title="Quick PDF Download"
                >
                  <svg v-if="pdfLoading[prescription.id]" class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" x2="12" y1="15" y2="3"/>
                  </svg>
                </button>

                <!-- Email Button -->
                <button
                  @click="quickEmailPrescription(prescription.id)"
                  :disabled="emailLoading[prescription.id]"
                  class="p-2 text-purple-500 hover:bg-purple-50 rounded-lg transition-colors disabled:opacity-50"
                  title="Quick Email"
                >
                  <svg v-if="emailLoading[prescription.id]" class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                  </svg>
                </button>

                <!-- Share Button -->
                <button
                  @click="sharePrescription(prescription)"
                  class="p-2 text-orange-500 hover:bg-orange-50 rounded-lg transition-colors"
                  title="Share Prescription"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                    <polyline points="16 6 12 2 8 6"/>
                    <line x1="12" x2="12" y1="2" y2="15"/>
                  </svg>
                </button>

                <!-- Delete Button -->
                <button @click="deletePrescription(prescription.id)"
                  class="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"/>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                  </svg>
                </button>
              </div>

              <!-- Mobile Menu -->
              <div class="sm:hidden relative">
                <button
                  @click="toggleMobileMenu(prescription.id)"
                  class="p-2 text-gray-500 hover:text-gray-700"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>

                <div v-if="showMobileMenu[prescription.id]" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <button @click="viewPrescription(prescription.id); hideMobileMenu(prescription.id)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    👁️ View
                  </button>
                  <button @click="quickGeneratePDF(prescription.id); hideMobileMenu(prescription.id)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📄 PDF
                  </button>
                  <button @click="quickEmailPrescription(prescription.id); hideMobileMenu(prescription.id)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📧 Email
                  </button>
                  <button @click="deletePrescription(prescription.id); hideMobileMenu(prescription.id)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    🗑️ Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- Modal -->
    <div v-if="isAddPrescriptionModalOpen" class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4" @click.self="isAddPrescriptionModalOpen = false">
      <div class="w-full max-w-4xl bg-white rounded-2xl shadow-2xl max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-teal-500 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                <path d="M12 17h.01"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-teal-900">Add New Prescription</h3>
          </div>
          <button @click="isAddPrescriptionModalOpen = false" class="p-2 hover:bg-teal-200 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-teal-700">
              <path d="M18 6 6 18"></path>
              <path d="m6 6 12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Content -->
        <div class="grid grid-cols-12 max-h-[calc(90vh-80px)] overflow-hidden">
          <!-- Left Column - Prescription List -->
          <div class="col-span-4 border-r border-teal-200 bg-teal-50 p-4 space-y-3 overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
              <h4 class="font-medium text-teal-900">Medications</h4>
              <button @click="addNewPrescriptionForm" class="p-2 text-teal-600 hover:text-teal-700 hover:bg-teal-100 rounded-lg transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14"></path>
                  <path d="M12 5v14"></path>
                </svg>
              </button>
            </div>

            <div v-for="(form, index) in prescriptionForms" :key="form.id"
              class="p-3 rounded-lg flex justify-between items-center cursor-pointer border transition-all"
              :class="{ 'bg-white border-teal-300 shadow-sm ring-1 ring-teal-200': form.isSelected, 'bg-white border-teal-100 hover:border-teal-200': !form.isSelected }"
              @click="selectPrescriptionForm(index)">
              <div class="flex-1">
                <span class="text-sm font-medium text-gray-900 block">{{
                  form.medicationName || "New Medication"
                }}</span>
                <div v-if="form.medicationName" class="text-xs text-teal-600 mt-1">
                  {{ form.dose }} • {{ form.frequency }}
                </div>
              </div>
              <button v-if="prescriptionForms.length > 1" @click.stop="deletePrescriptionForm(index)"
                class="text-coral-500 hover:text-coral-600 p-1 hover:bg-coral-50 rounded transition-colors ml-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" x2="10" y1="11" y2="17"></line>
                  <line x1="14" x2="14" y1="11" y2="17"></line>
                </svg>
              </button>
            </div>
          </div>

          <!-- Right Column - Form -->
          <div class="col-span-8 p-6 space-y-4 overflow-y-auto bg-white">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Medication Name</label>
              <div class="relative">
                <input v-model="searchQuery" @input="debounceSearch"
                  class="w-full px-4 py-3 pl-10 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                  placeholder="Search medication..." type="text" />
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.3-4.3"></path>
                </svg>

                <!-- Search Results Dropdown -->
                <div v-if="searchQuery && searchResults.length > 0"
                  class="absolute z-50 w-full mt-1 bg-white border border-teal-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  <div @click="selectDrug({ name: searchQuery })"
                    class="p-3 hover:bg-teal-50 cursor-pointer border-b border-teal-100 transition-colors">
                    <div class="text-sm font-medium text-gray-900">
                      {{ searchQuery }}
                    </div>
                  </div>
                  <div v-for="result in searchResults" :key="result.ui" @click="selectDrug(result)"
                    class="p-3 hover:bg-teal-50 cursor-pointer border-b border-teal-100 last:border-b-0 transition-colors">
                    <div class="text-sm font-medium text-gray-900">
                      {{ result.name }} - {{ result.rxcui }}
                    </div>
                  </div>
                </div>

                      <!-- No Results Message -->
                      <div v-if="
                        searchQuery &&
                        searchResults.length === 0 &&
                        !isLoading
                      " class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500" @click="selectDrug({ name: searchQuery })">
                          {{ searchQuery }}
                        </div>
                      </div>

                      <!-- Loading State -->
                      <div v-if="isLoading" class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500">Searching...</div>
                      </div>
                    </div>
                  </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Dose</label>
                <input v-model="currentForm.dose"
                  class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                  placeholder="Enter dose" type="text" />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                <input v-model="currentForm.route"
                  class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                  placeholder="Enter route" type="text" />
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                <input v-model="currentForm.frequency"
                  class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                  placeholder="Enter frequency" type="text" />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                <input v-model="currentForm.duration"
                  class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                  placeholder="Enter duration" type="text" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Instructions</label>
              <textarea v-model="currentForm.instructions"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors resize-none"
                placeholder="Enter instructions" rows="3"></textarea>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-between items-center px-6 py-4 bg-gray-50 border-t border-gray-200">
          <button @click="isAddPrescriptionModalOpen = false"
            class="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium">
            Cancel
          </button>
          <button @click="savePrescriptions"
            class="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors font-medium shadow-sm">
            Save All
          </button>
        </div>
      </div>
    </div>

    <!-- Preview Modal -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-4 border-b">
          <h3 class="text-lg font-medium">Prescription Preview</h3>
          <button @click="closePreviewModal" class="text-gray-400 hover:text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="p-4 overflow-auto max-h-[calc(90vh-120px)]">
          <div v-html="previewHtml" class="prescription-preview"></div>
        </div>
        <div class="flex justify-end gap-3 p-4 border-t">
          <button @click="printPrescription" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Print
          </button>
          <button @click="closePreviewModal" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
            Close
          </button>
        </div>
      </div>
    </div>

    <!-- Share Modal -->
    <ShareModal
      v-if="selectedPrescriptionForShare"
      :show="showShareModal"
      :share-type="'prescription'"
      :item-id="selectedPrescriptionForShare.id"
      :consultation-id="encounterId"
      @close="closeShareModal"
      @shared="handlePrescriptionShared"
    />

</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import axios from 'axios';
import ShareModal from './ShareModal.vue';
import { useNotifications } from '@/composables/useNotifications';
// import PrescriptionViewer from '../prescription/PrescriptionViewer.vue';

// Initialize notifications
const { showSuccess, showError } = useNotifications();

// Props
interface Props {
  encounterId: string | number
  isEncounterTemp?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEncounterTemp: false,
});

// Reactive data
const prescriptions = ref([]);
const isAddPrescriptionModalOpen = ref(false);
const showPreviewModal = ref(false);
const previewHtml = ref('');
const showShareModal = ref(false);
const selectedPrescriptionForShare = ref(null);



// Quick actions state
const pdfLoading = reactive({});
const emailLoading = reactive({});
const showMobileMenu = reactive({});

const prescriptionForms = ref([
  {
    id: 1,
    medicationName: "",
    dose: "",
    route: "",
    frequency: "",
    duration: "",
    instructions: "",
    isSelected: true,
  },
]);

const currentFormIndex = ref(0);
const searchQuery = ref("");
const searchResults = ref([]);
const isLoading = ref(false);
let searchTimeout: NodeJS.Timeout | null = null;



// Computed
const currentForm = computed(() => {
  return prescriptionForms.value[currentFormIndex.value] || {};
});

// Lifecycle
onMounted(() => {
  loadPrescriptions();
});

// Methods
const loadPrescriptions = async () => {
  try {
    const response = await axios.get(`/consultation-prescriptions?consultation_id=${props.encounterId}`);
    if (response.data.data) {
      prescriptions.value = response.data.data || [];
    }
  } catch (error) {
    console.error('Error loading prescriptions:', error);
  }
};

const handleAddPrescriptionForm = () => {
  isAddPrescriptionModalOpen.value = true;
};

const addNewPrescriptionForm = () => {
  const newForm = {
    id: Date.now(),
    medicationName: "",
    dose: "",
    route: "",
    frequency: "",
    duration: "",
    instructions: "",
    isSelected: false,
  };

  // Deselect all forms
  prescriptionForms.value.forEach(form => form.isSelected = false);

  // Add and select new form
  newForm.isSelected = true;
  prescriptionForms.value.push(newForm);
  currentFormIndex.value = prescriptionForms.value.length - 1;
};

const selectPrescriptionForm = (index) => {
  prescriptionForms.value.forEach(form => form.isSelected = false);
  prescriptionForms.value[index].isSelected = true;
  currentFormIndex.value = index;
};

const deletePrescriptionForm = (index) => {
  if (prescriptionForms.value.length > 1) {
    prescriptionForms.value.splice(index, 1);
    if (currentFormIndex.value >= prescriptionForms.value.length) {
      currentFormIndex.value = prescriptionForms.value.length - 1;
    }
    prescriptionForms.value[currentFormIndex.value].isSelected = true;
  }
};

const debounceSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  searchTimeout = setTimeout(() => {
    searchMedications();
  }, 300);
};

const searchMedications = () => {
  if (!searchQuery.value.trim() || searchQuery.value.length < 2) {
    searchResults.value = [];
    return;
  }

  // Clear previous timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  // Debounce search
  searchTimeout = setTimeout(async () => {
    await performMedicationSearch();
  }, 300);
};

const performMedicationSearch = async () => {
  isLoading.value = true;

  try {
    const response = await fetch(`/drugs/search?query=${encodeURIComponent(searchQuery.value)}&limit=10`);
    const data = await response.json();

    if (data.success) {
      searchResults.value = data.data.drugs.map(drug => ({
        name: drug.display_name || drug.name,
        rxcui: drug.rxcui,
        ui: drug.rxcui,
        score: drug.score
      }));
    } else {
      searchResults.value = [];
    }
  } catch (error) {
    console.error('Error searching medications:', error);
    searchResults.value = [];
  } finally {
    isLoading.value = false;
  }
};

const selectDrug = (drug) => {
  currentForm.value.medicationName = drug.name;
  searchQuery.value = "";
  searchResults.value = [];
};



const savePrescriptions = async () => {
  try {
    // Save all prescription forms
    const validForms = prescriptionForms.value.filter(form =>
      form.medicationName && form.dose && form.frequency
    );

    if (validForms.length === 0) {
      showError("Please fill in at least medication name, dose, and frequency for one prescription.");
      return;
    }

    // Save each prescription form individually using the consultation-prescriptions API
    const savedPrescriptions = [];

    for (const form of validForms) {
      const prescriptionData = {
        consultation_id: props.encounterId,
        medication_name: form.medicationName,
        dosage: form.dose,
        frequency: form.frequency,
        duration: form.duration,
        instructions: form.instructions || `Take ${form.frequency.toLowerCase()}`,
        status: 'active'
      };

      console.log('Saving prescription:', prescriptionData);

      const response = await axios.post('/consultation-prescriptions', prescriptionData);

      if (response.data) {
        savedPrescriptions.push(response.data.data);
      }
    }

    if (savedPrescriptions.length > 0) {
      console.log('Prescriptions saved successfully:', savedPrescriptions);
      // Reload prescriptions to show the new ones
      await loadPrescriptions();
      isAddPrescriptionModalOpen.value = false;
      resetForms();
      showSuccess(`${savedPrescriptions.length} prescription(s) saved successfully!`);
    } else {
      throw new Error('Failed to save any prescriptions');
    }
  } catch (error) {
    console.error('Error saving prescriptions:', error);
    showError('Failed to save prescriptions. Please try again.');
  }
};

const resetForms = () => {
  prescriptionForms.value = [
    {
      id: 1,
      medicationName: "",
      dose: "",
      route: "",
      frequency: "",
      duration: "",
      instructions: "",
      isSelected: true,
    },
  ];
  currentFormIndex.value = 0;
};

const deletePrescription = (id) => {
  prescriptions.value = prescriptions.value.filter(p => p.id !== id);
};

const formatDuration = (duration) => {
  return duration || 'Not specified';
};





const closePreviewModal = () => {
  showPreviewModal.value = false;
  previewHtml.value = '';
};

const printPrescription = () => {
  // Create a new window for printing
  const printWindow = window.open('', '_blank');
  printWindow.document.write(`
    <html>
      <head>
        <title>Prescription</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        ${previewHtml.value}
      </body>
    </html>
  `);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
};

// Quick action methods
const quickGeneratePDF = async (prescriptionId) => {
  try {
    pdfLoading[prescriptionId] = true;

    // For now, just show the prescription view since PDF generation for consultation prescriptions
    // might need a different endpoint
    await viewPrescription(prescriptionId);
    showSuccess('Prescription view opened. You can print this page using your browser\'s print function (Ctrl+P).');

  } catch (error) {
    console.error('Error generating PDF:', error);
    showError('Failed to generate PDF. Please try again.');
  } finally {
    pdfLoading[prescriptionId] = false;
  }
};

const quickEmailPrescription = async (prescriptionId) => {
  try {
    emailLoading[prescriptionId] = true;

    const prescription = prescriptions.value.find(p => p.id === prescriptionId);
    const patientEmail = prescription?.patient?.email;

    if (!patientEmail) {
      console.error('Patient email not found');
      return;
    }

    const response = await axios.post(`/prescriptions/${prescriptionId}/email`, {
      email: patientEmail,
      message: 'Please find your prescription attached.'
    });

    if (response.data.success) {
      console.log('Prescription emailed successfully');
    }
    console.error('Error sending email:', error);
    console.error('Failed to send email');
  } finally {
    emailLoading[prescriptionId] = false;
  }
};

// Mobile menu methods
const toggleMobileMenu = (prescriptionId) => {
  showMobileMenu[prescriptionId] = !showMobileMenu[prescriptionId];
};

const hideMobileMenu = (prescriptionId) => {
  showMobileMenu[prescriptionId] = false;
};

// View all prescriptions for consultation with letterhead
const viewAllPrescriptions = async () => {
  try {
    if (prescriptions.value.length === 0) {
      showError('No prescriptions to view. Please add some medications first.');
      return;
    }

    // Create a combined prescription view by calling the consultation letterhead API
    const response = await axios.get(`/consultations/${props.encounterId}/prescriptions-letterhead`);

    if (response.data) {
      previewHtml.value = response.data;
      showPreviewModal.value = true;
    } else {
      console.error('No HTML content received');
      showError('Failed to load prescriptions preview');
    }
  } catch (error) {
    console.error('Error loading prescriptions view:', error);
    showError('Failed to load prescriptions preview. Please try again.');
  }
};



// View individual prescription with letterhead
const viewPrescription = async (prescriptionId) => {
  try {
    console.log('Viewing prescription:', prescriptionId);

    // Find the prescription in our local data
    const prescription = prescriptions.value.find(p => p.id === prescriptionId);
    if (!prescription) {
      showError('Prescription not found');
      return;
    }

    // Generate a simple view for this individual prescription
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
          <h1 style="margin: 0; color: #333;">Medical Prescription</h1>
          <p style="margin: 5px 0; color: #666;">Consultation ID: ${props.encounterId}</p>
          <p style="margin: 5px 0; color: #666;">Date: ${new Date().toLocaleDateString()}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h2 style="color: #333; border-bottom: 1px solid #ccc; padding-bottom: 10px;">Prescribed Medication</h2>
          <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${prescription.medication_name || prescription.medicationName}</h3>
            <p style="margin: 5px 0;"><strong>Dosage:</strong> ${prescription.dosage || prescription.dose || 'Not specified'}</p>
            <p style="margin: 5px 0;"><strong>Frequency:</strong> ${prescription.frequency || 'Not specified'}</p>
            <p style="margin: 5px 0;"><strong>Duration:</strong> ${prescription.duration || 'Not specified'}</p>
            ${prescription.instructions || prescription.directions_for_use ?
              `<p style="margin: 5px 0;"><strong>Instructions:</strong> ${prescription.instructions || prescription.directions_for_use}</p>` :
              ''
            }
          </div>
        </div>

        <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
          <p>This prescription is generated electronically and is valid for medical use.</p>
        </div>
      </div>
    `;

    previewHtml.value = html;
    showPreviewModal.value = true;

  } catch (error) {
    console.error('Error loading prescription view:', error);
    showError('Failed to load prescription preview. Please try again.');
  }
};

// Share functions
const sharePrescription = (prescription) => {
  selectedPrescriptionForShare.value = prescription;
  showShareModal.value = true;
};

const closeShareModal = () => {
  showShareModal.value = false;
  selectedPrescriptionForShare.value = null;
};

const handlePrescriptionShared = () => {
  showSuccess('Prescription shared successfully!');
  closeShareModal();
};

</script>
