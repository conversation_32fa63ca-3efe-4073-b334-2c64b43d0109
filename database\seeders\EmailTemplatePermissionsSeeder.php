<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class EmailTemplatePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create email template permissions if they don't exist
        $permissions = [
            'view email templates',
            'edit email templates',
            'manage email templates', // For admin system template management
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to roles
        $clinicAdminRole = Role::where('name', 'clinic_admin')->first();
        if ($clinicAdminRole) {
            $clinicAdminRole->givePermissionTo(['view email templates', 'edit email templates']);
        }

        $providerRole = Role::where('name', 'provider')->first();
        if ($providerRole) {
            $providerRole->givePermissionTo(['view email templates', 'edit email templates']);
        }

        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo(['view email templates', 'edit email templates', 'manage email templates']);
        }

        $this->command->info('Email template permissions created and assigned successfully!');
    }
}
