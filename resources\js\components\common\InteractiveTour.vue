<template>
  <div v-if="show && currentStep" class="fixed inset-0 z-50">
    <!-- Overlay -->
    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
    
    <!-- Spotlight -->
    <div 
      v-if="currentStep.target"
      class="absolute border-4 border-primary rounded-lg"
      :style="spotlightStyle"
    ></div>
    
    <!-- Tour Step Content -->
    <div 
      class="absolute bg-white rounded-lg shadow-xl max-w-sm w-full mx-4 p-6"
      :style="tooltipStyle"
    >
      <!-- Progress Indicator -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex space-x-1">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="w-2 h-2 rounded-full"
            :class="{
              'bg-primary': index <= currentStepIndex,
              'bg-gray-300': index > currentStepIndex
            }"
          ></div>
        </div>
        <span class="text-sm text-gray-500">{{ currentStepIndex + 1 }} / {{ steps.length }}</span>
      </div>

      <!-- Step Content -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          {{ currentStep.title }}
        </h3>
        <p class="text-gray-600 mb-4">
          {{ currentStep.content }}
        </p>
        
        <!-- Step Image/GIF -->
        <div v-if="currentStep.image" class="mb-4">
          <img 
            :src="currentStep.image" 
            :alt="currentStep.title"
            class="w-full rounded-lg border border-gray-200"
          />
        </div>
        
        <!-- Interactive Elements -->
        <div v-if="currentStep.interactive" class="mb-4 p-3 bg-blue-50 rounded-lg">
          <p class="text-sm text-blue-800 mb-2">
            <strong>Try it:</strong> {{ currentStep.interactive.instruction }}
          </p>
          <div v-if="currentStep.interactive.completed" class="flex items-center text-green-600">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm font-medium">Completed!</span>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <button 
          v-if="currentStepIndex > 0"
          @click="previousStep"
          class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200"
        >
          Previous
        </button>
        <div v-else></div>
        
        <div class="flex space-x-2">
          <button 
            @click="skipTour"
            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800"
          >
            Skip Tour
          </button>
          
          <button 
            v-if="currentStepIndex < steps.length - 1"
            @click="nextStep"
            :disabled="currentStep.interactive && !currentStep.interactive.completed"
            class="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ currentStep.interactive && !currentStep.interactive.completed ? 'Complete Action' : 'Next' }}
          </button>
          
          <button 
            v-else
            @click="finishTour"
            class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"
          >
            Finish Tour
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

const props = defineProps({
  show: Boolean,
  steps: {
    type: Array,
    required: true
    // Step format: { title, content, target?, image?, interactive?: { instruction, completed } }
  },
  onComplete: Function,
  onSkip: Function
})

const emit = defineEmits(['complete', 'skip', 'step-change'])

const currentStepIndex = ref(0)

const currentStep = computed(() => props.steps[currentStepIndex.value])

const spotlightStyle = ref({})
const tooltipStyle = ref({})

const updatePositions = async () => {
  if (!currentStep.value?.target) return
  
  await nextTick()
  
  const targetElement = document.querySelector(currentStep.value.target)
  if (!targetElement) return
  
  const rect = targetElement.getBoundingClientRect()
  const padding = 8
  
  // Spotlight positioning
  spotlightStyle.value = {
    left: `${rect.left - padding}px`,
    top: `${rect.top - padding}px`,
    width: `${rect.width + padding * 2}px`,
    height: `${rect.height + padding * 2}px`
  }
  
  // Tooltip positioning
  const tooltipWidth = 384 // max-w-sm = 384px
  const tooltipHeight = 300 // estimated height
  const margin = 16
  
  let left = rect.left + rect.width / 2 - tooltipWidth / 2
  let top = rect.bottom + margin
  
  // Adjust if tooltip goes off screen
  if (left < margin) left = margin
  if (left + tooltipWidth > window.innerWidth - margin) {
    left = window.innerWidth - tooltipWidth - margin
  }
  
  if (top + tooltipHeight > window.innerHeight - margin) {
    top = rect.top - tooltipHeight - margin
  }
  
  tooltipStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  }
}

const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    currentStepIndex.value++
    emit('step-change', currentStepIndex.value)
    updatePositions()
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    emit('step-change', currentStepIndex.value)
    updatePositions()
  }
}

const skipTour = () => {
  emit('skip')
  if (props.onSkip) props.onSkip()
}

const finishTour = () => {
  emit('complete')
  if (props.onComplete) props.onComplete()
}

const handleResize = () => {
  updatePositions()
}

const handleKeydown = (event) => {
  if (!props.show) return
  
  switch (event.key) {
    case 'Escape':
      skipTour()
      break
    case 'ArrowRight':
    case ' ':
      event.preventDefault()
      if (currentStepIndex.value < props.steps.length - 1) {
        nextStep()
      } else {
        finishTour()
      }
      break
    case 'ArrowLeft':
      event.preventDefault()
      previousStep()
      break
  }
}

// Watch for interactive completion
const checkInteractiveCompletion = (stepIndex) => {
  const step = props.steps[stepIndex]
  if (!step?.interactive) return
  
  // Set up observers or event listeners based on the step's requirements
  // This is a simplified example - you'd implement specific logic for each interactive step
  if (step.interactive.trigger) {
    const checkElement = () => {
      const element = document.querySelector(step.interactive.trigger)
      if (element) {
        step.interactive.completed = true
      }
    }
    
    // Check periodically
    const interval = setInterval(() => {
      checkElement()
      if (step.interactive.completed) {
        clearInterval(interval)
      }
    }, 500)
  }
}

onMounted(() => {
  if (props.show) {
    updatePositions()
    checkInteractiveCompletion(currentStepIndex.value)
  }
  
  window.addEventListener('resize', handleResize)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeydown)
})

// Watch for show prop changes
watch(() => props.show, (newVal) => {
  if (newVal) {
    updatePositions()
    checkInteractiveCompletion(currentStepIndex.value)
  }
})

watch(currentStepIndex, (newIndex) => {
  checkInteractiveCompletion(newIndex)
})
</script>
