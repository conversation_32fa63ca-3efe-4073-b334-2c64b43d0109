<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Doctor <PERSON></h1>
      <p class="text-gray-600">Manage your professional settings, digital signature, and letter templates</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Settings Forms -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Professional Information -->
        <Card>
          <CardHeader>
            <CardTitle>Professional Information</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label for="prefix">Professional Prefix</Label>
                <Select v-model="settings.professional_prefix">
                  <SelectTrigger>
                    <SelectValue placeholder="Select prefix" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dr">Dr</SelectItem>
                    <SelectItem value="Prof">Prof</SelectItem>
                    <SelectItem value="Mr">Mr</SelectItem>
                    <SelectItem value="Mrs">Mrs</SelectItem>
                    <SelectItem value="Ms">Ms</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label for="registration_body">Registration Body</Label>
                <Select v-model="settings.registration_body">
                  <SelectTrigger>
                    <SelectValue placeholder="Select body" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GMC">GMC (General Medical Council)</SelectItem>
                    <SelectItem value="NMC">NMC (Nursing and Midwifery Council)</SelectItem>
                    <SelectItem value="GDC">GDC (General Dental Council)</SelectItem>
                    <SelectItem value="GOC">GOC (General Optical Council)</SelectItem>
                    <SelectItem value="HCPC">HCPC (Health and Care Professions Council)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="md:col-span-2">
                <Label for="registration_number">Registration Number</Label>
                <Input
                  v-model="settings.registration_number"
                  placeholder="Enter your professional registration number"
                />
              </div>
            </div>

            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <Checkbox
                  v-model="settings.include_registration_in_letters"
                  id="include_registration"
                />
                <Label for="include_registration">Include registration in letters</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Practice Information -->
        <Card>
          <CardHeader>
            <CardTitle>Practice Information</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <Label for="practice_name">Practice Name</Label>
              <Input
                v-model="settings.practice_name"
                placeholder="Your practice or clinic name"
              />
            </div>

            <div>
              <Label for="practice_address">Practice Address</Label>
              <Textarea
                v-model="settings.practice_address"
                placeholder="Full practice address"
                rows="3"
              />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label for="practice_phone">Practice Phone</Label>
                <Input
                  v-model="settings.practice_phone"
                  placeholder="Practice phone number"
                />
              </div>

              <div>
                <Label for="practice_email">Practice Email</Label>
                <Input
                  v-model="settings.practice_email"
                  type="email"
                  placeholder="Practice email address"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Letter Settings -->
        <Card>
          <CardHeader>
            <CardTitle>Letter Settings</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <Checkbox
                  v-model="settings.include_signature_in_letters"
                  id="include_signature"
                />
                <Label for="include_signature">Include digital signature in letters</Label>
              </div>
            </div>

            <div>
              <Label class="text-base font-medium">Default Letter Settings</Label>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                <div>
                  <Label for="font_family">Font Family</Label>
                  <Select v-model="letterSettings.font_family">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Arial">Arial</SelectItem>
                      <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                      <SelectItem value="Calibri">Calibri</SelectItem>
                      <SelectItem value="Helvetica">Helvetica</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label for="font_size">Font Size</Label>
                  <Input
                    v-model.number="letterSettings.font_size"
                    type="number"
                    min="8"
                    max="16"
                  />
                </div>

                <div>
                  <Label for="line_height">Line Height</Label>
                  <Input
                    v-model.number="letterSettings.line_height"
                    type="number"
                    step="0.1"
                    min="1"
                    max="3"
                  />
                </div>

                <div>
                  <Label for="margin">Margin (mm)</Label>
                  <Input
                    v-model.number="letterSettings.margin_top"
                    type="number"
                    min="10"
                    max="50"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Save Button -->
        <div class="flex justify-end">
          <Button @click="saveSettings" :disabled="loading">
            {{ loading ? 'Saving...' : 'Save Settings' }}
          </Button>
        </div>
      </div>

      <!-- Right Column - Digital Signature & Templates -->
      <div class="space-y-6">
        <!-- Digital Signature -->
        <Card>
          <CardHeader>
            <CardTitle>Digital Signature</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div v-if="hasSignature" class="text-center">
              <div class="border rounded-lg p-4 bg-gray-50">
                <img
                  :src="signatureDataUrl"
                  alt="Digital Signature"
                  class="max-h-20 mx-auto"
                />
              </div>
              <div class="mt-4 space-y-2">
                <Button variant="outline" @click="removeSignature" class="w-full">
                  Remove Signature
                </Button>
              </div>
            </div>

            <div v-else class="text-center">
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <FileImage class="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p class="text-gray-500 mb-4">No digital signature uploaded</p>
                <input
                  ref="signatureInput"
                  type="file"
                  accept="image/*"
                  @change="uploadSignature"
                  class="hidden"
                />
                <Button @click="$refs.signatureInput?.click()" variant="outline">
                  Upload Signature
                </Button>
              </div>
            </div>

            <div class="text-xs text-gray-500">
              <p>Supported formats: PNG, JPG, SVG</p>
              <p>Maximum size: 2MB</p>
              <p>Recommended: 400x100 pixels</p>
            </div>
          </CardContent>
        </Card>

        <!-- Quick Actions -->
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full justify-start" @click="manageTemplates">
              <FileText class="w-4 h-4 mr-2" />
              Manage Letter Templates
            </Button>
            <Button variant="outline" class="w-full justify-start" @click="viewMedicalLetters">
              <Mail class="w-4 h-4 mr-2" />
              View Medical Letters
            </Button>
            <Button variant="outline" class="w-full justify-start" @click="exportSettings">
              <Download class="w-4 h-4 mr-2" />
              Export Settings
            </Button>
          </CardContent>
        </Card>

        <!-- Registration Display Preview -->
        <Card v-if="registrationDisplay">
          <CardHeader>
            <CardTitle>Registration Display</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <p class="font-medium">{{ fullProfessionalName }}</p>
              <p class="text-sm text-gray-600">{{ registrationDisplay }}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Letter Templates Modal -->
    <LetterTemplatesModal
      v-if="showTemplatesModal"
      @close="showTemplatesModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDoctorSettingsApi, type DoctorSettings } from '@/composables/useDoctorSettingsApi'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FileImage, FileText, Mail, Download } from 'lucide-vue-next'
import LetterTemplatesModal from './LetterTemplatesModal.vue'

const router = useRouter()
const {
  settings: apiSettings,
  loading,
  error,
  getSettings,
  updateSettings,
  uploadSignature: apiUploadSignature,
  removeSignature: apiRemoveSignature,
  hasDigitalSignature,
  getSignatureDataUrl,
  getFullProfessionalName,
  getRegistrationDisplay
} = useDoctorSettingsApi()

const showTemplatesModal = ref(false)
const signatureInput = ref<HTMLInputElement>()

const settings = reactive<Partial<DoctorSettings>>({
  professional_prefix: '',
  registration_number: '',
  registration_body: '',
  practice_name: '',
  practice_address: '',
  practice_phone: '',
  practice_email: '',
  include_signature_in_letters: true,
  include_registration_in_letters: true
})

const letterSettings = reactive({
  font_family: 'Arial',
  font_size: 12,
  line_height: 1.5,
  margin_top: 20,
  margin_bottom: 20,
  margin_left: 20,
  margin_right: 20
})

const hasSignature = computed(() => hasDigitalSignature())
const signatureDataUrl = computed(() => getSignatureDataUrl())
const fullProfessionalName = computed(() => getFullProfessionalName())
const registrationDisplay = computed(() => getRegistrationDisplay())

const saveSettings = async () => {
  try {
    await updateSettings({
      ...settings,
      default_letter_settings: letterSettings
    })
    // Show success message
  } catch (err) {
    console.error('Error saving settings:', err)
  }
}

const uploadSignature = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    try {
      await apiUploadSignature(file)
      // Refresh settings to get updated signature info
      await getSettings()
    } catch (err) {
      console.error('Error uploading signature:', err)
    }
  }
}

const removeSignature = async () => {
  if (confirm('Are you sure you want to remove your digital signature?')) {
    try {
      await apiRemoveSignature()
      await getSettings()
    } catch (err) {
      console.error('Error removing signature:', err)
    }
  }
}

const manageTemplates = () => {
  showTemplatesModal.value = true
}

const viewMedicalLetters = () => {
  router.push('/medical-letters')
}

const exportSettings = () => {
  // Export settings as JSON
  const dataStr = JSON.stringify({ ...settings, letterSettings }, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'doctor-settings.json'
  link.click()
  URL.revokeObjectURL(url)
}

const loadSettings = () => {
  if (apiSettings.value) {
    Object.keys(settings).forEach(key => {
      if (key in apiSettings.value!) {
        settings[key as keyof typeof settings] = apiSettings.value![key as keyof DoctorSettings] as any
      }
    })

    // Load letter settings
    if (apiSettings.value.default_letter_settings) {
      Object.keys(letterSettings).forEach(key => {
        if (apiSettings.value!.default_letter_settings?.[key]) {
          letterSettings[key as keyof typeof letterSettings] = apiSettings.value!.default_letter_settings[key]
        }
      })
    }
  }
}

onMounted(async () => {
  await getSettings()
  loadSettings()
})
</script>
