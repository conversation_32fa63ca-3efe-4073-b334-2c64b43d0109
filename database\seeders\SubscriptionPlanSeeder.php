<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Free Plan
        SubscriptionPlan::updateOrCreate(
            ['slug' => 'free'],
            [
                'name' => 'Free Plan',
                'slug' => 'free',
                'stripe_product_id' => 'prod_free_plan',
                'stripe_price_id' => 'price_free_plan',
                'price' => 0.00,
                'currency' => 'gbp',
                'interval' => 'month',
                'interval_count' => 1,
                'description' => 'Perfect for getting started with basic healthcare features',
                'features' => [
                    'chat_messages_per_hour' => 10,
                    'chat_messages_per_day' => 50,
                    'appointments_per_month' => 2,
                    'api_requests_per_minute' => 60,
                    'api_requests_per_hour' => 1000,
                    'additional_features' => [
                        'Basic AI health chat',
                        '2 appointments per month',
                        'Basic health tracking',
                        'Community access',
                        'Email support'
                    ]
                ],
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        // Premium Plan
        SubscriptionPlan::updateOrCreate(
            ['slug' => 'premium'],
            [
                'name' => 'Premium Plan',
                'slug' => 'premium',
                'stripe_product_id' => 'prod_premium_plan',
                'stripe_price_id' => 'price_premium_plan',
                'price' => 29.99,
                'currency' => 'gbp',
                'interval' => 'month',
                'interval_count' => 1,
                'description' => 'Enhanced healthcare experience with unlimited access and priority support',
                'features' => [
                    'chat_messages_per_hour' => 100,
                    'chat_messages_per_day' => 500,
                    'appointments_per_month' => 10,
                    'api_requests_per_minute' => 200,
                    'api_requests_per_hour' => 5000,
                    'additional_features' => [
                        'Unlimited AI health chat',
                        '10 appointments per month',
                        'Advanced health analytics',
                        'Priority appointment booking',
                        'Telemedicine consultations',
                        'Health record management',
                        'Prescription management',
                        'Priority support',
                        'Family account sharing'
                    ]
                ],
                'is_active' => true,
                'sort_order' => 2,
            ]
        );



        $this->command->info('Subscription plans seeded successfully!');
    }
}
