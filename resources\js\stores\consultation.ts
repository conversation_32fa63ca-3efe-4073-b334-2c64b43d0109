import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useConsultationStore = defineStore('consultation', () => {
  // State
  const consultations = ref<any[]>([])
  const currentConsultation = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Getters
  const getConsultationById = computed(() => {
    return (id: string | number) => {
      return consultations.value.find(c => c.id === id)
    }
  })
  
  const getConsultationsByPatient = computed(() => {
    return (patientId: string | number) => {
      return consultations.value.filter(c => c.patient_id === patientId)
    }
  })
  
  const getConsultationsByStatus = computed(() => {
    return (status: string) => {
      return consultations.value.filter(c => c.status === status)
    }
  })
  
  // Actions
  const fetchConsultations = async (params?: any) => {
    try {
      loading.value = true
      error.value = null
      
      const queryParams = new URLSearchParams(params).toString()
      const response = await fetch(`/consultations-list${queryParams ? '?' + queryParams : ''}`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch consultations')
      }
      
      const data = await response.json()
      consultations.value = data.data || []
      
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchConsultation = async (id: string | number) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch(`/api/consultations/${id}`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch consultation')
      }
      
      const data = await response.json()
      currentConsultation.value = data.data
      
      // Update in consultations array if it exists
      const index = consultations.value.findIndex(c => c.id === id)
      if (index !== -1) {
        consultations.value[index] = data.data
      }
      
      return data.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const createConsultation = async (consultationData: any) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch('/consultations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify(consultationData),
      })
      
      if (!response.ok) {
        throw new Error('Failed to create consultation')
      }
      
      const data = await response.json()
      const newConsultation = data.data
      
      // Add to consultations array
      consultations.value.unshift(newConsultation)
      currentConsultation.value = newConsultation
      
      return newConsultation
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateConsultation = async (id: string | number, consultationData: any) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch(`/api/consultations/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify(consultationData),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update consultation')
      }
      
      const data = await response.json()
      const updatedConsultation = data.data
      
      // Update in consultations array
      const index = consultations.value.findIndex(c => c.id === id)
      if (index !== -1) {
        consultations.value[index] = updatedConsultation
      }
      
      // Update current consultation if it's the same
      if (currentConsultation.value?.id === id) {
        currentConsultation.value = updatedConsultation
      }
      
      return updatedConsultation
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const deleteConsultation = async (id: string | number) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch(`/api/consultations/${id}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete consultation')
      }
      
      // Remove from consultations array
      const index = consultations.value.findIndex(c => c.id === id)
      if (index !== -1) {
        consultations.value.splice(index, 1)
      }
      
      // Clear current consultation if it's the same
      if (currentConsultation.value?.id === id) {
        currentConsultation.value = null
      }
      
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const createFromAppointment = async (appointmentId: string | number) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch(`/api/consultations/from-appointment/${appointmentId}`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to create consultation from appointment')
      }
      
      const data = await response.json()
      const newConsultation = data.data
      
      // Add to consultations array
      consultations.value.unshift(newConsultation)
      currentConsultation.value = newConsultation
      
      return newConsultation
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateStatus = async (id: string | number, status: string) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch('/api/consultations/status/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({ id, status }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update consultation status')
      }
      
      const data = await response.json()
      
      // Update in consultations array
      const index = consultations.value.findIndex(c => c.id === id)
      if (index !== -1) {
        consultations.value[index].status = status
      }
      
      // Update current consultation if it's the same
      if (currentConsultation.value?.id === id) {
        currentConsultation.value.status = status
      }
      
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const clearError = () => {
    error.value = null
  }
  
  const clearCurrent = () => {
    currentConsultation.value = null
  }
  
  return {
    // State
    consultations,
    currentConsultation,
    loading,
    error,
    
    // Getters
    getConsultationById,
    getConsultationsByPatient,
    getConsultationsByStatus,
    
    // Actions
    fetchConsultations,
    fetchConsultation,
    createConsultation,
    updateConsultation,
    deleteConsultation,
    createFromAppointment,
    updateStatus,
    clearError,
    clearCurrent,
  }
})