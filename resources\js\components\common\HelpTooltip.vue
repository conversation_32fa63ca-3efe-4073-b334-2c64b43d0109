<template>
  <div class="relative inline-block">
    <!-- Trigger Element -->
    <div 
      @mouseenter="showTooltip = true"
      @mouseleave="showTooltip = false"
      @click="toggleTooltip"
      class="cursor-help"
    >
      <slot name="trigger">
        <svg class="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </slot>
    </div>

    <!-- Tooltip Content -->
    <div 
      v-if="showTooltip"
      class="absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg"
      :class="tooltipClasses"
      style="max-width: 250px;"
    >
      <!-- Arrow -->
      <div 
        class="absolute w-2 h-2 bg-gray-900 transform rotate-45"
        :class="arrowClasses"
      ></div>
      
      <!-- Content -->
      <div class="relative">
        <div v-if="title" class="font-medium mb-1">{{ title }}</div>
        <div class="text-gray-200">{{ content }}</div>
        
        <!-- Links -->
        <div v-if="links && links.length > 0" class="mt-2 pt-2 border-t border-gray-700">
          <a 
            v-for="link in links" 
            :key="link.url"
            :href="link.url"
            target="_blank"
            class="block text-blue-300 hover:text-blue-200 text-xs underline"
          >
            {{ link.text }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  content: {
    type: String,
    required: true
  },
  title: String,
  position: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
  },
  links: Array, // [{ text: 'Learn more', url: '/docs' }]
  persistent: {
    type: Boolean,
    default: false
  }
})

const showTooltip = ref(false)

const toggleTooltip = () => {
  if (props.persistent) {
    showTooltip.value = !showTooltip.value
  }
}

const tooltipClasses = computed(() => {
  const base = 'whitespace-normal'
  
  switch (props.position) {
    case 'top':
      return `${base} bottom-full left-1/2 transform -translate-x-1/2 mb-2`
    case 'bottom':
      return `${base} top-full left-1/2 transform -translate-x-1/2 mt-2`
    case 'left':
      return `${base} right-full top-1/2 transform -translate-y-1/2 mr-2`
    case 'right':
      return `${base} left-full top-1/2 transform -translate-y-1/2 ml-2`
    default:
      return `${base} bottom-full left-1/2 transform -translate-x-1/2 mb-2`
  }
})

const arrowClasses = computed(() => {
  switch (props.position) {
    case 'top':
      return 'top-full left-1/2 transform -translate-x-1/2'
    case 'bottom':
      return 'bottom-full left-1/2 transform -translate-x-1/2'
    case 'left':
      return 'left-full top-1/2 transform -translate-y-1/2'
    case 'right':
      return 'right-full top-1/2 transform -translate-y-1/2'
    default:
      return 'top-full left-1/2 transform -translate-x-1/2'
  }
})
</script>
