<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Clinic;
use App\Models\Provider;
use App\Models\Patient;
use Illuminate\Support\Facades\Notification;
use App\Notifications\PatientRegistrationNotification;
use Illuminate\Support\Facades\Hash;

class PatientRegistrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed roles and permissions
        $this->artisan('db:seed', ['--class' => 'ProductionSafeRolesPermissionsSeeder']);
        
        // Create a clinic
        $this->clinic = Clinic::create([
            'name' => 'Test Clinic',
            'email' => '<EMAIL>',
            'phone' => '************',
            'address' => '123 Test St',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
            'is_active' => true,
        ]);
    }

    public function test_provider_can_create_patient_without_password()
    {
        Notification::fake();

        // Create a provider user
        $providerUser = User::create([
            'name' => 'Dr. Test Provider',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'provider',
            'clinic_id' => $this->clinic->id,
            'is_active' => true,
        ]);
        $providerUser->assignRole('provider');

        // Create provider profile
        $provider = Provider::create([
            'user_id' => $providerUser->id,
            'clinic_id' => $this->clinic->id,
            'specialization' => 'General Medicine',
            'verification_status' => 'verified',
            'rating' => 0,
        ]);

        // Patient data without password
        $patientData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone_number' => '************',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'address' => '456 Patient St',
            'postal_code' => '67890',
            'nhs_number' => 'NHS123456',
            'registered_gp_name' => 'Dr. GP',
            'registered_gp_address' => '789 GP St',
            'medical_history' => 'No significant history',
            'current_medications' => 'None',
            'emergency_contact' => 'Jane Doe - ************',
        ];

        // Make the request as the provider
        $response = $this->actingAs($providerUser)
            ->postJson('/provider/create-patient', $patientData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'message',
            'patient' => [
                'id',
                'first_name',
                'last_name',
                'email',
                'user' => [
                    'id',
                    'name',
                    'email',
                ]
            ]
        ]);

        // Verify patient was created in database
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'John Doe',
            'role' => 'patient',
        ]);

        $this->assertDatabaseHas('patients', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'nhs_number' => 'NHS123456',
            'created_by_provider_id' => $provider->id,
        ]);

        // Verify registration email was sent
        $patient = Patient::where('first_name', 'John')->first();
        Notification::assertSentTo(
            $patient->user,
            PatientRegistrationNotification::class
        );
    }

    public function test_admin_can_create_patient_without_password()
    {
        Notification::fake();

        // Create an admin user
        $adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
        ]);
        $adminUser->assignRole('admin');

        // Patient data without password
        $patientData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone_number' => '************',
            'date_of_birth' => '1985-05-15',
            'gender' => 'female', 
            'address' => '789 Admin St',
            'postal_code' => '54321',
            'clinic_id' => $this->clinic->id,
            'medical_history' => 'Hypertension',
            'current_medications' => 'Lisinopril',
        ];

        // Make the request as the admin
        $response = $this->actingAs($adminUser)
            ->postJson('/save-patient', $patientData);

        if ($response->status() !== 201) {
            dump('Response status: ' . $response->status());
            dump('Response content: ' . $response->content());
        }

        $response->assertStatus(201);

        // Verify patient was created in database
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Jane Smith',
            'role' => 'patient',
        ]);

        $this->assertDatabaseHas('patients', [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'clinic_id' => $this->clinic->id,
        ]);

        // Verify registration email was sent
        $patient = Patient::where('first_name', 'Jane')->first();
        Notification::assertSentTo(
            $patient->user,
            PatientRegistrationNotification::class
        );
    }

    public function test_patient_creation_requires_required_fields()
    {
        // Create an admin user
        $adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
        ]);
        $adminUser->assignRole('admin');

        // Patient data missing required fields
        $patientData = [
            'email' => '<EMAIL>',
            // Missing first_name, last_name, phone_number, date_of_birth, gender, address, postal_code
        ];

        // Make the request as the admin
        $response = $this->actingAs($adminUser)
            ->postJson('/save-patient', $patientData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'first_name',
            'last_name', 
            'phone_number',
            'date_of_birth',
            'gender',
            'address',
            'postal_code'
        ]);
    }
}