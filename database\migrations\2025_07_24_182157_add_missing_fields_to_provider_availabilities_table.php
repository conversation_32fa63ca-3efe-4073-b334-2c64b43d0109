<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add missing fields to provider_availabilities table for clinic sessions migration
     */
    public function up(): void
    {
        Schema::table('provider_availabilities', function (Blueprint $table) {
            // Add missing fields that the ProviderAvailability model expects
            if (!Schema::hasColumn('provider_availabilities', 'service_id')) {
                $table->foreignId('service_id')->nullable()->after('clinic_id')->constrained()->onDelete('set null');
            }
            if (!Schema::hasColumn('provider_availabilities', 'time_slot')) {
                $table->integer('time_slot')->nullable()->after('end_time')->comment('Duration in minutes');
            }
            if (!Schema::hasColumn('provider_availabilities', 'parent_id')) {
                $table->unsignedBigInteger('parent_id')->nullable()->after('time_slot')->comment('Parent session ID for child sessions');
            }
            if (!Schema::hasColumn('provider_availabilities', 'wp_session_data')) {
                $table->json('wp_session_data')->nullable()->after('wp_clinic_session_id')->comment('WordPress session data for migration tracking');
            }

            // Add indexes for performance
            $table->index('service_id');
            $table->index('parent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provider_availabilities', function (Blueprint $table) {
            // Drop the added columns in reverse order
            $columnsToCheck = ['wp_session_data', 'parent_id', 'time_slot', 'service_id'];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('provider_availabilities', $column)) {
                    // Drop indexes first if they exist
                    if (in_array($column, ['service_id', 'parent_id'])) {
                        try {
                            $table->dropIndex([$column]);
                        } catch (Exception $e) {
                            // Index might not exist, continue
                        }
                    }

                    // Drop foreign key constraint for service_id
                    if ($column === 'service_id') {
                        try {
                            $table->dropForeign(['service_id']);
                        } catch (Exception $e) {
                            // Foreign key might not exist, continue
                        }
                    }

                    $table->dropColumn($column);
                }
            }
        });
    }
};
