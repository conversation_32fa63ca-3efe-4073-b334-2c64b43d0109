<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Consolidated consultation documents enhancements for WordPress migration
     */
    public function up(): void
    {
        Schema::table('consultation_documents', function (Blueprint $table) {
            // Add missing WordPress uploaded_by tracking fields (wp_attachment_id, wp_encounter_id, wp_created_date already exist)
            if (!Schema::hasColumn('consultation_documents', 'wp_uploaded_by_id')) {
                $table->integer('wp_uploaded_by_id')->nullable()->after('wp_created_date');
            }
            if (!Schema::hasColumn('consultation_documents', 'wp_uploaded_by_email')) {
                $table->string('wp_uploaded_by_email')->nullable()->after('wp_uploaded_by_id');
            }
            if (!Schema::hasColumn('consultation_documents', 'wp_uploaded_by_name')) {
                $table->string('wp_uploaded_by_name')->nullable()->after('wp_uploaded_by_email');
            }

            // Allow null consultation_id for documents not linked to consultations
            $table->foreignId('consultation_id')->nullable()->change();

            // Add patient_id for direct patient document association
            if (!Schema::hasColumn('consultation_documents', 'patient_id')) {
                $table->foreignId('patient_id')->nullable()->after('consultation_id')->constrained()->onDelete('cascade');
                $table->index('patient_id');
            }

            // Make uploaded_by nullable for WordPress migration compatibility
            $table->foreignId('uploaded_by')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultation_documents', function (Blueprint $table) {
            // Only drop columns that were added by this migration
            $columnsToCheck = ['wp_uploaded_by_id', 'wp_uploaded_by_email', 'wp_uploaded_by_name'];
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('consultation_documents', $column)) {
                    $table->dropColumn($column);
                }
            }

            // Drop patient_id if it exists
            if (Schema::hasColumn('consultation_documents', 'patient_id')) {
                $table->dropForeign(['patient_id']);
                $table->dropIndex(['patient_id']);
                $table->dropColumn('patient_id');
            }
        });
    }
};
