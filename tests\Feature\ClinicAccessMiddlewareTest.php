<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Clinic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class ClinicAccessMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'clinic_admin']);
        Role::create(['name' => 'provider']);
        Role::create(['name' => 'patient']);
    }

    /** @test */
    public function admin_users_have_full_access_regardless_of_clinic_association()
    {
        $admin = User::factory()->create(['clinic_id' => null]);
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function clinic_admin_with_clinic_association_can_access_clinic_settings()
    {
        $clinic = Clinic::factory()->create();
        $clinicAdmin = User::factory()->create(['clinic_id' => $clinic->id]);
        $clinicAdmin->assignRole('clinic_admin');

        $response = $this->actingAs($clinicAdmin)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function clinic_admin_without_clinic_association_cannot_access_clinic_settings()
    {
        $clinicAdmin = User::factory()->create(['clinic_id' => null]);
        $clinicAdmin->assignRole('clinic_admin');

        $response = $this->actingAs($clinicAdmin)->get('/settings/clinic');

        $response->assertStatus(403);
    }

    /** @test */
    public function provider_cannot_access_clinic_settings_even_with_clinic_association()
    {
        $clinic = Clinic::factory()->create();
        $provider = User::factory()->create(['clinic_id' => $clinic->id]);
        $provider->assignRole('provider');

        $response = $this->actingAs($provider)->get('/settings/clinic');

        $response->assertStatus(403);
    }

    /** @test */
    public function patient_cannot_access_clinic_settings()
    {
        $clinic = Clinic::factory()->create();
        $patient = User::factory()->create(['clinic_id' => $clinic->id]);
        $patient->assignRole('patient');

        $response = $this->actingAs($patient)->get('/settings/clinic');

        $response->assertStatus(403);
    }

    /** @test */
    public function role_or_permission_middleware_works_with_admin_role()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function role_or_permission_middleware_works_with_clinic_admin_role()
    {
        $clinic = Clinic::factory()->create();
        $clinicAdmin = User::factory()->create(['clinic_id' => $clinic->id]);
        $clinicAdmin->assignRole('clinic_admin');

        $response = $this->actingAs($clinicAdmin)->get('/settings/clinic');

        $response->assertStatus(200);
    }

    /** @test */
    public function middleware_blocks_users_without_required_roles()
    {
        $clinic = Clinic::factory()->create();
        $provider = User::factory()->create(['clinic_id' => $clinic->id]);
        $provider->assignRole('provider');

        $response = $this->actingAs($provider)->get('/settings/clinic');

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_requests_are_redirected_to_login()
    {
        $response = $this->get('/settings/clinic');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function api_endpoints_require_proper_authentication()
    {
        $response = $this->get('/settings/clinic/data');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function api_endpoints_require_proper_authorization()
    {
        $clinic = Clinic::factory()->create();
        $provider = User::factory()->create(['clinic_id' => $clinic->id]);
        $provider->assignRole('provider');

        $response = $this->actingAs($provider)->get('/settings/clinic/data');

        $response->assertStatus(403);
    }

    /** @test */
    public function middleware_allows_access_to_authorized_users()
    {
        $clinic = Clinic::factory()->create();
        $clinicAdmin = User::factory()->create(['clinic_id' => $clinic->id]);
        $clinicAdmin->assignRole('clinic_admin');

        $response = $this->actingAs($clinicAdmin)->get('/settings/clinic/data');

        $response->assertStatus(200);
    }

    /** @test */
    public function middleware_blocks_users_with_no_clinic_association_from_api()
    {
        $clinicAdmin = User::factory()->create(['clinic_id' => null]);
        $clinicAdmin->assignRole('clinic_admin');

        $response = $this->actingAs($clinicAdmin)->get('/settings/clinic/data');

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'No clinic association found'
            ]);
    }

    /** @test */
    public function admin_with_clinic_association_can_access_api()
    {
        $clinic = Clinic::factory()->create();
        $admin = User::factory()->create(['clinic_id' => $clinic->id]);
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/settings/clinic/data');

        $response->assertStatus(200);
    }

    /** @test */
    public function admin_without_clinic_association_cannot_access_clinic_specific_api()
    {
        $admin = User::factory()->create(['clinic_id' => null]);
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/settings/clinic/data');

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'No clinic association found'
            ]);
    }
}
