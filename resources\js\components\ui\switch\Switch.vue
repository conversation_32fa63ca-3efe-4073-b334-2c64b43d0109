<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface Props {
  checked?: boolean;
  disabled?: boolean;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  checked: false,
  disabled: false,
});

const emit = defineEmits<{
  'update:checked': [checked: boolean];
}>();

const handleClick = () => {
  if (!props.disabled) {
    emit('update:checked', !props.checked);
  }
};

const switchClasses = computed(() => {
  return cn(
    'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50',
    props.checked ? 'bg-primary' : 'bg-input',
    props.disabled && 'cursor-not-allowed opacity-50',
    props.class
  );
});

const thumbClasses = computed(() => {
  return cn(
    'pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform',
    props.checked ? 'translate-x-5' : 'translate-x-0'
  );
});
</script>

<template>
  <button
    type="button"
    role="switch"
    :aria-checked="checked"
    :disabled="disabled"
    :class="switchClasses"
    @click="handleClick"
  >
    <span :class="thumbClasses" />
  </button>
</template>
