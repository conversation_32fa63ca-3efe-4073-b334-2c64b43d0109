<?php

namespace App\Services;

use App\Models\ClinicTdlSetting;
use Exception;

class AzureBlobService
{
    private const REQUEST_CONTAINER = 'requests';
    private const RESULTS_CONTAINER = 'results';

    /**
     * Upload HL7 request to Azure Blob Storage
     */
    public function uploadRequest(ClinicTdlSetting $settings, string $requestId, string $hl7Message): string
    {
        if (!$settings->isConfigured()) {
            throw new Exception('Azure storage not configured for this clinic');
        }

        try {
            // Load Azure Storage SDK
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $containerName = self::REQUEST_CONTAINER;
            $fileName = $this->generateRequestFileName($settings->clinic_id, $requestId);
            
            // Set content type for HL7
            $options = new \MicrosoftAzure\Storage\Blob\Models\CreateBlockBlobOptions();
            $options->setContentType('application/hl7-v2');
            
            // Upload HL7 message to Azure
            $blobClient->createBlockBlob($containerName, $fileName, $hl7Message, $options);
            
            return $fileName;
            
        } catch (Exception $e) {
            throw new Exception('Failed to upload request to Azure: ' . $e->getMessage());
        }
    }

    /**
     * Poll Azure for new result files
     */
    public function pollForResults(ClinicTdlSetting $settings): array
    {
        if (!$settings->isConfigured()) {
            throw new Exception('Azure storage not configured for this clinic');
        }

        try {
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $containerName = self::RESULTS_CONTAINER;
            $clinicPrefix = "CLINIC_{$settings->clinic_id}_";
            
            // List blobs with clinic prefix
            $listBlobsOptions = new \MicrosoftAzure\Storage\Blob\Models\ListBlobsOptions();
            $listBlobsOptions->setPrefix($clinicPrefix);
            
            $blobList = $blobClient->listBlobs($containerName, $listBlobsOptions);
            $blobs = $blobList->getBlobs();
            
            $results = [];
            
            foreach ($blobs as $blob) {
                try {
                    // Download the blob content
                    $blobContent = $blobClient->getBlob($containerName, $blob->getName());
                    $content = stream_get_contents($blobContent->getContentStream());
                    
                    // Determine format (HL7 or JSON)
                    $format = (strpos($content, 'MSH|') === 0) ? 'hl7' : 'json';
                    
                    $results[] = [
                        'file_name' => $blob->getName(),
                        'content' => $content,
                        'format' => $format,
                        'last_modified' => $blob->getProperties()->getLastModified(),
                        'size' => $blob->getProperties()->getContentLength()
                    ];
                    
                } catch (Exception $e) {
                    // Log error but continue processing other files
                    error_log("Error processing blob {$blob->getName()}: " . $e->getMessage());
                    continue;
                }
            }
            
            return $results;
            
        } catch (Exception $e) {
            throw new Exception('Failed to poll Azure for results: ' . $e->getMessage());
        }
    }

    /**
     * Download specific result file
     */
    public function downloadResult(ClinicTdlSetting $settings, string $fileName): array
    {
        if (!$settings->isConfigured()) {
            throw new Exception('Azure storage not configured for this clinic');
        }

        try {
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $containerName = self::RESULTS_CONTAINER;
            
            // Download the blob
            $blobContent = $blobClient->getBlob($containerName, $fileName);
            $content = stream_get_contents($blobContent->getContentStream());
            
            // Determine format
            $format = (strpos($content, 'MSH|') === 0) ? 'hl7' : 'json';
            
            return [
                'file_name' => $fileName,
                'content' => $content,
                'format' => $format
            ];
            
        } catch (Exception $e) {
            throw new Exception('Failed to download result from Azure: ' . $e->getMessage());
        }
    }

    /**
     * Delete processed file from Azure
     */
    public function deleteFile(ClinicTdlSetting $settings, string $fileName, string $container = self::RESULTS_CONTAINER): bool
    {
        if (!$settings->isConfigured()) {
            throw new Exception('Azure storage not configured for this clinic');
        }

        try {
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $blobClient->deleteBlob($container, $fileName);
            
            return true;
            
        } catch (Exception $e) {
            // Log error but don't throw - file deletion is not critical
            error_log("Failed to delete file {$fileName} from Azure: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test Azure connection
     */
    public function testConnection(string $connectionString): bool
    {
        try {
            $this->loadAzureSDK();
            
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            // Try to list containers to test connection
            $blobClient->listContainers();
            
            return true;
            
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Create required containers if they don't exist
     */
    public function createContainers(ClinicTdlSetting $settings): bool
    {
        if (!$settings->isConfigured()) {
            throw new Exception('Azure storage not configured for this clinic');
        }

        try {
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $containers = [self::REQUEST_CONTAINER, self::RESULTS_CONTAINER];
            
            foreach ($containers as $container) {
                try {
                    $blobClient->createContainer($container);
                } catch (Exception $e) {
                    // Container might already exist, which is fine
                    if (strpos($e->getMessage(), 'ContainerAlreadyExists') === false) {
                        throw $e;
                    }
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception('Failed to create Azure containers: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique filename for request
     */
    private function generateRequestFileName(int $clinicId, string $requestId): string
    {
        return "REQ_CLINIC_{$clinicId}_{$requestId}_" . date('YmdHis') . ".hl7";
    }

    /**
     * Load Azure Storage SDK
     */
    private function loadAzureSDK(): void
    {
        if (!class_exists('\MicrosoftAzure\Storage\Blob\BlobRestProxy')) {
            throw new Exception('Azure Storage SDK not installed. Please run: composer require microsoft/azure-storage-blob');
        }
    }

    /**
     * Get file statistics
     */
    public function getFileStats(ClinicTdlSetting $settings): array
    {
        if (!$settings->isConfigured()) {
            return [
                'requests_count' => 0,
                'results_count' => 0,
                'total_size' => 0
            ];
        }

        try {
            $this->loadAzureSDK();
            
            $connectionString = $settings->azure_connection_string;
            $blobClient = \MicrosoftAzure\Storage\Blob\BlobRestProxy::createBlobService($connectionString);
            
            $stats = [
                'requests_count' => 0,
                'results_count' => 0,
                'total_size' => 0
            ];
            
            // Count request files
            $requestBlobs = $blobClient->listBlobs(self::REQUEST_CONTAINER);
            $stats['requests_count'] = count($requestBlobs->getBlobs());
            
            // Count result files
            $resultBlobs = $blobClient->listBlobs(self::RESULTS_CONTAINER);
            $stats['results_count'] = count($resultBlobs->getBlobs());
            
            // Calculate total size
            foreach ($requestBlobs->getBlobs() as $blob) {
                $stats['total_size'] += $blob->getProperties()->getContentLength();
            }
            
            foreach ($resultBlobs->getBlobs() as $blob) {
                $stats['total_size'] += $blob->getProperties()->getContentLength();
            }
            
            return $stats;
            
        } catch (Exception $e) {
            return [
                'requests_count' => 0,
                'results_count' => 0,
                'total_size' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
