<template>
  <div class="space-y-4">
    <!-- Upload Area -->
    <div
      ref="dropzoneElement"
      class="dropzone-custom border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
      :class="{ 'border-blue-500 bg-blue-50': isDragging }"
      @click="openFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div class="space-y-3">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div class="text-lg font-medium text-gray-900">Drop files here or click to upload</div>
        <div class="text-sm text-gray-500">{{ acceptedTypesText || 'PNG, JPG, GIF up to 10MB' }}</div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      :accept="acceptedTypes || 'image/*'"
      :multiple="multiple"
      @change="handleFileSelect"
      class="hidden"
    />

    <!-- Error Message -->
    <div v-if="errorMessage" class="text-sm text-red-600">
      {{ errorMessage }}
    </div>

    <!-- Uploaded Files Preview -->
    <div v-if="previewFiles.length > 0" class="mt-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">New Images</h4>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(file, index) in previewFiles"
          :key="`new-${index}`"
          class="relative group"
        >
          <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <img
              :src="file.preview"
              :alt="file.name"
              class="w-full h-full object-cover"
            />
          </div>

          <!-- File Info -->
          <div class="mt-2">
            <p class="text-xs text-gray-600 truncate">{{ file.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
          </div>

          <!-- Remove Button -->
          <button
            @click="removeFile(index)"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Existing Images (for edit mode) -->
    <div v-if="existingImages && existingImages.length > 0" class="mt-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Current Images</h4>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(image, index) in existingImages"
          :key="`existing-${index}`"
          class="relative group"
        >
          <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <img
              :src="image.url"
              :alt="image.name || 'Product image'"
              class="w-full h-full object-cover"
            />
          </div>

          <!-- Remove Button for Existing Images -->
          <button
            @click="removeExistingImage(index)"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Array, File, null],
    default: null
  },
  multiple: {
    type: Boolean,
    default: false
  },
  acceptedTypes: {
    type: String,
    default: 'image/*'
  },
  acceptedTypesText: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  existingImages: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'remove-existing'])

const fileInput = ref(null)
const dropzoneElement = ref(null)
const errorMessage = ref('')
const previewFiles = ref([])
const isDragging = ref(false)

// File handling functions
const openFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  processFiles(files)
}

const handleDragOver = (event) => {
  event.preventDefault()
  isDragging.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  isDragging.value = false
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragging.value = false
  const files = Array.from(event.dataTransfer.files)
  processFiles(files)
}

const processFiles = async (files) => {
  errorMessage.value = ''

  // Filter only image files
  const imageFiles = files.filter(file => file.type.startsWith('image/'))

  if (imageFiles.length !== files.length) {
    errorMessage.value = 'Only image files are allowed'
  }

  // Check file sizes
  const oversizedFiles = imageFiles.filter(file => file.size > props.maxSize)
  if (oversizedFiles.length > 0) {
    errorMessage.value = `Some files are too large. Maximum size is ${formatFileSize(props.maxSize)}`
    return
  }

  // Check max files for single upload
  if (!props.multiple && imageFiles.length > 1) {
    errorMessage.value = 'Only one file is allowed'
    return
  }

  // Create previews
  const newPreviews = []
  for (const file of imageFiles) {
    const preview = await createFilePreview(file)
    newPreviews.push({
      file,
      preview,
      name: file.name,
      size: file.size
    })
  }

  if (props.multiple) {
    previewFiles.value = [...previewFiles.value, ...newPreviews]
    const allFiles = previewFiles.value.map(p => p.file)
    emit('update:modelValue', allFiles)
  } else {
    previewFiles.value = newPreviews
    emit('update:modelValue', newPreviews[0]?.file || null)
  }

  // Clear file input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const createFilePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.readAsDataURL(file)
  })
}

const removeFile = (index) => {
  previewFiles.value.splice(index, 1)

  if (props.multiple) {
    const allFiles = previewFiles.value.map(p => p.file)
    emit('update:modelValue', allFiles)
  } else {
    emit('update:modelValue', previewFiles.value[0]?.file || null)
  }
}

const removeExistingImage = (index) => {
  emit('remove-existing', index)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
    previewFiles.value = []
  }
})
</script>

<style scoped>
/* Custom dropzone styling */
.dropzone-custom {
  transition: all 0.3s ease;
}

.dropzone-custom:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

/* Image preview grid styling */
.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Smooth transitions for hover effects */
.group:hover .opacity-0 {
  opacity: 1;
}

.transition-opacity {
  transition: opacity 0.2s ease-in-out;
}

/* File input styling */
input[type="file"] {
  display: none;
}

/* Error message styling */
.text-red-600 {
  color: #dc2626;
}

/* Success styling */
.text-green-600 {
  color: #059669;
}

/* Grid responsive styling */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>
