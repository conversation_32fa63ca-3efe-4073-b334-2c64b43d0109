/**
 * Light Theme Configuration for Medroid Application
 * 
 * This file defines consistent styling classes for the light theme
 * to ensure UI consistency across the entire application.
 */

export const themeConfig = {
    // Background Colors
    backgrounds: {
        primary: 'bg-white',
        secondary: 'bg-gray-50',
        card: 'bg-white border border-gray-200',
        table: 'bg-white',
        tableRow: 'bg-white hover:bg-gray-50',
        tableHeader: 'bg-gray-50',
        input: 'bg-white',
        button: 'bg-white',
        overlay: 'bg-black bg-opacity-50'
    },

    // Text Colors
    text: {
        primary: 'text-gray-900',
        secondary: 'text-gray-700',
        muted: 'text-gray-500',
        light: 'text-gray-400',
        white: 'text-white',
        error: 'text-red-600',
        success: 'text-green-600',
        warning: 'text-yellow-600',
        info: 'text-blue-600'
    },

    // Border Colors
    borders: {
        default: 'border-gray-200',
        light: 'border-gray-100',
        medium: 'border-gray-300',
        focus: 'border-teal-500',
        error: 'border-red-300',
        success: 'border-green-300'
    },

    // Button Styles
    buttons: {
        primary: 'inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200',
        secondary: 'inline-flex items-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200',
        success: 'inline-flex items-center px-4 py-2.5 border border-green-300 rounded-lg shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200',
        danger: 'inline-flex items-center px-4 py-2.5 border border-red-300 rounded-lg shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200',
        warning: 'inline-flex items-center px-4 py-2.5 border border-yellow-300 rounded-lg shadow-sm text-sm font-medium text-yellow-700 bg-yellow-50 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200'
    },

    // Form Controls
    forms: {
        input: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 bg-white',
        select: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 bg-white',
        textarea: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 bg-white',
        label: 'block text-sm font-medium text-gray-700 mb-2',
        error: 'mt-1 text-sm text-red-600',
        help: 'mt-1 text-sm text-gray-500'
    },

    // Status Badges
    badges: {
        active: 'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800',
        inactive: 'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800',
        pending: 'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800',
        completed: 'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800',
        cancelled: 'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
    },

    // Cards and Containers
    containers: {
        card: 'bg-white overflow-hidden shadow-sm sm:rounded-lg border border-gray-200',
        cardBody: 'p-6',
        section: 'mb-6',
        panel: 'bg-gray-50 p-4 rounded-lg border border-gray-200'
    },

    // Tables
    tables: {
        wrapper: 'overflow-x-auto',
        table: 'min-w-full divide-y divide-gray-200',
        header: 'bg-gray-50',
        headerCell: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
        body: 'bg-white divide-y divide-gray-200',
        row: 'hover:bg-gray-50 transition-colors duration-150',
        cell: 'px-6 py-4 whitespace-nowrap text-sm'
    },

    // Loading and Empty States
    states: {
        loading: 'text-center py-8',
        loadingSpinner: 'inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600',
        empty: 'text-center py-12',
        emptyIcon: 'w-16 h-16 mx-auto text-gray-300 mb-4',
        emptyText: 'text-gray-500 text-lg',
        emptySubtext: 'text-gray-400 text-sm mt-1'
    },

    // Navigation
    navigation: {
        breadcrumb: 'text-sm font-medium text-gray-500 hover:text-teal-600',
        breadcrumbActive: 'text-sm font-medium text-gray-700',
        link: 'text-teal-600 hover:text-teal-800 transition-colors duration-200'
    }
};

// Helper function to get theme classes
export function getThemeClass(category, variant = 'default') {
    return themeConfig[category]?.[variant] || '';
}

// Helper function to build component classes
export function buildComponentClasses(baseClasses, ...themeClasses) {
    return [baseClasses, ...themeClasses.filter(Boolean)].join(' ');
}