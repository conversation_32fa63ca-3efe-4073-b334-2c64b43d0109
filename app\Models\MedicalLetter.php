<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalLetter extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'patient_id',
        'created_by',
        'clinic_id',
        'letter_type',
        'template_name',
        'recipient_name',
        'recipient_title',
        'recipient_address',
        'subject',
        'content',
        'additional_notes',
        'letter_data',
        'status',
        'letter_date',
        'sent_date',
        'sent_method',
        'include_signature',
        'include_letterhead',
        'file_path',
        'attachments',
    ];

    protected $casts = [
        'letter_data' => 'array',
        'letter_date' => 'date',
        'sent_date' => 'date',
        'include_signature' => 'boolean',
        'include_letterhead' => 'boolean',
        'attachments' => 'array',
    ];

    /**
     * Get the consultation associated with the letter.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the patient associated with the letter.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who created the letter.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the clinic associated with the letter.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Scope to filter by letter type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('letter_type', $type);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get draft letters.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope to get final letters.
     */
    public function scopeFinal($query)
    {
        return $query->where('status', 'final');
    }

    /**
     * Scope to get sent letters.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Check if letter is draft.
     */
    public function isDraft()
    {
        return $this->status === 'draft';
    }

    /**
     * Check if letter is final.
     */
    public function isFinal()
    {
        return $this->status === 'final';
    }

    /**
     * Check if letter is sent.
     */
    public function isSent()
    {
        return $this->status === 'sent';
    }

    /**
     * Get the letter type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'referral' => 'Referral Letter',
            'discharge' => 'Discharge Summary',
            'sick_note' => 'Sick Note',
            'medical_report' => 'Medical Report',
            'prescription_letter' => 'Prescription Letter',
            'follow_up' => 'Follow-up Letter',
            'consultation_summary' => 'Consultation Summary',
        ];

        return $types[$this->letter_type] ?? ucfirst(str_replace('_', ' ', $this->letter_type));
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'draft' => 'Draft',
            'final' => 'Final',
            'sent' => 'Sent',
            'archived' => 'Archived',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the full recipient name with title.
     */
    public function getFullRecipientNameAttribute()
    {
        $name = '';
        
        if ($this->recipient_title) {
            $name .= $this->recipient_title . ' ';
        }
        
        $name .= $this->recipient_name;
        
        return $name;
    }

    /**
     * Mark letter as final.
     */
    public function markAsFinal()
    {
        $this->status = 'final';
        $this->save();
    }

    /**
     * Mark letter as sent.
     */
    public function markAsSent($method = null, $date = null)
    {
        $this->status = 'sent';
        $this->sent_method = $method;
        $this->sent_date = $date ?? now()->toDateString();
        $this->save();
    }

    /**
     * Archive the letter.
     */
    public function archive()
    {
        $this->status = 'archived';
        $this->save();
    }

    /**
     * Check if letter has attachments.
     */
    public function hasAttachments()
    {
        return !empty($this->attachments);
    }

    /**
     * Check if letter has been generated as PDF.
     */
    public function hasPdfFile()
    {
        return !empty($this->file_path) && file_exists(storage_path('app/' . $this->file_path));
    }

    /**
     * Get the PDF file URL.
     */
    public function getPdfUrl()
    {
        if (!$this->hasPdfFile()) {
            return null;
        }

        return route('medical-letters.download', $this->id);
    }

    /**
     * Generate letter content from template.
     */
    public function generateFromTemplate($templateData = [])
    {
        // This would integrate with a template engine
        // For now, we'll just merge the template data
        $this->letter_data = array_merge($this->letter_data ?? [], $templateData);
        $this->save();
    }

    /**
     * Get template variables for letter generation.
     */
    public function getTemplateVariables()
    {
        $variables = [
            'patient_name' => $this->patient->user->name ?? '',
            'patient_dob' => $this->patient->date_of_birth ? $this->patient->date_of_birth->format('d/m/Y') : '',
            'doctor_name' => $this->creator->name ?? '',
            'clinic_name' => $this->clinic->name ?? '',
            'letter_date' => $this->letter_date->format('d/m/Y'),
            'recipient_name' => $this->full_recipient_name,
        ];

        // Add consultation data if available
        if ($this->consultation) {
            $variables = array_merge($variables, [
                'consultation_date' => $this->consultation->consultation_date->format('d/m/Y'),
                'chief_complaint' => $this->consultation->chief_complaint,
                'diagnosis' => $this->consultation->diagnosis,
                'treatment_plan' => $this->consultation->treatment_plan,
            ]);
        }

        // Merge with custom letter data
        return array_merge($variables, $this->letter_data ?? []);
    }
}
