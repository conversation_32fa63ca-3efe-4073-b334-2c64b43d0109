<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Service;
use App\Models\Provider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class CountryFilterService
{
    /**
     * Get the current user's country code
     */
    public function getCurrentUserCountry(): ?string
    {
        // First check for X-Country-Code header (for API requests)
        $headerCountry = Request::header('X-Country-Code');
        if ($headerCountry) {
            return $headerCountry;
        }

        // Then check authenticated user
        $user = Auth::user();
        return $user ? $user->country_code : null;
    }

    /**
     * Filter products based on user's country
     */
    public function filterProductsByCountry(Builder $query, ?string $userCountryCode = null): Builder
    {
        $userCountryCode = $userCountryCode ?? $this->getCurrentUserCountry();

        if (!$userCountryCode) {
            return $query; // No filtering if no country specified
        }

        return $query->where(function ($q) use ($userCountryCode) {
            // Include products where:
            // 1. Provider is from the same country AND (no specific country pricing OR has pricing for user's country)
            // 2. OR product has specific pricing for the user's country and is available (regardless of provider country)
            $q->where(function ($subQ) use ($userCountryCode) {
                // Provider from same country
                $subQ->whereHas('user.provider', function ($providerQ) use ($userCountryCode) {
                    $providerQ->where('country_code', $userCountryCode);
                })->where(function ($countrySubQ) use ($userCountryCode) {
                    // AND either no specific country pricing OR has pricing for user's country
                    $countrySubQ->whereDoesntHave('countries')
                               ->orWhereHas('countries', function ($countryQ) use ($userCountryCode) {
                                   $countryQ->where('code', $userCountryCode)
                                          ->where('product_countries.is_available', true);
                               });
                });
            })->orWhere(function ($subQ) use ($userCountryCode) {
                // OR product has specific pricing for user's country and is available (any provider)
                $subQ->whereHas('countries', function ($countryQ) use ($userCountryCode) {
                    $countryQ->where('code', $userCountryCode)
                           ->where('product_countries.is_available', true);
                });
            });
        });
    }

    /**
     * Filter services based on user's country
     */
    public function filterServicesByCountry(Builder $query, ?string $userCountryCode = null): Builder
    {
        $userCountryCode = $userCountryCode ?? $this->getCurrentUserCountry();

        if (!$userCountryCode) {
            return $query; // No filtering if no country specified
        }

        return $query->where(function ($q) use ($userCountryCode) {
            // Include services where:
            // 1. Provider is from the same country AND (no specific country pricing OR has pricing for user's country)
            // 2. OR service has specific pricing for the user's country and is available (regardless of provider country)
            $q->where(function ($subQ) use ($userCountryCode) {
                // Provider from same country
                $subQ->whereHas('provider', function ($providerQ) use ($userCountryCode) {
                    $providerQ->where('country_code', $userCountryCode);
                })->where(function ($countrySubQ) use ($userCountryCode) {
                    // AND either no specific country pricing OR has pricing for user's country
                    $countrySubQ->whereDoesntHave('countries')
                               ->orWhereHas('countries', function ($countryQ) use ($userCountryCode) {
                                   $countryQ->where('code', $userCountryCode)
                                          ->where('service_countries.is_available', true);
                               });
                });
            })->orWhere(function ($subQ) use ($userCountryCode) {
                // OR service has specific pricing for user's country and is available (any provider)
                $subQ->whereHas('countries', function ($countryQ) use ($userCountryCode) {
                    $countryQ->where('code', $userCountryCode)
                           ->where('service_countries.is_available', true);
                });
            });
        });
    }

    /**
     * Filter providers based on user's country
     */
    public function filterProvidersByCountry(Builder $query, ?string $userCountryCode = null): Builder
    {
        $userCountryCode = $userCountryCode ?? $this->getCurrentUserCountry();
        
        if (!$userCountryCode) {
            return $query; // No filtering if no country specified
        }

        return $query->where(function ($q) use ($userCountryCode) {
            // Include providers where:
            // 1. Provider is from the same country
            // 2. OR provider has services/products available in user's country
            $q->where('country_code', $userCountryCode)
              ->orWhereHas('services.countries', function ($countryQ) use ($userCountryCode) {
                  $countryQ->where('code', $userCountryCode)
                         ->where('service_countries.is_available', true);
              })
              ->orWhereHas('user.products.countries', function ($countryQ) use ($userCountryCode) {
                  $countryQ->where('code', $userCountryCode)
                         ->where('product_countries.is_available', true);
              });
        });
    }

    /**
     * Get products for a specific country
     */
    public function getProductsForCountry(string $countryCode): \Illuminate\Database\Eloquent\Collection
    {
        $query = Product::with(['category', 'images', 'user.provider', 'countries'])
                       ->where('is_active', true);
        
        return $this->filterProductsByCountry($query, $countryCode)->get();
    }

    /**
     * Get services for a specific country
     */
    public function getServicesForCountry(string $countryCode): \Illuminate\Database\Eloquent\Collection
    {
        $query = Service::with(['provider', 'countries'])
                       ->where('active', true);
        
        return $this->filterServicesByCountry($query, $countryCode)->get();
    }

    /**
     * Get providers for a specific country
     */
    public function getProvidersForCountry(string $countryCode): \Illuminate\Database\Eloquent\Collection
    {
        $query = Provider::with(['user', 'services', 'country'])
                        ->where('verification_status', 'approved');
        
        return $this->filterProvidersByCountry($query, $countryCode)->get();
    }
}
