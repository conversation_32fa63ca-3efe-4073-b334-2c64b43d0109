<?php

namespace App\Repositories;

use App\Models\Provider;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProviderRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Provider $model
     * @return void
     */
    public function __construct(Provider $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find provider by user ID.
     *
     * @param int $userId
     * @return Provider|null
     */
    public function findByUserId(int $userId): ?Provider
    {
        return $this->findOneBy(['user_id' => $userId]);
    }
    
    /**
     * Find providers by clinic.
     *
     * @param int $clinicId
     * @return Collection
     */
    public function findByClinic(int $clinicId): Collection
    {
        return $this->findBy(['clinic_id' => $clinicId]);
    }
    
    /**
     * Find verified providers.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findVerified(?int $clinicId = null): Collection
    {
        $criteria = ['verification_status' => 'verified'];
        
        if ($clinicId) {
            $criteria['clinic_id'] = $clinicId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find providers by specialization.
     *
     * @param string $specialization
     * @param int|null $clinicId
     * @return Collection
     */
    public function findBySpecialization(string $specialization, ?int $clinicId = null): Collection
    {
        $criteria = ['specialization' => $specialization];
        
        if ($clinicId) {
            $criteria['clinic_id'] = $clinicId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Search providers by name or specialization.
     *
     * @param string $search
     * @param int|null $clinicId
     * @return Collection
     */
    public function search(string $search, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->with('user')
              ->where(function ($q) use ($search) {
                  $q->where('specialization', 'like', "%{$search}%")
                    ->orWhere('bio', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                                  ->orWhere('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%");
                    });
              });
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Find providers by rating range.
     *
     * @param float $minRating
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByRating(float $minRating, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('rating', '>=', $minRating);
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Get providers with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with('user', 'clinic', 'services.countries', 'country');

        // Apply country filtering first if user country is available
        if (!empty($filters['user_country_code'])) {
            $countryFilterService = new \App\Services\CountryFilterService();
            $query = $countryFilterService->filterProvidersByCountry($query, $filters['user_country_code']);
        }

        // Apply clinic filter
        if (!empty($filters['clinic_id'])) {
            $query->where('clinic_id', $filters['clinic_id']);
        }
        
        // Apply verification status filter
        if (!empty($filters['verification_status'])) {
            $query->where('verification_status', $filters['verification_status']);
        }
        
        // Apply specialization filter
        if (!empty($filters['specialization'])) {
            $query->where('specialization', $filters['specialization']);
        }
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('specialization', 'like', "%{$search}%")
                  ->orWhere('bio', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }
        
        // Apply rating filter
        if (!empty($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        // Always filter out inactive providers
        $query->whereHas('user', function ($q) {
            $q->where('is_active', true);
        });

        // Only show providers with approved and active services for public use
        if (!empty($filters['public_only'])) {
            $query->whereHas('services', function ($q) {
                $q->where('active', true)
                  ->where('approval_status', 'approved');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Get provider statistics for a clinic.
     *
     * @param int $clinicId
     * @return array
     */
    public function getStatistics(int $clinicId): array
    {
        $query = $this->model->newQuery();
        $query->where('clinic_id', $clinicId);
        
        $total = $query->count();
        $verified = $query->where('verification_status', 'verified')->count();
        $pending = $query->where('verification_status', 'pending')->count();
        
        // Specialization distribution
        $specializations = $query->groupBy('specialization')
                                ->selectRaw('specialization, count(*) as count')
                                ->pluck('count', 'specialization')
                                ->toArray();
        
        return [
            'total' => $total,
            'verification_status' => [
                'verified' => $verified,
                'pending' => $pending,
                'rejected' => $total - $verified - $pending
            ],
            'specializations' => $specializations
        ];
    }
    
    /**
     * Get providers for location-based search.
     *
     * @param array $filters
     * @return Collection
     */
    public function getProvidersForLocationSearch(array $filters = []): Collection
    {
        $query = $this->model->newQuery();
        $query->with(['user', 'services', 'clinic']);

        // Apply clinic filtering
        if (!empty($filters['clinic_id'])) {
            $query->where('clinic_id', $filters['clinic_id']);
        }

        // Always filter out inactive providers
        $query->whereHas('user', function ($q) {
            $q->where('is_active', true);
        });

        // Only show providers with approved and active services for public use
        if (!empty($filters['public_only'])) {
            $query->whereHas('services', function ($q) {
                $q->where('active', true)
                  ->where('approval_status', 'approved');
            });
        }

        // Apply other filters
        if (!empty($filters['specialization'])) {
            $query->where('specialization', $filters['specialization']);
        }

        if (!empty($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        return $query->get();
    }

    /**
     * Update provider verification status.
     *
     * @param int $providerId
     * @param string $status
     * @param string|null $reason
     * @return bool
     */
    public function updateVerificationStatus(int $providerId, string $status, ?string $reason = null): bool
    {
        $data = [
            'verification_status' => $status,
            'verified_at' => $status === 'verified' ? now() : null,
            'rejection_reason' => $reason
        ];

        $provider = $this->update($providerId, $data);
        return $provider !== null;
    }
}
