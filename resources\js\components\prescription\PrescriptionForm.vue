<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">
          {{ isEditing ? 'Edit Prescription' : 'New Prescription' }}
        </h1>
        <p class="text-gray-600">
          {{ isEditing ? 'Update prescription details' : 'Create a new prescription for patient' }}
        </p>
      </div>
      <div class="flex space-x-2">
        <Button variant="outline" @click="$router.back()">Cancel</Button>
        <Button @click="savePrescription" :disabled="loading || !canSave">
          {{ loading ? 'Saving...' : 'Save Prescription' }}
        </Button>
      </div>
    </div>

    <form @submit.prevent="savePrescription" class="space-y-6">
      <!-- Basic Information -->
      <Card>
        <CardHeader>
          <CardTitle>Prescription Information</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label for="patient">Patient *</Label>
              <PatientSelect
                v-model="form.patient_id"
                :disabled="isEditing"
                required
              />
            </div>

            <div>
              <Label for="type">Prescription Type *</Label>
              <Select v-model="form.type" required>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New Prescription</SelectItem>
                  <SelectItem value="repeat">Repeat Prescription</SelectItem>
                  <SelectItem value="acute">Acute Prescription</SelectItem>
                  <SelectItem value="chronic">Chronic Prescription</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label for="prescribed_date">Prescribed Date *</Label>
              <Input
                v-model="form.prescribed_date"
                type="date"
                required
              />
            </div>

            <div>
              <Label for="valid_until">Valid Until</Label>
              <Input
                v-model="form.valid_until"
                type="date"
              />
            </div>
          </div>

          <div>
            <Label for="clinical_indication">Clinical Indication</Label>
            <Textarea
              v-model="form.clinical_indication"
              placeholder="Reason for prescribing these medications"
              rows="2"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label for="pharmacy_name">Preferred Pharmacy</Label>
              <Input
                v-model="form.pharmacy_name"
                placeholder="Pharmacy name"
              />
            </div>

            <div>
              <Label for="pharmacy_address">Pharmacy Address</Label>
              <Input
                v-model="form.pharmacy_address"
                placeholder="Pharmacy address"
              />
            </div>
          </div>

          <div>
            <Label for="additional_instructions">Additional Instructions</Label>
            <Textarea
              v-model="form.additional_instructions"
              placeholder="General instructions for the patient"
              rows="2"
            />
          </div>

          <div>
            <Label for="warnings">Warnings</Label>
            <Textarea
              v-model="form.warnings"
              placeholder="Important warnings or precautions"
              rows="2"
            />
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox
              v-model="form.is_private"
              id="private"
            />
            <Label for="private">Private prescription</Label>
          </div>
        </CardContent>
      </Card>

      <!-- Prescription Items -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Medications</CardTitle>
            <Button type="button" @click="addMedicationItem" variant="outline">
              <Plus class="w-4 h-4 mr-2" />
              Add Medication
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="form.items.length === 0" class="text-center py-8 text-gray-500">
            No medications added yet. Click "Add Medication" to start.
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(item, index) in form.items"
              :key="index"
              class="border rounded-lg p-4 space-y-4"
            >
              <div class="flex items-center justify-between">
                <h4 class="font-medium">Medication {{ index + 1 }}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeMedicationItem(index)"
                  class="text-red-600 hover:text-red-700"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                  <Label>Medication *</Label>
                  <MedicationSelect
                    v-model="item.medication_id"
                    @medication-selected="(med) => onMedicationSelected(index, med)"
                    required
                  />
                </div>

                <div>
                  <Label>Dosage *</Label>
                  <Input
                    v-model="item.dosage"
                    placeholder="e.g., 1 tablet, 5ml"
                    required
                  />
                </div>

                <div>
                  <Label>Frequency *</Label>
                  <Select v-model="item.frequency" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="once daily">Once daily</SelectItem>
                      <SelectItem value="twice daily">Twice daily</SelectItem>
                      <SelectItem value="three times daily">Three times daily</SelectItem>
                      <SelectItem value="four times daily">Four times daily</SelectItem>
                      <SelectItem value="every 4 hours">Every 4 hours</SelectItem>
                      <SelectItem value="every 6 hours">Every 6 hours</SelectItem>
                      <SelectItem value="every 8 hours">Every 8 hours</SelectItem>
                      <SelectItem value="as needed">As needed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Quantity *</Label>
                  <Input
                    v-model.number="item.quantity"
                    type="number"
                    min="1"
                    required
                  />
                </div>

                <div>
                  <Label>Unit *</Label>
                  <Select v-model="item.quantity_unit" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tablets">Tablets</SelectItem>
                      <SelectItem value="capsules">Capsules</SelectItem>
                      <SelectItem value="ml">ml</SelectItem>
                      <SelectItem value="bottles">Bottles</SelectItem>
                      <SelectItem value="tubes">Tubes</SelectItem>
                      <SelectItem value="inhalers">Inhalers</SelectItem>
                      <SelectItem value="patches">Patches</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Duration (days)</Label>
                  <Input
                    v-model.number="item.duration_days"
                    type="number"
                    min="1"
                    placeholder="e.g., 7"
                  />
                </div>
              </div>

              <div>
                <Label>Directions for Use *</Label>
                <Textarea
                  v-model="item.directions_for_use"
                  placeholder="How the patient should take this medication"
                  rows="2"
                  required
                />
              </div>

              <div>
                <Label>Additional Instructions</Label>
                <Textarea
                  v-model="item.additional_instructions"
                  placeholder="Any additional instructions for this medication"
                  rows="2"
                />
              </div>

              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="flex items-center space-x-2">
                  <Checkbox
                    v-model="item.take_with_food"
                    :id="`food-${index}`"
                  />
                  <Label :for="`food-${index}`">Take with food</Label>
                </div>

                <div class="flex items-center space-x-2">
                  <Checkbox
                    v-model="item.avoid_alcohol"
                    :id="`alcohol-${index}`"
                  />
                  <Label :for="`alcohol-${index}`">Avoid alcohol</Label>
                </div>

                <div class="flex items-center space-x-2">
                  <Checkbox
                    v-model="item.is_repeat_eligible"
                    :id="`repeat-${index}`"
                  />
                  <Label :for="`repeat-${index}`">Repeat eligible</Label>
                </div>

                <div v-if="item.is_repeat_eligible">
                  <Label>Repeats allowed</Label>
                  <Input
                    v-model.number="item.repeats_allowed"
                    type="number"
                    min="0"
                    max="5"
                    placeholder="0"
                  />
                </div>
              </div>

              <div v-if="item.warnings">
                <Label>Warnings</Label>
                <Textarea
                  v-model="item.warnings"
                  placeholder="Specific warnings for this medication"
                  rows="2"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePrescriptionApi, type Prescription, type PrescriptionItem } from '@/composables/usePrescriptionApi'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2 } from 'lucide-vue-next'
import PatientSelect from '@/components/ui/PatientSelect.vue'
import MedicationSelect from '@/components/ui/MedicationSelect.vue'

const route = useRoute()
const router = useRouter()
const { currentPrescription, loading, error, getPrescription, createPrescription, updatePrescription } = usePrescriptionApi()

const isEditing = ref(false)
const prescriptionId = ref<number | null>(null)

const form = reactive({
  patient_id: null as number | null,
  consultation_id: null as number | null,
  type: '',
  prescribed_date: '',
  valid_until: '',
  clinical_indication: '',
  additional_instructions: '',
  warnings: '',
  is_private: false,
  pharmacy_name: '',
  pharmacy_address: '',
  items: [] as Partial<PrescriptionItem>[]
})

const canSave = computed(() => {
  return form.patient_id && 
         form.type && 
         form.prescribed_date && 
         form.items.length > 0 &&
         form.items.every(item => 
           item.medication_id && 
           item.dosage && 
           item.frequency && 
           item.quantity && 
           item.quantity_unit && 
           item.directions_for_use
         )
})

const addMedicationItem = () => {
  form.items.push({
    medication_id: null,
    medication_name: '',
    strength: '',
    form: '',
    dosage: '',
    frequency: '',
    route: 'oral',
    quantity: 1,
    quantity_unit: '',
    duration_days: null,
    directions_for_use: '',
    additional_instructions: '',
    take_with_food: false,
    avoid_alcohol: false,
    warnings: '',
    is_repeat_eligible: false,
    repeats_allowed: 0
  })
}

const removeMedicationItem = (index: number) => {
  form.items.splice(index, 1)
}

const onMedicationSelected = (index: number, medication: any) => {
  const item = form.items[index]
  if (item && medication) {
    item.medication_name = medication.display_name || medication.name
    item.strength = medication.strength
    item.form = medication.form
    item.route = medication.route || 'oral'
  }
}

const savePrescription = async () => {
  try {
    const prescriptionData = {
      ...form,
      items: form.items.map(item => ({
        ...item,
        take_with_food: item.take_with_food || false,
        avoid_alcohol: item.avoid_alcohol || false,
        is_repeat_eligible: item.is_repeat_eligible || false,
        repeats_allowed: item.repeats_allowed || 0
      }))
    }

    if (isEditing.value && prescriptionId.value) {
      await updatePrescription(prescriptionId.value, prescriptionData)
      router.push(`/prescriptions/${prescriptionId.value}`)
    } else {
      const response = await createPrescription(prescriptionData)
      if (response?.data) {
        router.push(`/prescriptions/${response.data.id}`)
      }
    }
  } catch (err) {
    console.error('Error saving prescription:', err)
  }
}

const loadPrescription = (prescription: Prescription) => {
  Object.keys(form).forEach(key => {
    if (key === 'items') return // Handle items separately
    if (key in prescription) {
      form[key as keyof typeof form] = prescription[key as keyof Prescription] as any
    }
  })

  // Load prescription items
  if (prescription.items) {
    form.items = prescription.items.map(item => ({ ...item }))
  }
}

onMounted(async () => {
  // Set default date to today
  const today = new Date().toISOString().split('T')[0]
  form.prescribed_date = today
  
  // Set default valid until date (6 months from now)
  const sixMonthsFromNow = new Date()
  sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6)
  form.valid_until = sixMonthsFromNow.toISOString().split('T')[0]

  // Check if editing existing prescription
  if (route.params.id) {
    isEditing.value = true
    prescriptionId.value = Number(route.params.id)
    await getPrescription(prescriptionId.value)
    if (currentPrescription.value) {
      loadPrescription(currentPrescription.value)
    }
  } else {
    // Check for consultation_id in query params
    if (route.query.consultation_id) {
      form.consultation_id = Number(route.query.consultation_id)
    }
    
    // Add one empty medication item by default
    addMedicationItem()
  }
})
</script>
