<?php

namespace App\Services;

use App\Models\User;
use App\Models\Clinic;
use App\Models\LabTestRequest;

class HL7MessageService
{
    /**
     * Generate HL7 ORM^O01 message for lab order
     */
    public function generateOrderMessage(LabTestRequest $request): string
    {
        $patient = $request->patient;
        $provider = $request->provider;
        $clinic = $request->clinic;
        $tests = $request->tests;

        // HL7 field separators
        $fieldSeparator = '|';
        $componentSeparator = '^';
        $subcomponentSeparator = '&';
        $repeatSeparator = '~';
        $escapeChar = '\\';

        // Message header details
        $sendingApplication = 'MEDROID';
        $sendingFacility = $this->formatFacilityName($clinic->name);
        $receivingApplication = 'TDL';
        $receivingFacility = 'TDL';
        $messageType = 'ORM^O01';
        $processingId = 'P'; // Production
        $versionId = '2.3';

        // Generate unique message control ID
        $messageControlId = 'MED' . date('ymdHis') . rand(100, 999);
        $datetime = date('YmdHis');

        // Build HL7 segments
        $segments = [];

        // MSH - Message Header
        $segments[] = $this->buildMSHSegment(
            $fieldSeparator,
            $componentSeparator . $subcomponentSeparator . $repeatSeparator . $escapeChar,
            $sendingApplication,
            $sendingFacility,
            $receivingApplication,
            $receivingFacility,
            $datetime,
            $messageType,
            $messageControlId,
            $processingId,
            $versionId
        );

        // PID - Patient Identification
        $segments[] = $this->buildPIDSegment($patient, $fieldSeparator);

        // PV1 - Patient Visit
        $segments[] = $this->buildPV1Segment($provider, $clinic, $fieldSeparator);

        // ORC - Common Order
        $segments[] = $this->buildORCSegment($request, $provider, $fieldSeparator);

        // OBR - Observation Request (one for each test)
        foreach ($tests as $index => $test) {
            $segments[] = $this->buildOBRSegment(
                $test, 
                $request, 
                $provider, 
                $index + 1, 
                $fieldSeparator
            );
        }

        return implode("\r\n", $segments);
    }

    /**
     * Parse HL7 ORU^R01 result message
     */
    public function parseResultMessage(string $hl7Message): array
    {
        $segments = explode("\r\n", $hl7Message);
        $result = [
            'patient_info' => [],
            'tests' => [],
            'report_date' => null,
            'lab_reference' => null
        ];

        foreach ($segments as $segment) {
            $fields = explode('|', $segment);
            $segmentType = substr($segment, 0, 3);

            switch ($segmentType) {
                case 'MSH':
                    $result['lab_reference'] = $fields[10] ?? null;
                    break;

                case 'PID':
                    $result['patient_info'] = $this->parsePIDSegment($fields);
                    break;

                case 'OBR':
                    $currentTest = $this->parseOBRSegment($fields);
                    $result['tests'][] = $currentTest;
                    break;

                case 'OBX':
                    if (!empty($result['tests'])) {
                        $lastTestIndex = count($result['tests']) - 1;
                        $biomarker = $this->parseOBXSegment($fields);
                        $result['tests'][$lastTestIndex]['biomarkers'][] = $biomarker;
                    }
                    break;
            }
        }

        $result['report_date'] = date('Y-m-d');
        return $result;
    }

    /**
     * Build MSH segment
     */
    private function buildMSHSegment(
        string $fieldSeparator,
        string $encodingChars,
        string $sendingApp,
        string $sendingFacility,
        string $receivingApp,
        string $receivingFacility,
        string $datetime,
        string $messageType,
        string $messageControlId,
        string $processingId,
        string $versionId
    ): string {
        return "MSH{$fieldSeparator}{$encodingChars}{$fieldSeparator}{$sendingApp}{$fieldSeparator}{$sendingFacility}{$fieldSeparator}{$receivingApp}{$fieldSeparator}{$receivingFacility}{$fieldSeparator}{$datetime}{$fieldSeparator}{$fieldSeparator}{$messageType}{$fieldSeparator}{$messageControlId}{$fieldSeparator}{$processingId}{$fieldSeparator}{$versionId}";
    }

    /**
     * Build PID segment
     */
    private function buildPIDSegment(User $patient, string $fieldSeparator): string
    {
        $patientId = $patient->id;
        $patientName = $this->formatPatientName($patient);
        $dob = $this->formatDateOfBirth($patient);
        $gender = $this->formatGender($patient);
        $address = $this->formatAddress($patient);

        return "PID{$fieldSeparator}1{$fieldSeparator}{$patientId}{$fieldSeparator}{$patientId}{$fieldSeparator}{$fieldSeparator}{$patientName}{$fieldSeparator}{$fieldSeparator}{$dob}{$fieldSeparator}{$gender}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$address}";
    }

    /**
     * Build PV1 segment
     */
    private function buildPV1Segment(User $provider, Clinic $clinic, string $fieldSeparator): string
    {
        $providerName = $this->formatProviderName($provider);
        $clinicName = $this->formatFacilityName($clinic->name);

        return "PV1{$fieldSeparator}1{$fieldSeparator}O{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$providerName}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$clinicName}";
    }

    /**
     * Build ORC segment
     */
    private function buildORCSegment(LabTestRequest $request, User $provider, string $fieldSeparator): string
    {
        $orderNumber = $request->order_number;
        $providerName = $this->formatProviderName($provider);
        $orderDateTime = $request->created_at->format('YmdHis');

        return "ORC{$fieldSeparator}NW{$fieldSeparator}{$orderNumber}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$orderDateTime}{$fieldSeparator}{$fieldSeparator}{$providerName}";
    }

    /**
     * Build OBR segment
     */
    private function buildOBRSegment(array $test, LabTestRequest $request, User $provider, int $setId, string $fieldSeparator): string
    {
        $orderNumber = $request->order_number;
        $testCode = $test['test_code'];
        $testName = $test['test_name'];
        $providerName = $this->formatProviderName($provider);
        $orderDateTime = $request->created_at->format('YmdHis');

        return "OBR{$fieldSeparator}{$setId}{$fieldSeparator}{$orderNumber}{$fieldSeparator}{$fieldSeparator}{$testCode}^{$testName}{$fieldSeparator}{$fieldSeparator}{$orderDateTime}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$fieldSeparator}{$providerName}";
    }

    /**
     * Parse PID segment
     */
    private function parsePIDSegment(array $fields): array
    {
        return [
            'patient_id' => $fields[3] ?? '',
            'name' => $this->parsePatientName($fields[5] ?? ''),
            'dob' => $this->parseDateOfBirth($fields[7] ?? ''),
            'gender' => $fields[8] ?? ''
        ];
    }

    /**
     * Parse OBR segment
     */
    private function parseOBRSegment(array $fields): array
    {
        $testInfo = explode('^', $fields[4] ?? '');
        
        return [
            'test_code' => $testInfo[0] ?? '',
            'test_name' => $testInfo[1] ?? '',
            'biomarkers' => []
        ];
    }

    /**
     * Parse OBX segment
     */
    private function parseOBXSegment(array $fields): array
    {
        $observationId = explode('^', $fields[3] ?? '');
        
        return [
            'name' => $observationId[1] ?? $observationId[0] ?? '',
            'value' => $fields[5] ?? '',
            'units' => $fields[6] ?? '',
            'reference_range' => $fields[7] ?? '',
            'abnormal_flag' => $fields[8] ?? 'N',
            'observation_datetime' => $this->parseDateTime($fields[14] ?? '')
        ];
    }

    /**
     * Helper methods for formatting
     */
    private function formatFacilityName(string $name): string
    {
        return strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 10));
    }

    private function formatPatientName(User $patient): string
    {
        return strtoupper($patient->last_name ?? '') . '^' . strtoupper($patient->first_name ?? '');
    }

    private function formatProviderName(User $provider): string
    {
        return strtoupper($provider->last_name ?? '') . '^' . strtoupper($provider->first_name ?? '');
    }

    private function formatDateOfBirth(User $patient): string
    {
        if (isset($patient->date_of_birth)) {
            return date('Ymd', strtotime($patient->date_of_birth));
        }
        return '';
    }

    private function formatGender(User $patient): string
    {
        return strtoupper(substr($patient->gender ?? 'U', 0, 1));
    }

    private function formatAddress(User $patient): string
    {
        $address = $patient->address ?? '';
        return str_replace(['|', '^', '&', '~', '\\'], ' ', $address);
    }

    private function parsePatientName(string $nameField): string
    {
        $parts = explode('^', $nameField);
        $lastName = $parts[0] ?? '';
        $firstName = $parts[1] ?? '';
        return trim($firstName . ' ' . $lastName);
    }

    private function parseDateOfBirth(string $dobField): string
    {
        if (strlen($dobField) === 8) {
            return substr($dobField, 0, 4) . '-' . substr($dobField, 4, 2) . '-' . substr($dobField, 6, 2);
        }
        return '';
    }

    private function parseDateTime(string $dateTimeField): ?string
    {
        if (strlen($dateTimeField) >= 8) {
            $date = substr($dateTimeField, 0, 8);
            return substr($date, 0, 4) . '-' . substr($date, 4, 2) . '-' . substr($date, 6, 2);
        }
        return null;
    }
}
