import { ref } from 'vue'
import axios from 'axios'

export interface VitalSignField {
  key: string
  type: string
  unit?: string
  label: string
  placeholder?: string
  required: boolean
  min?: number
  max?: number
  calculated?: boolean
  formula?: string
}

export interface TabField {
  key: string
  type: string
  label: string
  placeholder?: string
  required: boolean
  multiple: boolean
  order?: number
  templates?: string[]
  icon?: string
}

export interface ConsultationTemplate {
  vital_signs: Record<string, VitalSignField>
  main_tabs: Record<string, TabField>
  additional_tabs: Record<string, TabField>
}

export interface ConsultationData {
  vital_signs?: Record<string, any>
  main_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
  additional_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
}

export function useConsultationTemplate() {
  const template = ref<ConsultationTemplate | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Load template from API
  const loadTemplate = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await axios.get('/consultations/template')

      if (response.data && response.data.data) {
        template.value = response.data.data
        console.log('Template loaded successfully:', template.value)
      } else {
        throw new Error('Invalid template response format')
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to load consultation template'
      console.error('Error loading consultation template:', err)
      console.error('Response status:', err.response?.status)
      console.error('Response data:', err.response?.data)

      // Provide fallback template structure if API fails
      template.value = {
        vital_signs: {
          temperature: { key: 'temperature', type: 'number', label: 'Temperature', unit: '°F', required: false, placeholder: '' },
          blood_pressure_systolic: { key: 'blood_pressure_systolic', type: 'number', label: 'Blood Pressure (Systolic)', unit: 'mmHg', required: false, placeholder: '' },
          blood_pressure_diastolic: { key: 'blood_pressure_diastolic', type: 'number', label: 'Blood Pressure (Diastolic)', unit: 'mmHg', required: false, placeholder: '' },
          heart_rate: { key: 'heart_rate', type: 'number', label: 'Heart Rate', unit: 'bpm', required: false, placeholder: '' },
          respiratory_rate: { key: 'respiratory_rate', type: 'number', label: 'Respiratory Rate', unit: 'breaths/min', required: false, placeholder: '16' },
          oxygen_saturation: { key: 'oxygen_saturation', type: 'number', label: 'Oxygen Saturation', unit: '%', required: false, placeholder: '98' },
          weight: { key: 'weight', type: 'number', label: 'Weight', unit: 'kg', required: false, placeholder: '70' },
          height: { key: 'height', type: 'number', label: 'Height', unit: 'cm', required: false, placeholder: '175' }
        },
        main_tabs: {
          concerns: { key: 'concerns', type: 'textarea', label: 'Present Concerns', required: true, multiple: true, order: 1 },
          history: { key: 'history', type: 'textarea', label: 'History of Present Illness', required: false, multiple: true, order: 2 },
          examination: { key: 'examination', type: 'textarea', label: 'Physical Examination', required: false, multiple: true, order: 3 },
          plan: { key: 'plan', type: 'textarea', label: 'Assessment & Plan', required: false, multiple: true, order: 4 }
        },
        additional_tabs: {
          allergies: { key: 'allergies', type: 'textarea', label: 'Allergies', required: false, multiple: true },
          family_history: { key: 'family_history', type: 'textarea', label: 'Family History', required: false, multiple: true },
          social_history: { key: 'social_history', type: 'textarea', label: 'Social History', required: false, multiple: true },
          past_medical_history: { key: 'past_medical_history', type: 'textarea', label: 'Past Medical History', required: false, multiple: true },
          medications: { key: 'medications', type: 'textarea', label: 'Current Medications', required: false, multiple: true }
        }
      }
      console.log('Using fallback template')
    } finally {
      loading.value = false
    }
  }



  // Initialize empty consultation data based on template
  const initializeConsultationData = (): ConsultationData => {
    const data: ConsultationData = {
      vital_signs: {},
      main_tabs: {},
      additional_tabs: {}
    }

    // Initialize vital signs with empty values
    if (template.value?.vital_signs) {
      Object.keys(template.value.vital_signs).forEach(key => {
        data.vital_signs![key] = ''
      })
    }

    // Initialize main tabs with empty arrays
    if (template.value?.main_tabs) {
      Object.keys(template.value.main_tabs).forEach(key => {
        data.main_tabs![key] = []
      })
    }

    // Additional tabs start empty (user adds them as needed)
    data.additional_tabs = {}

    return data
  }

  // Add new entry to a tab
  const addTabEntry = (consultationData: ConsultationData, tabType: 'main_tabs' | 'additional_tabs', tabKey: string, content: string) => {
    if (!consultationData[tabType]) {
      consultationData[tabType] = {}
    }
    
    if (!consultationData[tabType]![tabKey]) {
      consultationData[tabType]![tabKey] = []
    }

    const newEntry = {
      id: Date.now(), // Simple ID generation
      content,
      created_at: new Date().toISOString()
    }

    consultationData[tabType]![tabKey].push(newEntry)
  }

  // Remove entry from a tab
  const removeTabEntry = (consultationData: ConsultationData, tabType: 'main_tabs' | 'additional_tabs', tabKey: string, entryId: number) => {
    if (consultationData[tabType]?.[tabKey]) {
      consultationData[tabType]![tabKey] = consultationData[tabType]![tabKey].filter(entry => entry.id !== entryId)
    }
  }



  // Calculate BMI if height and weight are provided
  const calculateBMI = (consultationData: ConsultationData) => {
    const weight = parseFloat(consultationData.vital_signs?.weight || '0')
    const height = parseFloat(consultationData.vital_signs?.height || '0')
    
    if (weight > 0 && height > 0) {
      const heightInMeters = height / 100
      const bmi = weight / (heightInMeters * heightInMeters)
      consultationData.vital_signs!.bmi = bmi.toFixed(1)
    }
  }

  // Validate consultation data
  const validateConsultationData = (consultationData: ConsultationData): string[] => {
    const errors: string[] = []

    // Check required main tabs
    if (template.value?.main_tabs) {
      Object.entries(template.value.main_tabs).forEach(([key, field]) => {
        if (field.required && (!consultationData.main_tabs?.[key] || consultationData.main_tabs[key].length === 0)) {
          errors.push(`${field.label} is required`)
        }
      })
    }

    // Check vital signs ranges
    if (template.value?.vital_signs && consultationData.vital_signs) {
      Object.entries(template.value.vital_signs).forEach(([key, field]) => {
        const value = parseFloat(consultationData.vital_signs![key] || '0')
        if (value > 0) {
          if (field.min !== undefined && value < field.min) {
            errors.push(`${field.label} must be at least ${field.min}${field.unit || ''}`)
          }
          if (field.max !== undefined && value > field.max) {
            errors.push(`${field.label} must not exceed ${field.max}${field.unit || ''}`)
          }
        }
      })
    }

    return errors
  }

  return {
    template,
    loading,
    error,
    loadTemplate,
    initializeConsultationData,
    addTabEntry,
    removeTabEntry,
    calculateBMI,
    validateConsultationData
  }
}
