<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Medication;

class MedicationSeeder extends Seeder
{
    public function run(): void
    {
        $medications = [
            // Common Pain Relief
            [
                'name' => 'Paracetamol',
                'brand_name' => 'Panadol',
                'active_ingredient' => 'Paracetamol',
                'strength' => '500mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'Pain relief and fever reduction',
                'indications' => ['Pain relief', 'Fever reduction', 'Headache'],
                'contraindications' => ['Severe liver disease'],
                'side_effects' => ['Nausea', 'Skin rash (rare)'],
                'drug_class' => 'Analgesic',
                'requires_prescription' => false,
                'is_active' => true,
                'regulatory_code' => 'UK001'
            ],
            [
                'name' => 'Ibuprofen',
                'brand_name' => 'Nurofen',
                'active_ingredient' => 'Ibuprofen',
                'strength' => '200mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'Anti-inflammatory pain relief',
                'indications' => ['Pain relief', 'Inflammation', 'Fever'],
                'contraindications' => ['Peptic ulcer', 'Severe heart failure'],
                'side_effects' => ['Stomach upset', 'Dizziness', 'Headache'],
                'drug_class' => 'NSAID',
                'requires_prescription' => false,
                'is_active' => true,
                'regulatory_code' => 'UK002'
            ],
            
            // Antibiotics
            [
                'name' => 'Amoxicillin',
                'brand_name' => 'Amoxil',
                'active_ingredient' => 'Amoxicillin',
                'strength' => '250mg',
                'form' => 'capsule',
                'route' => 'oral',
                'description' => 'Broad-spectrum antibiotic',
                'indications' => ['Bacterial infections', 'Respiratory tract infections'],
                'contraindications' => ['Penicillin allergy'],
                'side_effects' => ['Diarrhea', 'Nausea', 'Skin rash'],
                'drug_class' => 'Antibiotic',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK003'
            ],
            [
                'name' => 'Amoxicillin',
                'brand_name' => 'Amoxil',
                'active_ingredient' => 'Amoxicillin',
                'strength' => '500mg',
                'form' => 'capsule',
                'route' => 'oral',
                'description' => 'Broad-spectrum antibiotic',
                'indications' => ['Bacterial infections', 'Respiratory tract infections'],
                'contraindications' => ['Penicillin allergy'],
                'side_effects' => ['Diarrhea', 'Nausea', 'Skin rash'],
                'drug_class' => 'Antibiotic',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK004'
            ],
            
            // Cardiovascular
            [
                'name' => 'Lisinopril',
                'brand_name' => 'Prinivil',
                'active_ingredient' => 'Lisinopril',
                'strength' => '10mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'ACE inhibitor for blood pressure',
                'indications' => ['Hypertension', 'Heart failure'],
                'contraindications' => ['Pregnancy', 'Angioedema history'],
                'side_effects' => ['Dry cough', 'Dizziness', 'Fatigue'],
                'drug_class' => 'ACE Inhibitor',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK005'
            ],
            [
                'name' => 'Amlodipine',
                'brand_name' => 'Norvasc',
                'active_ingredient' => 'Amlodipine',
                'strength' => '5mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'Calcium channel blocker',
                'indications' => ['Hypertension', 'Angina'],
                'contraindications' => ['Severe aortic stenosis'],
                'side_effects' => ['Ankle swelling', 'Flushing', 'Dizziness'],
                'drug_class' => 'Calcium Channel Blocker',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK006'
            ],
            
            // Diabetes
            [
                'name' => 'Metformin',
                'brand_name' => 'Glucophage',
                'active_ingredient' => 'Metformin HCl',
                'strength' => '500mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'Type 2 diabetes medication',
                'indications' => ['Type 2 diabetes', 'PCOS'],
                'contraindications' => ['Kidney disease', 'Liver disease'],
                'side_effects' => ['Nausea', 'Diarrhea', 'Metallic taste'],
                'drug_class' => 'Antidiabetic',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK007'
            ],
            
            // Respiratory
            [
                'name' => 'Salbutamol',
                'brand_name' => 'Ventolin',
                'active_ingredient' => 'Salbutamol',
                'strength' => '100mcg',
                'form' => 'inhaler',
                'route' => 'inhalation',
                'description' => 'Bronchodilator for asthma',
                'indications' => ['Asthma', 'COPD', 'Bronchospasm'],
                'contraindications' => ['Hypersensitivity to salbutamol'],
                'side_effects' => ['Tremor', 'Palpitations', 'Headache'],
                'drug_class' => 'Bronchodilator',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK008'
            ],
            
            // Gastrointestinal
            [
                'name' => 'Omeprazole',
                'brand_name' => 'Losec',
                'active_ingredient' => 'Omeprazole',
                'strength' => '20mg',
                'form' => 'capsule',
                'route' => 'oral',
                'description' => 'Proton pump inhibitor',
                'indications' => ['GERD', 'Peptic ulcer', 'Dyspepsia'],
                'contraindications' => ['Hypersensitivity to omeprazole'],
                'side_effects' => ['Headache', 'Nausea', 'Diarrhea'],
                'drug_class' => 'Proton Pump Inhibitor',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK009'
            ],
            
            // Mental Health
            [
                'name' => 'Sertraline',
                'brand_name' => 'Zoloft',
                'active_ingredient' => 'Sertraline HCl',
                'strength' => '50mg',
                'form' => 'tablet',
                'route' => 'oral',
                'description' => 'SSRI antidepressant',
                'indications' => ['Depression', 'Anxiety', 'PTSD'],
                'contraindications' => ['MAOI use', 'Pregnancy (relative)'],
                'side_effects' => ['Nausea', 'Insomnia', 'Sexual dysfunction'],
                'drug_class' => 'SSRI',
                'requires_prescription' => true,
                'is_active' => true,
                'regulatory_code' => 'UK010'
            ]
        ];

        foreach ($medications as $medicationData) {
            // Check if medication already exists
            $exists = Medication::where('name', $medicationData['name'])
                ->where('strength', $medicationData['strength'])
                ->where('form', $medicationData['form'])
                ->exists();

            if (!$exists) {
                Medication::create($medicationData);
            }
        }
    }
}
