<?php

/**
 * Custom Laravel development server with broken pipe error handling
 * This script handles the "Broken pipe" error that occurs when clients disconnect
 * before the server finishes writing data.
 */

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate the Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists(__DIR__.'/public'.$uri)) {
    return false;
}

// Set error handling to suppress broken pipe errors
set_error_handler(function($severity, $message, $file, $line) {
    // Suppress broken pipe errors (errno 32)
    if (strpos($message, 'Broken pipe') !== false || 
        strpos($message, 'errno=32') !== false ||
        strpos($message, 'file_put_contents') !== false) {
        return true; // Suppress the error
    }
    
    // Let other errors be handled normally
    return false;
});

try {
    require_once __DIR__.'/public/index.php';
} catch (Exception $e) {
    // Only log non-broken-pipe errors
    if (strpos($e->getMessage(), 'Broken pipe') === false) {
        error_log("Server error: " . $e->getMessage());
    }
}

restore_error_handler();