<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationNote extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'created_by',
        'note_type',
        'content',
        'attachments',
        'is_private',
        'note_date',
    ];

    protected $casts = [
        'attachments' => 'array',
        'is_private' => 'boolean',
        'note_date' => 'datetime',
    ];

    /**
     * Get the consultation that owns the note.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the user who created the note.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by note type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('note_type', $type);
    }

    /**
     * Scope to get only public notes.
     */
    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    /**
     * Scope to get only private notes.
     */
    public function scopePrivate($query)
    {
        return $query->where('is_private', true);
    }

    /**
     * Scope to order by note date.
     */
    public function scopeOrderByDate($query, $direction = 'desc')
    {
        return $query->orderBy('note_date', $direction);
    }

    /**
     * Check if the note has attachments.
     */
    public function hasAttachments()
    {
        return !empty($this->attachments);
    }

    /**
     * Get the note type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'general' => 'General Note',
            'follow_up' => 'Follow-up Note',
            'amendment' => 'Amendment',
            'addendum' => 'Addendum',
        ];

        return $types[$this->note_type] ?? ucfirst($this->note_type);
    }
}
