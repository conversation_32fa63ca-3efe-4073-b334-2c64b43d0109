# WordPress Plugin Setup Guide

## Overview

This guide explains how to set up the WordPress EHR plugin to work with the Laravel migration system.

## Step 1: Install Migration Controller

The migration controller has been added to your existing KiviCare plugin. The following files have been created/modified:

### Files Added:
- `app/controllers/KCMigrationController.php` - Main migration controller (extended existing)
- `app/controllers/KCMigrationHelpers.php` - Helper methods for data formatting

### Files Modified:
- `app/baseClasses/KCRoutes.php` - Added migration routes

## Step 2: Available API Endpoints

The following endpoints are now available in your WordPress KiviCare plugin:

### Available Endpoints (via KiviCare Routing):
```
# Core Data
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinics
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_users&clinic_id=1
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_services&clinic_id=1
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_appointments&clinic_id=1
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_encounters&clinic_id=1
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_prescriptions&clinic_id=1
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_bills&clinic_id=1

# Settings (Multi-level support)
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_admin_settings
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_clinic_settings&clinic_id=1

# Reference Data
GET /wp-admin/admin-ajax.php?action=ajax_get&route_name=laravel_get_static_data
```

## Step 3: Authentication Setup

### WordPress Session Authentication

The migration system uses WordPress session-based authentication. You need to:

1. **Log into WordPress Admin** as an administrator
2. **Get the session cookie** from your browser
3. **Get a WordPress nonce** for AJAX requests

### Getting Session Cookie:

1. Log into your WordPress admin panel
2. Open browser developer tools (F12)
3. Go to Application/Storage tab → Cookies
4. Find your WordPress site domain
5. Copy the value of `wordpress_logged_in_[hash]` cookie

### Getting WordPress Nonce:

Add this temporary code to your WordPress theme's `functions.php` or create a simple PHP page:

```php
<?php
// Temporary script to get nonce - remove after getting the value
if (is_user_logged_in() && current_user_can('administrator')) {
    echo "WordPress Nonce: " . wp_create_nonce('wp_rest');
}
?>
```

## Step 4: Laravel Configuration

Update your Laravel `.env` file with the WordPress details:

```env
# WordPress Migration Configuration
WP_API_URL=https://your-wordpress-site.com/wp-admin/admin-ajax.php
WP_API_AUTH="wordpress_logged_in_12345abcdef=username%7C1234567890%7Cabcdefghijklmnopqrstuvwxyz"
WP_API_NONCE="abc123def456"
```

## Step 5: Test the Connection

Test the WordPress API connection from Laravel:

```bash
# Test basic connectivity
php artisan migratewp:test-api

# Test clinic data retrieval
php artisan migratewp:wp-clinics --dry-run
```

## Step 6: Security Considerations

### Important Security Notes:

1. **Administrator Access Required**: Only WordPress administrators can access migration endpoints
2. **Session Expiry**: WordPress sessions expire, you may need to refresh the cookie
3. **HTTPS Required**: Always use HTTPS for production migrations
4. **Temporary Access**: Consider creating a temporary admin user just for migration

### Production Setup:

1. Create a dedicated WordPress admin user for migration
2. Use a strong password and limit access
3. Remove the migration routes after migration is complete
4. Consider IP restrictions for additional security

## Step 7: Troubleshooting

### Common Issues:

#### 1. Permission Denied (403)
- Ensure you're logged in as WordPress administrator
- Check that the session cookie is valid and not expired
- Verify the nonce is correct

#### 2. Invalid Nonce
- WordPress nonces expire after 24 hours
- Generate a new nonce using the PHP script above
- Update your Laravel `.env` file

#### 3. Session Expired
- WordPress sessions expire based on "Remember Me" setting
- Log back into WordPress admin
- Get a new session cookie value

#### 4. CORS Issues
- Ensure requests include proper headers
- WordPress may block cross-origin requests
- Consider running migration from same domain

### Debug Steps:

1. **Test WordPress Login**:
   ```bash
   curl -b "wordpress_logged_in_xxx=cookie_value" \
        https://your-site.com/wp-admin/admin-ajax.php?action=migration_get_clinics
   ```

2. **Check WordPress Error Logs**:
   ```bash
   tail -f /path/to/wordpress/wp-content/debug.log
   ```

3. **Enable WordPress Debug Mode**:
   ```php
   // In wp-config.php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```

## Step 8: Migration Execution

Once authentication is working:

```bash
# 1. Test with dry run
php artisan migratewp:all --clinic=1 --dry-run

# 2. Migrate single clinic
php artisan migratewp:all --clinic=1

# 3. Migrate all clinics
php artisan migratewp:all --clinic=all
```

## Step 9: Cleanup

After successful migration:

1. **Remove migration routes** from `KCRoutes.php`
2. **Delete temporary admin user** (if created)
3. **Remove debug code** from WordPress
4. **Clear Laravel logs** and caches

## API Response Format

All endpoints return data in this format:

```json
{
  "status": true,
  "message": "Data retrieved successfully",
  "data": [
    // Array of formatted data objects
  ]
}
```

## Need Help?

- Check the troubleshooting section above
- Review Laravel migration logs: `storage/logs/migration.log`
- Check WordPress debug logs
- Verify all authentication details are correct
