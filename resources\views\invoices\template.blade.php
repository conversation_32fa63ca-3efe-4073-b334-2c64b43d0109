<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
            font-size: 13px;
            line-height: 1.4;
            color: #1a1a1a;
            background: #ffffff;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .invoice-container {
            max-width: 210mm;
            margin: 10mm auto;
            padding: 12mm 10mm;
            background: #ffffff;
            min-height: 277mm;
            position: relative;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 18px;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -9px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 50%, #e5e7eb 100%);
        }

        .clinic-info {
            flex: 1;
            max-width: 52%;
        }

        .clinic-name {
            font-size: 22px;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 5px;
            letter-spacing: -0.4px;
            line-height: 1.1;
        }

        .clinic-details {
            color: #64748b;
            font-size: 12px;
            line-height: 1.3;
            font-weight: 400;
        }

        .invoice-info {
            text-align: right;
            min-width: 220px;
        }

        .invoice-title {
            font-size: 32px;
            font-weight: 900;
            color: #0f172a;
            margin-bottom: 10px;
            letter-spacing: -0.8px;
            line-height: 0.9;
        }

        .invoice-meta {
            background: #f8fafc;
            padding: 12px;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
        }

        .invoice-meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .invoice-meta-item:last-child {
            margin-bottom: 0;
        }

        .invoice-meta-label {
            color: #64748b;
            font-weight: 500;
            min-width: 75px;
        }

        .invoice-meta-value {
            color: #0f172a;
            font-weight: 600;
            text-align: right;
        }

        /* Patient & Provider Details Section */
        .patient-section {
            margin-bottom: 18px;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 6px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 700;
            color: #0f172a;
            margin: 0;
        }

        .patient-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: #fafbfc;
            padding: 14px;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }

        .patient-field {
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .patient-field:last-child {
            margin-bottom: 0;
        }

        .field-label {
            font-weight: 600;
            color: #374151;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.4px;
            min-width: 100px;
            flex-shrink: 0;
        }

        .field-value {
            color: #0f172a;
            font-size: 12px;
            font-weight: 500;
            flex: 1;
        }

        /* Services Section */
        .services-section {
            margin-bottom: 18px;
        }

        .services-header {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .services-title {
            font-size: 14px;
            font-weight: 700;
            color: #0f172a;
        }

        /* Payment Status */
        .payment-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 16px;
            font-weight: 600;
            font-size: 10px;
            letter-spacing: 0.3px;
            text-transform: uppercase;
        }

        .status-paid {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-unpaid {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .services-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            overflow: hidden;
            background: #ffffff;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
        }

        .services-table th {
            background: #f8fafc;
            color: #374151;
            padding: 10px 12px;
            font-weight: 600;
            text-align: left;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.4px;
            border-bottom: 1px solid #e5e7eb;
            position: relative;
        }

        .services-table th:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 20%;
            bottom: 20%;
            width: 1px;
            background: #e5e7eb;
        }

        .services-table td {
            padding: 10px 12px;
            font-size: 12px;
            color: #0f172a;
            border-bottom: 1px solid #f1f5f9;
            font-weight: 500;
        }

        .services-table tbody tr:last-child td {
            border-bottom: none;
        }

        .services-table tbody tr:hover {
            background: #fafbfc;
        }

        /* Total Section */
        .total-section {
            margin-top: 12px;
            padding-top: 10px;
            border-top: 2px solid #e5e7eb;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
            padding: 10px 12px;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }

        .total-label {
            font-size: 13px;
            font-weight: 700;
            color: #0f172a;
            text-transform: uppercase;
            letter-spacing: 0.4px;
        }

        .total-amount {
            font-size: 16px;
            font-weight: 800;
            color: #0f172a;
            letter-spacing: -0.4px;
        }

        /* Notes Section */
        .notes-section {
            margin-top: 16px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 5px;
            border-left: 3px solid #17c3b2;
            border: 1px solid #e2e8f0;
        }

        .notes-title {
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 5px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.4px;
        }

        .notes-content {
            color: #475569;
            line-height: 1.4;
            font-size: 12px;
        }

        /* Footer */
        .footer {
            text-align: center;
            color: #64748b;
            font-size: 10px;
            line-height: 1.4;
            margin-top: 24px;
            padding-top: 12px;
            border-top: 1px solid #e2e8f0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 35px;
            height: 1px;
            background: #17c3b2;
        }

        .footer-highlight {
            color: #0f172a;
            font-weight: 600;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .footer-contact {
            margin-top: 6px;
            font-size: 9px;
            color: #9ca3af;
        }

        /* Text Utilities */
        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        /* Print Styles */
        @media print {
            @page {
                size: A4;
                margin: 15mm 12mm;
            }

            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                font-size: 11px;
                margin: 0;
                padding: 0;
                background: white !important;
            }

            .invoice-container {
                padding: 0;
                max-width: none;
                width: auto;
                margin: 0;
                min-height: auto;
                box-shadow: none;
                background: #ffffff !important;
                border: none;
            }

            .header {
                page-break-inside: avoid;
                margin-bottom: 12px;
            }

            .services-table {
                page-break-inside: avoid;
            }

            .footer {
                page-break-inside: avoid;
            }

            .payment-status {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .patient-section,
            .services-section {
                page-break-inside: avoid;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 16px;
                margin: 0;
            }

            .header {
                flex-direction: column;
                gap: 20px;
            }

            .invoice-info {
                text-align: left;
                min-width: auto;
            }

            .patient-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .services-table {
                font-size: 11px;
            }

            .services-table th,
            .services-table td {
                padding: 8px 10px;
            }

            .field-label {
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="clinic-info">
                <div class="clinic-name">{{ $letterhead['clinic_name'] }}</div>
                <div class="clinic-details">
                    @if($letterhead['address'])
                        {{ $letterhead['address'] }}<br>
                    @endif
                    @if($letterhead['phone'])
                        Phone: {{ $letterhead['phone'] }}<br>
                    @endif
                    @if($letterhead['email'])
                        Email: {{ $letterhead['email'] }}<br>
                    @endif
                    @if($letterhead['website'])
                        Website: {{ $letterhead['website'] }}
                    @endif
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-meta">
                    <div class="invoice-meta-item">
                        <span class="invoice-meta-label">Invoice #:</span>
                        <span class="invoice-meta-value">{{ $invoice_number }}</span>
                    </div>
                    <div class="invoice-meta-item">
                        <span class="invoice-meta-label">Date:</span>
                        <span class="invoice-meta-value">{{ $invoice_date }}</span>
                    </div>
                    @if($due_date)
                    <div class="invoice-meta-item">
                        <span class="invoice-meta-label">Due Date:</span>
                        <span class="invoice-meta-value">{{ $due_date }}</span>
                    </div>
                    @endif
                    <div class="invoice-meta-item">
                        <span class="invoice-meta-label">Generated:</span>
                        <span class="invoice-meta-value">{{ $generated_at }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patient & Provider Details Section -->
        <div class="patient-section">
            <div class="patient-grid">
                <!-- Patient Details -->
                <div>
                    <div class="section-header">
                        <h2 class="section-title">Patient Details</h2>
                    </div>
                    <div class="patient-field">
                        <div class="field-label">Name</div>
                        <div class="field-value">{{ $patient->user->name ?? 'N/A' }}</div>
                    </div>
                    <div class="patient-field">
                        <div class="field-label">Date of Birth</div>
                        <div class="field-value">{{ $patient->date_of_birth ?? 'N/A' }}</div>
                    </div>
                    <div class="patient-field">
                        <div class="field-label">Gender</div>
                        <div class="field-value">{{ $patient->gender ?? 'N/A' }}</div>
                    </div>
                </div>

                <!-- Provider Details -->
                <div>
                    <div class="section-header">
                        <h2 class="section-title">Provider Details</h2>
                    </div>
                    <div class="patient-field">
                        <div class="field-label">Provider Name</div>
                        <div class="field-value">{{ $provider->user->name ?? 'N/A' }}</div>
                    </div>
                    <div class="patient-field">
                        <div class="field-label">Specialization</div>
                        <div class="field-value">{{ $provider->specialization ?? 'N/A' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="services-section">
            <div class="services-header">
                <h2 class="services-title">Services</h2>
                <span class="payment-status
                    @if($payment_status === 'paid') status-paid
                    @elseif($payment_status === 'unpaid') status-unpaid
                    @else status-pending @endif">
                    @if($payment_status === 'paid')
                        ✓ Paid
                    @elseif($payment_status === 'unpaid')
                        ⚠ Unpaid
                    @else
                        ⏳ {{ ucfirst($payment_status) }}
                    @endif
                </span>
            </div>

            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 40%;">Item Name</th>
                        <th style="width: 20%;">Price</th>
                        <th style="width: 20%;">Quantity</th>
                        <th style="width: 20%;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($items as $item)
                    <tr>
                        <td>{{ $item->item_name }}</td>
                        <td>£{{ number_format($item->unit_price, 2) }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>£{{ number_format($item->total_price, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-row">
                    <span class="total-label">Total</span>
                    <span class="total-amount">£{{ number_format($total_amount, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Notes -->
        @if($bill->notes)
        <div class="notes-section">
            <div class="notes-title">Notes</div>
            <div class="notes-content">{{ $bill->notes }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div class="footer-highlight">Thank you for choosing our services</div>
            <div>This invoice was generated on {{ $generated_at }}</div>
            @if($letterhead['footer_text'])
                <div>{{ $letterhead['footer_text'] }}</div>
            @endif
            @if(!isset($is_preview))
                <div class="footer-contact">
                    For questions about this invoice, please contact {{ $letterhead['email'] ?? $letterhead['phone'] }}
                </div>
            @endif
        </div>
    </div>
</body>
</html>