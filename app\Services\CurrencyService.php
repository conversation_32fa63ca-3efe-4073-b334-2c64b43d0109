<?php

namespace App\Services;

use App\Models\Country;
use App\Models\User;

class CurrencyService
{
    /**
     * Currency symbols mapping
     */
    private static array $currencySymbols = [
        'USD' => '$',
        'GBP' => '£',
        'EUR' => '€',
        'INR' => '₹',
        'CAD' => 'C$',
        'AUD' => 'A$',
        'NGN' => '₦',
        'ZAR' => 'R'
    ];

    /**
     * Get country by country code
     */
    public static function getCountryByCode(string $countryCode): ?Country
    {
        return Country::where('code', $countryCode)->first();
    }

    /**
     * Get currency symbol for a country code
     */
    public static function getCurrencySymbol(string $countryCode = 'GB'): string
    {
        $country = self::getCountryByCode($countryCode);
        
        if ($country && $country->currency_symbol) {
            return $country->currency_symbol;
        }
        
        // Fallback to mapping
        $currencyCode = $country->currency_code ?? 'GBP';
        return self::$currencySymbols[$currencyCode] ?? '£';
    }

    /**
     * Get currency code for a country code
     */
    public static function getCurrencyCode(string $countryCode = 'GB'): string
    {
        $country = self::getCountryByCode($countryCode);
        return $country->currency_code ?? 'GBP';
    }

    /**
     * Format amount with currency symbol
     */
    public static function formatCurrency(
        float $amount, 
        string $countryCode = 'GB', 
        int $decimals = 2,
        bool $showSymbol = true
    ): string {
        $country = self::getCountryByCode($countryCode);
        $symbol = $country->currency_symbol ?? self::getCurrencySymbol($countryCode);
        
        $formattedAmount = number_format($amount, $decimals);
        
        return $showSymbol ? $symbol . $formattedAmount : $formattedAmount;
    }

    /**
     * Format price for display
     */
    public static function formatPrice(float $price, string $countryCode = 'GB'): string
    {
        return self::formatCurrency($price, $countryCode, 2, true);
    }

    /**
     * Get user's currency information
     */
    public static function getUserCurrencyInfo(?User $user = null): array
    {
        $countryCode = $user?->country_code ?? 'GB';
        $country = self::getCountryByCode($countryCode);
        
        if (!$country) {
            return [
                'name' => 'United Kingdom',
                'code' => 'GB',
                'currency_code' => 'GBP',
                'currency_symbol' => '£',
                'currency_name' => 'British Pound'
            ];
        }
        
        return [
            'name' => $country->name,
            'code' => $country->code,
            'currency_code' => $country->currency_code,
            'currency_symbol' => $country->currency_symbol,
            'currency_name' => $country->currency_name,
            'phone_code' => $country->phone_code,
            'timezone' => $country->timezone
        ];
    }

    /**
     * Get supported payment gateways for a country
     */
    public static function getSupportedPaymentGateways(string $countryCode = 'GB'): array
    {
        $country = self::getCountryByCode($countryCode);
        return $country->supported_payment_gateways ?? ['stripe'];
    }

    /**
     * Get tax rate for a country
     */
    public static function getTaxRate(string $countryCode = 'GB'): float
    {
        $country = self::getCountryByCode($countryCode);
        return (float) ($country->tax_rate ?? 0);
    }

    /**
     * Calculate price with tax
     */
    public static function calculatePriceWithTax(float $basePrice, string $countryCode = 'GB'): float
    {
        $taxRate = self::getTaxRate($countryCode);
        return $basePrice + ($basePrice * $taxRate / 100);
    }

    /**
     * Get locale for country code
     */
    public static function getLocaleForCountry(string $countryCode): string
    {
        $localeMap = [
            'US' => 'en_US',
            'GB' => 'en_GB',
            'IN' => 'en_IN',
            'CA' => 'en_CA',
            'AU' => 'en_AU',
            'DE' => 'de_DE',
            'FR' => 'fr_FR',
            'NG' => 'en_NG',
            'ZA' => 'en_ZA'
        ];
        
        return $localeMap[$countryCode] ?? 'en_GB';
    }

    /**
     * Convert amount between currencies (placeholder for future implementation)
     */
    public static function convertCurrency(
        float $amount, 
        string $fromCurrency, 
        string $toCurrency
    ): float {
        // For now, return the same amount
        // In the future, this could integrate with a currency conversion API
        return $amount;
    }

    /**
     * Get all supported currencies
     */
    public static function getSupportedCurrencies(): array
    {
        return Country::select('currency_code', 'currency_symbol', 'currency_name')
            ->distinct()
            ->orderBy('currency_code')
            ->get()
            ->toArray();
    }

    /**
     * Check if a currency is supported
     */
    public static function isCurrencySupported(string $currencyCode): bool
    {
        return Country::where('currency_code', $currencyCode)->exists();
    }

    /**
     * Get default country (fallback)
     */
    public static function getDefaultCountry(): Country
    {
        return Country::where('code', 'GB')->first() ?? 
               Country::first() ?? 
               new Country([
                   'name' => 'United Kingdom',
                   'code' => 'GB',
                   'currency_code' => 'GBP',
                   'currency_symbol' => '£',
                   'currency_name' => 'British Pound'
               ]);
    }

    /**
     * Format currency for API responses
     */
    public static function formatForApi(float $amount, string $countryCode = 'GB'): array
    {
        $country = self::getCountryByCode($countryCode);
        
        return [
            'amount' => $amount,
            'formatted' => self::formatPrice($amount, $countryCode),
            'currency_code' => $country->currency_code ?? 'GBP',
            'currency_symbol' => $country->currency_symbol ?? '£',
            'currency_name' => $country->currency_name ?? 'British Pound'
        ];
    }

    /**
     * Get currency display preferences for a user
     */
    public static function getUserCurrencyPreferences(?User $user = null): array
    {
        $countryCode = $user?->country_code;
        $country = $countryCode ? self::getCountryByCode($countryCode) : null;

        return [
            'country_code' => $countryCode,
            'currency_code' => $country?->currency_code,
            'currency_symbol' => $country?->currency_symbol,
            'locale' => $countryCode ? self::getLocaleForCountry($countryCode) : null,
            'tax_rate' => $countryCode ? self::getTaxRate($countryCode) : null,
            'payment_gateways' => $countryCode ? self::getSupportedPaymentGateways($countryCode) : []
        ];
    }
}
