<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import Icon from '@/components/Icon.vue';
import Pagination from '@/components/Pagination.vue';
import axios from 'axios';

const props = defineProps({
    patientId: [String, Number]
});

const { showSuccess, showError } = useNotifications();

// Data
const loading = ref(false);
const patient = ref(null);
const appointments = ref([]);
const searchQuery = ref('');
const selectedAppointments = ref([]);
const filters = ref({
    provider_name: '',
    service_name: '',
    status: '',
    date: ''
});

const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
    from: 0,
    to: 0
});

// Computed
const pageTitle = computed(() => {
    return patient.value
        ? `Appointments - ${patient.value.first_name} ${patient.value.last_name}`
        : `Patient Appointments`;
});

const allSelected = computed({
    get: () => appointments.value.length > 0 && selectedAppointments.value.length === appointments.value.length,
    set: (value) => {
        selectedAppointments.value = value ? appointments.value.map(a => a.id) : [];
    }
});

const filteredAppointments = computed(() => {
    let filtered = appointments.value;

    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(appointment => 
            appointment.provider?.user?.name?.toLowerCase().includes(query) ||
            appointment.service?.name?.toLowerCase().includes(query) ||
            appointment.status?.toLowerCase().includes(query) ||
            appointment.scheduled_at?.toLowerCase().includes(query)
        );
    }

    if (filters.value.provider_name) {
        filtered = filtered.filter(appointment => 
            appointment.provider?.user?.name?.toLowerCase().includes(filters.value.provider_name.toLowerCase())
        );
    }

    if (filters.value.service_name) {
        filtered = filtered.filter(appointment => 
            appointment.service?.name?.toLowerCase().includes(filters.value.service_name.toLowerCase())
        );
    }

    if (filters.value.status) {
        filtered = filtered.filter(appointment => 
            appointment.status === filters.value.status
        );
    }

    if (filters.value.date) {
        filtered = filtered.filter(appointment => 
            appointment.scheduled_at?.includes(filters.value.date)
        );
    }

    return filtered;
});

// Methods
const loadPatient = async () => {
    try {
        const response = await axios.get(`/patients/${props.patientId}`);
        patient.value = response.data.data;
    } catch (error) {
        console.error('Error loading patient:', error);
        showError('Failed to load patient information');
    }
};

const loadAppointments = async (page = 1) => {
    loading.value = true;
    try {
        const response = await axios.get(`/patients/${props.patientId}/appointments-list`, {
            params: {
                page,
                per_page: pagination.value.per_page
            }
        });

        appointments.value = response.data.data;
        pagination.value = {
            current_page: response.data.current_page,
            last_page: response.data.last_page,
            per_page: response.data.per_page,
            total: response.data.total,
            from: response.data.from,
            to: response.data.to
        };
    } catch (error) {
        console.error('Error loading appointments:', error);
        showError('Failed to load appointments');
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const getStatusColor = (status) => {
    const colors = {
        'scheduled': 'bg-blue-100 text-blue-800',
        'confirmed': 'bg-green-100 text-green-800',
        'completed': 'bg-gray-100 text-gray-800',
        'cancelled': 'bg-red-100 text-red-800',
        'no-show': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

const viewAppointment = (appointmentId) => {
    router.visit(`/appointments/${appointmentId}`);
};

const clearFilters = () => {
    filters.value = {
        provider_name: '',
        service_name: '',
        status: '',
        date: ''
    };
    searchQuery.value = '';
};

// Watchers
watch(() => pagination.value.current_page, (newPage) => {
    loadAppointments(newPage);
});

// Lifecycle
onMounted(() => {
    loadPatient();
    loadAppointments();
});
</script>

<template>
    <Head :title="pageTitle" />

    <AppLayout>
        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
                            <p class="mt-1 text-sm text-gray-600" v-if="patient">
                                Patient ID: {{ patient.patient_unique_id || patient.id }}
                            </p>
                        </div>
                        <div class="flex space-x-3">
                            <button
                                @click="router.visit(`/patients/${patientId}`)"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                                Back to Patient
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                                <input
                                    v-model="searchQuery"
                                    type="text"
                                    placeholder="Search appointments..."
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
                                <input
                                    v-model="filters.provider_name"
                                    type="text"
                                    placeholder="Provider name..."
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                                <input
                                    v-model="filters.service_name"
                                    type="text"
                                    placeholder="Service name..."
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <select
                                    v-model="filters.status"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="">All Statuses</option>
                                    <option value="scheduled">Scheduled</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                    <option value="no-show">No Show</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                                <input
                                    v-model="filters.date"
                                    type="date"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                        <div class="mt-4 flex justify-end">
                            <button
                                @click="clearFilters"
                                class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
                            >
                                Clear Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Appointments Table -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            Appointments ({{ pagination.total }})
                        </h3>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date & Time
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Provider
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Service
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-if="loading">
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                        Loading appointments...
                                    </td>
                                </tr>
                                <tr v-else-if="filteredAppointments.length === 0">
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                        No appointments found
                                    </td>
                                </tr>
                                <tr v-else v-for="appointment in filteredAppointments" :key="appointment.id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatDate(appointment.scheduled_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ appointment.provider?.user?.name || 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ appointment.service?.name || 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusColor(appointment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ appointment.status }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button
                                            @click="viewAppointment(appointment.id)"
                                            class="text-blue-600 hover:text-blue-900 mr-3"
                                        >
                                            View
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <Pagination
                        v-if="pagination.total > pagination.per_page"
                        :currentPage="pagination.current_page"
                        :lastPage="pagination.last_page"
                        :total="pagination.total"
                        :perPage="pagination.per_page"
                        :from="pagination.from"
                        :to="pagination.to"
                        @page-changed="loadAppointments"
                    />
                </div>
            </div>
        </div>
    </AppLayout>
</template>
