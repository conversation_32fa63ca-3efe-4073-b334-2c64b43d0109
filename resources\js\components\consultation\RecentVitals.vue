<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Header -->
    <div class="bg-purple-50 px-4 py-3 border-b border-purple-100">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-3">
          <div class="w-7 h-7 bg-purple-500 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Recent Vitals</h3>
            <p class="text-xs text-gray-500">Last {{ vitals.length }} readings</p>
          </div>
        </div>

        <div class="flex gap-1">
          <button @click="refreshVitals" class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
              <path d="M21 3v5h-5"/>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
              <path d="M3 21v-5h5"/>
            </svg>
          </button>

          <button @click="showChart = !showChart" class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="20" x2="18" y2="10"/>
              <line x1="12" y1="20" x2="12" y2="4"/>
              <line x1="6" y1="20" x2="6" y2="14"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4">
      <div v-if="loading" class="flex items-center justify-center py-8">
        <svg class="animate-spin h-6 w-6 text-green-600" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="ml-2 text-gray-600">Loading vitals...</span>
      </div>

      <div v-else-if="vitals.length === 0" class="text-center py-8 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto mb-2 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
        </svg>
        <p>No recent vitals recorded</p>
        <p class="text-sm">Vital signs will appear here once recorded</p>
      </div>

      <!-- Chart View -->
      <div v-else-if="showChart" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Blood Pressure Chart -->
          <div v-if="hasVitalType('blood_pressure')" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <h3 class="font-medium text-sm mb-2 flex items-center gap-2">
              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
              Blood Pressure
            </h3>
            <div class="h-32 flex items-end justify-between gap-1">
              <div v-for="(vital, index) in getVitalsByType('blood_pressure').slice(-7)" :key="index" 
                   class="flex flex-col items-center gap-1 flex-1">
                <div class="flex flex-col gap-1">
                  <div class="bg-red-500 rounded-sm" 
                       :style="`height: ${(vital.systolic / 200) * 80}px; width: 8px`"></div>
                  <div class="bg-red-300 rounded-sm" 
                       :style="`height: ${(vital.diastolic / 120) * 60}px; width: 8px`"></div>
                </div>
                <span class="text-xs text-gray-500 transform -rotate-45 origin-center">
                  {{ formatShortDate(vital.recorded_at) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Heart Rate Chart -->
          <div v-if="hasVitalType('heart_rate')" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <h3 class="font-medium text-sm mb-2 flex items-center gap-2">
              <div class="w-3 h-3 bg-pink-500 rounded-full"></div>
              Heart Rate
            </h3>
            <div class="h-32 flex items-end justify-between gap-1">
              <div v-for="(vital, index) in getVitalsByType('heart_rate').slice(-7)" :key="index" 
                   class="flex flex-col items-center gap-1 flex-1">
                <div class="bg-pink-500 rounded-sm" 
                     :style="`height: ${(vital.value / 150) * 100}px; width: 8px`"></div>
                <span class="text-xs text-gray-500 transform -rotate-45 origin-center">
                  {{ formatShortDate(vital.recorded_at) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Temperature Chart -->
          <div v-if="hasVitalType('temperature')" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <h3 class="font-medium text-sm mb-2 flex items-center gap-2">
              <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
              Temperature
            </h3>
            <div class="h-32 flex items-end justify-between gap-1">
              <div v-for="(vital, index) in getVitalsByType('temperature').slice(-7)" :key="index" 
                   class="flex flex-col items-center gap-1 flex-1">
                <div class="bg-orange-500 rounded-sm" 
                     :style="`height: ${((vital.value - 35) / 7) * 100}px; width: 8px`"></div>
                <span class="text-xs text-gray-500 transform -rotate-45 origin-center">
                  {{ formatShortDate(vital.recorded_at) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Oxygen Saturation Chart -->
          <div v-if="hasVitalType('oxygen_saturation')" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <h3 class="font-medium text-sm mb-2 flex items-center gap-2">
              <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              Oxygen Saturation
            </h3>
            <div class="h-32 flex items-end justify-between gap-1">
              <div v-for="(vital, index) in getVitalsByType('oxygen_saturation').slice(-7)" :key="index" 
                   class="flex flex-col items-center gap-1 flex-1">
                <div class="bg-blue-500 rounded-sm" 
                     :style="`height: ${((vital.value - 90) / 10) * 100}px; width: 8px`"></div>
                <span class="text-xs text-gray-500 transform -rotate-45 origin-center">
                  {{ formatShortDate(vital.recorded_at) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-else class="space-y-3">
        <div v-for="vital in vitals.slice(0, 5)" :key="vital.id"
             class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center" :class="getVitalIconClass(vital.type)">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
              </svg>
            </div>
            
            <div>
              <h3 class="font-medium text-sm">{{ formatVitalName(vital.type) }}</h3>
              <div class="text-xs text-gray-500">{{ formatDate(vital.recorded_at) }}</div>
            </div>
          </div>

          <div class="text-right">
            <div class="font-medium text-sm">{{ formatVitalValue(vital) }}</div>
            <div class="text-xs" :class="getVitalStatusClass(vital)">
              {{ getVitalStatus(vital) }}
            </div>
          </div>
        </div>

        <!-- Show More Button -->
        <div v-if="vitals.length > 5" class="text-center pt-2">
          <button @click="showAllVitals" class="text-sm text-blue-600 hover:text-blue-800">
            View all {{ vitals.length }} vitals
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- All Vitals Modal -->
  <div v-if="showAllModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">All Vital Signs</h3>
        <button @click="showAllModal = false" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="space-y-2">
        <div v-for="vital in vitals" :key="vital.id" 
             class="flex items-center justify-between p-3 border rounded-lg">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="getVitalIconClass(vital.type)">
              <component :is="getVitalIcon(vital.type)" class="w-4 h-4" />
            </div>
            
            <div>
              <h3 class="font-medium text-sm">{{ formatVitalName(vital.type) }}</h3>
              <div class="text-xs text-gray-500">{{ formatDate(vital.recorded_at) }}</div>
            </div>
          </div>

          <div class="text-right">
            <div class="font-medium text-sm">{{ formatVitalValue(vital) }}</div>
            <div class="text-xs" :class="getVitalStatusClass(vital)">
              {{ getVitalStatus(vital) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import axios from 'axios'

interface VitalSign {
  id: number
  type: string
  value: number
  systolic?: number
  diastolic?: number
  unit: string
  recorded_at: string
  recorded_by?: string
  notes?: string
}

interface Props {
  patientId: string | number | null
  encounterId?: string | number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'vital-selected': [vital: VitalSign]
}>()

// Component state
const vitals = ref<VitalSign[]>([])
const loading = ref(false)
const showChart = ref(false)
const showAllModal = ref(false)

// Methods
const loadVitals = async (): Promise<void> => {
  if (!props.patientId) {
    vitals.value = []
    return
  }

  try {
    loading.value = true
    const response = await axios.get(`/patients/${props.patientId}/vitals`, {
      params: {
        limit: 10,
        encounter_id: props.encounterId
      }
    })

    if (response.data.success) {
      vitals.value = response.data.data || []
    }
  } catch (error) {
    console.error('Error loading vitals:', error)
  } finally {
    loading.value = false
  }
}

const refreshVitals = (): void => {
  loadVitals()
}

const showAllVitals = (): void => {
  showAllModal.value = true
}

const hasVitalType = (type: string): boolean => {
  return vitals.value.some(vital => vital.type === type)
}

const getVitalsByType = (type: string): VitalSign[] => {
  return vitals.value.filter(vital => vital.type === type)
}

const getVitalIcon = (type: string) => {
  const icons: Record<string, string> = {
    blood_pressure: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`,
    heart_rate: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`,
    temperature: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"/></svg>`,
    oxygen_saturation: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>`,
    respiratory_rate: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`,
    weight: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`,
    height: `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`
  }
  return icons[type] || `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`
}

const getVitalIconClass = (type: string): string => {
  const classes: Record<string, string> = {
    blood_pressure: 'bg-red-100 text-red-600',
    heart_rate: 'bg-pink-100 text-pink-600',
    temperature: 'bg-orange-100 text-orange-600',
    oxygen_saturation: 'bg-blue-100 text-blue-600',
    respiratory_rate: 'bg-green-100 text-green-600',
    weight: 'bg-purple-100 text-purple-600',
    height: 'bg-indigo-100 text-indigo-600'
  }
  return classes[type] || 'bg-gray-100 text-gray-600'
}

const formatVitalName = (type: string): string => {
  const names: Record<string, string> = {
    blood_pressure: 'Blood Pressure',
    heart_rate: 'Heart Rate',
    temperature: 'Temperature',
    oxygen_saturation: 'Oxygen Saturation',
    respiratory_rate: 'Respiratory Rate',
    weight: 'Weight',
    height: 'Height'
  }
  return names[type] || type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatVitalValue = (vital: VitalSign): string => {
  if (vital.type === 'blood_pressure' && vital.systolic && vital.diastolic) {
    return `${vital.systolic}/${vital.diastolic} ${vital.unit}`
  }
  return `${vital.value} ${vital.unit}`
}

const getVitalStatus = (vital: VitalSign): string => {
  // Simple status logic - can be enhanced with proper medical ranges
  if (vital.type === 'blood_pressure' && vital.systolic && vital.diastolic) {
    if (vital.systolic > 140 || vital.diastolic > 90) return 'High'
    if (vital.systolic < 90 || vital.diastolic < 60) return 'Low'
    return 'Normal'
  }
  
  if (vital.type === 'heart_rate') {
    if (vital.value > 100) return 'High'
    if (vital.value < 60) return 'Low'
    return 'Normal'
  }
  
  if (vital.type === 'temperature') {
    if (vital.value > 37.5) return 'Fever'
    if (vital.value < 36) return 'Low'
    return 'Normal'
  }
  
  if (vital.type === 'oxygen_saturation') {
    if (vital.value < 95) return 'Low'
    return 'Normal'
  }
  
  return 'Normal'
}

const getVitalStatusClass = (vital: VitalSign): string => {
  const status = getVitalStatus(vital)
  if (status === 'High' || status === 'Fever') return 'text-red-600'
  if (status === 'Low') return 'text-orange-600'
  return 'text-green-600'
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const formatShortDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
}

// Watchers
watch(() => props.patientId, () => {
  loadVitals()
})

// Lifecycle
onMounted(() => {
  loadVitals()
})
</script>
