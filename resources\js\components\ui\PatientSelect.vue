<template>
  <div class="relative">
    <div class="relative">
      <Input
        v-model="searchQuery"
        :placeholder="placeholder"
        @input="onSearch"
        @focus="showDropdown = true"
        @blur="onBlur"
        :disabled="disabled"
        class="pr-10"
      />
      <Search class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
    </div>

    <!-- Dropdown -->
    <div
      v-if="showDropdown && !disabled"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
    >
      <div v-if="loading" class="p-3 text-center text-gray-500">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600 mx-auto"></div>
      </div>

      <div v-else-if="error" class="p-3 text-center text-red-500 text-sm">
        {{ error }}
      </div>

      <div v-else-if="patients.length === 0 && searchQuery" class="p-3 text-center text-gray-500 text-sm">
        No patients found
      </div>

      <div v-else-if="patients.length === 0" class="p-3 text-center text-gray-500 text-sm">
        Start typing to search patients
      </div>

      <div v-else>
        <button
          v-for="patient in patients"
          :key="patient.id"
          @mousedown.prevent="selectPatient(patient)"
          class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
        >
          <div class="font-medium">{{ patient.user?.name || 'Unknown Patient' }}</div>
          <div class="text-sm text-gray-500">
            {{ patient.user?.email }}
            <span v-if="patient.date_of_birth">
              • DOB: {{ formatDate(patient.date_of_birth) }}
            </span>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-vue-next'
import { useApi } from '@/composables/useApi'
import { useNotifications } from '@/composables/useNotifications'

interface Patient {
  id: number
  user_id: number
  date_of_birth?: string
  phone?: string
  address?: string
  emergency_contact?: string
  medical_history?: string
  user?: {
    id: number
    name: string
    email: string
  }
}

interface Props {
  modelValue?: number | null
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: number | null): void
  (e: 'patient-selected', patient: Patient): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search for a patient...',
  disabled: false,
  required: false
})

const emit = defineEmits<Emits>()

const api = useApi()
const { showError, showWarning } = useNotifications()
const searchQuery = ref('')
const showDropdown = ref(false)
const patients = ref<Patient[]>([])
const selectedPatient = ref<Patient | null>(null)
const loading = ref(false)
const error = ref('')

let searchTimeout: NodeJS.Timeout

const onSearch = () => {
  console.log('PatientSelect: onSearch triggered, query:', searchQuery.value)
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(async () => {
    if (searchQuery.value.length >= 2) {
      console.log('PatientSelect: Searching for patients with query:', searchQuery.value)
      await searchPatients()
    } else {
      patients.value = []
    }
  }, 300)
}

const searchPatients = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('PatientSelect: Making API call to search patients')
    // Use provider-specific search endpoint via web routes
    const response = await api.get(`/provider/search-patients?q=${encodeURIComponent(searchQuery.value)}`)
    console.log('PatientSelect: API response:', response)
    if (response?.data) {
      patients.value = response.data.data || response.data
      console.log('PatientSelect: Found patients:', patients.value.length)
    }
  } catch (err) {
    error.value = 'Failed to search patients'
    console.error('PatientSelect: Error searching patients:', err)

    // Show user-friendly error notification
    if (err.response?.status === 403) {
      showError('You do not have permission to search patients')
    } else if (err.response?.status === 401) {
      showError('Please log in to search patients')
    } else {
      showError('Failed to search patients. Please try again.')
    }
  } finally {
    loading.value = false
  }
}

const selectPatient = (patient: Patient) => {
  selectedPatient.value = patient
  searchQuery.value = patient.user?.name || 'Unknown Patient'
  showDropdown.value = false
  emit('update:modelValue', patient.id)
  emit('patient-selected', patient)
}

const onBlur = () => {
  // Delay hiding dropdown to allow for click events
  setTimeout(() => {
    showDropdown.value = false
  }, 200)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Load patient details if modelValue is provided
const loadPatientById = async (patientId: number) => {
  try {
    const response = await api.get(`/patients/${patientId}`)
    if (response?.data) {
      selectedPatient.value = response.data
      searchQuery.value = response.data.user?.name || 'Unknown Patient'
    }
  } catch (err) {
    console.error('Error loading patient:', err)
    showWarning('Could not load patient details')
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== selectedPatient.value?.id) {
    loadPatientById(newValue)
  } else if (!newValue) {
    selectedPatient.value = null
    searchQuery.value = ''
  }
})

onMounted(() => {
  if (props.modelValue) {
    loadPatientById(props.modelValue)
  }
})
</script>
