<template>
    <div v-if="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        Share {{ shareType }}
                    </h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="handleShare">
                    <!-- Email Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Send to Email Addresses
                        </label>
                        
                        <!-- Quick Contact Buttons -->
                        <div v-if="contacts.length > 0" class="mb-3">
                            <p class="text-xs text-gray-500 mb-2">Quick add:</p>
                            <div class="flex flex-wrap gap-2">
                                <button v-for="contact in contacts" 
                                        :key="contact.email"
                                        @click="addContactEmail(contact)"
                                        type="button"
                                        class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
                                    {{ contact.name }}
                                </button>
                            </div>
                        </div>

                        <!-- Email Input -->
                        <div class="relative">
                            <div class="flex flex-wrap gap-1 p-2 border border-gray-300 rounded-md min-h-[40px] focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                                <!-- Selected Emails -->
                                <span v-for="(email, index) in selectedEmails" 
                                      :key="index"
                                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ email }}
                                    <button @click="removeEmail(index)" 
                                            type="button"
                                            class="ml-1 text-blue-600 hover:text-blue-800">
                                        ×
                                    </button>
                                </span>
                                
                                <!-- Input Field -->
                                <input v-model="emailInput"
                                       @keydown.enter.prevent="addEmailFromInput"
                                       @keydown.backspace="handleBackspace"
                                       type="email"
                                       placeholder="Enter email address..."
                                       class="flex-1 min-w-[120px] border-none outline-none text-sm">
                            </div>
                        </div>
                    </div>

                    <!-- Message (for prescriptions) -->
                    <div v-if="shareType === 'prescription'" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Additional Message (Optional)
                        </label>
                        <textarea v-model="message" 
                                  rows="3"
                                  placeholder="Add a personal message..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <!-- Include Items (for consultation summary) -->
                    <div v-if="shareType === 'consultation'" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Include in Summary
                        </label>
                        <div class="space-y-2">
                            <label v-for="item in includeOptions" 
                                   :key="item.value"
                                   class="flex items-center">
                                <input v-model="includeItems" 
                                       :value="item.value"
                                       type="checkbox" 
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">{{ item.label }}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Encryption Option (for documents) -->
                    <div v-if="shareType === 'document'" class="mb-4">
                        <label class="flex items-center">
                            <input v-model="encryptDocument" 
                                   type="checkbox" 
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Encrypt document for security</span>
                        </label>
                    </div>

                    <!-- Privacy Notice -->
                    <div class="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-yellow-800">
                                    <strong>Confidential Medical Information</strong><br>
                                    This contains sensitive medical data. Ensure recipients are authorized to receive this information.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" 
                                @click="closeModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit" 
                                :disabled="selectedEmails.length === 0 || sharing"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ sharing ? 'Sharing...' : 'Share' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import axios from 'axios'
import { useNotifications } from '@/composables/useNotifications'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    shareType: {
        type: String,
        required: true,
        validator: value => ['document', 'prescription', 'medical_letter', 'consultation'].includes(value)
    },
    itemId: {
        type: [String, Number],
        required: false,
        default: null
    },
    consultationId: {
        type: [String, Number],
        required: true
    }
})

const emit = defineEmits(['close', 'shared'])

// Initialize notifications
const { showSuccess, showError, showAlert } = useNotifications()

const selectedEmails = ref([])
const emailInput = ref('')
const message = ref('')
const encryptDocument = ref(false)
const includeItems = ref(['prescriptions', 'documents'])
const contacts = ref([])
const sharing = ref(false)

const includeOptions = [
    { value: 'prescriptions', label: 'Prescriptions' },
    { value: 'documents', label: 'Documents' },
    { value: 'medical_letters', label: 'Medical Letters' },
    { value: 'notes', label: 'Notes' },
    { value: 'diagnoses', label: 'Diagnoses' },
    { value: 'treatment_plans', label: 'Treatment Plans' }
]

const addContactEmail = (contact) => {
    if (!selectedEmails.value.includes(contact.email)) {
        selectedEmails.value.push(contact.email)
    }
}

const addEmailFromInput = () => {
    const email = emailInput.value.trim()
    if (email && isValidEmail(email) && !selectedEmails.value.includes(email)) {
        selectedEmails.value.push(email)
        emailInput.value = ''
    }
}

const removeEmail = (index) => {
    selectedEmails.value.splice(index, 1)
}

const handleBackspace = () => {
    if (emailInput.value === '' && selectedEmails.value.length > 0) {
        selectedEmails.value.pop()
    }
}

const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
}

const loadContacts = async () => {
    try {
        const response = await axios.get(`/consultation-sharing/consultations/${props.consultationId}/contacts`)
        contacts.value = response.data.data || []
    } catch (error) {
        console.error('Error loading contacts:', error)
    }
}

const handleShare = async () => {
    if (!props.itemId) {
        showError('No item selected for sharing')
        return
    }

    if (selectedEmails.value.length === 0) {
        showError('Please select at least one email address')
        return
    }

    sharing.value = true
    try {
        let endpoint = ''
        const payload = {
            emails: selectedEmails.value
        }

        switch (props.shareType) {
            case 'document':
                endpoint = `/consultation-sharing/documents/${props.itemId}/share`
                payload.encrypt_document = encryptDocument.value
                break
            case 'prescription':
                endpoint = `/consultation-sharing/prescriptions/${props.itemId}/share`
                if (message.value) payload.message = message.value
                break
            case 'medical_letter':
                endpoint = `/consultation-sharing/medical-letters/${props.itemId}/share`
                break
            case 'consultation':
                endpoint = `/consultation-sharing/consultations/${props.itemId}/share`
                payload.include_items = includeItems.value
                break
        }

        const response = await axios.post(endpoint, payload)
        
        if (response.data.success) {
            emit('shared', response.data.data)
            closeModal()
            // Show success message
            showSuccess(`${props.shareType.charAt(0).toUpperCase() + props.shareType.slice(1)} shared successfully!`)
        }
    } catch (error) {
        console.error('Error sharing:', error)
        showError('Failed to share. Please try again.')
    } finally {
        sharing.value = false
    }
}

const closeModal = () => {
    selectedEmails.value = []
    emailInput.value = ''
    message.value = ''
    encryptDocument.value = false
    includeItems.value = ['prescriptions', 'documents']
    emit('close')
}

watch(() => props.show, (newValue) => {
    if (newValue) {
        loadContacts()
    }
})

onMounted(() => {
    if (props.show) {
        loadContacts()
    }
})
</script>
