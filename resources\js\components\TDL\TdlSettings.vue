<template>
  <div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-900">TDL Labs Settings</h2>
      <div class="flex items-center space-x-3">
        <!-- Status Indicator -->
        <div class="flex items-center space-x-2">
          <div
            class="w-3 h-3 rounded-full"
            :class="isConfigured ? 'bg-green-500' : 'bg-red-500'"
          ></div>
          <span class="text-sm font-medium" :class="isConfigured ? 'text-green-700' : 'text-red-700'">
            {{ isConfigured ? 'Active' : 'Not Configured' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Configuration Form -->
    <form @submit.prevent="saveSettings" class="space-y-6">
      <!-- Basic Configuration -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Configuration</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">TDL Account ID *</label>
            <input
              v-model="form.tdl_account_id"
              type="text"
              required
              placeholder="Enter your TDL account identifier"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label class="flex items-center">
              <input
                v-model="form.is_active"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">Enable TDL Integration</span>
            </label>
          </div>
        </div>

        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Azure Storage Connection String *</label>
          <div class="relative">
            <input
              v-model="form.azure_connection_string"
              :type="showConnectionString ? 'text' : 'password'"
              required
              placeholder="DefaultEndpointsProtocol=https;AccountName=..."
              class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div class="absolute inset-y-0 right-0 flex items-center space-x-2 pr-3">
              <button
                type="button"
                @click="showConnectionString = !showConnectionString"
                class="text-gray-400 hover:text-gray-600"
              >
                <i :class="showConnectionString ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
              <button
                type="button"
                @click="testAzureConnection"
                :disabled="!form.azure_connection_string || testingConnection"
                class="text-blue-600 hover:text-blue-800 disabled:opacity-50"
                title="Test Connection"
              >
                <i class="fas fa-plug" :class="{ 'fa-spin': testingConnection }"></i>
              </button>
            </div>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            Your Azure Blob Storage connection string for secure file transfer
          </p>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Advanced Settings</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Polling Frequency (minutes)</label>
            <input
              v-model.number="form.settings.polling_frequency"
              type="number"
              min="5"
              max="60"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p class="mt-1 text-xs text-gray-500">How often to check for new results</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Notification Email</label>
            <input
              v-model="form.settings.notification_email"
              type="email"
              placeholder="<EMAIL>"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p class="mt-1 text-xs text-gray-500">Email for important notifications</p>
          </div>
        </div>

        <div class="mt-6 space-y-4">
          <label class="flex items-center">
            <input
              v-model="form.settings.auto_send_requests"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Automatically send requests to TDL</span>
          </label>

          <label class="flex items-center">
            <input
              v-model="form.settings.auto_process_results"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Automatically process incoming results</span>
          </label>

          <label class="flex items-center">
            <input
              v-model="form.settings.require_physician_review"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Require physician review for all results</span>
          </label>

          <label class="flex items-center">
            <input
              v-model="form.settings.auto_cleanup_files"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Automatically cleanup processed files</span>
          </label>
        </div>
      </div>

      <!-- Connection Status -->
      <div v-if="connectionStatus" class="bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Connection Status</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">TDL Account:</span>
              <span class="text-sm font-medium" :class="connectionStatus.has_tdl_account ? 'text-green-600' : 'text-red-600'">
                {{ connectionStatus.has_tdl_account ? 'Configured' : 'Not Set' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Azure Storage:</span>
              <span class="text-sm font-medium" :class="connectionStatus.has_azure_connection ? 'text-green-600' : 'text-red-600'">
                {{ connectionStatus.has_azure_connection ? 'Connected' : 'Not Connected' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Integration Status:</span>
              <span class="text-sm font-medium" :class="connectionStatus.is_active ? 'text-green-600' : 'text-gray-600'">
                {{ connectionStatus.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>

          <div v-if="azureStats" class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Request Files:</span>
              <span class="text-sm font-medium">{{ azureStats.requests_count }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Result Files:</span>
              <span class="text-sm font-medium">{{ azureStats.results_count }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Storage Used:</span>
              <span class="text-sm font-medium">{{ formatFileSize(azureStats.total_size) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-between items-center pt-6 border-t">
        <div class="flex space-x-3">
          <button
            type="button"
            @click="testConnection"
            :disabled="!isConfigured || testingConnection"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors"
          >
            <i class="fas fa-plug mr-2" :class="{ 'fa-spin': testingConnection }"></i>
            Test Connection
          </button>
          
          <button
            type="button"
            @click="processResults"
            :disabled="!isConfigured || processingResults"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            <i class="fas fa-sync-alt mr-2" :class="{ 'fa-spin': processingResults }"></i>
            Process Results
          </button>
        </div>

        <div class="flex space-x-3">
          <button
            v-if="form.is_active"
            type="button"
            @click="disableIntegration"
            class="px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
          >
            Disable Integration
          </button>
          
          <button
            type="submit"
            :disabled="saving"
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <i v-if="saving" class="fas fa-spinner fa-spin mr-2"></i>
            {{ saving ? 'Saving...' : 'Save Settings' }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNotifications } from '@/composables/useNotifications'
import tdlLabService from '@/services/tdlLabService'
import type { TdlSettings, TdlConnectionStatus, AzureFileStats } from '@/types/tdl'

const { showSuccess, showError, showConfirm } = useNotifications()

// Reactive data
const form = ref({
  tdl_account_id: '',
  azure_connection_string: '',
  is_active: false,
  settings: {
    auto_send_requests: true,
    auto_process_results: true,
    polling_frequency: 15,
    notification_email: '',
    require_physician_review: true,
    auto_cleanup_files: true
  }
})

const connectionStatus = ref(null)
const azureStats = ref(null)
const showConnectionString = ref(false)
const saving = ref(false)
const testingConnection = ref(false)
const processingResults = ref(false)

// Computed
const isConfigured = computed(() => {
  return form.value.tdl_account_id && 
         form.value.azure_connection_string && 
         form.value.is_active
})

// Methods
const fetchSettings = async () => {
  try {
    const response = await fetch('/lab/settings')
    const data = await response.json()
    
    if (data.success && data.data.settings) {
      const settings = data.data.settings
      form.value = {
        tdl_account_id: settings.tdl_account_id || '',
        azure_connection_string: settings.has_azure_connection ? '••••••••••••••••' : '',
        is_active: settings.is_active || false,
        settings: {
          ...form.value.settings,
          ...settings.settings
        }
      }
    }
  } catch (error) {
    console.error('Error fetching settings:', error)
  }
}

const fetchConnectionStatus = async () => {
  try {
    const response = await fetch('/lab/settings/status')
    const data = await response.json()
    
    if (data.success) {
      connectionStatus.value = data.data.status
    }
  } catch (error) {
    console.error('Error fetching connection status:', error)
  }
}

const fetchAzureStats = async () => {
  try {
    const response = await fetch('/lab/settings/azure-stats')
    const data = await response.json()
    
    if (data.success) {
      azureStats.value = data.data.stats
    }
  } catch (error) {
    console.error('Error fetching Azure stats:', error)
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    const response = await fetch('/lab/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify(form.value)
    })

    const data = await response.json()

    if (data.success) {
      showSuccess('TDL settings saved successfully')
      await fetchConnectionStatus()
      await fetchAzureStats()
    } else {
      showError(data.message || 'Failed to save settings')
    }
  } catch (error) {
    console.error('Error saving settings:', error)
    showError('Failed to save settings')
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  testingConnection.value = true
  try {
    const response = await fetch('/lab/settings/test-connection', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      }
    })

    const data = await response.json()

    if (data.success) {
      showSuccess('Connection test successful')
    } else {
      showError(data.message || 'Connection test failed')
    }
  } catch (error) {
    console.error('Error testing connection:', error)
    showError('Connection test failed')
  } finally {
    testingConnection.value = false
  }
}

const testAzureConnection = async () => {
  if (!form.value.azure_connection_string) return

  testingConnection.value = true
  try {
    const response = await fetch('/lab/settings/test-azure', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        azure_connection_string: form.value.azure_connection_string
      })
    })

    const data = await response.json()

    if (data.success) {
      showSuccess('Azure connection test successful')
    } else {
      showError(data.message || 'Azure connection test failed')
    }
  } catch (error) {
    console.error('Error testing Azure connection:', error)
    showError('Azure connection test failed')
  } finally {
    testingConnection.value = false
  }
}

const processResults = async () => {
  processingResults.value = true
  try {
    const response = await fetch('/lab/settings/process-results', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      }
    })

    const data = await response.json()

    if (data.success) {
      showSuccess(`Processed ${data.data.processed_count} results`)
      await fetchAzureStats()
    } else {
      showError(data.message || 'Failed to process results')
    }
  } catch (error) {
    console.error('Error processing results:', error)
    showError('Failed to process results')
  } finally {
    processingResults.value = false
  }
}

const disableIntegration = async () => {
  const confirmed = await showConfirm(
    'Disable TDL Integration',
    'Are you sure you want to disable TDL integration? This will stop all lab processing.',
    'Yes, disable it',
    'Cancel'
  )
  if (!confirmed) return

  try {
    const response = await fetch('/lab/settings/disable', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      }
    })

    const data = await response.json()

    if (data.success) {
      showSuccess('TDL integration disabled')
      form.value.is_active = false
      await fetchConnectionStatus()
    } else {
      showError(data.message || 'Failed to disable integration')
    }
  } catch (error) {
    console.error('Error disabling integration:', error)
    toast.error('Failed to disable integration')
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Lifecycle
onMounted(() => {
  fetchSettings()
  fetchConnectionStatus()
  fetchAzureStats()
})
</script>


