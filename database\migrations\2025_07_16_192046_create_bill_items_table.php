<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bill_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bill_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->nullable()->constrained()->onDelete('set null');

            $table->string('item_name'); // Service name or custom item name
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2); // Price per unit
            $table->integer('quantity')->default(1);
            $table->decimal('total_price', 10, 2); // unit_price * quantity

            $table->timestamps();

            $table->index('bill_id');
            $table->index('service_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bill_items');
    }
};
