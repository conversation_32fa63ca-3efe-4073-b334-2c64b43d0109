<script setup lang="ts">
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import PublicLayout from '@/layouts/PublicLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  planSlug: string;
  returnUrl: string;
}

const props = defineProps<Props>();
</script>

<template>
  <PublicLayout>
    <Head title="Login Required" />

    <div class="container mx-auto py-12 px-4 sm:px-6 lg:px-8 max-w-2xl">
      <div class="text-center">
        <!-- Lock Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
          <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>

        <h1 class="text-3xl font-bold text-gray-900 mb-4">
          Login Required
        </h1>
        
        <p class="text-lg text-gray-600 mb-8">
          You need to be logged in to subscribe to a plan. Please login or create an account to continue.
        </p>

        <!-- Action Cards -->
        <div class="grid gap-6 md:grid-cols-2 mb-8">
          <!-- Login Card -->
          <Card>
            <CardHeader>
              <CardTitle>Already have an account?</CardTitle>
              <CardDescription>Sign in to your existing account</CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                as="a" 
                :href="route('login', { redirect: returnUrl })"
                class="w-full"
                size="lg"
              >
                Login
              </Button>
            </CardContent>
          </Card>

          <!-- Register Card -->
          <Card>
            <CardHeader>
              <CardTitle>New to Medroid?</CardTitle>
              <CardDescription>Create a free account to get started</CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                as="a" 
                :href="route('register', { redirect: returnUrl })"
                variant="outline"
                class="w-full"
                size="lg"
              >
                Create Account
              </Button>
            </CardContent>
          </Card>
        </div>

        <!-- Benefits Section -->
        <div class="bg-gray-50 rounded-lg p-6 mb-8">
          <h2 class="text-xl font-bold text-gray-900 mb-4">Why create an account?</h2>
          <div class="grid gap-4 md:grid-cols-3 text-left">
            <div>
              <div class="flex items-center mb-2">
                <svg class="h-5 w-5 text-teal-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium">Secure & Private</span>
              </div>
              <p class="text-sm text-gray-600">Your health data is encrypted and protected</p>
            </div>
            
            <div>
              <div class="flex items-center mb-2">
                <svg class="h-5 w-5 text-teal-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium">Personalized Care</span>
              </div>
              <p class="text-sm text-gray-600">Get recommendations tailored to your health</p>
            </div>
            
            <div>
              <div class="flex items-center mb-2">
                <svg class="h-5 w-5 text-teal-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium">Track Progress</span>
              </div>
              <p class="text-sm text-gray-600">Monitor your health journey over time</p>
            </div>
          </div>
        </div>

        <!-- Back to Plans -->
        <div class="text-center">
          <Button 
            as="a" 
            :href="route('membership.index')"
            variant="ghost"
          >
            ← Back to Plans
          </Button>
        </div>
      </div>
    </div>
  </PublicLayout>
</template>
