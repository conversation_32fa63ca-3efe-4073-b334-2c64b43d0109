<template>
    <div class="payment-gateway-selector">
        <!-- Loading State -->
        <div v-if="isLoading" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p class="text-sm text-gray-600 mt-2">Loading payment options...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-red-800">Error Loading Payment Options</span>
            </div>
            <p class="text-sm text-red-700 mt-1">{{ error }}</p>
            <button 
                @click="loadGateways" 
                class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
                Try Again
            </button>
        </div>

        <!-- Gateway Selection -->
        <div v-else-if="availableGateways.length > 0" class="space-y-4">
            <!-- Country & Currency Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-blue-900">Payment Region</h3>
                            <p class="text-xs text-blue-700">{{ currencyInfo?.name || 'Unknown' }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-blue-900">{{ currencyInfo?.currency_symbol }} {{ currencyInfo?.currency_code }}</div>
                        <div class="text-xs text-blue-700">{{ currencyInfo?.currency_name }}</div>
                    </div>
                </div>
            </div>

            <!-- Gateway Options -->
            <div class="space-y-3">
                <h3 class="text-lg font-semibold text-gray-900">Select Payment Method</h3>
                
                <div 
                    v-for="gateway in availableGateways" 
                    :key="gateway.id"
                    @click="selectGateway(gateway)"
                    :class="[
                        'border-2 rounded-lg p-4 cursor-pointer transition-all duration-200',
                        selectedGateway?.id === gateway.id 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300 bg-white'
                    ]"
                >
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <!-- Gateway Logo -->
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <img 
                                    v-if="gateway.logo" 
                                    :src="gateway.logo" 
                                    :alt="gateway.name"
                                    class="w-8 h-8 object-contain"
                                    @error="$event.target.style.display = 'none'"
                                />
                                <svg v-else class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                </svg>
                            </div>
                            
                            <div>
                                <div class="flex items-center space-x-2">
                                    <h4 class="text-base font-semibold text-gray-900">{{ gateway.name }}</h4>
                                    <span v-if="gateway.is_primary" class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                                        Recommended
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600">{{ gateway.description }}</p>
                                
                                <!-- Payment Methods Preview -->
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="text-xs text-gray-500">Supports:</span>
                                    <div class="flex space-x-1">
                                        <span 
                                            v-for="(method, key) in Object.keys(gateway.payment_methods).slice(0, 3)" 
                                            :key="key"
                                            class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                                        >
                                            {{ gateway.payment_methods[method].name }}
                                        </span>
                                        <span 
                                            v-if="Object.keys(gateway.payment_methods).length > 3"
                                            class="text-xs text-gray-500"
                                        >
                                            +{{ Object.keys(gateway.payment_methods).length - 3 }} more
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Selection Indicator -->
                        <div class="flex items-center">
                            <div 
                                :class="[
                                    'w-5 h-5 rounded-full border-2 flex items-center justify-center',
                                    selectedGateway?.id === gateway.id 
                                        ? 'border-blue-500 bg-blue-500' 
                                        : 'border-gray-300'
                                ]"
                            >
                                <svg 
                                    v-if="selectedGateway?.id === gateway.id"
                                    class="w-3 h-3 text-white" 
                                    fill="currentColor" 
                                    viewBox="0 0 20 20"
                                >
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Gateway Features -->
                    <div v-if="selectedGateway?.id === gateway.id && gateway.features" class="mt-4 pt-4 border-t border-gray-200">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Features</h5>
                        <div class="grid grid-cols-2 gap-2">
                            <div 
                                v-for="(enabled, feature) in gateway.features" 
                                :key="feature"
                                v-if="enabled"
                                class="flex items-center space-x-2"
                            >
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-xs text-gray-700">{{ formatFeatureName(feature) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Continue Button -->
            <button
                v-if="selectedGateway"
                @click="proceedWithGateway"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
            >
                Continue with {{ selectedGateway.name }}
            </button>
        </div>

        <!-- No Gateways Available -->
        <div v-else class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Payment Methods Available</h3>
            <p class="text-sm text-gray-600">
                No payment gateways are currently available for your region.
            </p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'
import { useCountryManager } from '@/composables/useCountryManager'

// Props
const props = defineProps({
    autoSelect: {
        type: Boolean,
        default: true
    }
})

// Emits
const emit = defineEmits(['gateway-selected', 'error'])

// State
const isLoading = ref(true)
const error = ref(null)
const availableGateways = ref([])
const selectedGateway = ref(null)
const currencyInfo = ref(null)

// Page data and country manager
const page = usePage()
const currentUser = computed(() => page.props.auth?.user || null)
const { getEffectiveCountryCode, effectiveCountry } = useCountryManager()

// Load available gateways
const loadGateways = async () => {
    try {
        isLoading.value = true
        error.value = null
        
        const countryCode = getEffectiveCountryCode()
        const response = await axios.get(`/api/payment-gateways/available?country_code=${countryCode}`)
        
        if (response.data.success) {
            availableGateways.value = response.data.data.available_gateways
            currencyInfo.value = response.data.data.currency
            
            // Auto-select primary gateway if enabled
            if (props.autoSelect && availableGateways.value.length > 0) {
                const primaryGateway = availableGateways.value.find(g => g.is_primary)
                if (primaryGateway) {
                    selectGateway(primaryGateway)
                }
            }
        } else {
            throw new Error(response.data.message || 'Failed to load payment gateways')
        }
    } catch (err) {
        error.value = err.response?.data?.message || err.message || 'Failed to load payment gateways'
        emit('error', err)
    } finally {
        isLoading.value = false
    }
}

// Select gateway
const selectGateway = (gateway) => {
    selectedGateway.value = gateway
    emit('gateway-selected', gateway)
}

// Proceed with selected gateway
const proceedWithGateway = () => {
    if (selectedGateway.value) {
        emit('gateway-selected', selectedGateway.value)
    }
}

// Format feature names
const formatFeatureName = (feature) => {
    return feature
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
}

// Load gateways on mount
onMounted(() => {
    loadGateways()
})
</script>

<style scoped>
.payment-gateway-selector {
    max-width: 600px;
    margin: 0 auto;
}

/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gateway-option {
    animation: fadeIn 0.3s ease-out;
}
</style>
