<?php

namespace App\Repositories;

use App\Models\Clinic;
use Illuminate\Database\Eloquent\Collection;

class ClinicRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Clinic $model
     * @return void
     */
    public function __construct(Clinic $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find clinic by slug.
     *
     * @param string $slug
     * @return Clinic|null
     */
    public function findBySlug(string $slug): ?Clinic
    {
        return $this->findOneBy(['slug' => $slug]);
    }
    
    /**
     * Find active clinics.
     *
     * @return Collection
     */
    public function findActive(): Collection
    {
        return $this->findBy(['is_active' => true]);
    }
    
    /**
     * Search clinics by name or city.
     *
     * @param string $search
     * @return Collection
     */
    public function search(string $search): Collection
    {
        $query = $this->model->newQuery();
        $query->where('is_active', true)
              ->where(function ($q) use ($search) {
                  $q->where('name', 'like', "%{$search}%")
                    ->orWhere('city', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
              });
        
        return $query->get();
    }
}
