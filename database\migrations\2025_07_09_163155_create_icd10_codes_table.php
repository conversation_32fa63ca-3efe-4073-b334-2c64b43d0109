<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('icd10_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 10)->unique(); // ICD-10 code (e.g., A00.0)
            $table->string('category', 10)->index(); // Category code (e.g., A00)
            $table->string('short_description', 255);
            $table->text('long_description')->nullable();
            $table->string('chapter', 100)->index(); // Chapter name
            $table->string('section', 100)->nullable()->index(); // Section name
            $table->boolean('is_billable')->default(true);
            $table->boolean('is_valid_for_submission')->default(true);
            $table->string('gender_restriction', 10)->nullable(); // M, F, or null
            $table->integer('age_low')->nullable(); // Minimum age in days
            $table->integer('age_high')->nullable(); // Maximum age in days
            $table->json('synonyms')->nullable(); // Alternative names/terms
            $table->json('includes')->nullable(); // What this code includes
            $table->json('excludes')->nullable(); // What this code excludes
            $table->string('version', 20)->default('2024'); // ICD-10 version
            $table->timestamps();

            // Indexes for fast searching
            $table->index(['chapter', 'section']);
            $table->index(['is_billable', 'is_valid_for_submission']);
            $table->fullText(['short_description', 'long_description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('icd10_codes');
    }
};
