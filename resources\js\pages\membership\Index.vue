<script setup lang="ts">
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { computed, ref } from 'vue';
import PublicLayout from '@/layouts/PublicLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

// Get page props to check authentication
const page = usePage();
const user = computed(() => page.props.auth?.user || null);

interface SubscriptionPlan {
  id: number;
  name: string;
  slug: string;
  price: number;
  formatted_price: string;
  currency: string;
  interval: string;
  description: string;
  features: string[];
  is_free: boolean;
}

interface Props {
  plans: SubscriptionPlan[];
  currentSubscription?: any;
  isAuthenticated: boolean;
}

const props = defineProps<Props>();

// Billing period state (monthly/yearly)
const billingPeriod = ref('monthly');
const isYearly = computed({
  get: () => billingPeriod.value === 'yearly',
  set: (value) => billingPeriod.value = value ? 'yearly' : 'monthly'
});

// Calculate yearly price (20% discount)
const getYearlyPrice = (plan: SubscriptionPlan) => {
  if (plan.is_free) return 'Free';
  const monthlyPrice = parseFloat(String(plan.price));
  const yearlyPrice = (monthlyPrice * 12 * 0.8).toFixed(2); // 20% discount
  const symbol = plan.currency === 'gbp' ? '£' : '$';
  return `${symbol}${yearlyPrice}`;
};

// Check if user is on this plan
const isCurrentPlan = (plan: SubscriptionPlan) => {
  if (!props.currentSubscription || !props.currentSubscription.plan) return false;
  return props.currentSubscription.plan.slug === plan.slug;
};

// Since we only have 2 plans, we don't need a "most popular" badge
const getPopularPlan = computed(() => {
  return null; // Disabled for 2-plan layout
});

// Get the checkout URL for a plan
const getCheckoutUrl = (plan: SubscriptionPlan) => {
  const period = billingPeriod.value === 'yearly' ? '?period=yearly' : '';
  return `/membership/checkout/${plan.slug}${period}`;
};

// Get the appropriate button text based on authentication and current plan
const getButtonText = (plan: SubscriptionPlan) => {
  if (isCurrentPlan(plan)) return 'Current Plan';
  if (props.isAuthenticated) {
    // For authenticated users, show upgrade/subscribe options
    if (plan.is_free) return 'Get Started';
    return 'Subscribe';
  } else {
    // For unauthenticated users, show sign up options
    if (plan.is_free) return 'Sign Up Free';
    return 'Sign Up with Premium';
  }
};

// FAQ functionality
const openFaq = ref<number | null>(null);

const toggleFaq = (index: number) => {
  openFaq.value = openFaq.value === index ? null : index;
};
</script>

<template>
  <PublicLayout>
    <Head title="Membership Plans" />

    <!-- Compact Header -->
    <div class="bg-white border-b border-gray-100">
      <div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-medroid-navy mb-3">
            Choose Your Healthcare Plan
          </h1>
          <p class="text-medroid-slate max-w-2xl mx-auto mb-6">
            Simple, transparent pricing for your healthcare needs.
          </p>

          <!-- Compact Billing Period Toggle -->
          <div class="inline-flex bg-gray-100 rounded-lg p-1">
            <button
              @click="billingPeriod = 'monthly'"
              :class="[
                'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
                billingPeriod === 'monthly'
                  ? 'bg-white text-medroid-navy shadow-sm'
                  : 'text-medroid-slate hover:text-medroid-navy'
              ]"
            >
              Monthly
            </button>
            <button
              @click="billingPeriod = 'yearly'"
              :class="[
                'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
                billingPeriod === 'yearly'
                  ? 'bg-white text-medroid-navy shadow-sm'
                  : 'text-medroid-slate hover:text-medroid-navy'
              ]"
            >
              Yearly
              <span class="ml-1 text-xs text-green-600">-20%</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pricing Section -->
    <div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- Pricing Cards -->
      <div class="grid gap-6 lg:grid-cols-2 max-w-4xl mx-auto">
        <Card
          v-for="plan in props.plans"
          :key="plan.id"
          :class="[
            'relative overflow-hidden flex flex-col transition-all duration-200 hover:shadow-lg border',
            plan.is_free
              ? 'border-gray-200 hover:border-medroid-orange/50'
              : 'border-medroid-teal/30 hover:border-medroid-teal',
            isCurrentPlan(plan) ? 'ring-2 ring-medroid-teal ring-offset-1' : ''
          ]"
        >
          <!-- Popular Badge for Premium Plan -->
          <div v-if="!plan.is_free" class="absolute top-0 right-0 bg-medroid-teal text-white px-2 py-1 text-xs font-medium rounded-bl-md">
            Popular
          </div>

          <CardHeader class="text-center pb-4">
            <div class="flex items-center justify-center mb-3">
              <div v-if="plan.is_free" class="w-10 h-10 bg-medroid-orange/10 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-medroid-orange" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div v-else class="w-10 h-10 bg-medroid-teal/10 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-medroid-teal" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>

            <CardTitle class="text-xl font-bold text-medroid-navy mb-1">{{ plan.name }}</CardTitle>
            <CardDescription class="text-sm text-medroid-slate mb-4">{{ plan.description }}</CardDescription>

            <div class="mb-4">
              <span class="text-3xl font-bold text-medroid-navy">
                {{ billingPeriod === 'monthly' ? plan.formatted_price : getYearlyPrice(plan) }}
              </span>
              <span class="text-medroid-slate ml-1 text-sm">
                /{{ billingPeriod === 'monthly' ? 'mo' : 'yr' }}
              </span>
              <div v-if="billingPeriod === 'yearly' && !plan.is_free" class="text-xs text-green-600 font-medium mt-1">
                Save 20% annually
              </div>
            </div>
          </CardHeader>

          <CardContent class="flex-grow px-4">
            <ul class="space-y-2">
              <li v-for="(feature, index) in plan.features?.additional_features || []" :key="index" class="flex items-start text-sm">
                <div class="flex-shrink-0 w-4 h-4 rounded-full bg-medroid-teal/10 flex items-center justify-center mr-2 mt-0.5">
                  <svg class="h-2.5 w-2.5 text-medroid-teal" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-medroid-slate">{{ feature }}</span>
              </li>
            </ul>
          </CardContent>

          <CardFooter class="pt-4 px-4 pb-4">
            <Link
              v-if="!isCurrentPlan(plan)"
              :href="getCheckoutUrl(plan)"
              class="w-full"
            >
              <Button
                :class="[
                  'w-full py-2.5 text-sm font-medium transition-all duration-200',
                  plan.is_free
                    ? 'bg-medroid-orange hover:bg-medroid-orange/90 text-white border-0'
                    : 'bg-medroid-teal hover:bg-medroid-teal/90 text-white border-0'
                ]"
              >
                {{ getButtonText(plan) }}
              </Button>
            </Link>
            <Button
              v-else
              variant="outline"
              disabled
              class="w-full py-2.5 text-sm font-medium border-medroid-teal text-medroid-teal"
            >
              {{ getButtonText(plan) }}
            </Button>
          </CardFooter>
        </Card>
      </div>

      <!-- Compact FAQ Section -->
      <div class="mt-12 max-w-4xl mx-auto">
        <h2 class="text-2xl font-bold text-center mb-8 text-medroid-navy">FAQ</h2>

        <div class="grid md:grid-cols-2 gap-4">
          <!-- FAQ Item 1 -->
          <div class="border border-gray-200 rounded-lg">
            <button
              @click="toggleFaq(0)"
              class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <span class="font-medium text-medroid-navy text-sm">Can I change my plan later?</span>
              <svg
                :class="['w-4 h-4 text-medroid-slate transition-transform', openFaq === 0 ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-show="openFaq === 0" class="px-4 pb-3">
              <p class="text-sm text-medroid-slate">Yes, you can upgrade, downgrade, or cancel your subscription at any time from your account settings.</p>
            </div>
          </div>

          <!-- FAQ Item 2 -->
          <div class="border border-gray-200 rounded-lg">
            <button
              @click="toggleFaq(1)"
              class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <span class="font-medium text-medroid-navy text-sm">How does the free plan work?</span>
              <svg
                :class="['w-4 h-4 text-medroid-slate transition-transform', openFaq === 1 ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-show="openFaq === 1" class="px-4 pb-3">
              <p class="text-sm text-medroid-slate">The free plan gives you basic access to our healthcare services with some usage limits. It's perfect for getting started.</p>
            </div>
          </div>

          <!-- FAQ Item 3 -->
          <div class="border border-gray-200 rounded-lg">
            <button
              @click="toggleFaq(2)"
              class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <span class="font-medium text-medroid-navy text-sm">Any long-term commitments?</span>
              <svg
                :class="['w-4 h-4 text-medroid-slate transition-transform', openFaq === 2 ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-show="openFaq === 2" class="px-4 pb-3">
              <p class="text-sm text-medroid-slate">No, all our plans are subscription-based and can be cancelled at any time without penalty.</p>
            </div>
          </div>

          <!-- FAQ Item 4 -->
          <div class="border border-gray-200 rounded-lg">
            <button
              @click="toggleFaq(3)"
              class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <span class="font-medium text-medroid-navy text-sm">How secure is my payment?</span>
              <svg
                :class="['w-4 h-4 text-medroid-slate transition-transform', openFaq === 3 ? 'rotate-180' : '']"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-show="openFaq === 3" class="px-4 pb-3">
              <p class="text-sm text-medroid-slate">We use Stripe for payment processing, which is PCI compliant and uses industry-standard encryption.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PublicLayout>
</template>
