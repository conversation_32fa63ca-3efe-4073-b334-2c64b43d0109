<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrescriptionRefillRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'original_prescription_id',
        'patient_id',
        'requested_by',
        'reviewed_by',
        'new_prescription_id',
        'status',
        'request_reason',
        'patient_notes',
        'requested_items',
        'requested_at',
        'reviewed_at',
        'review_notes',
        'rejection_reason',
        'requires_consultation',
        'expires_at',
    ];

    protected $casts = [
        'requested_items' => 'array',
        'requested_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'requires_consultation' => 'boolean',
        'expires_at' => 'date',
    ];

    /**
     * Get the original prescription.
     */
    public function originalPrescription()
    {
        return $this->belongsTo(Prescription::class, 'original_prescription_id');
    }

    /**
     * Get the patient who needs the refill.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who requested the refill.
     */
    public function requester()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the clinician who reviewed the request.
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the new prescription created from this refill request.
     */
    public function newPrescription()
    {
        return $this->belongsTo(Prescription::class, 'new_prescription_id');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved requests.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get rejected requests.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope to get expired requests.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere('expires_at', '<', now()->toDateString());
    }

    /**
     * Scope to filter by patient.
     */
    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    /**
     * Scope to filter by reviewer.
     */
    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewed_by', $reviewerId);
    }

    /**
     * Check if request is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if request is approved.
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if request is rejected.
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if request is expired.
     */
    public function isExpired()
    {
        return $this->status === 'expired' || 
               ($this->expires_at && $this->expires_at->isPast());
    }

    /**
     * Check if request requires consultation.
     */
    public function requiresConsultation()
    {
        return $this->requires_consultation;
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending Review',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'expired' => 'Expired',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the status color for UI display.
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            'expired' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    /**
     * Approve the refill request.
     */
    public function approve($reviewerId, $notes = null, $newPrescriptionId = null)
    {
        $this->status = 'approved';
        $this->reviewed_by = $reviewerId;
        $this->reviewed_at = now();
        $this->review_notes = $notes;
        
        if ($newPrescriptionId) {
            $this->new_prescription_id = $newPrescriptionId;
        }
        
        $this->save();
    }

    /**
     * Reject the refill request.
     */
    public function reject($reviewerId, $reason, $notes = null)
    {
        $this->status = 'rejected';
        $this->reviewed_by = $reviewerId;
        $this->reviewed_at = now();
        $this->rejection_reason = $reason;
        $this->review_notes = $notes;
        $this->save();
    }

    /**
     * Mark the request as expired.
     */
    public function markAsExpired()
    {
        $this->status = 'expired';
        $this->save();
    }

    /**
     * Get the requested items with medication details.
     */
    public function getRequestedItemsWithDetails()
    {
        if (!$this->requested_items) {
            return collect();
        }

        $originalItems = $this->originalPrescription->items;
        
        return collect($this->requested_items)->map(function ($requestedItem) use ($originalItems) {
            $originalItem = $originalItems->find($requestedItem['prescription_item_id']);
            
            return [
                'original_item' => $originalItem,
                'requested_quantity' => $requestedItem['quantity'] ?? $originalItem->quantity,
                'notes' => $requestedItem['notes'] ?? null,
            ];
        });
    }

    /**
     * Get the time since request was made.
     */
    public function getTimeSinceRequestAttribute()
    {
        return $this->requested_at->diffForHumans();
    }

    /**
     * Check if request is urgent (based on medication type or patient condition).
     */
    public function isUrgent()
    {
        // This could be enhanced to check for critical medications
        // For now, we'll consider requests for chronic medications as potentially urgent
        $originalPrescription = $this->originalPrescription;
        
        return $originalPrescription && $originalPrescription->type === 'chronic';
    }
}
