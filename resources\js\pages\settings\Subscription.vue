<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, computed, withDefaults, defineProps } from 'vue';
import axios from 'axios';
import { useCurrency } from '@/composables/useCurrency';

import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { type BreadcrumbItem } from '@/types';

interface SubscriptionPlan {
    id: number;
    name: string;
    slug: string;
    price: number;
    formatted_price: string;
    currency: string;
    interval: string;
    description: string;
    features: string[];
    is_free: boolean;
    rate_limits: {
        chat_messages_per_hour: number;
        chat_messages_per_day: number;
        appointments_per_month: number;
        api_requests_per_minute: number;
        api_requests_per_hour: number;
    };
}

interface CurrentSubscription {
    plan: SubscriptionPlan | null;
    status: string;
    started_at: string | null;
    ends_at: string | null;
    is_active: boolean;
    usage_limits: any;
    current_usage: any;
    usage_percentages: any;
    stripe_data?: StripeSubscriptionData | null;
}

interface StripeSubscriptionData {
    id: string;
    status: string;
    current_period_start: number;
    current_period_end: number;
    cancel_at_period_end: boolean;
    canceled_at: number | null;
    trial_end: number | null;
    next_payment_date: number;
}

interface PaymentMethod {
    id: string;
    type: string;
    card: {
        brand: string;
        last4: string;
        exp_month: number;
        exp_year: number;
    };
    is_default: boolean;
}

interface Invoice {
    id: string;
    number: string;
    amount_paid: number;
    amount_due: number;
    currency: string;
    status: string;
    created: string;
    due_date: string | null;
    paid: boolean;
    hosted_invoice_url: string;
    invoice_pdf: string;
    description: string;
    subscription_id: string | null;
}

interface Props {
    plans?: SubscriptionPlan[];
    currentSubscription?: CurrentSubscription | null;
}

const props = withDefaults(defineProps<Props>(), {
    plans: () => [],
    currentSubscription: () => null,
});

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Subscription settings',
        href: '/settings/subscription',
    },
];

const loading = ref(false);
const plans = ref<SubscriptionPlan[]>(props.plans || []);
const currentSubscription = ref<CurrentSubscription | null>(props.currentSubscription || null);
const stripeData = ref<StripeSubscriptionData | null>(props.currentSubscription?.stripe_data || null);
const paymentMethods = ref<PaymentMethod[]>([]);
const invoices = ref<Invoice[]>([]);
const invoicesLoading = ref(false);
const paymentMethodsLoading = ref(false);
const error = ref('');
const success = ref('');
const upgrading = ref(false);
const activeTab = ref('overview');
const showAddPaymentMethodModal = ref(false);
const addingPaymentMethod = ref(false);
const loadingStripe = ref(false);
const stripe = ref<any>(null);
const cardElement = ref<any>(null);
const stripePublicKey = ref('');

const fetchData = async () => {
    try {
        loading.value = true;
        error.value = '';

        console.log('Props received:', {
            plans: props.plans?.length,
            currentSubscription: !!props.currentSubscription
        });

        // Always fetch payment methods on initial load to ensure they're available
        await fetchPaymentMethods();
        
        // Update stripe data if currentSubscription was refreshed
        if (currentSubscription.value?.stripe_data) {
            stripeData.value = currentSubscription.value.stripe_data;
        }

    } catch (err: any) {
        console.error('Error in fetchData:', err);
        error.value = err.response?.data?.message || 'Failed to load subscription data';
    } finally {
        loading.value = false;
    }
};

const fetchInvoices = async () => {
    // Check if user has a paid subscription and is authenticated
    const hasSubscription = currentSubscription.value &&
                           currentSubscription.value.plan &&
                           !currentSubscription.value.plan.is_free;

    console.log('fetchInvoices called:', {
        hasCurrentSubscription: !!currentSubscription.value,
        hasPlan: !!currentSubscription.value?.plan,
        planIsFree: currentSubscription.value?.plan?.is_free,
        hasSubscription
    });

    if (!hasSubscription) {
        console.log('Skipping invoices fetch - user has no paid subscription');
        invoices.value = [];
        invoicesLoading.value = false;
        return;
    }

    try {
        invoicesLoading.value = true;
        console.log('Making API call to /web-api/subscription/invoices');

        const response = await axios.get('/web-api/subscription/invoices');

        if (response.data.success) {
            invoices.value = response.data.invoices || [];
            console.log('Invoices loaded successfully:', invoices.value.length);
        } else {
            console.warn('Failed to load invoices:', response.data.message);
            invoices.value = [];
        }
    } catch (err: any) {
        console.error('Error fetching invoices:', err);
        if (err.response?.status === 401) {
            console.warn('Authentication required for invoices');
        }
        invoices.value = [];
    } finally {
        invoicesLoading.value = false;
    }
};

const fetchPaymentMethods = async () => {
    console.log('fetchPaymentMethods called');
    
    // Always fetch payment methods if user is authenticated - they may have saved payment methods
    // even if they don't currently have an active subscription

    try {
        paymentMethodsLoading.value = true;
        console.log('Making API call to /web-api/subscription/payment-methods');

        const response = await axios.get('/web-api/subscription/payment-methods');

        if (response.data.success) {
            const methods = response.data.payment_methods || [];
            const defaultMethod = response.data.default_payment_method;
            
            // Mark the default payment method
            paymentMethods.value = methods.map(method => ({
                ...method,
                is_default: defaultMethod && method.id === defaultMethod.id
            }));
            
            console.log('Payment methods loaded successfully:', paymentMethods.value.length);
            console.log('Default payment method ID:', defaultMethod?.id);
        } else {
            console.warn('Failed to load payment methods:', response.data.message);
            paymentMethods.value = [];
        }
    } catch (err: any) {
        console.error('Error fetching payment methods:', err);
        if (err.response?.status === 401) {
            console.warn('Authentication required for payment methods');
        }
        paymentMethods.value = [];
    } finally {
        paymentMethodsLoading.value = false;
    }
};

const downloadInvoice = (invoiceId: string) => {
    window.open(`/web-api/subscription/invoices/${invoiceId}/download`, '_blank');
};

const resendInvoice = async (invoiceId: string) => {
    try {
        const response = await axios.post(`/web-api/subscription/invoices/${invoiceId}/resend`);

        if (response.data.success) {
            success.value = 'Invoice has been sent to your email';
        } else {
            error.value = response.data.message || 'Failed to send invoice';
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to send invoice';
    }
};

const upgradeToPlan = async (planSlug: string) => {
    try {
        upgrading.value = true;
        error.value = '';
        success.value = '';

        const response = await axios.post('/web-api/subscription/upgrade', {
            plan_slug: planSlug
        });

        if (response.data.success) {
            success.value = response.data.message;
            await fetchData(); // Refresh data
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to upgrade plan';
    } finally {
        upgrading.value = false;
    }
};

const cancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will be downgraded to the free plan at the end of your billing period.')) {
        return;
    }

    try {
        upgrading.value = true;
        error.value = '';
        success.value = '';

        const response = await axios.post('/web-api/subscription/cancel');

        if (response.data.success) {
            success.value = response.data.message;
            // Refresh subscription data from server
            const refreshResponse = await axios.get('/settings/subscription');
            if (refreshResponse.data.props.currentSubscription) {
                currentSubscription.value = refreshResponse.data.props.currentSubscription;
                stripeData.value = refreshResponse.data.props.currentSubscription.stripe_data || null;
            }
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to cancel subscription';
    } finally {
        upgrading.value = false;
    }
};

const resumeSubscription = async () => {
    try {
        upgrading.value = true;
        error.value = '';
        success.value = '';

        const response = await axios.post('/web-api/subscription/resume');

        if (response.data.success) {
            success.value = response.data.message;
            // Refresh subscription data from server
            const refreshResponse = await axios.get('/settings/subscription');
            if (refreshResponse.data.props.currentSubscription) {
                currentSubscription.value = refreshResponse.data.props.currentSubscription;
                stripeData.value = refreshResponse.data.props.currentSubscription.stripe_data || null;
            }
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to resume subscription';
    } finally {
        upgrading.value = false;
    }
};

const isCurrentPlan = (planSlug: string): boolean => {
    return currentSubscription.value?.plan?.slug === planSlug;
};

const canUpgrade = (plan: SubscriptionPlan): boolean => {
    if (!currentSubscription.value?.plan) return true;
    return plan.price > currentSubscription.value.plan.price;
};

const getUsagePercentage = (type: string): number => {
    return currentSubscription.value?.usage_percentages?.[type] || 0;
};

const getUsageColor = (percentage: number): string => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
};

const formatDate = (timestamp: number | null): string => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleDateString();
};

// Use currency composable
const { formatPrice, loadCountries } = useCurrency();

const formatCurrency = (amount: number, currency: string = 'gbp'): string => {
    return formatPrice(amount / 100);
};

const isSubscriptionCancelled = (): boolean => {
    // Check if subscription is cancelled but still active until period end
    return (stripeData.value?.cancel_at_period_end || 
            stripeData.value?.status === 'canceled' || 
            currentSubscription.value?.status === 'cancelled') || false;
};

const getNextPaymentDate = (): string => {
    if (!stripeData.value?.next_payment_date) return 'N/A';
    return formatDate(stripeData.value.next_payment_date);
};

const getSubscriptionStatus = (): string => {
    if (!stripeData.value) return currentSubscription.value?.status || 'free';

    if (stripeData.value.cancel_at_period_end) {
        return 'Cancelling';
    }

    return stripeData.value.status;
};

onMounted(async () => {
    try {
        // Load countries for currency formatting
        loadCountries();

        // If we have initial data from props, don't show loading state
        if (props.plans && props.plans.length > 0 && props.currentSubscription) {
            loading.value = false;
        }

        await fetchData();
    } catch (err) {
        console.error('Error in onMounted:', err);
        error.value = 'Failed to load subscription data';
        loading.value = false;
    }
});

const setDefaultPaymentMethod = async (paymentMethodId: string) => {
    try {
        // Get the payment method details for better success message
        const selectedMethod = paymentMethods.value.find(method => method.id === paymentMethodId);
        const cardDetails = selectedMethod ? `${selectedMethod.card.brand.toUpperCase()} •••• ${selectedMethod.card.last4}` : 'Payment method';
        
        const response = await axios.put('/web-api/subscription/payment-methods/default', {
            payment_method_id: paymentMethodId
        });

        if (response.data.success) {
            success.value = `${cardDetails} is now your default payment method`;
            await fetchPaymentMethods();
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to update default payment method';
    }
};

const removePaymentMethod = async (paymentMethodId: string) => {
    // Check if this is the only payment method
    if (paymentMethods.value.length === 1) {
        error.value = 'Cannot remove the only payment method. Please add another payment method first.';
        return;
    }

    if (!confirm('Are you sure you want to remove this payment method?')) {
        return;
    }

    try {
        const response = await axios.delete('/web-api/subscription/payment-methods', {
            data: { payment_method_id: paymentMethodId }
        });

        if (response.data.success) {
            success.value = response.data.message;
            await fetchPaymentMethods();
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to remove payment method';
    }
};

const initializeStripe = async () => {
    try {
        loadingStripe.value = true;
        
        // Get Stripe public key from backend
        const response = await axios.get('/web-api/subscription/stripe-config');
        stripePublicKey.value = response.data.publishable_key;
        
        // Check if Stripe is already loaded
        if ((window as any).Stripe) {
            stripe.value = (window as any).Stripe(stripePublicKey.value);
            await setupCardElement();
        } else {
            // Load Stripe.js
            const script = document.createElement('script');
            script.src = 'https://js.stripe.com/v3/';
            script.onload = async () => {
                stripe.value = (window as any).Stripe(stripePublicKey.value);
                await setupCardElement();
            };
            document.head.appendChild(script);
        }
    } catch (err) {
        console.error('Failed to initialize Stripe:', err);
        error.value = 'Failed to load payment form. Please try again.';
    } finally {
        loadingStripe.value = false;
    }
};

const setupCardElement = async () => {
    if (!stripe.value) return;
    
    const elements = stripe.value.elements();
    cardElement.value = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                    color: '#aab7c4',
                },
            },
        },
    });
    
    // Wait for the modal to be fully rendered
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Mount the card element with retry logic
    let retries = 0;
    const mountElement = () => {
        const cardContainer = document.getElementById('card-element');
        if (cardContainer && cardElement.value) {
            try {
                // Clear any existing content
                cardContainer.innerHTML = '';
                cardElement.value.mount('#card-element');
                console.log('Card element mounted successfully');
            } catch (error) {
                console.error('Failed to mount card element:', error);
                if (retries < 3) {
                    retries++;
                    setTimeout(mountElement, 100);
                }
            }
        } else if (retries < 10) {
            retries++;
            setTimeout(mountElement, 100);
        }
    };
    
    mountElement();
};

const addPaymentMethod = async () => {
    if (!stripe.value || !cardElement.value) {
        error.value = 'Payment form not loaded. Please try again.';
        return;
    }

    try {
        addingPaymentMethod.value = true;
        error.value = '';
        success.value = '';

        // Create payment method
        const { error: stripeError, paymentMethod } = await stripe.value.createPaymentMethod({
            type: 'card',
            card: cardElement.value,
        });

        if (stripeError) {
            error.value = stripeError.message;
            return;
        }

        // Add payment method to backend
        const response = await axios.post('/web-api/subscription/payment-methods', {
            payment_method_id: paymentMethod.id
        });

        if (response.data.success) {
            success.value = response.data.message;
            showAddPaymentMethodModal.value = false;
            await fetchPaymentMethods();
        } else {
            error.value = response.data.message;
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to add payment method';
    } finally {
        addingPaymentMethod.value = false;
    }
};

const openAddPaymentMethodModal = async () => {
    showAddPaymentMethodModal.value = true;
    
    // Wait for the modal to be rendered in the DOM
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // Initialize Stripe
    await initializeStripe();
};

// Watch for tab changes to load data when needed
const handleTabChange = (value: string) => {
    console.log('Tab changed to:', value);
    activeTab.value = value;

    // The functions themselves now check for subscription status before making API calls
    if (value === 'invoices' && invoices.value.length === 0) {
        console.log('Triggering fetchInvoices from tab change');
        fetchInvoices();
    }

    if (value === 'payment-methods' && paymentMethods.value.length === 0) {
        console.log('Triggering fetchPaymentMethods from tab change');
        fetchPaymentMethods();
    }
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Subscription settings" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall 
                    title="Subscription settings" 
                    description="Manage your subscription plan and view usage statistics" 
                />

                <!-- Loading State -->
                <div v-if="loading" class="flex justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                </div>

                <!-- Error Alert -->
                <Alert v-if="error" variant="destructive" class="mb-4">
                    <AlertDescription>{{ error }}</AlertDescription>
                </Alert>

                <!-- Success Alert -->
                <Alert v-if="success" class="mb-4 border-green-200 bg-green-50">
                    <AlertDescription class="text-green-800">{{ success }}</AlertDescription>
                </Alert>

                <div v-if="!loading" class="space-y-6">
                    <!-- Tabs Navigation -->
                    <Tabs :model-value="activeTab" @update:model-value="handleTabChange" class="w-full">
                        <TabsList class="grid w-full grid-cols-3">
                            <TabsTrigger value="overview">Overview</TabsTrigger>
                            <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
                            <TabsTrigger value="invoices">Invoices & Billing</TabsTrigger>
                        </TabsList>

                        <!-- Overview Tab -->
                        <TabsContent value="overview" class="space-y-6">
                            <!-- Current Subscription -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center justify-between">
                                Current Plan
                                <Badge
                                    :variant="currentSubscription?.is_active ? 'default' : 'secondary'"
                                    class="ml-2"
                                >
                                    {{ currentSubscription?.status || 'Free' }}
                                </Badge>
                            </CardTitle>
                            <CardDescription>
                                {{ currentSubscription?.plan?.description || 'Your current subscription plan' }}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">{{ currentSubscription?.plan?.name || 'Free Plan' }}</span>
                                    <span class="text-2xl font-bold">
                                        {{ currentSubscription?.plan?.formatted_price || 'Free' }}
                                        <span v-if="currentSubscription?.plan && !currentSubscription.plan.is_free" class="text-sm font-normal text-gray-500">
                                            /{{ currentSubscription.plan.interval }}
                                        </span>
                                    </span>
                                </div>

                                <!-- Usage Statistics -->
                                <div v-if="currentSubscription" class="space-y-3">
                                    <h4 class="font-medium text-sm">Usage This Period</h4>

                                    <!-- Chat Messages -->
                                    <div class="space-y-1">
                                        <div class="flex justify-between text-sm">
                                            <span>Chat Messages (Daily)</span>
                                            <span>
                                                {{ currentSubscription.current_usage?.chat_messages_today || 0 }} /
                                                {{ currentSubscription.usage_limits?.chat_messages_per_day || 0 }}
                                            </span>
                                        </div>
                                        <Progress
                                            :value="getUsagePercentage('chat_messages_day')"
                                            :class="getUsageColor(getUsagePercentage('chat_messages_day'))"
                                        />
                                    </div>

                                    <!-- Appointments -->
                                    <div class="space-y-1">
                                        <div class="flex justify-between text-sm">
                                            <span>Appointments (Monthly)</span>
                                            <span>
                                                {{ currentSubscription.current_usage?.appointments_this_month || 0 }} /
                                                {{ currentSubscription.usage_limits?.appointments_per_month || 0 }}
                                            </span>
                                        </div>
                                        <Progress
                                            :value="getUsagePercentage('appointments_month')"
                                            :class="getUsageColor(getUsagePercentage('appointments_month'))"
                                        />
                                    </div>
                                </div>

                                <!-- Subscription Details -->
                                <div v-if="stripeData" class="space-y-3 pt-4 border-t">
                                    <h4 class="font-medium text-sm">Subscription Details</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div v-if="stripeData.next_payment_date && !isSubscriptionCancelled()">
                                            <span class="text-gray-600">Next Payment:</span>
                                            <div class="font-medium">{{ getNextPaymentDate() }}</div>
                                        </div>

                                        <div v-if="stripeData.current_period_end">
                                            <span class="text-gray-600">{{ isSubscriptionCancelled() ? 'Ends' : 'Renews' }}:</span>
                                            <div class="font-medium">{{ formatDate(stripeData.current_period_end) }}</div>
                                        </div>

                                        <div>
                                            <span class="text-gray-600">Status:</span>
                                            <div class="font-medium capitalize" :class="isSubscriptionCancelled() ? 'text-yellow-600' : ''">
                                                {{ getSubscriptionStatus() }}
                                                <span v-if="isSubscriptionCancelled()" class="text-xs text-yellow-600 block">
                                                    (Active until {{ formatDate(stripeData?.current_period_end) }})
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cancellation Notice - Always show when subscription is cancelled -->
                                <div v-if="isSubscriptionCancelled()" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div class="flex-1">
                                            <h4 class="font-medium text-yellow-800">Subscription Cancelled</h4>
                                            <p class="text-sm text-yellow-700 mt-1">
                                                <strong>Subscription will be cancelled at the end of the current billing period.</strong>
                                                <br>
                                                Your subscription will end on {{ formatDate(stripeData?.current_period_end) }}. You can resume anytime before then to continue your plan.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div v-if="currentSubscription?.plan && !currentSubscription.plan.is_free" class="flex gap-2 pt-4 border-t">
                                    <Button
                                        v-if="isSubscriptionCancelled()"
                                        variant="default"
                                        size="sm"
                                        @click="resumeSubscription"
                                        :disabled="upgrading"
                                        class="bg-green-600 hover:bg-green-700 text-white"
                                    >
                                        <span v-if="upgrading">Activating...</span>
                                        <span v-else>Activate Subscription</span>
                                    </Button>

                                    <Button
                                        v-else-if="!isSubscriptionCancelled() && (stripeData?.status === 'active' || currentSubscription?.status === 'active')"
                                        variant="outline"
                                        @click="cancelSubscription"
                                        :disabled="upgrading"
                                        class="text-red-600 border-red-200 hover:bg-red-50"
                                    >
                                        <span v-if="upgrading">Cancelling...</span>
                                        <span v-else>Cancel Subscription</span>
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                        </TabsContent>

                        <!-- Payment Methods Tab -->
                        <TabsContent value="payment-methods" class="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Payment Methods</CardTitle>
                                    <CardDescription>Manage your payment methods and billing information</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <!-- Loading State -->
                                    <div v-if="paymentMethodsLoading" class="flex justify-center py-8">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                                    </div>

                                    <!-- Payment Methods List -->
                                    <div v-else-if="paymentMethods.length > 0" class="space-y-6">
                                        <!-- Default Payment Method Summary -->
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <div class="flex items-center gap-3">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="font-medium text-blue-900">Default Payment Method</h4>
                                                    <p class="text-sm text-blue-700 mt-1">
                                                        <template v-if="paymentMethods.find(m => m.is_default)">
                                                            <strong>{{ paymentMethods.find(m => m.is_default)?.card.brand.toUpperCase() }} •••• {{ paymentMethods.find(m => m.is_default)?.card.last4 }}</strong>
                                                            - This card will be charged for your subscription renewals
                                                        </template>
                                                        <template v-else>
                                                            No default payment method set
                                                        </template>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- All Payment Methods -->
                                        <div
                                            v-for="method in paymentMethods"
                                            :key="method.id"
                                            :class="[
                                                'flex items-center justify-between p-4 border rounded-lg transition-all duration-200',
                                                method.is_default 
                                                    ? 'border-green-300 bg-green-50 ring-1 ring-green-200' 
                                                    : 'border-gray-200 hover:border-gray-300'
                                            ]"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div :class="[
                                                    'w-8 h-8 rounded flex items-center justify-center',
                                                    method.is_default ? 'bg-green-100' : 'bg-gray-100'
                                                ]">
                                                    <svg :class="[
                                                        'w-4 h-4',
                                                        method.is_default ? 'text-green-600' : 'text-gray-600'
                                                    ]" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z"></path>
                                                    </svg>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="flex items-center gap-2">
                                                        <span class="font-medium">
                                                            {{ method.card.brand.toUpperCase() }} •••• {{ method.card.last4 }}
                                                        </span>
                                                        <div v-if="method.is_default" class="flex items-center gap-1">
                                                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                            </svg>
                                                            <Badge variant="secondary" class="bg-green-100 text-green-800 border-green-200">
                                                                Default Payment Method
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                    <div class="text-sm text-gray-500 mt-1">
                                                        Expires {{ method.card.exp_month }}/{{ method.card.exp_year }}
                                                        <span v-if="method.is_default" class="text-green-600 font-medium ml-2">
                                                            • Used for automatic billing
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <Button
                                                    v-if="!method.is_default"
                                                    variant="outline"
                                                    size="sm"
                                                    @click="setDefaultPaymentMethod(method.id)"
                                                >
                                                    Set Default
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    @click="removePaymentMethod(method.id)"
                                                    :disabled="paymentMethods.length === 1"
                                                    :class="[
                                                        'text-red-600 border-red-200 hover:bg-red-50',
                                                        paymentMethods.length === 1 ? 'opacity-50 cursor-not-allowed' : ''
                                                    ]"
                                                    :title="paymentMethods.length === 1 ? 'Cannot remove the only payment method' : 'Remove this payment method'"
                                                >
                                                    Remove
                                                </Button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- No Payment Methods -->
                                    <div v-else class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No payment methods</h3>
                                        <p class="mt-1 text-sm text-gray-500">Add a payment method to manage your subscription.</p>
                                    </div>

                                    <!-- Add Payment Method Button -->
                                    <div class="pt-4 border-t">
                                        <Button 
                                            variant="outline" 
                                            class="w-full"
                                            @click="openAddPaymentMethodModal()"
                                        >
                                            Add Payment Method
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <!-- Invoices Tab -->
                        <TabsContent value="invoices" class="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Invoice History</CardTitle>
                                    <CardDescription>View and download your subscription invoices</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <!-- Loading State -->
                                    <div v-if="invoicesLoading" class="flex justify-center py-8">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                                    </div>

                                    <!-- No Invoices -->
                                    <div v-else-if="invoices.length === 0" class="text-center py-8">
                                        <p class="text-gray-500">No invoices found</p>
                                    </div>

                                    <!-- Invoices Table -->
                                    <div v-else class="overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Invoice #</TableHead>
                                                    <TableHead>Date</TableHead>
                                                    <TableHead>Amount</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Actions</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                <TableRow v-for="invoice in invoices" :key="invoice.id">
                                                    <TableCell class="font-medium">
                                                        {{ invoice.number || invoice.id.substring(0, 8) }}
                                                    </TableCell>
                                                    <TableCell>
                                                        {{ new Date(invoice.created).toLocaleDateString() }}
                                                    </TableCell>
                                                    <TableCell>
                                                        {{ invoice.currency.toUpperCase() }} {{ invoice.amount_paid.toFixed(2) }}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge
                                                            :variant="invoice.paid ? 'default' : 'secondary'"
                                                            :class="invoice.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                                                        >
                                                            {{ invoice.status }}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div class="flex space-x-2">
                                                            <Button
                                                                size="sm"
                                                                variant="outline"
                                                                @click="downloadInvoice(invoice.id)"
                                                            >
                                                                Download
                                                            </Button>
                                                            <Button
                                                                size="sm"
                                                                variant="ghost"
                                                                @click="resendInvoice(invoice.id)"
                                                            >
                                                                Resend
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </SettingsLayout>

        <!-- Add Payment Method Modal -->
        <div v-if="showAddPaymentMethodModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Add Payment Method</h3>
                    <button @click="showAddPaymentMethodModal = false" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Card Information
                        </label>
                        <div 
                            id="card-element"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[40px] flex items-center justify-center"
                        >
                            <span v-if="loadingStripe" class="text-gray-500 text-sm">Loading payment form...</span>
                            <!-- Stripe Elements will be mounted here -->
                        </div>
                    </div>
                    
                    <div class="text-xs text-gray-500">
                        <p>Your payment information is secure and encrypted.</p>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <Button variant="outline" @click="showAddPaymentMethodModal = false">
                        Cancel
                    </Button>
                    <Button 
                        @click="addPaymentMethod"
                        :disabled="addingPaymentMethod"
                        class="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        <span v-if="addingPaymentMethod">Adding...</span>
                        <span v-else>Add Payment Method</span>
                    </Button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
