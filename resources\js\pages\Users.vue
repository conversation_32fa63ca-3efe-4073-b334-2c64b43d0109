<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';
import Icon from '@/components/Icon.vue';
import { useNotifications } from '@/composables/useNotifications';
import ClinicianToggle from '@/components/admin/ClinicianToggle.vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Users', href: '/users' },
];

// Get user from page props
const page = usePage();
const user = computed(() => {
    try {
        return page.props.auth?.user || null;
    } catch (error) {
        console.warn('Error accessing user data:', error);
        return null;
    }
});

// Permissions
const canEditUsers = computed(() => {
    return user.value?.user_permissions?.includes('edit users') ||
           user.value?.roles?.some(role => role.name === 'admin') ||
           user.value?.roles?.some(role => role.name === 'clinic_admin') ||
           user.value?.role === 'admin' ||
           user.value?.role === 'clinic_admin';
});

const canCreateUsers = computed(() => {
    return user.value?.user_permissions?.includes('create users') ||
           user.value?.roles?.some(role => role.name === 'admin') ||
           user.value?.roles?.some(role => role.name === 'clinic_admin') ||
           user.value?.role === 'admin' ||
           user.value?.role === 'clinic_admin';
});

const isClinicAdmin = computed(() => {
    // Check Spatie roles first, then fallback to role column
    return user.value?.roles?.some(role => role.name === 'clinic_admin') ||
           user.value?.role === 'clinic_admin' ||
           false;
});

const isAdmin = computed(() => {
    // Check Spatie roles first, then fallback to role column
    return user.value?.roles?.some(role => role.name === 'admin') ||
           user.value?.role === 'admin' ||
           false;
});

// Filter clinics based on user role
const availableClinics = computed(() => {
    if (isAdmin.value) {
        return clinics.value;
    }
    // Non-admin users can only see their own clinic
    return clinics.value.filter(clinic => clinic.id === user.value?.clinic_id);
});

// Current user ID for impersonation check
const currentUserId = computed(() => {
    return user.value?.id;
});

const loading = ref(false);
const users = ref([]);
const clinics = ref([]);
const searchQuery = ref('');
const selectedRole = ref('all');
const statusFilter = ref('all');
const impersonating = ref(false);
const showEditModal = ref(false);
const showCreateModal = ref(false);
const showRoleModal = ref(false);
const selectedUser = ref(null);

// Professional notification system
const { showSuccess, showError, showConfirm } = useNotifications();
const editForm = ref({
    name: '',
    email: '',
    role: '',
    clinic_id: ''
});

const createForm = ref({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'patient',
    clinic_id: ''
});

const roleForm = ref({
    role: ''
});

// Pagination state
const currentPage = ref(1);
const perPage = ref(10);
const totalUsers = ref(0);
const totalPages = ref(0);

const filteredUsers = computed(() => {
    if (!Array.isArray(users.value)) {
        console.warn('Users is not an array:', users.value);
        return [];
    }

    return users.value.filter(user => {
        if (!user || !user.id) return false;

        const matchesSearch = !searchQuery.value ||
            user.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            user.email?.toLowerCase().includes(searchQuery.value.toLowerCase());

        const matchesRole = selectedRole.value === 'all' ||
            getUserPrimaryRole(user) === selectedRole.value;

        const matchesStatus = statusFilter.value === 'all' ||
            (statusFilter.value === 'active' && user.is_active) ||
            (statusFilter.value === 'inactive' && !user.is_active);

        return matchesSearch && matchesRole && matchesStatus;
    });
});

const fetchUsers = async (page = 1) => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('per_page', perPage.value.toString());

        if (searchQuery.value.trim()) {
            params.append('search', searchQuery.value.trim());
        }

        if (selectedRole.value !== 'all') {
            params.append('role', selectedRole.value);
        }

        console.log('Fetching users with params:', params.toString());
        const response = await window.axios.get(`/users-list?${params.toString()}`);
        console.log('Users API response:', response.data);

        // Handle paginated response
        const paginatedData = response.data.data;
        users.value = paginatedData.data || [];
        currentPage.value = paginatedData.current_page || 1;
        totalUsers.value = paginatedData.total || 0;
        totalPages.value = paginatedData.last_page || 1;

        console.log('Processed users:', users.value);
        console.log('Pagination info:', {
            currentPage: currentPage.value,
            totalUsers: totalUsers.value,
            totalPages: totalPages.value
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        users.value = []; // Set empty array on error
    } finally {
        loading.value = false;
    }
};

const fetchClinics = async () => {
    try {
        const response = await window.axios.get('/clinics-list-dropdown');
        clinics.value = response.data.data || response.data.clinics || [];
        console.log('Clinics loaded:', clinics.value);
    } catch (error) {
        console.error('Error fetching clinics:', error);
        clinics.value = [];
    }
};


// Get the primary role for a user (from Spatie roles or role column)
const getUserPrimaryRole = (user) => {
    if (!user) return 'N/A';

    // Check if user has Spatie roles
    if (user.roles && user.roles.length > 0) {
        return user.roles[0].name;
    }

    // Fall back to role column
    return user.role || 'patient';
};

// Get role badge classes
const getRoleBadgeClass = (role) => {
    const classes = {
        'admin': 'bg-red-100 text-red-800 border-red-200',
        'provider': 'bg-blue-100 text-blue-800 border-blue-200',
        'patient': 'bg-green-100 text-green-800 border-green-200',
        'clinic_admin': 'bg-purple-100 text-purple-800 border-purple-200',
        'bot': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return classes[role] || 'bg-gray-100 text-gray-800 border-gray-200';
};

const impersonateUser = async (userId) => {
    if (await showConfirm('Impersonate User', 'Are you sure you want to login as this user?', 'Yes, Login', 'Cancel')) {
        impersonating.value = true;
        try {
            const response = await window.axios.post(`/impersonate/${userId}`);
            if (response.data.redirect_url) {
                window.location.href = response.data.redirect_url;
            }
        } catch (error) {
            console.error('Error impersonating user:', error);
            showError('Failed to impersonate user. Please try again.');
        } finally {
            impersonating.value = false;
        }
    }
};

const openEditModal = (user) => {
    selectedUser.value = user;
    editForm.value = {
        name: user.name,
        email: user.email,
        role: getUserPrimaryRole(user),
        clinic_id: user.clinic_id || (isAdmin.value ? '' : (user.value?.clinic_id || ''))
    };
    showEditModal.value = true;
};

const closeEditModal = () => {
    showEditModal.value = false;
    selectedUser.value = null;
    editForm.value = {
        name: '',
        email: '',
        role: '',
        clinic_id: ''
    };
};

const updateUser = async () => {
    try {
        const response = await window.axios.put(`/users/${selectedUser.value.id}`, editForm.value);

        if (response.data.success || response.status === 200) {
            showSuccess('User updated successfully');
            closeEditModal();
            fetchUsers(); // Refresh the users list
        } else {
            showError('Failed to update user: ' + (response.data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error updating user:', error);
        if (error.response && error.response.data && error.response.data.errors) {
            const errors = Object.values(error.response.data.errors).flat();
            showError('Validation errors: ' + errors.join(', '));
        } else {
            showError('Error updating user. Please try again.');
        }
    }
};

const openCreateModal = () => {
    createForm.value = {
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'patient',
        clinic_id: isAdmin.value ? '' : (user.value?.clinic_id || '')
    };
    showCreateModal.value = true;
};

const closeCreateModal = () => {
    showCreateModal.value = false;
    createForm.value = {
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'patient',
        clinic_id: ''
    };
};

const createUser = async () => {
    try {
        const response = await window.axios.post('/users', createForm.value);

        if (response.data || response.status === 201) {
            showSuccess('User created successfully');
            closeCreateModal();
            fetchUsers(); // Refresh the users list
        } else {
            showError('Failed to create user: ' + (response.data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error creating user:', error);
        if (error.response && error.response.data && error.response.data.errors) {
            const errors = Object.values(error.response.data.errors).flat();
            showError('Validation errors: ' + errors.join(', '));
        } else {
            showError('Error creating user. Please try again.');
        }
    }
};

const openRoleModal = (user) => {
    selectedUser.value = user;
    roleForm.value = {
        role: getUserPrimaryRole(user)
    };
    showRoleModal.value = true;
};

const closeRoleModal = () => {
    showRoleModal.value = false;
    selectedUser.value = null;
    roleForm.value = {
        role: ''
    };
};

const assignRole = async () => {
    try {
        const response = await window.axios.post(`/users/${selectedUser.value.id}/assign-role`, roleForm.value);

        if (response.data || response.status === 200) {
            showSuccess('Role assigned successfully');
            closeRoleModal();
            fetchUsers(); // Refresh the users list
        } else {
            showError('Failed to assign role: ' + (response.data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error assigning role:', error);
        if (error.response && error.response.data && error.response.data.errors) {
            const errors = Object.values(error.response.data.errors).flat();
            showError('Validation errors: ' + errors.join(', '));
        } else {
            showError('Error assigning role. Please try again.');
        }
    }
};

const toggleUserStatus = async (userId, currentStatus) => {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';

    if (await showConfirm(`${action.charAt(0).toUpperCase() + action.slice(1)} User`, `Are you sure you want to ${action} this user?`, action.charAt(0).toUpperCase() + action.slice(1), 'Cancel')) {
        try {
            const response = await window.axios.patch(`/users/${userId}/toggle-status`, {
                is_active: newStatus
            });

            if (response.data.success) {
                // Update the user in the local array
                const userIndex = users.value.findIndex(user => user.id === userId);
                if (userIndex !== -1) {
                    users.value[userIndex].is_active = newStatus;
                }
                showSuccess(`User ${action}d successfully.`);
            }
        } catch (error) {
            console.error('Error toggling user status:', error);
            showError(`Failed to ${action} user. Please try again.`);
        }
    }
};

// Clinician toggle event handlers
const onUserUpdated = (updatedUser) => {
    // Update the user in the list
    const userIndex = users.value.findIndex(u => u.id === updatedUser.id);
    if (userIndex !== -1) {
        users.value[userIndex] = { ...users.value[userIndex], is_clinician: updatedUser.is_clinician };
    }
    showSuccess('Clinician access updated successfully');
};

const onToggleError = (errorMessage) => {
    showError(errorMessage);
};

// Debounce function
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Debounced search function
const debouncedSearch = debounce(() => {
    currentPage.value = 1;
    fetchUsers(1);
}, 500);

// Watch for search changes with debounce
watch(searchQuery, () => {
    debouncedSearch();
});

// Watch for role filter changes (immediate)
watch(selectedRole, () => {
    currentPage.value = 1;
    fetchUsers(1);
});

// Pagination methods
const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
        fetchUsers(page);
    }
};

const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        goToPage(currentPage.value + 1);
    }
};

const prevPage = () => {
    if (currentPage.value > 1) {
        goToPage(currentPage.value - 1);
    }
};

const changePerPage = (newPerPage) => {
    perPage.value = newPerPage;
    currentPage.value = 1;
    fetchUsers(1);
};

onMounted(() => {
    console.log('Users component mounted - with pagination and search');
    console.log('Search query:', searchQuery.value);
    console.log('Selected role:', selectedRole.value);
    console.log('Current user:', user.value);
    console.log('User roles:', user.value?.roles);
    console.log('Is admin:', isAdmin.value);
    console.log('Is clinic admin:', isClinicAdmin.value);
    fetchUsers();
    fetchClinics();
});
</script>

<template>
    <Head title="User Management" />

    <AppLayout>
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8 py-6">
            <div class="mx-auto max-w-7xl sm:px-4 lg:px-6">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 mb-4 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 mb-1">User Management</h2>
                                <p class="text-gray-600">Manage system users and their permissions efficiently</p>
                                <nav class="flex mt-3" aria-label="Breadcrumb">
                                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                        <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                            <Link v-if="index < breadcrumbs.length - 1"
                                                :href="breadcrumb.href"
                                                class="text-sm font-medium text-gray-500 hover:text-teal-600 dark:text-gray-400 dark:hover:text-white">
                                                {{ breadcrumb.title }}
                                            </Link>
                                            <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                                {{ breadcrumb.title }}
                                            </span>
                                            <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </li>
                                    </ol>
                                </nav>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button
                                    v-if="canCreateUsers"
                                    @click="openCreateModal"
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-teal-700 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
                                >
                                    <Icon name="plus" class="w-4 h-4 mr-2" />
                                    Add User
                                </button>
                                <button
                                    @click="fetchUsers"
                                    :disabled="loading"
                                    class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors disabled:opacity-50"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filters Section -->
                    <div class="p-6 bg-gradient-to-r from-gray-50 to-white">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Search</label>
                                <div class="relative">
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        placeholder="Search users..."
                                        class="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
                                    />
                                    <Icon name="search" class="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Role</label>
                                <select
                                    v-model="selectedRole"
                                    class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
                                    style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                >
                                    <option value="all">All Roles</option>
                                    <option value="admin">Admin</option>
                                    <option value="provider">Provider</option>
                                    <option value="patient">Patient</option>
                                    <option value="clinic_admin">Clinic Admin</option>
                                    <option value="bot">Bot</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Status</label>
                                <select
                                    v-model="statusFilter"
                                    class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
                                    style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                >
                                    <option value="all">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users List -->
                <div class="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Users</h3>
                            <div class="text-sm text-gray-600">
                                {{ filteredUsers.length }} users • Page {{ currentPage }} of {{ totalPages }}
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Loading State -->
                        <div v-if="loading" class="text-center py-16">
                            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-teal-200 border-t-teal-600"></div>
                            <p class="mt-4 text-gray-600">Loading users...</p>
                        </div>

                        <!-- Empty State -->
                        <div v-else-if="filteredUsers.length === 0" class="text-center py-16">
                            <Icon name="users" class="w-16 h-16 text-gray-400 mx-auto mb-6" />
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">No users found</h3>
                            <p class="text-gray-600 mb-8 max-w-md mx-auto">No users match your current filters. Try adjusting your search criteria.</p>
                        </div>

                        <!-- User Cards -->
                        <div v-else class="space-y-3">
                            <div
                                v-for="user in filteredUsers"
                                :key="user.id"
                                class="bg-white border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 hover:border-gray-200"
                            >
                                <div class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3 flex-1">
                                            <!-- User Avatar -->
                                            <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center">
                                                <Icon name="user" class="w-4 h-4 text-white" />
                                            </div>

                                            <!-- User Details -->
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center space-x-2 mb-1">
                                                    <h3 class="text-sm font-semibold text-gray-900 truncate">
                                                        {{ user.name }}
                                                    </h3>
                                                    <span :class="getRoleBadgeClass(getUserPrimaryRole(user))" class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-md border">
                                                        {{ getUserPrimaryRole(user) }}
                                                    </span>
                                                    <span :class="user?.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-md">
                                                        {{ user?.is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </div>
                                                <div class="flex items-center space-x-4 text-xs text-gray-600">
                                                    <span>{{ user.email }}</span>
                                                    <span>Last: {{ user?.last_login || 'Never' }}</span>
                                                </div>
                                            </div>

                                            <!-- Clinician Toggle -->
                                            <div class="flex items-center">
                                                <ClinicianToggle
                                                    :user="user"
                                                    @updated="onUserUpdated"
                                                    @error="onToggleError"
                                                />
                                            </div>

                                            <!-- Actions -->
                                            <div class="flex items-center space-x-1">
                                                <button
                                                    v-if="canEditUsers"
                                                    @click="openEditModal(user)"
                                                    class="inline-flex items-center px-2 py-1 bg-teal-600 text-white text-xs font-medium rounded hover:bg-teal-700 transition-colors"
                                                >
                                                    <Icon name="edit" class="w-3 h-3 mr-1" />
                                                    Edit
                                                </button>
                                                <button
                                                    v-if="isAdmin && user.id !== currentUserId"
                                                    @click="impersonateUser(user.id)"
                                                    :disabled="impersonating"
                                                    class="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
                                                >
                                                    <Icon name="user" class="w-3 h-3 mr-1" />
                                                    Login As
                                                </button>
                                                <button
                                                    v-if="getUserPrimaryRole(user) !== 'admin'"
                                                    @click="toggleUserStatus(user.id, user.is_active)"
                                                    :class="user?.is_active ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'"
                                                    class="inline-flex items-center px-2 py-1 text-white text-xs font-medium rounded transition-colors"
                                                >
                                                    {{ user?.is_active ? 'Deactivate' : 'Activate' }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination Controls -->
                        <div v-if="!loading && filteredUsers.length > 0" class="mt-6 flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">
                                    Showing {{ ((currentPage - 1) * perPage) + 1 }} to {{ Math.min(currentPage * perPage, totalUsers) }} of {{ totalUsers }} results
                                </span>
                            </div>

                            <div class="flex items-center space-x-2">
                                <!-- Per page selector -->
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-700">Show:</span>
                                    <select
                                        :value="perPage"
                                        @change="changePerPage(parseInt($event.target.value))"
                                        class="px-3 py-1.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white"
                                    >
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>

                                <!-- Pagination buttons -->
                                <div class="flex items-center space-x-1">
                                    <button
                                        @click="prevPage"
                                        :disabled="currentPage <= 1"
                                        class="px-3 py-1.5 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white text-gray-700 transition-colors"
                                    >
                                        Previous
                                    </button>

                                    <!-- Page numbers -->
                                    <template v-for="page in Math.min(totalPages, 5)" :key="page">
                                        <button
                                            v-if="page <= totalPages"
                                            @click="goToPage(page)"
                                            :class="page === currentPage ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white border-teal-600' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'"
                                            class="px-3 py-1.5 text-sm border rounded-lg transition-colors"
                                        >
                                            {{ page }}
                                        </button>
                                    </template>

                                    <span v-if="totalPages > 5" class="px-2 text-gray-500">...</span>

                                    <button
                                        v-if="totalPages > 5 && currentPage < totalPages - 2"
                                        @click="goToPage(totalPages)"
                                        :class="totalPages === currentPage ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white border-teal-600' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'"
                                        class="px-3 py-1.5 text-sm border rounded-lg transition-colors"
                                    >
                                        {{ totalPages }}
                                    </button>

                                    <button
                                        @click="nextPage"
                                        :disabled="currentPage >= totalPages"
                                        class="px-3 py-1.5 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white text-gray-700 transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit User Modal -->
        <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Edit User</h3>
                        <button @click="closeEditModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Modal Body -->
                    <form @submit.prevent="updateUser">
                        <!-- Name -->
                        <div class="mb-4">
                            <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Name <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="editForm.name"
                                type="text"
                                id="edit_name"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Email -->
                        <div class="mb-4">
                            <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="editForm.email"
                                type="email"
                                id="edit_email"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Clinic Selection (Only for Admin users) -->
                        <div v-if="isAdmin" class="mb-4">
                            <label for="edit_clinic_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Clinic <span class="text-red-500">*</span>
                            </label>
                            <select
                                v-model="editForm.clinic_id"
                                id="edit_clinic_id"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="">Select a clinic</option>
                                <option v-for="clinic in availableClinics" :key="clinic.id" :value="clinic.id">
                                    {{ clinic.name }}
                                </option>
                            </select>
                        </div>

                        <!-- Role -->
                        <div class="mb-6">
                            <label for="edit_role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role <span class="text-red-500">*</span>
                            </label>
                            <select
                                v-model="editForm.role"
                                id="edit_role"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="admin">Admin</option>
                                <option value="clinic_admin">Clinic Admin</option>
                                <option value="provider">Provider</option>
                                <option value="patient">Patient</option>
                            </select>
                        </div>

                        <!-- Modal Footer -->
                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                @click="closeEditModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 border border-transparent rounded-lg hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                            >
                                Update User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Create User Modal -->
        <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Create New User</h3>
                        <button @click="closeCreateModal" class="text-gray-400 hover:text-gray-600">
                            <Icon name="x" class="w-5 h-5" />
                        </button>
                    </div>

                    <!-- Modal Body -->
                    <form @submit.prevent="createUser">
                        <!-- Name -->
                        <div class="mb-4">
                            <label for="create_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Name <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="createForm.name"
                                type="text"
                                id="create_name"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Email -->
                        <div class="mb-4">
                            <label for="create_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="createForm.email"
                                type="email"
                                id="create_email"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Password -->
                        <div class="mb-4">
                            <label for="create_password" class="block text-sm font-medium text-gray-700 mb-2">
                                Password <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="createForm.password"
                                type="password"
                                id="create_password"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-4">
                            <label for="create_password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                Confirm Password <span class="text-red-500">*</span>
                            </label>
                            <input
                                v-model="createForm.password_confirmation"
                                type="password"
                                id="create_password_confirmation"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                        </div>

                        <!-- Clinic Selection (Only for Admin users) -->
                        <div v-if="isAdmin" class="mb-4">
                            <label for="create_clinic_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Clinic <span class="text-red-500">*</span>
                            </label>
                            <select
                                v-model="createForm.clinic_id"
                                id="create_clinic_id"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="">Select a clinic</option>
                                <option v-for="clinic in availableClinics" :key="clinic.id" :value="clinic.id">
                                    {{ clinic.name }}
                                </option>
                            </select>
                        </div>

                        <!-- Role -->
                        <div class="mb-6">
                            <label for="create_role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role <span class="text-red-500">*</span>
                            </label>
                            <select
                                v-model="createForm.role"
                                id="create_role"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="patient">Patient</option>
                                <option value="provider">Provider</option>
                                <option v-if="!isClinicAdmin" value="clinic_admin">Clinic Admin</option>
                                <option v-if="isAdmin" value="admin">Admin</option>
                            </select>
                        </div>

                        <!-- Modal Footer -->
                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                @click="closeCreateModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 border border-transparent rounded-lg hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                            >
                                Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Role Assignment Modal -->
        <div v-if="showRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Assign Role</h3>
                        <button @click="closeRoleModal" class="text-gray-400 hover:text-gray-600">
                            <Icon name="x" class="w-5 h-5" />
                        </button>
                    </div>

                    <!-- Modal Body -->
                    <form @submit.prevent="assignRole">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-4">
                                Assign role for: <strong>{{ selectedUser?.name }}</strong>
                            </p>
                        </div>

                        <!-- Role -->
                        <div class="mb-6">
                            <label for="assign_role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role <span class="text-red-500">*</span>
                            </label>
                            <select
                                v-model="roleForm.role"
                                id="assign_role"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="patient">Patient</option>
                                <option value="provider">Provider</option>
                                <option value="clinic_admin">Clinic Admin</option>
                                <option v-if="isAdmin" value="admin">Admin</option>
                            </select>
                        </div>

                        <!-- Modal Footer -->
                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                @click="closeRoleModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 border border-transparent rounded-lg hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                            >
                                Assign Role
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
