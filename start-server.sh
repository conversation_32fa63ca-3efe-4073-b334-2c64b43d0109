#!/bin/bash

# Laravel Development Server Startup Script
# Handles broken pipe errors and provides better error handling

echo "🚀 Starting Laravel Development Server..."

# Find available port
PORT=8000
while lsof -ti:$PORT >/dev/null 2>&1; do
    PORT=$((PORT + 1))
    if [ $PORT -gt 8010 ]; then
        echo "❌ Error: No available ports found between 8000-8010"
        exit 1
    fi
done

echo "📡 Using port $PORT"

# Set PHP configuration for better error handling
export PHP_CLI_SERVER_WORKERS=4

# Start server with our custom router
echo "🔧 Starting server with enhanced error handling..."
php -S 127.0.0.1:$PORT -t public server.php

echo "✅ Server started successfully on http://127.0.0.1:$PORT"