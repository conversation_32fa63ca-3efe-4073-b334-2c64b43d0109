<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_email_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('system_template_id')->constrained('email_templates')->onDelete('cascade');
            $table->string('subject');
            $table->text('content');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['clinic_id', 'system_template_id']);
            $table->index(['clinic_id', 'is_active']);

            // Unique constraint - one customization per clinic per system template
            $table->unique(['clinic_id', 'system_template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_email_templates');
    }
};
