<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "United States"
            $table->string('code', 2)->unique(); // ISO 3166-1 alpha-2 code (e.g., "US")
            $table->string('code_3', 3)->unique(); // ISO 3166-1 alpha-3 code (e.g., "USA")
            $table->string('currency_code', 3); // ISO 4217 currency code (e.g., "USD")
            $table->string('currency_symbol', 10); // e.g., "$"
            $table->string('currency_name'); // e.g., "US Dollar"
            $table->string('phone_code'); // e.g., "+1"
            $table->string('timezone')->nullable(); // Primary timezone
            $table->json('supported_payment_gateways'); // ["stripe", "paypal", "razorpay"]
            $table->json('supported_languages')->nullable(); // ["en", "es"]
            $table->decimal('tax_rate', 5, 2)->default(0.00); // Default tax rate percentage
            $table->boolean('is_active')->default(true);
            $table->boolean('is_supported')->default(true); // Whether we support this country
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'is_supported']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
