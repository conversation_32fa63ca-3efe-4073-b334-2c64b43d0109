<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('taxes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., "VAT", "Sales Tax", "GST"
            $table->string('type')->default('percentage'); // percentage or fixed
            $table->decimal('rate', 8, 4); // Tax rate (e.g., 20.0000 for 20% or 5.00 for $5 fixed)
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_inclusive')->default(false); // Whether tax is included in item price
            $table->timestamps();

            $table->index(['clinic_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taxes');
    }
};
