<?php

namespace App\Repositories;

use App\Models\Patient;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class PatientRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Patient $model
     * @return void
     */
    public function __construct(Patient $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find patient by user ID.
     *
     * @param int $userId
     * @return Patient|null
     */
    public function findByUserId(int $userId): ?Patient
    {
        return $this->findOneBy(['user_id' => $userId]);
    }
    
    /**
     * Find patients by clinic.
     *
     * @param int $clinicId
     * @return Collection
     */
    public function findByClinic(int $clinicId): Collection
    {
        return $this->findBy(['clinic_id' => $clinicId]);
    }
    
    /**
     * Find patients by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->findBy(['created_by_provider_id' => $providerId]);
    }
    
    /**
     * Search patients by name, email, or phone.
     *
     * @param string $search
     * @param int|null $clinicId
     * @return Collection
     */
    public function search(string $search, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->with('user')
              ->whereHas('user', function ($q) use ($search) {
                  $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%");
              });
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Find patients with upcoming appointments.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findWithUpcomingAppointments(?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->with('user')
              ->whereHas('appointments', function ($q) {
                  $q->where('scheduled_at', '>', now())
                    ->whereIn('status', ['scheduled', 'confirmed']);
              });
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Find patients by age range.
     *
     * @param int $minAge
     * @param int $maxAge
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByAgeRange(int $minAge, int $maxAge, ?int $clinicId = null): Collection
    {
        $maxDate = Carbon::now()->subYears($minAge)->format('Y-m-d');
        $minDate = Carbon::now()->subYears($maxAge + 1)->format('Y-m-d');
        
        $query = $this->model->newQuery();
        $query->with('user')
              ->whereBetween('date_of_birth', [$minDate, $maxDate]);
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Find patients by gender.
     *
     * @param string $gender
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByGender(string $gender, ?int $clinicId = null): Collection
    {
        $criteria = ['gender' => $gender];
        
        if ($clinicId) {
            $criteria['clinic_id'] = $clinicId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Get patients with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['clinic']);

        // Apply clinic filter
        if (!empty($filters['clinic_id'])) {
            $query->where('clinic_id', $filters['clinic_id']);
        }

        // Apply search filter - search in Laravel patient columns only
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('patient_unique_id', 'like', "%{$search}%")
                  ->orWhere('nhs_number', 'like', "%{$search}%");
            });
        }
        
        // Apply gender filter
        if (!empty($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }
        
        // Apply age range filter
        if (!empty($filters['min_age']) || !empty($filters['max_age'])) {
            $minAge = $filters['min_age'] ?? 0;
            $maxAge = $filters['max_age'] ?? 120;
            
            $maxDate = Carbon::now()->subYears($minAge)->format('Y-m-d');
            $minDate = Carbon::now()->subYears($maxAge + 1)->format('Y-m-d');
            
            $query->whereBetween('date_of_birth', [$minDate, $maxDate]);
        }
        
        // Apply provider filter
        if (!empty($filters['provider_id'])) {
            $query->where('created_by_provider_id', $filters['provider_id']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDir = $filters['sort_dir'] ?? 'desc';

        // Handle special sorting cases
        switch ($sortBy) {
            case 'name':
                // Sort by first_name, then last_name
                $query->orderBy('first_name', $sortDir)
                      ->orderBy('last_name', $sortDir);
                break;
            case 'email':
                $query->orderBy('email', $sortDir);
                break;
            case 'clinic':
                // Sort by clinic name through relationship
                $query->leftJoin('clinics', 'patients.clinic_id', '=', 'clinics.id')
                      ->orderBy('clinics.name', $sortDir)
                      ->select('patients.*'); // Ensure we only select patient columns
                break;
            case 'patient_unique_id':
                $query->orderBy('patient_unique_id', $sortDir);
                break;
            case 'created_at':
            default:
                $query->orderBy('created_at', $sortDir);
                break;
        }

        return $query->paginate($perPage);
    }
    
    /**
     * Get patient statistics for a clinic.
     *
     * @param int $clinicId
     * @return array
     */
    public function getStatistics(int $clinicId): array
    {
        $query = $this->model->newQuery();
        $query->where('clinic_id', $clinicId);
        
        $total = $query->count();
        $maleCount = $query->where('gender', 'male')->count();
        $femaleCount = $query->where('gender', 'female')->count();
        
        // Age distribution
        $ageRanges = [
            '0-18' => $this->findByAgeRange(0, 18, $clinicId)->count(),
            '19-35' => $this->findByAgeRange(19, 35, $clinicId)->count(),
            '36-50' => $this->findByAgeRange(36, 50, $clinicId)->count(),
            '51-65' => $this->findByAgeRange(51, 65, $clinicId)->count(),
            '65+' => $this->findByAgeRange(66, 120, $clinicId)->count(),
        ];
        
        return [
            'total' => $total,
            'gender_distribution' => [
                'male' => $maleCount,
                'female' => $femaleCount,
                'other' => $total - $maleCount - $femaleCount
            ],
            'age_distribution' => $ageRanges
        ];
    }
}
