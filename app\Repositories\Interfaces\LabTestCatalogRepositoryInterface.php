<?php

namespace App\Repositories\Interfaces;

use App\Models\LabTestCatalog;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface LabTestCatalogRepositoryInterface
{
    public function getAll(int $perPage = 20): LengthAwarePaginator;
    public function getActive(): Collection;
    public function findById(int $id): ?LabTestCatalog;
    public function findByCode(string $testCode): ?LabTestCatalog;
    public function findByCodes(array $testCodes): Collection;
    public function getByCategory(string $category): Collection;
    public function search(string $query, int $perPage = 20): LengthAwarePaginator;
    public function getCategories(): array;
    public function create(array $data): LabTestCatalog;
    public function update(LabTestCatalog $test, array $data): LabTestCatalog;
    public function delete(LabTestCatalog $test): bool;
}
