<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import Icon from '@/components/Icon.vue';
import Pagination from '@/components/Pagination.vue';

const props = defineProps({
    patientId: [String, Number]
});

const { showSuccess, showError } = useNotifications();

// Get current user for permission checks
const page = usePage();
const currentUser = computed(() => page.props.auth?.user || null);

// Permission checks
const isAdmin = computed(() =>
    currentUser.value?.roles?.some(role => role.name === 'admin') ||
    currentUser.value?.role === 'admin' ||
    false
);

const isClinician = computed(() =>
    currentUser.value?.is_clinician ||
    currentUser.value?.roles?.some(role => role.name === 'provider') ||
    false
);

// Users who can perform clinical actions (edit, view, delete consultations)
const canPerformClinicalActions = computed(() => isAdmin.value || isClinician.value);

// Data
const loading = ref(false);
const patient = ref(null);
const consultations = ref([]);
const searchQuery = ref('');
const selectedConsultations = ref([]);
const filters = ref({
    doctor_name: '',
    clinic_name: '',
    patient_name: '',
    date: ''
});

const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
    from: 0,
    to: 0
});

// Computed
const pageTitle = computed(() => {
    return patient.value
        ? `Consultations - ${patient.value.first_name} ${patient.value.last_name}`
        : `Patient Consultations`;
});

const allSelected = computed({
    get: () => consultations.value.length > 0 && selectedConsultations.value.length === consultations.value.length,
    set: (value) => {
        selectedConsultations.value = value ? consultations.value.map(c => c.id) : [];
    }
});

// Methods
const fetchPatientData = async () => {
    try {
        const response = await window.axios.get(`/patients/${props.patientId}`);
        if (response.data.success) {
            patient.value = response.data.data;
        }
    } catch (error) {
        console.error('Error fetching patient data:', error);
        showError('Failed to load patient data');
    }
};

const fetchConsultations = async (page = 1) => {
    loading.value = true;
    try {
        const params = {
            page,
            per_page: pagination.value.per_page,
            search: searchQuery.value,
            ...filters.value
        };

        const response = await window.axios.get(`/patients/${props.patientId}/consultations-list`, { params });

        if (response.data.success) {
            const paginatedData = response.data.data;
            consultations.value = paginatedData.data || [];
            pagination.value = {
                current_page: paginatedData.current_page || 1,
                last_page: paginatedData.last_page || 1,
                per_page: paginatedData.per_page || 15,
                total: paginatedData.total || 0,
                from: paginatedData.from || 0,
                to: paginatedData.to || 0
            };
        } else {
            consultations.value = [];
            showError('Failed to load consultations');
        }
    } catch (error) {
        console.error('Error fetching consultations:', error);
        consultations.value = [];

        if (error.response?.status === 403) {
            showError('You do not have permission to view consultations');
        } else if (error.response?.status === 401) {
            showError('Please log in to view consultations');
        } else {
            showError('Failed to load consultations. Please try again.');
        }
    } finally {
        loading.value = false;
    }
};

const handleSearch = () => {
    fetchConsultations(1);
};

const clearFilters = () => {
    searchQuery.value = '';
    filters.value = {
        doctor_name: '',
        clinic_name: '',
        patient_name: '',
        date: ''
    };
    fetchConsultations(1);
};

const toggleConsultationSelection = (consultationId) => {
    const index = selectedConsultations.value.indexOf(consultationId);
    if (index > -1) {
        selectedConsultations.value.splice(index, 1);
    } else {
        selectedConsultations.value.push(consultationId);
    }
};

const handlePageChange = (page) => {
    fetchConsultations(page);
};

const goBack = () => {
    router.visit('/patients');
};

const addConsultation = () => {
    router.visit(`/consultations/create?patient_id=${props.patientId}`);
};

const viewConsultation = (consultation) => {
    router.visit(`/consultation-dashboard/${props.patientId}?consultation_id=${consultation.id}`);
};

const editConsultation = (consultation) => {
    router.visit(`/consultations/${consultation.id}/edit`);
};

const deleteConsultation = async (consultation) => {
    if (confirm('Are you sure you want to delete this consultation?')) {
        try {
            await window.axios.delete(`/consultations/${consultation.id}`);
            showSuccess('Consultation deleted successfully');
            fetchConsultations(pagination.value.current_page);
        } catch (error) {
            showError('Failed to delete consultation');
        }
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const formatTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
        case 'completed':
            return 'bg-green-100 text-green-800';
        case 'active':
            return 'bg-green-100 text-green-800';
        case 'in_progress':
            return 'bg-blue-100 text-blue-800';
        case 'scheduled':
            return 'bg-yellow-100 text-yellow-800';
        case 'draft':
            return 'bg-gray-100 text-gray-800';
        case 'cancelled':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getStatusDisplayText = (status) => {
    switch (status?.toLowerCase()) {
        case 'draft':
            return 'Draft';
        case 'active':
            return 'Active';
        case 'completed':
            return 'Completed';
        case 'in_progress':
            return 'In Progress';
        case 'scheduled':
            return 'Scheduled';
        case 'cancelled':
            return 'Cancelled';
        default:
            return status || 'Unknown';
    }
};

// Watchers
watch([searchQuery, filters], () => {
    fetchConsultations(1);
}, { deep: true });

// Lifecycle
onMounted(() => {
    fetchPatientData();
    fetchConsultations();
});
</script>

<template>
    <Head :title="pageTitle" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button @click="goBack" class="flex items-center px-3 py-2 text-sm font-medium text-white bg-gray-800 rounded-md hover:bg-gray-700">
                        <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                        Back
                    </button>
                    <div>
                        <h2 class="text-xl font-semibold leading-tight text-gray-800">
                            Patient Consultations
                        </h2>
                        <p v-if="patient" class="text-sm text-gray-600">
                            {{ patient.first_name }} {{ patient.last_name }} • {{ patient.email }}
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button
                        v-if="canPerformClinicalActions"
                        @click="addConsultation"
                        class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                    >
                        <Icon name="plus" class="w-4 h-4 mr-2" />
                        Add Consultation
                    </button>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <!-- Search and Filters -->
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                            <!-- Search -->
                            <div class="flex-1 max-w-lg">
                                <div class="relative">
                                    <Icon name="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        placeholder="Search consultations by ID, doctor name, patient name, and diagnosis or symptoms..."
                                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        @keyup.enter="handleSearch"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Filters Row -->
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <select v-model="filters.doctor_name" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Filter by Doctor Name</option>
                                </select>
                            </div>
                            <div>
                                <select v-model="filters.clinic_name" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Filter by Clinic Name</option>
                                </select>
                            </div>
                            <div>
                                <input
                                    v-model="filters.patient_name"
                                    type="text"
                                    placeholder="Filter by Patient Name"
                                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            <div>
                                <input
                                    v-model="filters.date"
                                    type="date"
                                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>

                        <!-- Clear Filters -->
                        <div class="mt-3 flex justify-end">
                            <button @click="clearFilters" class="text-sm text-blue-600 hover:text-blue-800">
                                Clear all filters
                            </button>
                        </div>
                    </div>

                    <div v-if="loading" class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-3 text-gray-600">Loading consultations...</span>
                    </div>

                    <div v-else-if="consultations.length === 0" class="text-center py-12">
                        <Icon name="calendar" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No consultations found</h3>
                        <p class="text-gray-500">This patient doesn't have any consultations yet.</p>
                    </div>

                    <div v-else>

                        <!-- Consultations Table -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="w-4 px-6 py-3">
                                            <input
                                                type="checkbox"
                                                v-model="allSelected"
                                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            DOCTOR NAME
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            CLINIC NAME
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            PATIENT NAME
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            DATE
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            STATUS
                                        </th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            ACTIONS
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="consultation in consultations" :key="consultation.id" class="hover:bg-gray-50">
                                        <td class="w-4 px-6 py-4">
                                            <input
                                                type="checkbox"
                                                :value="consultation.id"
                                                v-model="selectedConsultations"
                                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-blue-600">
                                                {{ consultation.provider?.user?.name || consultation.provider?.name || 'Unknown Provider' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                {{ consultation.clinic?.name || 'Unknown Clinic' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                {{ consultation.patient?.first_name }} {{ consultation.patient?.last_name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                {{ formatDate(consultation.consultation_date) }}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ formatTime(consultation.consultation_date) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(consultation.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ consultation.status || 'Active' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="flex items-center justify-center space-x-1">
                                                <button
                                                    v-if="canPerformClinicalActions"
                                                    @click="editConsultation(consultation)"
                                                    class="p-1 text-gray-400 hover:text-blue-600"
                                                    title="Edit"
                                                >
                                                    <Icon name="edit" class="w-4 h-4" />
                                                </button>
                                                <button
                                                    v-if="canPerformClinicalActions"
                                                    @click="viewConsultation(consultation)"
                                                    class="p-1 text-gray-400 hover:text-green-600"
                                                    title="View"
                                                >
                                                    <Icon name="eye" class="w-4 h-4" />
                                                </button>
                                                <button
                                                    v-if="canPerformClinicalActions"
                                                    @click="deleteConsultation(consultation)"
                                                    class="p-1 text-gray-400 hover:text-red-600"
                                                    title="Delete"
                                                >
                                                    <Icon name="trash" class="w-4 h-4" />
                                                </button>
                                                <div v-if="!canPerformClinicalActions" class="text-xs text-gray-500">
                                                    View Only
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <Pagination
                            v-if="pagination.total > pagination.per_page"
                            :currentPage="pagination.current_page"
                            :lastPage="pagination.last_page"
                            :total="pagination.total"
                            :perPage="pagination.per_page"
                            :from="pagination.from"
                            :to="pagination.to"
                            @page-changed="handlePageChange"
                        />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
