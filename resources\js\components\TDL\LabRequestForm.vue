<template>
  <div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Create Lab Request</h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <i class="fas fa-times text-xl"></i>
      </button>
    </div>

    <form @submit.prevent="submitRequest" class="space-y-6">
      <!-- Patient Information -->
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Patient Information</h3>

        <!-- Patient Selection (only show when no patientId is provided) -->
        <div v-if="!patientId" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Patient *</label>
            <select
              v-model="form.patient_id"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Patient</option>
              <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                {{ patient.first_name }} {{ patient.last_name }} - {{ patient.email }}
              </option>
            </select>
          </div>

          <div v-if="selectedPatient">
            <label class="block text-sm font-medium text-gray-700 mb-2">Patient Details</label>
            <div class="bg-gray-50 p-3 rounded-lg text-sm">
              <p><strong>DOB:</strong> {{ selectedPatient.date_of_birth || 'Not provided' }}</p>
              <p><strong>Gender:</strong> {{ selectedPatient.gender || 'Not provided' }}</p>
              <p><strong>Phone:</strong> {{ selectedPatient.phone || 'Not provided' }}</p>
            </div>
          </div>
        </div>

        <!-- Patient Info Display (when patientId is provided) -->
        <div v-else class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </div>
            <div>
              <p class="font-medium text-blue-900">Lab request for current consultation patient</p>
              <p class="text-sm text-blue-700">Patient ID: {{ patientId }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Test Selection -->
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Selected Tests</h3>
          <button
            type="button"
            @click="showTestCatalog = true"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>
            Add Tests
          </button>
        </div>

        <div v-if="form.selected_tests.length === 0" class="text-center py-8 text-gray-500">
          <i class="fas fa-flask text-4xl mb-4"></i>
          <p>No tests selected. Click "Add Tests" to browse the catalog.</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="test in form.selected_tests"
            :key="test.test_code"
            class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <h4 class="font-medium text-gray-900">{{ test.test_name }}</h4>
              <p class="text-sm text-gray-500">{{ test.test_code }} - {{ test.category }}</p>
              <div v-if="test.requires_fasting" class="text-xs text-orange-600 mt-1">
                <i class="fas fa-clock mr-1"></i>
                Fasting required ({{ test.fasting_hours }}h)
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span class="font-semibold text-green-600">£{{ test.price.toFixed(2) }}</span>
              <button
                type="button"
                @click="removeTest(test.test_code)"
                class="text-red-500 hover:text-red-700 transition-colors"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Total Cost -->
          <div class="border-t pt-3 flex justify-between items-center">
            <span class="text-lg font-semibold text-gray-900">Total Cost:</span>
            <span class="text-xl font-bold text-green-600">£{{ totalCost.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- Clinical Information -->
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Clinical Information</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Clinical Notes</label>
            <textarea
              v-model="form.clinical_notes"
              rows="3"
              placeholder="Enter clinical indication, symptoms, or relevant notes..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Collection Date</label>
              <input
                v-model="form.collection_date"
                type="date"
                :min="today"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div class="flex items-center space-x-6 pt-6">
              <label class="flex items-center">
                <input
                  v-model="form.urgent"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Urgent</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="form.fasting_status"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Patient Fasting</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Test Requirements Summary -->
      <div v-if="testRequirements" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="font-semibold text-yellow-800 mb-2">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          Test Requirements
        </h4>
        <div class="space-y-2 text-sm text-yellow-700">
          <div v-if="testRequirements.fasting_required">
            <strong>Fasting Required:</strong> {{ testRequirements.max_fasting_hours }} hours
          </div>
          <div v-if="testRequirements.sample_types.length > 0">
            <strong>Sample Types:</strong> {{ testRequirements.sample_types.join(', ') }}
          </div>
          <div v-if="testRequirements.special_instructions.length > 0">
            <strong>Special Instructions:</strong>
            <ul class="list-disc list-inside mt-1">
              <li v-for="instruction in testRequirements.special_instructions" :key="instruction">
                {{ instruction }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          @click="$emit('close')"
          class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="!canSubmit || submitting"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <i v-if="submitting" class="fas fa-spinner fa-spin mr-2"></i>
          {{ submitting ? 'Creating...' : 'Create Lab Request' }}
        </button>
      </div>
    </form>

    <!-- Test Catalog Modal -->
    <div v-if="showTestCatalog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="p-4 border-b flex justify-between items-center">
          <h3 class="text-lg font-semibold">Select Lab Tests</h3>
          <button
            @click="showTestCatalog = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
          <LabTestCatalog
            :preselected-tests="form.selected_tests"
            @tests-selected="onTestsSelected"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useNotifications } from '@/composables/useNotifications'
import LabTestCatalog from './LabTestCatalog.vue'
import tdlLabService from '@/services/tdlLabService'
import type { LabTest, LabTestRequest, TestSelectionRequirements, User } from '@/types/tdl'

const { showSuccess, showError } = useNotifications()

// Props
interface Props {
    consultationId?: string | number
    patientId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
    consultationId: undefined,
    patientId: undefined
})

// Emits
interface Emits {
    (e: 'close'): void
    (e: 'request-created', request: LabTestRequest): void
}

const emit = defineEmits<Emits>()

// Reactive data
interface FormData {
    patient_id: number | string
    consultation_id?: number
    selected_tests: LabTest[]
    clinical_notes: string
    collection_date: string
    urgent: boolean
    fasting_status: boolean
}

const form = ref<FormData>({
    patient_id: props.patientId || '',
    consultation_id: props.consultationId,
    selected_tests: [],
    clinical_notes: '',
    collection_date: new Date().toISOString().split('T')[0],
    urgent: false,
    fasting_status: false
})

const patients = ref<User[]>([])
const testRequirements = ref<TestSelectionRequirements | null>(null)
const showTestCatalog = ref<boolean>(false)
const submitting = ref<boolean>(false)

// Computed
const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const patientId = computed(() => {
  return props.patientId
})

const selectedPatient = computed(() => {
    return patients.value.find(p => p.id === form.value.patient_id)
})

const totalCost = computed(() => {
    return form.value.selected_tests.reduce((sum, test) => sum + test.price, 0)
})

const canSubmit = computed(() => {
    return form.value.patient_id &&
           form.value.selected_tests.length > 0 &&
           !submitting.value
})

// Methods
const fetchPatients = async (): Promise<void> => {
    try {
        const data = await tdlLabService.getPatients()

        if (data.success) {
            patients.value = data.data.patients
        }
    } catch (error) {
        console.error('Error fetching patients:', error)
        showError('Failed to load patients')
    }
}

const validateTestSelection = async (): Promise<void> => {
    if (form.value.selected_tests.length === 0) {
        testRequirements.value = null
        return
    }

    try {
        const testCodes = form.value.selected_tests.map(test => test.test_code)
        const data = await tdlLabService.validateTestSelection(testCodes)

        if (data.success) {
            testRequirements.value = data.data.requirements
        }
    } catch (error) {
        console.error('Error validating tests:', error)
    }
}

const onTestsSelected = (tests: LabTest[]): void => {
    form.value.selected_tests = tests
    showTestCatalog.value = false
    validateTestSelection()
}

const removeTest = (testCode: string): void => {
    form.value.selected_tests = form.value.selected_tests.filter(
        test => test.test_code !== testCode
    )
    validateTestSelection()
}

const submitRequest = async (): Promise<void> => {
    if (!canSubmit.value) return

    submitting.value = true

    try {
        const requestData = {
            patient_id: Number(form.value.patient_id),
            consultation_id: form.value.consultation_id,
            test_codes: form.value.selected_tests.map(test => test.test_code),
            clinical_notes: form.value.clinical_notes,
            collection_date: form.value.collection_date,
            urgent: form.value.urgent,
            fasting_status: form.value.fasting_status
        }

        const data = await tdlLabService.createLabRequest(requestData)

        if (data.success) {
            showSuccess('Lab request created successfully')
            emit('request-created', data.data.request)
            emit('close')
        } else {
            showError(data.message || 'Failed to create lab request')
        }
    } catch (error) {
        console.error('Error creating lab request:', error)
        showError('Failed to create lab request')
    } finally {
        submitting.value = false
    }
}

// Watchers
watch(() => form.value.selected_tests, () => {
  validateTestSelection()
}, { deep: true })

// Lifecycle
onMounted(() => {
  // Only fetch patients if no patientId is provided (i.e., not from consultation)
  if (!props.patientId) {
    fetchPatients()
  }
})
</script>


