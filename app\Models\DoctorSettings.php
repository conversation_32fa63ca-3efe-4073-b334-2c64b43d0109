<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'professional_prefix',
        'registration_number',
        'registration_body',
        'digital_signature',
        'signature_format',
        'letter_templates',
        'consultation_templates',
        'practice_name',
        'practice_address',
        'practice_phone',
        'practice_email',
        'include_signature_in_letters',
        'include_registration_in_letters',
        'default_letter_settings',
    ];

    protected $casts = [
        'letter_templates' => 'array',
        'consultation_templates' => 'array',
        'default_letter_settings' => 'array',
        'include_signature_in_letters' => 'boolean',
        'include_registration_in_letters' => 'boolean',
    ];

    /**
     * Get the user that owns the doctor settings.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full professional name with prefix.
     */
    public function getFullProfessionalNameAttribute()
    {
        $prefix = $this->professional_prefix ? $this->professional_prefix . ' ' : '';
        return $prefix . $this->user->name;
    }

    /**
     * Get the registration display text.
     */
    public function getRegistrationDisplayAttribute()
    {
        if (!$this->registration_number || !$this->registration_body) {
            return null;
        }
        return $this->registration_body . ': ' . $this->registration_number;
    }

    /**
     * Get default letter template for a specific type.
     */
    public function getLetterTemplate($type = 'default')
    {
        $templates = $this->letter_templates ?? [];
        return $templates[$type] ?? $templates['default'] ?? null;
    }

    /**
     * Get default consultation template for a specific type.
     */
    public function getConsultationTemplate($type = 'default')
    {
        $templates = $this->consultation_templates ?? [];
        return $templates[$type] ?? $templates['default'] ?? null;
    }

    /**
     * Check if the doctor has a digital signature.
     */
    public function hasDigitalSignature()
    {
        return !empty($this->digital_signature);
    }

    /**
     * Get the signature image data URL.
     */
    public function getSignatureDataUrl()
    {
        if (!$this->hasDigitalSignature()) {
            return null;
        }

        $mimeType = 'image/' . $this->signature_format;
        return 'data:' . $mimeType . ';base64,' . $this->digital_signature;
    }
}
