<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import axios from 'axios';

const breadcrumbs = [
    {
        title: 'SMTP Settings',
        href: '/settings/smtp-settings',
    },
];

const page = usePage();
const user = page.props.auth.user;
const { showSuccess, showError } = useNotifications();

// State
const loading = ref(false);
const saving = ref(false);
const testing = ref(false);

// Data
const emailSettings = ref([]);
const systemConfig = ref({});
const clinicInfo = ref({});

// Clinic Email Settings
const clinicEmailSettings = ref({
    enabled: false,
    sender_name: '',
    sender_email: ''
});

// Domain Verification
const domainSettings = ref({
    domain: '',
    setup_complete: false,
    dns_records: [],
    instructions: [],
    verification_status: 'pending',
    progress: 0,
    status_text: 'Pending Verification',
    is_fully_authenticated: false,
    last_check: null,
    error_message: null,
    domain_verified: false,
    domain_authenticated: false,
    dkim_verified: false,
    spf_verified: false,
    dmarc_verified: false
});

// UI State
const domainSetupMode = ref(false);
const verifying = ref(false);
const showDomainSection = ref(false);

// Test email form
const testForm = ref({
    test_email: '',
    use_clinic_settings: true
});

// Computed properties
const isClinicEmailConfigured = computed(() => {
    return clinicEmailSettings.value.sender_name &&
           clinicEmailSettings.value.sender_email;
});

const configurationSource = computed(() => {
    if (clinicEmailSettings.value.enabled && isClinicEmailConfigured.value) {
        return 'Clinic-specific email configuration';
    }
    return 'System default email configuration';
});

const suggestedDomain = computed(() => {
    if (clinicInfo.value.suggested_domain) {
        return `mail.${clinicInfo.value.suggested_domain}`;
    }
    return '';
});

const domainPlaceholder = computed(() => {
    if (suggestedDomain.value) {
        return suggestedDomain.value;
    }
    return 'mail.yourclinic.com';
});

// Methods
const fetchEmailSettings = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/settings/smtp-settings/data');

        emailSettings.value = response.data.email_settings || [];
        systemConfig.value = response.data.system_config || {};
        clinicInfo.value = response.data.clinic_info || {};

        // Check if clinic has SMTP settings configured
        const clinicSettings = emailSettings.value.find(setting => 
            setting.email_provider === 'smtp' && setting.is_active
        );

        if (clinicSettings && clinicSettings.details) {
            clinicEmailSettings.value = {
                enabled: true,
                sender_name: clinicSettings.details.sender_name || '',
                sender_email: clinicSettings.details.sender_email || ''
            };

            // Check if domain is configured
            if (clinicSettings.additional_details && clinicSettings.additional_details.domain) {
                domainSettings.value = {
                    domain: clinicSettings.additional_details.domain,
                    setup_complete: true,
                    dns_records: clinicSettings.additional_details.dns_records || [],
                    verification_status: clinicSettings.additional_details.verification_status || 'pending',
                    progress: 0,
                    status_text: 'Pending Verification',
                    is_fully_authenticated: false,
                    last_check: clinicSettings.additional_details.last_verification_check || null,
                    error_message: clinicSettings.additional_details.verification_error_message || null,
                    domain_verified: clinicSettings.additional_details.domain_verified || false,
                    domain_authenticated: clinicSettings.additional_details.domain_authenticated || false,
                    dkim_verified: clinicSettings.additional_details.dkim_verified || false,
                    spf_verified: clinicSettings.additional_details.spf_verified || false,
                    dmarc_verified: clinicSettings.additional_details.dmarc_verified || false
                };
                showDomainSection.value = true;

                // Get current domain status
                await getDomainStatus();
            }
        }
    } catch (error) {
        console.error('Error fetching SMTP settings:', error);
        showError('Failed to fetch SMTP settings');
    } finally {
        loading.value = false;
    }
};

const saveEmailSettings = async () => {
    if (!clinicEmailSettings.value.sender_name || !clinicEmailSettings.value.sender_email) {
        showError('Please fill in all required fields');
        return;
    }

    try {
        const data = {
            email_provider: 'smtp',
            is_active: clinicEmailSettings.value.enabled,
            details: {
                sender_email: clinicEmailSettings.value.sender_email,
                sender_name: clinicEmailSettings.value.sender_name
            }
        };

        await axios.post('/settings/smtp-settings', data);
        showSuccess('SMTP settings saved successfully');
        await fetchEmailSettings();
    } catch (error) {
        console.error('Error saving SMTP settings:', error);
        if (error.response?.data?.errors) {
            const errors = Object.values(error.response.data.errors).flat();
            showError(errors.join(', '));
        } else {
            showError(error.response?.data?.message || 'Failed to save SMTP settings');
        }
    }
};

const setupDomain = async () => {
    if (!domainSettings.value.domain) {
        showError('Please enter a domain name');
        return;
    }

    saving.value = true;
    try {
        const response = await axios.post('/settings/smtp-settings/domain/setup', {
            domain: domainSettings.value.domain,
            sender_name: clinicEmailSettings.value.sender_name,
            sender_email: clinicEmailSettings.value.sender_email
        });

        if (response.data.success) {
            domainSettings.value.setup_complete = true;
            domainSettings.value.dns_records = response.data.dns_records;
            domainSettings.value.instructions = response.data.instructions;
            domainSetupMode.value = false;

            showSuccess('Domain setup successfully! Please add the DNS records to your domain provider.');
        }
    } catch (error) {
        console.error('Error setting up domain:', error);
        showError(error.response?.data?.message || 'Failed to setup domain');
    } finally {
        saving.value = false;
    }
};

const verifyDomain = async () => {
    verifying.value = true;
    try {
        const response = await axios.post('/settings/smtp-settings/domain/verify');

        if (response.data.success) {
            updateDomainStatus(response.data);

            if (response.data.is_fully_authenticated) {
                showSuccess('Domain fully authenticated! Your SMTP settings are ready to use.');
            } else {
                showSuccess('Verification check completed. Some DNS records may still be propagating.');
            }
        }
    } catch (error) {
        console.error('Error verifying domain:', error);
        showError(error.response?.data?.message || 'Failed to verify domain');
    } finally {
        verifying.value = false;
    }
};

const getDomainStatus = async () => {
    try {
        const response = await axios.get('/settings/smtp-settings/domain/status');

        if (response.data.success) {
            updateDomainStatus(response.data);
        }
    } catch (error) {
        console.error('Error getting domain status:', error);
        // Don't show error for status checks as they run automatically
    }
};

const updateDomainStatus = (data) => {
    domainSettings.value.progress = data.progress || 0;
    domainSettings.value.status_text = data.status_text || 'Pending Verification';
    domainSettings.value.is_fully_authenticated = data.is_fully_authenticated || false;
    domainSettings.value.last_check = data.last_check;
    domainSettings.value.error_message = data.error_message;

    // Update verification details if available
    if (data.verification_details) {
        domainSettings.value.domain_verified = data.verification_details.domain_verified || false;
        domainSettings.value.domain_authenticated = data.verification_details.domain_authenticated || false;
        domainSettings.value.dkim_verified = data.verification_details.dkim_verified || false;
        domainSettings.value.spf_verified = data.verification_details.spf_verified || false;
        domainSettings.value.dmarc_verified = data.verification_details.dmarc_verified || false;
    }

    if (data.formatted_records) {
        domainSettings.value.dns_records = data.formatted_records;
    }
};



const useSuggestedDomain = () => {
    if (suggestedDomain.value) {
        domainSettings.value.domain = suggestedDomain.value;
    }
};

const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('Copied to clipboard');
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        showError('Failed to copy to clipboard');
    }
};

const getStatusColor = (status) => {
    if (status === true || status === 'verified') return 'text-green-600';
    if (status === false || status === 'pending') return 'text-yellow-600';
    return 'text-red-600';
};

const getStatusIcon = (status) => {
    if (status === true || status === 'verified') return '✓';
    if (status === false || status === 'pending') return '⏳';
    return '✗';
};

const removeDomain = async () => {
    if (!confirm('Are you sure you want to remove the domain configuration? This will disable domain verification.')) {
        return;
    }

    saving.value = true;
    try {
        const response = await axios.delete('/settings/smtp-settings/domain/remove');

        if (response.data.success) {
            // Reset domain settings
            domainSettings.value = {
                domain: '',
                setup_complete: false,
                dns_records: [],
                instructions: [],
                verification_status: 'pending',
                progress: 0,
                status_text: 'Pending Verification',
                is_fully_authenticated: false,
                last_check: null,
                error_message: null,
                domain_verified: false,
                domain_authenticated: false,
                dkim_verified: false,
                spf_verified: false,
                dmarc_verified: false
            };
            showDomainSection.value = false;
            domainSetupMode.value = false;

            showSuccess('Domain configuration removed successfully');
        }
    } catch (error) {
        console.error('Error removing domain:', error);
        showError(error.response?.data?.message || 'Failed to remove domain configuration');
    } finally {
        saving.value = false;
    }
};

const testEmailConfiguration = async () => {
    if (!testForm.value.test_email) {
        showError('Please enter a test email address');
        return;
    }

    testing.value = true;
    try {
        const response = await axios.post('/settings/smtp-settings/test', {
            test_email: testForm.value.test_email,
            use_clinic_settings: testForm.value.use_clinic_settings
        });

        showSuccess(response.data.message);
    } catch (error) {
        console.error('Error testing email configuration:', error);
        showError(error.response?.data?.message || 'Failed to send test email');
    } finally {
        testing.value = false;
    }
};

onMounted(() => {
    fetchEmailSettings();
});


</script>

<template>
    <Head title="SMTP Settings" />

    <AppLayout>
        <SettingsLayout :breadcrumbs="breadcrumbs">
            <div class="space-y-6">
                <!-- Header -->
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">SMTP Settings</h1>
                    <p class="text-sm text-gray-600 mt-1">Configure SMTP settings for your clinic</p>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>

                <!-- SMTP Settings Form -->
                <div v-else class="space-y-6">
                    <!-- Configuration Status -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-800">{{ configurationSource }}</span>
                        </div>
                    </div>

                    <!-- SMTP Settings -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">SMTP Configuration</h3>
                                <p class="text-sm text-gray-600">Configure sender name and email for your clinic</p>
                            </div>
                            <label class="flex items-center">
                                <input
                                    v-model="clinicEmailSettings.enabled"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                >
                                <span class="ml-2 text-sm text-gray-700">Enable clinic-specific settings</span>
                            </label>
                        </div>

                        <form @submit.prevent="saveEmailSettings" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Sender Name</label>
                                    <input
                                        v-model="clinicEmailSettings.sender_name"
                                        type="text"
                                        :disabled="!clinicEmailSettings.enabled"
                                        placeholder="Your Clinic Name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                                        required
                                    >
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Sender Email</label>
                                    <input
                                        v-model="clinicEmailSettings.sender_email"
                                        type="email"
                                        :disabled="!clinicEmailSettings.enabled"
                                        placeholder="<EMAIL>"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                                        required
                                    >
                                </div>
                            </div>

                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    :disabled="saving || !clinicEmailSettings.enabled || !isClinicEmailConfigured"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="saving">Saving...</span>
                                    <span v-else>Save Settings</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Test Email Configuration -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Test SMTP Configuration</h3>
                        <p class="text-sm text-gray-600 mb-4">Send a test email to verify your SMTP settings are working correctly.</p>

                        <form @submit.prevent="testEmailConfiguration" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Test Email Address</label>
                                <input
                                    v-model="testForm.test_email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    required
                                >
                            </div>

                            <div class="flex items-center">
                                <input
                                    v-model="testForm.use_clinic_settings"
                                    type="checkbox"
                                    id="use_clinic_settings"
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                >
                                <label for="use_clinic_settings" class="ml-2 text-sm text-gray-700">
                                    Use clinic-specific SMTP settings (if available)
                                </label>
                            </div>

                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    :disabled="testing"
                                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="testing">Sending Test...</span>
                                    <span v-else>Send Test Email</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Domain Verification Section -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">Domain Verification</h3>
                                <p class="text-sm text-gray-600">Verify your domain to improve email deliverability and authentication</p>
                            </div>
                            <button
                                v-if="!showDomainSection && clinicEmailSettings.enabled"
                                @click="showDomainSection = true; domainSetupMode = true"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                Setup Domain
                            </button>
                        </div>

                        <!-- Domain Setup Form -->
                        <div v-if="domainSetupMode && !domainSettings.setup_complete" class="space-y-4">
                            <!-- Suggested Domain -->
                            <div v-if="suggestedDomain" class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-blue-900">Suggested Domain for Email</h4>
                                        <p class="text-sm text-blue-700">Based on your clinic website: {{ clinicInfo.website }}</p>
                                        <p class="text-sm text-blue-600 mt-1">We'll verify <strong>{{ clinicInfo.suggested_domain }}</strong> and you can send emails from:</p>
                                        <p class="text-sm font-mono text-blue-800 mt-1">{{ suggestedDomain }}</p>
                                    </div>
                                    <button
                                        @click="useSuggestedDomain"
                                        class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        Use This
                                    </button>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Domain Name</label>
                                <input
                                    v-model="domainSettings.domain"
                                    type="text"
                                    :placeholder="domainPlaceholder"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    required
                                >
                                <p class="text-xs text-gray-500 mt-1">Enter the domain you want to use for sending emails (e.g., mail.yourclinic.com)</p>
                            </div>

                            <div class="flex justify-end space-x-3">
                                <button
                                    @click="domainSetupMode = false; showDomainSection = false"
                                    type="button"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                >
                                    Cancel
                                </button>
                                <button
                                    @click="setupDomain"
                                    :disabled="saving || !domainSettings.domain"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="saving">Setting up...</span>
                                    <span v-else>Setup Domain</span>
                                </button>
                            </div>
                        </div>

                        <!-- Domain Status Display -->
                        <div v-if="showDomainSection && domainSettings.setup_complete" class="space-y-6">
                            <!-- Current Domain Info -->
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Current Domain</h4>
                                    <p class="text-lg font-mono text-blue-600">{{ domainSettings.domain }}</p>
                                </div>
                                <button
                                    @click="removeDomain"
                                    :disabled="saving"
                                    class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
                                >
                                    Remove Domain
                                </button>
                            </div>

                            <!-- Verification Progress -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-lg font-medium text-gray-900">Verification Progress</h4>
                                    <div class="flex items-center space-x-2">
                                        <button
                                            @click="verifyDomain"
                                            :disabled="verifying"
                                            class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                                        >
                                            <span v-if="verifying">Checking...</span>
                                            <span v-else>Check Now</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        class="h-2 rounded-full transition-all duration-300"
                                        :class="domainSettings.is_fully_authenticated ? 'bg-green-600' : 'bg-blue-600'"
                                        :style="{ width: domainSettings.progress + '%' }"
                                    ></div>
                                </div>

                                <!-- Status Text -->
                                <div class="flex items-center space-x-2">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="domainSettings.is_fully_authenticated ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                                    >
                                        {{ domainSettings.status_text }}
                                    </span>
                                    <span v-if="domainSettings.last_check" class="text-xs text-gray-500">
                                        Last checked: {{ new Date(domainSettings.last_check).toLocaleString() }}
                                    </span>
                                </div>

                                <!-- Error Message -->
                                <div v-if="domainSettings.error_message" class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p class="text-sm text-red-700">{{ domainSettings.error_message }}</p>
                                </div>
                            </div>

                            <!-- DNS Records Section -->
                            <div v-if="domainSettings.dns_records && domainSettings.dns_records.length > 0" class="space-y-4">
                                <h4 class="text-lg font-medium text-gray-900">DNS Records to Add</h4>
                                <p class="text-sm text-gray-600">Add these DNS records to your domain provider to complete verification:</p>

                                <div class="space-y-3">
                                    <div
                                        v-for="(record, index) in domainSettings.dns_records"
                                        :key="index"
                                        class="border border-gray-200 rounded-lg p-4 bg-gray-50"
                                    >
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-sm font-medium text-gray-900">{{ record.type }} Record</span>
                                                <span
                                                    class="inline-flex items-center text-xs"
                                                    :class="getStatusColor(record.status)"
                                                >
                                                    {{ getStatusIcon(record.status) }} {{ record.status_text || 'Pending' }}
                                                </span>
                                            </div>
                                            <button
                                                @click="copyToClipboard(record.value)"
                                                class="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                            >
                                                Copy Value
                                            </button>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Name/Host</label>
                                                <p class="mt-1 font-mono text-gray-900 break-all">{{ record.name || record.host || '@' }}</p>
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Type</label>
                                                <p class="mt-1 font-mono text-gray-900">{{ record.type }}</p>
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Value</label>
                                                <p class="mt-1 font-mono text-gray-900 break-all">{{ record.value }}</p>
                                            </div>
                                        </div>

                                        <div v-if="record.priority" class="mt-2">
                                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Priority</label>
                                            <p class="mt-1 font-mono text-gray-900">{{ record.priority }}</p>
                                        </div>

                                        <div v-if="record.description" class="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                                            {{ record.description }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Instructions -->
                                <div v-if="domainSettings.instructions && domainSettings.instructions.length > 0" class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <h5 class="text-sm font-medium text-yellow-800 mb-2">Setup Instructions</h5>
                                    <ul class="text-sm text-yellow-700 space-y-1">
                                        <li v-for="(instruction, index) in domainSettings.instructions" :key="index" class="flex items-start">
                                            <span class="mr-2">{{ index + 1 }}.</span>
                                            <span>{{ instruction }}</span>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Verification Details -->
                                <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                                    <h5 class="text-sm font-medium text-gray-900 mb-3">Verification Status Details</h5>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div class="flex items-center space-x-2">
                                            <span :class="getStatusColor(domainSettings.domain_verified)">
                                                {{ getStatusIcon(domainSettings.domain_verified) }}
                                            </span>
                                            <span class="text-gray-700">Domain</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span :class="getStatusColor(domainSettings.dkim_verified)">
                                                {{ getStatusIcon(domainSettings.dkim_verified) }}
                                            </span>
                                            <span class="text-gray-700">DKIM</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span :class="getStatusColor(domainSettings.spf_verified)">
                                                {{ getStatusIcon(domainSettings.spf_verified) }}
                                            </span>
                                            <span class="text-gray-700">SPF</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span :class="getStatusColor(domainSettings.dmarc_verified)">
                                                {{ getStatusIcon(domainSettings.dmarc_verified) }}
                                            </span>
                                            <span class="text-gray-700">DMARC</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
