<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_number',
        'consultation_id',
        'patient_id',
        'prescriber_id',
        'clinic_id',
        'status',
        'type',
        'prescribed_date',
        'valid_until',
        'clinical_indication',
        'additional_instructions',
        'warnings',
        'is_private',
        'is_electronic',
        'pharmacy_name',
        'pharmacy_address',
        'total_items',
        'total_cost',
        'dispensed_at',
        'dispensed_by',
        'dispensing_notes',
        'attachments',
        'wp_prescription_id',
    ];

    protected $casts = [
        'prescribed_date' => 'date',
        'valid_until' => 'date',
        'is_private' => 'boolean',
        'is_electronic' => 'boolean',
        'total_cost' => 'decimal:2',
        'dispensed_at' => 'datetime',
        'attachments' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            if (empty($prescription->prescription_number)) {
                $prescription->prescription_number = static::generatePrescriptionNumber();
            }
        });
    }

    /**
     * Generate a unique prescription number.
     */
    public static function generatePrescriptionNumber()
    {
        $prefix = 'RX';
        $year = date('Y');
        $month = date('m');
        
        // Get the next sequential number for this month
        $lastPrescription = static::where('prescription_number', 'like', "{$prefix}{$year}{$month}%")
                                 ->orderBy('prescription_number', 'desc')
                                 ->first();
        
        if ($lastPrescription) {
            $lastNumber = (int) substr($lastPrescription->prescription_number, -6);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . $year . $month . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the consultation associated with the prescription.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the patient associated with the prescription.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the prescriber (user) associated with the prescription.
     */
    public function prescriber()
    {
        return $this->belongsTo(User::class, 'prescriber_id');
    }

    /**
     * Get the clinic associated with the prescription.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the prescription items.
     */
    public function items()
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    /**
     * Get the refill requests for this prescription.
     */
    public function refillRequests()
    {
        return $this->hasMany(PrescriptionRefillRequest::class, 'original_prescription_id');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by prescriber.
     */
    public function scopeByPrescriber($query, $prescriberId)
    {
        return $query->where('prescriber_id', $prescriberId);
    }

    /**
     * Scope to filter by patient.
     */
    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    /**
     * Scope to get active prescriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get expired prescriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('valid_until', '<', now()->toDateString())
                    ->where('status', '!=', 'expired');
    }

    /**
     * Scope to get prescriptions expiring soon.
     */
    public function scopeExpiringSoon($query, $days = 7)
    {
        $expiryDate = now()->addDays($days)->toDateString();
        return $query->where('valid_until', '<=', $expiryDate)
                    ->where('valid_until', '>=', now()->toDateString())
                    ->whereIn('status', ['active', 'dispensed']);
    }

    /**
     * Check if prescription is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if prescription is expired.
     */
    public function isExpired()
    {
        return $this->valid_until && $this->valid_until->isPast();
    }

    /**
     * Check if prescription is dispensed.
     */
    public function isDispensed()
    {
        return $this->status === 'dispensed';
    }

    /**
     * Check if prescription is completed.
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if prescription is expiring soon.
     */
    public function isExpiringSoon($days = 7)
    {
        if (!$this->valid_until) {
            return false;
        }

        return $this->valid_until->diffInDays(now()) <= $days && !$this->isExpired();
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'draft' => 'Draft',
            'active' => 'Active',
            'dispensed' => 'Dispensed',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'expired' => 'Expired',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'new' => 'New Prescription',
            'repeat' => 'Repeat Prescription',
            'acute' => 'Acute Prescription',
            'chronic' => 'Chronic Prescription',
        ];

        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Update the total items count.
     */
    public function updateTotalItems()
    {
        $this->total_items = $this->items()->count();
        $this->save();
    }

    /**
     * Update the total cost.
     */
    public function updateTotalCost()
    {
        $this->total_cost = $this->items()->sum('total_cost');
        $this->save();
    }

    /**
     * Check if all items are dispensed.
     */
    public function allItemsDispensed()
    {
        return $this->items()->where('status', '!=', 'dispensed')->count() === 0;
    }

    /**
     * Get pending refill requests.
     */
    public function getPendingRefillRequests()
    {
        return $this->refillRequests()->where('status', 'pending')->get();
    }
}
