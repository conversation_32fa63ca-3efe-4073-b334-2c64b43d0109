<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;

class PrescriptionEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $emailData;

    /**
     * Create a new message instance.
     */
    public function __construct(array $emailData)
    {
        $this->emailData = $emailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your Prescription from ' . $this->emailData['clinic_name'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.prescription',
            with: [
                'patientName' => $this->emailData['patient_name'],
                'clinicName' => $this->emailData['clinic_name'],
                'message' => $this->emailData['message'],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        
        if (isset($this->emailData['pdf_path']) && file_exists($this->emailData['pdf_path'])) {
            $attachments[] = Attachment::fromPath($this->emailData['pdf_path'])
                ->as('prescription.pdf')
                ->withMime('application/pdf');
        }
        
        return $attachments;
    }
}
