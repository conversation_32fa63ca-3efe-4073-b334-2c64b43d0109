<template>
  <!-- Clean Vital Signs Card -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-teal-50 to-teal-100 px-4 sm:px-6 py-3 sm:py-4 border-b border-teal-200">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-teal-500 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
            </svg>
          </div>
          <div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
            <h2 class="text-lg font-semibold text-teal-900">Vital Signs</h2>

            <!-- AI populated badge -->
            <div
              v-if="isAIPopulated"
              class="px-2 py-1 rounded-full bg-blue-100 text-blue-700 text-xs font-medium flex items-center gap-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                <path d="M5 3v4"/>
                <path d="M19 17v4"/>
                <path d="M3 5h4"/>
                <path d="M17 19h4"/>
              </svg>
              AI populated
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <!-- Approve AI button -->
          <button
            v-if="isAIPopulated"
            @click="handleApproveAI"
            class="text-green-600 hover:text-green-700 flex items-center gap-1 text-xs px-2 py-1 hover:bg-green-50 rounded transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22 4 12 14.01 9 11.01"/>
            </svg>
            <span class="hidden sm:inline">Approve</span>
          </button>
          <button
            @click="cloneVitals"
            class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Clone Vitals"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"/>
              <path d="M12 5v14"/>
            </svg>
          </button>
          <button
            v-if="!isFirstInstance"
            @click="removeVitals"
            class="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
            title="Remove Vitals"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18"/>
              <path d="m6 6 12 12"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Vitals Grid -->
    <div class="p-3 sm:p-4">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 sm:gap-4">
        <div
          v-for="(field, index) in vitalFields"
          :key="index"
          class="relative group"
        >
          <div class="p-4 bg-white border border-gray-200 rounded-lg group-hover:border-teal-200 transition-colors">
            <div class="flex items-center justify-between mb-2">
              <label class="text-sm font-medium text-gray-600">{{ field.label }}</label>
              <div :class="getIconBackgroundClass(field.key)" class="h-6 w-6 rounded-full flex items-center justify-center">
                <!-- Temperature Icon -->
                <svg v-if="field.key === 'temperature'" xmlns="http://www.w3.org/2000/svg" :class="getIconColorClass(field.key)" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"/>
                </svg>
                <!-- Pulse Icon -->
                <svg v-else-if="field.key === 'pulse'" xmlns="http://www.w3.org/2000/svg" :class="getIconColorClass(field.key)" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                </svg>
                <!-- Blood Pressure Icon -->
                <svg v-else-if="field.key === 'blood_pressure'" xmlns="http://www.w3.org/2000/svg" :class="getIconColorClass(field.key)" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M12 1v6m0 6v6"/>
                </svg>
                <!-- Respiratory Rate Icon -->
                <svg v-else-if="field.key === 'respiratory_rate'" xmlns="http://www.w3.org/2000/svg" :class="getIconColorClass(field.key)" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4"/>
                  <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
                  <path d="M21 9c0 1.66-4 3-9 3s-9-1.34-9-3"/>
                  <path d="M21 15c0 1.66-4 3-9 3s-9-1.34-9-3"/>
                </svg>
                <!-- Saturation Icon -->
                <svg v-else-if="field.key === 'saturation'" xmlns="http://www.w3.org/2000/svg" :class="getIconColorClass(field.key)" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/>
                </svg>
              </div>
            </div>
            <div class="relative">
              <input
                v-model="localVitalSigns[field.key]"
                :class="getInputClass(field.key, localVitalSigns[field.key])"
                class="w-full bg-transparent border-b-2 focus:outline-none text-lg py-2 pr-12 transition-colors"
                :placeholder="loading ? 'Loading...' : getPlaceholder(field.key)"
                :disabled="loading"
                type="text"
                @change="handleChange"
                @input="validateInput(field.key, $event.target.value)"
                @focus="onInputFocus(field.key)"
                @blur="onInputBlur(field.key)"
              />
              <span class="absolute right-0 bottom-2 text-sm text-gray-500">{{ field.unit }}</span>

              <!-- Value Status Indicator -->
              <div v-if="localVitalSigns[field.key]" class="absolute -right-2 top-1">
                <div :class="getValueStatusClass(field.key, localVitalSigns[field.key])"
                     class="w-3 h-3 rounded-full border-2 border-white shadow-sm"
                     :title="getValueStatusText(field.key, localVitalSigns[field.key])">
                </div>
              </div>

              <!-- Quick Entry Buttons for BP -->
              <div v-if="field.key === 'blood_pressure' && showQuickEntry[field.key]"
                   class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-2">
                <div class="text-xs text-gray-600 mb-2">Quick Entry:</div>
                <div class="grid grid-cols-3 gap-1">
                  <button v-for="preset in bpPresets" :key="preset"
                          @click="setQuickValue(field.key, preset)"
                          class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                    {{ preset }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Save Button -->
      <div class="flex justify-between items-center mt-3">
        <button
          @click="handleSaveVitals"
          class="flex items-center gap-1 text-sm text-blue-500 hover:text-blue-600"
          :disabled="loading"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-save w-4 h-4"
          >
            <path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path>
            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
            <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
          </svg>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "VitalSigns",

  props: {
    instanceId: {
      type: String,
      required: true,
    },
    isFirstInstance: {
      type: Boolean,
      default: false,
    },
    encounterId: {
      type: [String, Number],
      default: null,
      required: true,
      validator: function (value) {
        return value !== null && value !== undefined && value !== "";
      },
    },
    // New property to receive vital signs data from parent
    vitalSignsData: {
      type: Object,
      default: null
    },
  },

  data() {
    return {
      loading: false,
      isAIPopulated: false,
      showQuickEntry: {},
      localVitalSigns: {
        temperature: "",
        pulse: "",
        blood_pressure: "",
        respiratory_rate: "",
        saturation: "",
      },
      vitalFields: [
        {
          label: "Temperature",
          key: "temperature",
          unit: "°C",
          normal: { min: 36.1, max: 37.2 }
        },
        {
          label: "Pulse",
          key: "pulse",
          unit: "BPM",
          normal: { min: 60, max: 100 }
        },
        {
          label: "Blood Pressure",
          key: "blood_pressure",
          unit: "mmHg",
          normal: { systolic: { min: 90, max: 140 }, diastolic: { min: 60, max: 90 } }
        },
        {
          label: "Respiratory Rate",
          key: "respiratory_rate",
          unit: "/min",
          normal: { min: 12, max: 20 }
        },
        {
          label: "Saturation",
          key: "saturation",
          unit: "%",
          normal: { min: 95, max: 100 }
        },
      ],
      bpPresets: ['120/80', '110/70', '130/85', '140/90'],
      validationErrors: {}
    };
  },

  watch: {
    vitalSignsData: {
      handler(newData) {
        if (newData) {
          this.localVitalSigns = { ...newData };
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    handleChange() {
      this.$emit('update:vitals', this.instanceId, this.localVitalSigns);
    },

    handleSaveVitals() {
      this.$emit('save:success', this.instanceId, this.localVitalSigns);
    },

    handleApproveAI() {
      this.isAIPopulated = false;
      this.$emit('ai-approved', this.instanceId);
    },

    cloneVitals() {
      this.$emit('clone');
    },

    removeVitals() {
      this.$emit('remove', this.instanceId);
    },

    getPlaceholder(key) {
      const placeholders = {
        temperature: '36.5',
        pulse: '72',
        blood_pressure: '120/80',
        respiratory_rate: '16',
        saturation: '98'
      };
      return placeholders[key] || '--';
    },

    getInputClass(key, value) {
      const baseClass = 'border-gray-200 focus:border-blue-500';
      if (!value) return baseClass;

      const status = this.getValueStatus(key, value);
      if (status === 'normal') return 'border-green-300 focus:border-green-500';
      if (status === 'abnormal') return 'border-red-300 focus:border-red-500';
      if (status === 'warning') return 'border-yellow-300 focus:border-yellow-500';

      return baseClass;
    },

    getValueStatus(key, value) {
      if (!value) return 'empty';

      const field = this.vitalFields.find(f => f.key === key);
      if (!field || !field.normal) return 'unknown';

      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        // Handle blood pressure specially
        if (key === 'blood_pressure' && value.includes('/')) {
          const [systolic, diastolic] = value.split('/').map(v => parseFloat(v));
          if (isNaN(systolic) || isNaN(diastolic)) return 'invalid';

          const sysNormal = systolic >= field.normal.systolic.min && systolic <= field.normal.systolic.max;
          const diaNormal = diastolic >= field.normal.diastolic.min && diastolic <= field.normal.diastolic.max;

          if (sysNormal && diaNormal) return 'normal';
          if (systolic > field.normal.systolic.max || diastolic > field.normal.diastolic.max) return 'abnormal';
          return 'warning';
        }
        return 'invalid';
      }

      if (numValue >= field.normal.min && numValue <= field.normal.max) return 'normal';
      if (numValue < field.normal.min * 0.8 || numValue > field.normal.max * 1.2) return 'abnormal';
      return 'warning';
    },

    getValueStatusClass(key, value) {
      const status = this.getValueStatus(key, value);
      const classes = {
        normal: 'bg-green-500',
        warning: 'bg-yellow-500',
        abnormal: 'bg-red-500',
        invalid: 'bg-gray-400'
      };
      return classes[status] || 'bg-gray-300';
    },

    getValueStatusText(key, value) {
      const status = this.getValueStatus(key, value);
      const texts = {
        normal: 'Normal range',
        warning: 'Outside normal range',
        abnormal: 'Significantly abnormal',
        invalid: 'Invalid format'
      };
      return texts[status] || 'Unknown';
    },

    getIconBackgroundClass(key) {
      const classes = {
        temperature: 'bg-orange-100',
        pulse: 'bg-teal-100',
        blood_pressure: 'bg-blue-100',
        respiratory_rate: 'bg-green-100',
        saturation: 'bg-purple-100'
      };
      return classes[key] || 'bg-gray-100';
    },

    getIconColorClass(key) {
      const classes = {
        temperature: 'text-orange-600',
        pulse: 'text-teal-600',
        blood_pressure: 'text-blue-600',
        respiratory_rate: 'text-green-600',
        saturation: 'text-purple-600'
      };
      return classes[key] || 'text-gray-600';
    },

    validateInput(key, value) {
      // Clear previous validation error
      this.$delete(this.validationErrors, key);

      if (!value) return;

      // Validate based on field type
      if (key === 'blood_pressure') {
        if (!value.includes('/')) {
          this.$set(this.validationErrors, key, 'Format: systolic/diastolic (e.g., 120/80)');
        }
      } else {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          this.$set(this.validationErrors, key, 'Please enter a valid number');
        }
      }
    },

    onInputFocus(key) {
      if (key === 'blood_pressure') {
        this.$set(this.showQuickEntry, key, true);
      }
    },

    onInputBlur(key) {
      // Delay hiding to allow clicking on quick entry buttons
      setTimeout(() => {
        this.$set(this.showQuickEntry, key, false);
      }, 200);
    },

    setQuickValue(key, value) {
      this.localVitalSigns[key] = value;
      this.$set(this.showQuickEntry, key, false);
      this.handleChange();
    }
  }
};
</script>
