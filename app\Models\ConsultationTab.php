<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationTab extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'patient_id',
        'type',
        'content',
        'metadata',
        'templates',
        'title',
        'order',
        'is_from_template',
        'is_ai_generated',
        'created_by',
    ];

    protected $casts = [
        'metadata' => 'array',
        'templates' => 'array',
        'is_from_template' => 'boolean',
        'is_ai_generated' => 'boolean',
    ];

    /**
     * Get the consultation that owns the tab.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the patient that owns the tab.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who created the tab.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by tab type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by consultation.
     */
    public function scopeByConsultation($query, $consultationId)
    {
        return $query->where('consultation_id', $consultationId);
    }

    /**
     * Scope to order by tab order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Get the display title for the tab.
     */
    public function getDisplayTitleAttribute()
    {
        return $this->title ?? ucfirst(str_replace('_', ' ', $this->type));
    }

    /**
     * Check if tab has content.
     */
    public function hasContent()
    {
        return !empty($this->content);
    }

    /**
     * Get the main form types that are always shown.
     */
    public static function getMainFormTypes()
    {
        return ['concerns', 'history', 'examination', 'plan'];
    }

    /**
     * Get the supplementary form types that can be added dynamically.
     */
    public static function getSupplementaryFormTypes()
    {
        return [
            'safeguarding' => 'Safeguarding',
            'notes' => 'Notes',
            'comments' => 'Comments',
            'allergies' => 'Allergies',
            'family_history' => 'Family History',
            'safety_netting' => 'Safety Netting',
            'medical_history' => 'Past Medical History',
            'medications' => 'Medications',
            'social_history' => 'Social History',
            'systems_review' => 'Systems Review',
            'preventative_care' => 'Preventative Care',
            'mental_health' => 'Mental Health',
            'lifestyle' => 'Lifestyle',
        ];
    }

    /**
     * Get the template options for examination type.
     */
    public static function getExaminationTemplates()
    {
        return ['CVS', 'Resp', 'Gastro', 'Neuro', 'MSK', 'Derm', 'ENT'];
    }
}