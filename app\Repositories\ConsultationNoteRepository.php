<?php

namespace App\Repositories;

use App\Models\ConsultationNote;
use Illuminate\Database\Eloquent\Collection;

class ConsultationNoteRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param ConsultationNote $model
     * @return void
     */
    public function __construct(ConsultationNote $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find notes by consultation.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findByConsultation(int $consultationId): Collection
    {
        $query = $this->model->newQuery();
        $query->where('consultation_id', $consultationId)
              ->with('creator')
              ->orderBy('note_date', 'desc');
        
        return $query->get();
    }
    
    /**
     * Find notes by type.
     *
     * @param int $consultationId
     * @param string $noteType
     * @return Collection
     */
    public function findByType(int $consultationId, string $noteType): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'note_type' => $noteType
        ]);
    }
    
    /**
     * Find private notes.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findPrivate(int $consultationId): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'is_private' => true
        ]);
    }
    
    /**
     * Find public notes.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findPublic(int $consultationId): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'is_private' => false
        ]);
    }
}
