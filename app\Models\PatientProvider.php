<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PatientProvider extends Model
{
    use HasFactory;

    protected $table = 'patient_provider';

    protected $fillable = [
        'patient_id',
        'provider_id',
        'relationship_type',
        'assigned_by_user_id',
        'assignment_reason',
        'is_active',
        'assigned_at',
        'deactivated_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'assigned_at' => 'datetime',
        'deactivated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by_user_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRelationshipType($query, $type)
    {
        return $query->where('relationship_type', $type);
    }

    /**
     * Create or update patient-provider relationship
     */
    public static function createRelationship($patientId, $providerId, $type, $assignedByUserId = null, $reason = null)
    {
        return static::updateOrCreate(
            [
                'patient_id' => $patientId,
                'provider_id' => $providerId,
                'relationship_type' => $type,
            ],
            [
                'assigned_by_user_id' => $assignedByUserId,
                'assignment_reason' => $reason,
                'is_active' => true,
                'assigned_at' => now(),
                'deactivated_at' => null,
            ]
        );
    }

    /**
     * Deactivate relationship
     */
    public function deactivate()
    {
        $this->update([
            'is_active' => false,
            'deactivated_at' => now(),
        ]);
    }
}
