<?php

namespace App\Notifications;

use App\Models\Appointment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class AppointmentPaymentLinkNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The appointment instance.
     *
     * @var \App\Models\Appointment
     */
    protected $appointment;

    /**
     * The payment link URL.
     *
     * @var string
     */
    protected $paymentLink;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Appointment  $appointment
     * @param  string  $paymentLink
     * @return void
     */
    public function __construct(Appointment $appointment, string $paymentLink)
    {
        $this->appointment = $appointment;
        $this->paymentLink = $paymentLink;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = Carbon::parse($this->appointment->date)->format('l, F j, Y');
        $startTime = $this->appointment->time_slot['start_time'];
        $endTime = $this->appointment->time_slot['end_time'];
        
        return (new MailMessage)
            ->subject('Payment Required for Your Appointment')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your appointment has been successfully booked and payment is required to confirm your slot.')
            ->line('**Appointment Details:**')
            ->line('Date: ' . $date)
            ->line('Time: ' . $startTime . ' - ' . $endTime)
            ->line('Provider: ' . $this->appointment->provider->user->name)
            ->line('Service: ' . ($this->appointment->service->name ?? 'General Consultation'))
            ->line('Amount: £' . number_format($this->appointment->amount, 2))
            ->line('Please complete your payment within 24 hours to secure your appointment slot.')
            ->action('Pay Now', $this->paymentLink)
            ->line('If you have any questions, please contact us.')
            ->line('Thank you for choosing our services!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'appointment_id' => $this->appointment->id,
            'payment_link' => $this->paymentLink,
            'amount' => $this->appointment->amount,
            'due_date' => $this->appointment->payment_due_by,
        ];
    }
}