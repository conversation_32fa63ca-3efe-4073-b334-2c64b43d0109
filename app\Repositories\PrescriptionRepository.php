<?php

namespace App\Repositories;

use App\Models\Prescription;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class PrescriptionRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Prescription $model
     * @return void
     */
    public function __construct(Prescription $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find prescriptions by patient.
     *
     * @param int $patientId
     * @return Collection
     */
    public function findByPatient(int $patientId): Collection
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Find prescriptions by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->findBy(['provider_id' => $providerId]);
    }
    
    /**
     * Find prescriptions by consultation.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findByConsultation(int $consultationId): Collection
    {
        return $this->findBy(['consultation_id' => $consultationId]);
    }
    
    /**
     * Find active prescriptions.
     *
     * @param int|null $patientId
     * @return Collection
     */
    public function findActive(?int $patientId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', 'active')
              ->where(function ($q) {
                  $q->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
              });
        
        if ($patientId) {
            $query->where('patient_id', $patientId);
        }
        
        return $query->get();
    }
    
    /**
     * Find prescriptions by status.
     *
     * @param string $status
     * @param int|null $patientId
     * @return Collection
     */
    public function findByStatus(string $status, ?int $patientId = null): Collection
    {
        $criteria = ['status' => $status];
        
        if ($patientId) {
            $criteria['patient_id'] = $patientId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find prescriptions expiring soon.
     *
     * @param int $days
     * @param int|null $patientId
     * @return Collection
     */
    public function findExpiringSoon(int $days = 7, ?int $patientId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', 'active')
              ->whereBetween('end_date', [now(), now()->addDays($days)]);
        
        if ($patientId) {
            $query->where('patient_id', $patientId);
        }
        
        return $query->get();
    }
    
    /**
     * Find prescriptions by medication.
     *
     * @param string $medication
     * @param int|null $patientId
     * @return Collection
     */
    public function findByMedication(string $medication, ?int $patientId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('medication_name', 'like', "%{$medication}%");
        
        if ($patientId) {
            $query->where('patient_id', $patientId);
        }
        
        return $query->get();
    }
    
    /**
     * Get prescriptions with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['patient.user', 'provider.user', 'consultation']);
        
        // Apply filters
        if (!empty($filters['patient_id'])) {
            $query->where('patient_id', $filters['patient_id']);
        }
        
        if (!empty($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }
        
        if (!empty($filters['consultation_id'])) {
            $query->where('consultation_id', $filters['consultation_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['medication'])) {
            $query->where('medication_name', 'like', "%{$filters['medication']}%");
        }
        
        if (!empty($filters['date_from'])) {
            $query->whereDate('start_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('start_date', '<=', $filters['date_to']);
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Get prescription statistics.
     *
     * @param int|null $providerId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getStatistics(?int $providerId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = $this->model->newQuery();
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        
        $total = $query->count();
        $active = $query->where('status', 'active')->count();
        $completed = $query->where('status', 'completed')->count();
        $cancelled = $query->where('status', 'cancelled')->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'completed' => $completed,
            'cancelled' => $cancelled,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }
}
