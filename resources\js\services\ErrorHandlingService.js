import { useToast } from 'vue-toastification'

class ErrorHandlingService {
  constructor() {
    this.toast = useToast()
    this.errorQueue = []
    this.isOnline = navigator.onLine
    this.setupGlobalHandlers()
  }

  setupGlobalHandlers() {
    // Global error handler for unhandled promises
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason)
      this.handleError(event.reason, 'Unhandled Promise Rejection')
    })

    // Global error handler for JavaScript errors
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error)
      this.handleError(event.error, 'JavaScript Error')
    })

    // Network status monitoring
    window.addEventListener('online', () => {
      this.isOnline = true
      this.toast.success('Connection restored')
      this.retryQueuedRequests()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.toast.warning('Connection lost. Some features may not work.')
    })
  }

  /**
   * Main error handling method
   */
  handleError(error, context = 'Unknown', options = {}) {
    const errorInfo = this.parseError(error, context)
    
    // Log error for debugging
    console.error(`[${context}]`, errorInfo)

    // Show user-friendly message
    if (!options.silent) {
      this.showUserError(errorInfo, options)
    }

    // Report error if enabled
    if (options.report !== false) {
      this.reportError(errorInfo, context)
    }

    return errorInfo
  }

  /**
   * Parse error into standardized format
   */
  parseError(error, context) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      context,
      type: 'unknown',
      title: 'Something went wrong',
      message: 'An unexpected error occurred. Please try again.',
      details: null,
      trace: null,
      canRetry: true,
      severity: 'error'
    }

    if (error?.response) {
      // Axios/HTTP error
      errorInfo.type = 'http'
      errorInfo.title = this.getHttpErrorTitle(error.response.status)
      errorInfo.message = error.response.data?.message || this.getHttpErrorMessage(error.response.status)
      errorInfo.details = error.response.data
      errorInfo.canRetry = this.isRetryableHttpError(error.response.status)
      errorInfo.severity = this.getHttpErrorSeverity(error.response.status)
    } else if (error?.name === 'ValidationError') {
      // Validation error
      errorInfo.type = 'validation'
      errorInfo.title = 'Validation Error'
      errorInfo.message = error.message || 'Please check your input and try again.'
      errorInfo.details = error.errors
      errorInfo.canRetry = true
      errorInfo.severity = 'warning'
    } else if (error?.name === 'NetworkError' || !this.isOnline) {
      // Network error
      errorInfo.type = 'network'
      errorInfo.title = 'Connection Problem'
      errorInfo.message = 'Please check your internet connection and try again.'
      errorInfo.canRetry = true
      errorInfo.severity = 'warning'
    } else if (error instanceof Error) {
      // JavaScript error
      errorInfo.type = 'javascript'
      errorInfo.title = error.name || 'JavaScript Error'
      errorInfo.message = error.message || 'An unexpected error occurred.'
      errorInfo.trace = error.stack
      errorInfo.canRetry = false
      errorInfo.severity = 'error'
    } else if (typeof error === 'string') {
      // String error
      errorInfo.message = error
      errorInfo.canRetry = true
    }

    return errorInfo
  }

  /**
   * Show user-friendly error message
   */
  showUserError(errorInfo, options = {}) {
    const toastOptions = {
      timeout: options.timeout || (errorInfo.severity === 'error' ? 8000 : 5000),
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false
    }

    switch (errorInfo.severity) {
      case 'error':
        this.toast.error(errorInfo.message, toastOptions)
        break
      case 'warning':
        this.toast.warning(errorInfo.message, toastOptions)
        break
      case 'info':
        this.toast.info(errorInfo.message, toastOptions)
        break
      default:
        this.toast.error(errorInfo.message, toastOptions)
    }
  }

  /**
   * Report error to backend
   */
  async reportError(errorInfo, context) {
    try {
      // Only report in production or when explicitly enabled
      if (process.env.NODE_ENV !== 'production' && !window.ENABLE_ERROR_REPORTING) {
        return
      }

      const reportData = {
        ...errorInfo,
        context,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        userId: window.user?.id || null,
        clinicId: window.user?.clinic_id || null
      }

      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify(reportData)
      })
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * HTTP error helpers
   */
  getHttpErrorTitle(status) {
    const titles = {
      400: 'Bad Request',
      401: 'Authentication Required',
      403: 'Access Denied',
      404: 'Not Found',
      422: 'Validation Error',
      429: 'Too Many Requests',
      500: 'Server Error',
      502: 'Service Unavailable',
      503: 'Service Unavailable',
      504: 'Request Timeout'
    }
    return titles[status] || 'Request Failed'
  }

  getHttpErrorMessage(status) {
    const messages = {
      400: 'The request was invalid. Please check your input.',
      401: 'Please log in to continue.',
      403: 'You don\'t have permission to perform this action.',
      404: 'The requested resource was not found.',
      422: 'Please check your input and try again.',
      429: 'Too many requests. Please wait a moment and try again.',
      500: 'A server error occurred. Please try again later.',
      502: 'The service is temporarily unavailable.',
      503: 'The service is temporarily unavailable.',
      504: 'The request timed out. Please try again.'
    }
    return messages[status] || 'An error occurred while processing your request.'
  }

  isRetryableHttpError(status) {
    return [408, 429, 500, 502, 503, 504].includes(status)
  }

  getHttpErrorSeverity(status) {
    if (status >= 500) return 'error'
    if (status >= 400) return 'warning'
    return 'info'
  }

  /**
   * Retry queued requests when connection is restored
   */
  retryQueuedRequests() {
    const queue = [...this.errorQueue]
    this.errorQueue = []
    
    queue.forEach(async (request) => {
      try {
        await request.retry()
        this.toast.success('Request completed successfully')
      } catch (error) {
        this.handleError(error, 'Retry Failed', { report: false })
      }
    })
  }

  /**
   * Add request to retry queue
   */
  queueForRetry(retryFunction, description = 'Request') {
    this.errorQueue.push({
      retry: retryFunction,
      description,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Create error boundary for Vue components
   */
  createErrorBoundary(component, fallbackComponent = null) {
    return {
      ...component,
      errorCaptured(error, instance, info) {
        this.handleError(error, `Vue Component: ${instance.$options.name || 'Unknown'}`, {
          details: { info, componentStack: instance.$parent }
        })
        
        if (fallbackComponent) {
          return fallbackComponent
        }
        
        return false // Prevent error from propagating
      }
    }
  }
}

// Create singleton instance
const errorHandlingService = new ErrorHandlingService()

// Export for use in components
export default errorHandlingService

// Export helper functions
export const handleError = (error, context, options) => 
  errorHandlingService.handleError(error, context, options)

export const reportError = (error, context) => 
  errorHandlingService.reportError(error, context)

export const queueForRetry = (retryFunction, description) => 
  errorHandlingService.queueForRetry(retryFunction, description)
