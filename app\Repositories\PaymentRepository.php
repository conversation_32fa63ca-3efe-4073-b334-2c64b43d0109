<?php

namespace App\Repositories;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class PaymentRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Payment $model
     * @return void
     */
    public function __construct(Payment $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find payments by patient.
     *
     * @param int $patientId
     * @return Collection
     */
    public function findByPatient(int $patientId): Collection
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Find payments by appointment.
     *
     * @param int $appointmentId
     * @return Collection
     */
    public function findByAppointment(int $appointmentId): Collection
    {
        return $this->findBy(['appointment_id' => $appointmentId]);
    }
    
    /**
     * Find payments by status.
     *
     * @param string $status
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByStatus(string $status, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', $status);
        
        if ($clinicId) {
            $query->whereHas('appointment.provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->get();
    }
    
    /**
     * Find payments by payment method.
     *
     * @param string $method
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByPaymentMethod(string $method, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('payment_method', $method);
        
        if ($clinicId) {
            $query->whereHas('appointment.provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->get();
    }
    
    /**
     * Find pending payments.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findPending(?int $clinicId = null): Collection
    {
        return $this->findByStatus('pending', $clinicId);
    }
    
    /**
     * Find successful payments.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findSuccessful(?int $clinicId = null): Collection
    {
        return $this->findByStatus('completed', $clinicId);
    }
    
    /**
     * Find failed payments.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findFailed(?int $clinicId = null): Collection
    {
        return $this->findByStatus('failed', $clinicId);
    }
    
    /**
     * Find payments by date range.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByDateRange(Carbon $startDate, Carbon $endDate, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->whereBetween('created_at', [$startDate, $endDate]);
        
        if ($clinicId) {
            $query->whereHas('appointment.provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->get();
    }
    
    /**
     * Find payments by transaction ID.
     *
     * @param string $transactionId
     * @return Payment|null
     */
    public function findByTransactionId(string $transactionId): ?Payment
    {
        return $this->findOneBy(['transaction_id' => $transactionId]);
    }
    
    /**
     * Get payments with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['patient.user', 'appointment.provider.user', 'appointment.service']);
        
        // Apply filters
        if (!empty($filters['patient_id'])) {
            $query->where('patient_id', $filters['patient_id']);
        }
        
        if (!empty($filters['appointment_id'])) {
            $query->where('appointment_id', $filters['appointment_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        if (!empty($filters['min_amount'])) {
            $query->where('amount', '>=', $filters['min_amount']);
        }
        
        if (!empty($filters['max_amount'])) {
            $query->where('amount', '<=', $filters['max_amount']);
        }
        
        if (!empty($filters['clinic_id'])) {
            $query->whereHas('appointment.provider', function ($q) use ($filters) {
                $q->where('clinic_id', $filters['clinic_id']);
            });
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Get payment statistics.
     *
     * @param int|null $clinicId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getStatistics(?int $clinicId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = $this->model->newQuery();
        
        if ($clinicId) {
            $query->whereHas('appointment.provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        
        $total = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $pending = $query->where('status', 'pending')->count();
        $failed = $query->where('status', 'failed')->count();
        
        $totalAmount = $query->sum('amount');
        $completedAmount = $query->where('status', 'completed')->sum('amount');
        $pendingAmount = $query->where('status', 'pending')->sum('amount');
        
        return [
            'total_payments' => $total,
            'completed_payments' => $completed,
            'pending_payments' => $pending,
            'failed_payments' => $failed,
            'total_amount' => $totalAmount,
            'completed_amount' => $completedAmount,
            'pending_amount' => $pendingAmount,
            'success_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'failure_rate' => $total > 0 ? round(($failed / $total) * 100, 2) : 0,
        ];
    }



    /**
     * Find payment by Stripe payment intent ID.
     *
     * @param string $paymentIntentId
     * @return Payment|null
     */
    public function findByStripePaymentIntent(string $paymentIntentId): ?Payment
    {
        return $this->model->newQuery()
            ->where('stripe_payment_intent_id', $paymentIntentId)
            ->first();
    }
}
