<?php

namespace App\Repositories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Category $model
     * @return void
     */
    public function __construct(Category $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find categories by clinic.
     *
     * @param int $clinicId
     * @param bool $activeOnly
     * @return Collection
     */
    public function findByClinic(int $clinicId, bool $activeOnly = false): Collection
    {
        $query = $this->model->newQuery()->where('clinic_id', $clinicId);

        if ($activeOnly) {
            $query->where('is_active', true);
        }

        return $query->orderBy('name')->get();
    }

    /**
     * Find active categories for dropdown.
     *
     * @param int $clinicId
     * @return Collection
     */
    public function findActiveForDropdown(int $clinicId): Collection
    {
        return $this->model->newQuery()
            ->where('clinic_id', $clinicId)
            ->where('is_active', true)
            ->orderBy('name')
            ->select('id', 'name')
            ->get();
    }
    
    /**
     * Find active categories.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findActive(?int $clinicId = null): Collection
    {
        $criteria = ['is_active' => true];
        
        if ($clinicId) {
            $criteria['clinic_id'] = $clinicId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find categories with service count.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findWithServiceCount(?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->withCount(['services' => function ($q) {
            $q->where('active', true)
              ->where('approval_status', 'approved');
        }]);
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->where('is_active', true)->get();
    }
    
    /**
     * Search categories by name.
     *
     * @param string $search
     * @param int|null $clinicId
     * @return Collection
     */
    public function search(string $search, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Get categories with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['clinic', 'services']);
        
        // Apply filters
        if (!empty($filters['clinic_id'])) {
            $query->where('clinic_id', $filters['clinic_id']);
        }
        
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }
        
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        return $query->orderBy('name')->paginate($perPage);
    }
    
    /**
     * Get category statistics for a clinic.
     *
     * @param int $clinicId
     * @return array
     */
    public function getStatistics(int $clinicId): array
    {
        $query = $this->model->newQuery();
        $query->where('clinic_id', $clinicId);
        
        $total = $query->count();
        $active = $query->where('is_active', true)->count();
        
        // Service distribution by category
        $serviceDistribution = $query->withCount(['services' => function ($q) {
            $q->where('active', true)
              ->where('approval_status', 'approved');
        }])
        ->get()
        ->pluck('services_count', 'name')
        ->toArray();
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'service_distribution' => $serviceDistribution
        ];
    }
}
