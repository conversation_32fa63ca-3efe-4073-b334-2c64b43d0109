<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'patient_id',
        'document_type',
        'file_path',
        'file_name',
        'original_name',
        'file_size',
        'mime_type',
        'description',
        'uploaded_by',
        'wp_document_id',
        'wp_attachment_id',
        'wp_encounter_id',
        'wp_created_date',
        'wp_uploaded_by_id',
        'wp_uploaded_by_email',
        'wp_uploaded_by_name',
    ];

    protected $casts = [
        'wp_created_date' => 'datetime',
    ];

    /**
     * Get the consultation that owns the document.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the patient that owns the document.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who uploaded the document.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope to filter by document type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Get the secure URL for accessing this document
     */
    public function getSecureUrlAttribute()
    {
        return url('/storage/' . str_replace('/', '_', dirname($this->file_path)) . '/' . $this->file_name);
    }

    /**
     * Get the download URL for this document
     */
    public function getDownloadUrlAttribute()
    {
        return url("/consultation-files/{$this->id}/download");
    }

    /**
     * Check if the file exists in storage
     */
    public function fileExists()
    {
        return \Illuminate\Support\Facades\Storage::disk('public')->exists($this->file_path);
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeHumanAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
