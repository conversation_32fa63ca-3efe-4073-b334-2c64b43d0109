<script setup lang="ts">
import { Head, useForm, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface SubscriptionPlan {
  id: number;
  name: string;
  slug: string;
  price: number;
  formatted_price: string;
  currency: string;
  interval: string;
  description: string;
  features: {
    additional_features?: string[];
  };
  is_free: boolean;
}

interface User {
  name: string;
  email: string;
}

interface Props {
  plan: SubscriptionPlan;
  stripePublicKey: string;
  user: User;
  paymentMethods?: any[];
  defaultPaymentMethod?: any;
}

const props = defineProps<Props>();
const page = usePage();

const loading = ref(false);
const error = ref('');
const stripe = ref<any>(null);
const elements = ref<any>(null);
const cardElement = ref<any>(null);

const successMessage = computed(() => (page.props.flash as any)?.success);

const form = useForm({
  payment_method_id: '',
  billing_email: props.user.email,
  billing_name: props.user.name,
  billing_address: '',
  billing_city: '',
  billing_postal_code: '',
});

// Initialize Stripe
onMounted(async () => {
  try {
    // Load Stripe.js
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.onload = initializeStripe;
    document.head.appendChild(script);
  } catch (err) {
    error.value = 'Failed to load payment system';
  }
});

const initializeStripe = () => {
  stripe.value = (window as any).Stripe(props.stripePublicKey);
  elements.value = stripe.value.elements();

  // Create card element with better styling
  cardElement.value = elements.value.create('card', {
    style: {
      base: {
        fontSize: '16px',
        color: '#1f2937',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        fontSmoothing: 'antialiased',
        '::placeholder': {
          color: '#9ca3af',
        },
        iconColor: '#6b7280',
      },
      invalid: {
        color: '#ef4444',
        iconColor: '#ef4444',
      },
    },
    hidePostalCode: true, // We collect this separately
  });

  // Mount card element
  cardElement.value.mount('#card-element');

  // Handle real-time validation errors from the card Element
  cardElement.value.on('change', (event: any) => {
    if (event.error) {
      error.value = event.error.message;
    } else {
      error.value = '';
    }
  });
};

// Autofill test card for development
const autofillTestCard = () => {
  if (cardElement.value) {
    // This will trigger the browser's autofill for test cards
    // Users can also manually enter: 4242 4242 4242 4242, 12/34, 123
    const event = new Event('focus');
    document.getElementById('card-element')?.dispatchEvent(event);
  }
};

const processPayment = async () => {
  loading.value = true;
  error.value = '';

  try {
    // For free plans, skip payment method creation
    if (props.plan.is_free) {
      form.payment_method_id = '';
      form.post(route('membership.process-payment', { slug: props.plan.slug }), {
        onError: (errors) => {
          error.value = errors.payment || 'Subscription failed. Please try again.';
          loading.value = false;
        },
        onSuccess: () => {
          loading.value = false;
        },
      });
      return;
    }

    // For paid plans, require Stripe
    if (!stripe.value || !cardElement.value) {
      error.value = 'Payment system not ready';
      loading.value = false;
      return;
    }

    // Create payment method
    const { error: stripeError, paymentMethod } = await stripe.value.createPaymentMethod({
      type: 'card',
      card: cardElement.value,
      billing_details: {
        name: form.billing_name,
        email: form.billing_email,
        address: {
          line1: form.billing_address,
          city: form.billing_city,
          postal_code: form.billing_postal_code,
        },
      },
    });

    if (stripeError) {
      error.value = stripeError.message;
      loading.value = false;
      return;
    }

    // Submit form with payment method
    form.payment_method_id = paymentMethod.id;
    form.post(route('membership.process-payment', { slug: props.plan.slug }), {
      onError: (errors) => {
        error.value = errors.payment || 'Payment failed. Please try again.';
        loading.value = false;
      },
      onSuccess: () => {
        loading.value = false;
      },
    });
  } catch (err: any) {
    error.value = err.message || 'An unexpected error occurred';
    loading.value = false;
  }
};

const calculateTotal = () => {
  return props.plan.formatted_price;
};
</script>

<template>
  <AppLayout>
    <Head :title="`Checkout - ${plan.name}`" />

    <div class="container mx-auto py-12 px-4 sm:px-6 lg:px-8 max-w-4xl">
      <!-- Success Message for Registration -->
      <div v-if="successMessage" class="mb-6">
        <Alert class="border-green-200 bg-green-50">
          <svg class="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <AlertDescription class="text-green-800 ml-2">
            {{ successMessage }}
          </AlertDescription>
        </Alert>
      </div>

      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Complete Your Subscription</h1>
        <p class="mt-2 text-gray-600">You're subscribing to {{ plan.name }}</p>
      </div>

      <div class="grid gap-8 lg:grid-cols-2">
        <!-- Order Summary -->
        <Card class="lg:order-1 order-2">
          <CardHeader class="pb-4">
            <CardTitle class="text-lg">Order Summary</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Compact Plan Info -->
            <div class="flex justify-between items-center">
              <div>
                <h3 class="font-medium">{{ plan.name }}</h3>
                <p class="text-sm text-gray-500 hidden sm:block">{{ plan.description }}</p>
              </div>
              <span class="font-medium text-lg">{{ plan.formatted_price }}/{{ plan.interval }}</span>
            </div>

            <!-- Features - Compact for Mobile -->
            <div class="border-t pt-4">
              <h4 class="font-medium mb-2 text-sm">What's included:</h4>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-1">
                <div v-for="(feature, index) in plan.features?.additional_features || []" :key="index" class="flex items-center text-sm text-gray-600">
                  <svg class="h-3 w-3 text-teal-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="truncate">{{ feature }}</span>
                </div>
              </div>
            </div>

            <!-- Total -->
            <div class="border-t pt-4">
              <div class="flex justify-between items-center font-medium">
                <span>Total</span>
                <span>{{ calculateTotal() }}/{{ plan.interval }}</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">Billed {{ plan.interval }}ly</p>
            </div>
          </CardContent>
        </Card>

        <!-- Payment Form -->
        <Card class="lg:order-2 order-1">
          <CardHeader>
            <CardTitle>Payment Information</CardTitle>
            <CardDescription>Enter your payment details to complete your subscription</CardDescription>
          </CardHeader>
          <CardContent>
            <form @submit.prevent="processPayment" class="space-y-6">
              <!-- Account Information (Read-only) -->
              <div class="space-y-3 bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-sm text-gray-700 mb-3">Account Information</h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label class="text-xs text-gray-600 mb-1 block">Full Name</Label>
                    <div class="text-sm font-medium text-gray-900 py-1">{{ form.billing_name }}</div>
                  </div>
                  <div>
                    <Label class="text-xs text-gray-600 mb-1 block">Email Address</Label>
                    <div class="text-sm font-medium text-gray-900 py-1 truncate" :title="form.billing_email">{{ form.billing_email }}</div>
                  </div>
                </div>
              </div>

              <!-- Billing Address (Paid Plans Only) -->
              <div v-if="!plan.is_free" class="space-y-4">
                <h4 class="font-medium text-sm text-gray-700 mb-3">Billing Address</h4>
                <div class="space-y-4">
                  <div>
                    <Label for="billing_address" class="text-sm font-medium text-gray-700 mb-2 block">Address Line 1</Label>
                    <Input
                      id="billing_address"
                      v-model="form.billing_address"
                      type="text"
                      required
                      placeholder="Enter your address"
                      class="w-full"
                    />
                  </div>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label for="billing_city" class="text-sm font-medium text-gray-700 mb-2 block">City</Label>
                      <Input
                        id="billing_city"
                        v-model="form.billing_city"
                        type="text"
                        required
                        placeholder="City"
                        class="w-full"
                      />
                    </div>
                    <div>
                      <Label for="billing_postal_code" class="text-sm font-medium text-gray-700 mb-2 block">Postal Code</Label>
                      <Input
                        id="billing_postal_code"
                        v-model="form.billing_postal_code"
                        type="text"
                        required
                        placeholder="Postal Code"
                        class="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Card Information (Paid Plans Only) -->
              <div v-if="!plan.is_free" class="space-y-2">
                <Label for="card-element" class="text-sm font-medium text-gray-700 mb-2 block">Card Information</Label>
                <div
                  id="card-element"
                  class="p-4 border-2 border-gray-300 rounded-lg bg-white min-h-[50px] focus-within:border-medroid-teal focus-within:ring-2 focus-within:ring-medroid-teal/20 transition-all duration-200"
                ></div>
                <div class="flex items-center justify-end mt-2">
                  <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span>Autofill</span>
                    <button
                      type="button"
                      class="px-2 py-1 bg-medroid-teal text-white rounded text-xs hover:bg-medroid-teal/90 transition-colors"
                      @click="autofillTestCard"
                    >
                      link
                    </button>
                  </div>
                </div>
              </div>

              <!-- Free Plan Notice -->
              <div v-if="plan.is_free" class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <h4 class="font-medium text-green-800">Free Plan Selected</h4>
                    <p class="text-sm text-green-700">No payment required. Click continue to activate your free plan.</p>
                  </div>
                </div>
              </div>

              <!-- Error Display -->
              <Alert v-if="error" variant="destructive">
                <AlertDescription>{{ error }}</AlertDescription>
              </Alert>

              <!-- Submit Button -->
              <Button
                type="submit"
                :disabled="loading || !form.billing_name || !form.billing_email || (!plan.is_free && (!form.billing_address || !form.billing_city || !form.billing_postal_code))"
                class="w-full"
                size="lg"
                :class="plan.is_free ? 'bg-green-600 hover:bg-green-700' : 'bg-medroid-teal hover:bg-medroid-teal/90'"
              >
                <span v-if="loading">{{ plan.is_free ? 'Activating...' : 'Processing...' }}</span>
                <span v-else>{{ plan.is_free ? 'Activate Free Plan' : `Subscribe for ${plan.formatted_price}/${plan.interval}` }}</span>
              </Button>

              <!-- Security Notice -->
              <div v-if="!plan.is_free" class="text-center text-sm text-gray-500">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span>Secured by Stripe</span>
                </div>
                <p class="mt-1">Your payment information is encrypted and secure</p>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      <!-- Terms and Conditions -->
      <div class="mt-8 text-center text-sm text-gray-500">
        <p>
          By subscribing, you agree to our 
          <a href="#" class="text-teal-600 hover:text-teal-500">Terms of Service</a> 
          and 
          <a href="#" class="text-teal-600 hover:text-teal-500">Privacy Policy</a>.
          You can cancel your subscription at any time.
        </p>
      </div>
    </div>
  </AppLayout>
</template>
