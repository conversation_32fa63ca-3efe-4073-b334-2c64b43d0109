<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6">
        <!-- Success Icon with Animation -->
        <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4">
          <div class="relative">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-green-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <!-- Animated ring -->
            <div class="absolute inset-0 w-16 h-16 border-4 border-green-200 rounded-full animate-ping"></div>
          </div>
        </div>

        <!-- Success Content -->
        <div class="text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            {{ title || 'Success!' }}
          </h3>
          <p class="text-gray-600 mb-6">
            {{ message || 'Your action was completed successfully.' }}
          </p>

          <!-- Success Details -->
          <div v-if="details && details.length > 0" class="mb-6 space-y-2">
            <div 
              v-for="(detail, index) in details" 
              :key="index"
              class="flex items-center justify-between p-2 bg-green-50 rounded-md"
            >
              <span class="text-sm text-green-800">{{ detail.label }}</span>
              <span class="text-sm font-medium text-green-900">{{ detail.value }}</span>
            </div>
          </div>

          <!-- Download Links -->
          <div v-if="downloads && downloads.length > 0" class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Downloads:</h4>
            <div class="space-y-2">
              <a 
                v-for="download in downloads" 
                :key="download.url"
                :href="download.url"
                :download="download.filename"
                class="flex items-center justify-center gap-2 p-2 text-sm text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                {{ download.label || download.filename }}
              </a>
            </div>
          </div>

          <!-- Next Actions -->
          <div v-if="nextActions && nextActions.length > 0" class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">What's next?</h4>
            <div class="space-y-2">
              <button 
                v-for="action in nextActions" 
                :key="action.id"
                @click="executeAction(action)"
                class="w-full p-2 text-sm text-left text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
              >
                {{ action.label }}
              </button>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <button 
              v-if="primaryAction"
              @click="executePrimaryAction"
              class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {{ primaryAction.label }}
            </button>
            
            <button 
              v-if="secondaryAction"
              @click="executeSecondaryAction"
              class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              {{ secondaryAction.label }}
            </button>
            
            <button 
              @click="close"
              class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              {{ closeLabel || 'Close' }}
            </button>
          </div>

          <!-- Auto-close Timer -->
          <div v-if="autoClose && autoCloseTime > 0" class="mt-4 text-sm text-gray-500">
            Auto-closing in {{ autoCloseTime }} seconds...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  show: Boolean,
  title: String,
  message: String,
  details: Array, // [{ label: 'Items processed', value: '5' }]
  downloads: Array, // [{ url: '', filename: '', label: '' }]
  nextActions: Array, // [{ id: 'action1', label: 'Do something', action: function }]
  primaryAction: Object, // { label: 'Continue', action: function }
  secondaryAction: Object, // { label: 'View Details', action: function }
  closeLabel: String,
  autoClose: {
    type: Boolean,
    default: false
  },
  autoCloseDelay: {
    type: Number,
    default: 5 // seconds
  }
})

const emit = defineEmits(['close', 'action'])

const autoCloseTime = ref(0)
let autoCloseTimer = null

const close = () => {
  clearAutoCloseTimer()
  emit('close')
}

const executeAction = (action) => {
  emit('action', action)
  if (action.action && typeof action.action === 'function') {
    action.action()
  }
}

const executePrimaryAction = () => {
  if (props.primaryAction) {
    executeAction(props.primaryAction)
  }
}

const executeSecondaryAction = () => {
  if (props.secondaryAction) {
    executeAction(props.secondaryAction)
  }
}

const startAutoCloseTimer = () => {
  if (props.autoClose && props.autoCloseDelay > 0) {
    autoCloseTime.value = props.autoCloseDelay
    autoCloseTimer = setInterval(() => {
      autoCloseTime.value--
      if (autoCloseTime.value <= 0) {
        close()
      }
    }, 1000)
  }
}

const clearAutoCloseTimer = () => {
  if (autoCloseTimer) {
    clearInterval(autoCloseTimer)
    autoCloseTimer = null
  }
  autoCloseTime.value = 0
}

onMounted(() => {
  if (props.show) {
    startAutoCloseTimer()
  }
})

onUnmounted(() => {
  clearAutoCloseTimer()
})

// Watch for show prop changes
watch(() => props.show, (newVal) => {
  if (newVal) {
    startAutoCloseTimer()
  } else {
    clearAutoCloseTimer()
  }
})
</script>
