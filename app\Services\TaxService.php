<?php

namespace App\Services;

use App\Models\Tax;
use App\Repositories\Interfaces\TaxRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class TaxService
{
    public function __construct(
        private TaxRepositoryInterface $taxRepository
    ) {}

    public function getAllByClinic(int $clinicId): Collection
    {
        return $this->taxRepository->getAllByClinic($clinicId);
    }

    public function getActiveByClinic(int $clinicId): Collection
    {
        return $this->taxRepository->getActiveByClinic($clinicId);
    }

    public function createTax(array $data, int $clinicId): Tax
    {
        $data['clinic_id'] = $clinicId;
        return $this->taxRepository->create($data);
    }

    public function updateTax(Tax $tax, array $data, int $userClinicId): Tax
    {
        if ($tax->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to tax', 403);
        }

        return $this->taxRepository->update($tax, $data);
    }

    public function deleteTax(Tax $tax, int $userClinicId): bool
    {
        if ($tax->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to tax', 403);
        }

        return $this->taxRepository->delete($tax);
    }

    public function getTaxById(int $id, int $userClinicId): Tax
    {
        $tax = $this->taxRepository->findById($id);
        
        if (!$tax) {
            throw new \Exception('Tax not found', 404);
        }

        if ($tax->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to tax', 403);
        }

        return $tax;
    }

    public function calculateTaxesForAmount(float $amount, int $clinicId): array
    {
        $taxes = $this->getActiveByClinic($clinicId);
        $taxData = [];
        $totalTaxAmount = 0;

        foreach ($taxes as $tax) {
            $taxAmount = $tax->calculateTaxAmount($amount);
            
            if ($taxAmount > 0) {
                $taxInfo = [
                    'id' => $tax->id,
                    'name' => $tax->name,
                    'type' => $tax->type,
                    'rate' => $tax->rate,
                    'amount' => $taxAmount,
                    'is_inclusive' => $tax->is_inclusive,
                ];
                
                $taxData[] = $taxInfo;
                $totalTaxAmount += $taxAmount;
            }
        }

        return [
            'tax_data' => $taxData,
            'total_tax_amount' => $totalTaxAmount,
        ];
    }
}
