<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $document_title ?? 'Medical Document' }} - {{ $clinic->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .letterhead-header {
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .letterhead-header img {
            max-height: 100px;
            margin-bottom: 10px;
        }

        .letterhead-header h1 {
            color: #333;
            margin: 0 0 15px 0;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .letterhead-header .clinic-info {
            margin-top: 10px;
            font-size: 12px;
            color: #555;
            line-height: 1.6;
        }

        .letterhead-header .clinic-info .address-line {
            margin: 5px 0;
        }

        .letterhead-header .clinic-info .contact-line {
            margin: 8px 0;
            font-weight: 500;
        }

        .default-header {
            padding: 20px 0;
        }

        .default-header .clinic-logo {
            margin-bottom: 15px;
        }

        .default-header .clinic-logo img {
            max-height: 80px;
            border-radius: 8px;
        }

        .default-header .clinic-name {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0 0 20px 0;
            letter-spacing: 0.5px;
        }

        .default-header .clinic-address {
            font-size: 14px;
            color: #34495e;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .default-header .clinic-contact {
            font-size: 13px;
            color: #7f8c8d;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .default-header .clinic-contact .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .document-content {
            min-height: 500px;
            margin-bottom: 50px;
        }
        
        .letterhead-footer {
            border-top: 1px solid #ccc;
            padding-top: 20px;
            margin-top: 50px;
            font-size: 11px;
            color: #666;
            text-align: center;
        }

        .letterhead-footer img {
            max-height: 50px;
            margin-bottom: 10px;
        }

        .default-footer {
            padding: 15px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 40px;
        }

        .default-footer .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 11px;
            color: #6c757d;
        }

        .default-footer .generation-info {
            font-style: italic;
        }

        .default-footer .clinic-license {
            font-weight: 500;
        }

        @media (max-width: 600px) {
            .default-footer .footer-content {
                flex-direction: column;
                text-align: center;
            }
        }
        
        .document-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .document-info .left {
            font-weight: bold;
        }
        
        .document-info .right {
            text-align: right;
            font-size: 11px;
        }
        
        .patient-details {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .patient-details h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .patient-details p {
            margin: 5px 0;
        }
        
        .provider-signature {
            margin-top: 50px;
            text-align: right;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        .provider-signature img {
            max-height: 60px;
            margin: 10px 0;
        }
        
        .provider-signature p {
            margin: 2px 0;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .letterhead-header {
                page-break-inside: avoid;
            }
            
            .letterhead-footer {
                position: fixed;
                bottom: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    {{-- Dynamic Header --}}
    <div class="letterhead-header">
        @php
            $letterheadSettings = $clinic->letterheadSettings ?? null;
        @endphp

        @if($letterheadSettings && $letterheadSettings->hasCustomHeader())
            @if($letterheadSettings->header_type === 'image')
                <img src="{{ $letterheadSettings->getHeaderContent() }}" alt="Clinic Header">
            @else
                {!! $letterheadSettings->getHeaderContent() !!}
            @endif
        @else
            {{-- Professional Default Header --}}
            <div class="default-header">
                @if($clinic->logo)
                    <div class="clinic-logo">
                        <img src="{{ asset('storage/' . $clinic->logo) }}" alt="{{ $clinic->name }} Logo">
                    </div>
                @endif

                <h1 class="clinic-name">{{ $clinic->name ?? 'Medical Clinic' }}</h1>

                @if($clinic->address || $clinic->city || $clinic->state || $clinic->postal_code)
                    <div class="clinic-address">
                        @if($clinic->address)
                            {{ $clinic->address }}@if($clinic->city || $clinic->state || $clinic->postal_code),@endif
                        @endif
                        @if($clinic->city)
                            {{ $clinic->city }}@if($clinic->state || $clinic->postal_code),@endif
                        @endif
                        @if($clinic->state)
                            {{ $clinic->state }}
                        @endif
                        @if($clinic->postal_code)
                            {{ $clinic->postal_code }}
                        @endif
                    </div>
                @endif

                @if($clinic->phone || $clinic->email || $clinic->website)
                    <div class="clinic-contact">
                        @if($clinic->phone)
                            <div class="contact-item">
                                <span>📞</span>
                                <span>{{ $clinic->phone }}</span>
                            </div>
                        @endif
                        @if($clinic->email)
                            <div class="contact-item">
                                <span>✉️</span>
                                <span>{{ $clinic->email }}</span>
                            </div>
                        @endif
                        @if($clinic->website)
                            <div class="contact-item">
                                <span>🌐</span>
                                <span>{{ $clinic->website }}</span>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        @endif
    </div>

    {{-- Document Content --}}
    <div class="document-content">
        @yield('document_content')
    </div>

    {{-- Dynamic Footer --}}
    <div class="letterhead-footer">
        @if($letterheadSettings && $letterheadSettings->hasCustomFooter())
            @if($letterheadSettings->footer_type === 'image')
                <img src="{{ $letterheadSettings->getFooterContent() }}" alt="Clinic Footer">
            @else
                {!! $letterheadSettings->getFooterContent() !!}
            @endif
        @else
            {{-- Professional Default Footer --}}
            <div class="default-footer">
                <div class="footer-content">
                    <div class="generation-info">
                        Generated on {{ now()->format('d/m/Y \a\t g:i A') }}
                    </div>

                    @if($clinic->license_number || $clinic->registration_number)
                        <div class="clinic-license">
                            @if($clinic->license_number)
                                License: {{ $clinic->license_number }}
                            @endif
                            @if($clinic->registration_number)
                                @if($clinic->license_number) | @endif
                                Reg: {{ $clinic->registration_number }}
                            @endif
                        </div>
                    @endif

                    <div class="document-security">
                        Document ID: {{ $document_reference ?? 'DOC-' . now()->format('YmdHis') }}
                    </div>
                </div>

                @if(!($letterheadSettings && $letterheadSettings->hasCustomFooter()))
                    <div style="margin-top: 10px; font-size: 10px; color: #adb5bd; font-style: italic;">
                        This document was generated electronically and is valid without signature
                    </div>
                @endif
            </div>
        @endif
    </div>
</body>
</html>
