<template>
  <AppLayout>
    <Head :title="isEditing ? 'Edit Consultation' : 'New Consultation'" />

    <div class="min-h-screen bg-slate-50">
      <!-- Modern Header -->
      <div class="bg-white shadow-sm border-b border-slate-200">
        <!-- Breadcrumb Navigation -->
        <div class="px-4 sm:px-6 lg:px-8 py-3 border-b border-slate-100 bg-slate-50/50">
          <nav class="flex items-center text-sm text-slate-600">
            <button @click="router.visit('/dashboard')" class="hover:text-slate-900 transition-colors font-medium">
              Dashboard
            </button>
            <svg class="mx-2 w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <button @click="router.visit('/consultations')" class="hover:text-slate-900 transition-colors font-medium">
              Consultations
            </button>
            <svg class="mx-2 w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span class="text-slate-900 font-semibold">
              {{ isEditing ? 'Edit Consultation' : 'New Consultation' }}
            </span>
          </nav>
        </div>

        <!-- Compact Header -->
        <div class="px-4 py-3 border-b border-slate-200">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
            <!-- Patient Information Section -->
            <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 min-w-0">
              <div v-if="consultation?.patient" class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 min-w-0">
                <!-- Patient Info -->
                <div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 min-w-0">
                  <h1 class="text-lg font-semibold text-slate-900 truncate">
                    {{ consultation.patient.user?.name || (consultation.patient.first_name + ' ' + consultation.patient.last_name).trim() || consultation.patient.email || 'Unknown Patient' }}
                  </h1>
                  <div class="flex items-center gap-2 flex-wrap">
                    <span class="text-sm text-slate-500">{{ consultation.patient.patient_unique_id || consultation.patient.id }}</span>
                    <!-- Consultation Status Badge -->
                    <span :class="getConsultationStatusClass(consultation.status || 'in_progress')"
                          class="inline-flex items-center px-2 py-1 rounded text-xs font-medium">
                      {{ formatConsultationStatus(consultation.status || 'in_progress') }}
                    </span>
                    <!-- Appointment Link Badge -->
                    <span
                      v-if="consultation.appointment_id"
                      class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 cursor-pointer hover:bg-blue-200 transition-colors"
                      @click="viewAppointment(consultation.appointment_id)"
                      title="View Related Appointment"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                      </svg>
                      Appointment
                    </span>
                  </div>
                </div>
                <div class="text-sm text-slate-500">
                  {{ formatDate(consultation.consultation_date) }}
                </div>
              </div>

              <!-- New Consultation State -->
              <div v-else>
                <h1 class="text-lg font-semibold text-slate-900">New Consultation</h1>
              </div>
            </div>
            <!-- Compact Action Buttons - Desktop Version -->
            <div class="hidden lg:flex flex-wrap items-center gap-1 sm:gap-2 w-full sm:w-auto">
              <button @click="saveConsultationExplicit" :disabled="saving"
                class="flex items-center gap-1 px-2 sm:px-3 py-1.5 text-xs sm:text-sm font-medium bg-teal-600 text-white rounded hover:bg-teal-700 disabled:opacity-50 transition-colors flex-1 sm:flex-initial">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                <span>{{ saving ? 'Saving...' : 'Save' }}</span>
              </button>

              <button @click="completeConsultation" :disabled="saving || !isEditing"
                class="flex items-center gap-1 px-2 sm:px-3 py-1.5 text-xs sm:text-sm font-medium bg-emerald-600 text-white rounded hover:bg-emerald-700 disabled:opacity-50 transition-colors flex-1 sm:flex-initial">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Complete</span>
              </button>

              <button @click="printConsultation"
                class="flex items-center gap-1 px-2 sm:px-3 py-1.5 text-xs sm:text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded hover:bg-slate-50 transition-colors flex-1 sm:flex-initial">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6 9 6 2 18 2 18 9"></polyline>
                  <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                  <rect x="6" y="14" width="12" height="8"></rect>
                </svg>
                <span>Print</span>
              </button>

              <button @click="openModal('followup')"
                class="flex items-center gap-1 px-2 sm:px-3 py-1.5 text-xs sm:text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors flex-1 sm:flex-initial">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M8 2v4"/>
                  <path d="M16 2v4"/>
                  <rect width="18" height="18" x="3" y="4" rx="2"/>
                  <path d="M3 10h18"/>
                  <path d="M8 14h.01"/>
                  <path d="M12 14h.01"/>
                  <path d="M16 14h.01"/>
                  <path d="M8 18h.01"/>
                  <path d="M12 18h.01"/>
                </svg>
                <span>Follow-up</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Compact Status Bar -->
      <div class="bg-slate-50 border-b border-slate-200 px-2 sm:px-4 py-2">
        <!-- Mobile Action Buttons - Only show on mobile -->
        <div class="lg:hidden mb-3">
          <div class="flex flex-wrap gap-2">
            <button @click="saveConsultationExplicit" :disabled="saving"
              class="flex-1 min-w-0 px-3 py-2 text-sm font-medium bg-teal-600 text-white rounded hover:bg-teal-700 disabled:opacity-50 transition-colors">
              {{ saving ? 'Saving...' : 'Save' }}
            </button>
            <button @click="completeConsultation" :disabled="saving || !isEditing"
              class="flex-1 min-w-0 px-3 py-2 text-sm font-medium bg-emerald-600 text-white rounded hover:bg-emerald-700 disabled:opacity-50 transition-colors">
              Complete
            </button>
            <button @click="printConsultation"
              class="flex-1 min-w-0 px-3 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded hover:bg-slate-50 transition-colors">
              Print
            </button>
            <button @click="openModal('followup')"
              class="flex-1 min-w-0 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors">
              Follow-up
            </button>
          </div>
        </div>

        <!-- Timer, Save Status, and AI Scribe Row -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
          <!-- Timer and Save Status in one row -->
          <div class="flex items-center gap-3 sm:gap-6 flex-wrap">
            <!-- Timer -->
            <div class="flex items-center gap-2 text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-500">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span class="font-medium text-slate-900">{{ formattedTime }}</span>
            </div>

            <!-- Save Status -->
            <div class="flex items-center gap-2">
              <span v-if="saving" class="text-sm text-teal-600">
                Saving...
              </span>
              <span v-else-if="hasUnsavedChanges" class="text-sm text-amber-600">
                Unsaved changes
              </span>
              <span v-else-if="lastSaved" class="text-sm text-emerald-600">
                Saved {{ formatTimeAgo(lastSaved) }}
              </span>
            </div>
          </div>

          <!-- AI Scribe -->
          <div class="w-full sm:w-auto">
            <AIScribe
              :encounter-id="consultationId || 'temp'"
              @records-updated="handleAIRecordsUpdated"
              @ai-populate="handleAIPopulate"
            />
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 overflow-auto">
        <div class="flex flex-col lg:flex-row h-full">
          <!-- Main Content Area -->
          <div class="flex-1 p-4 space-y-4">
            <!-- Vital Signs -->
            <VitalSignsCard
              :instance-id="'vital-1'"
              :is-first-instance="true"
              :encounter-id="consultationId || 'temp'"
              :vital-signs-data="consultationData.vital_signs"
              @update:vitals="updateVitalSigns"
              @save:success="saveVitalSigns"
              @clone="cloneVitalSigns"
              @remove="removeVitalSigns"
              @ai-approved="approveAIVitals"
            />

            <!-- Consultation Forms -->
            <div class="space-y-4">
              <MedicalRecordForm
                v-for="(form, index) in mainConsultationForms"
                :key="form.instanceId"
                :form="form"
                :display-plus-btn="index === 0"
                :ai-data="aiData"
                :approved-instances="approvedInstances"
                @update:content="updateFormContent"
                @save="saveFormContent"
                @clone="cloneForm"
                @ai-approved="approveAIContent"
              />
            </div>

            <!-- Quick Templates Section -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
              <div class="px-4 py-3 bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200">
                <div class="flex items-center gap-2">
                  <div class="w-6 h-6 bg-teal-500 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <h3 class="text-sm font-semibold text-teal-900">Quick Add Sections</h3>
                </div>
              </div>
              <div class="p-4">
                <ConsultationAdditionalTabs
                  :additional-tabs="consultationData.additional_tabs || {}"
                  :template="{}"
                  @update="updateAdditionalTabs"
                  @add-tab="addAdditionalTab"
                  @remove-tab="removeAdditionalTab"
                  @add-entry="addAdditionalTabEntry"
                  @remove-entry="removeAdditionalTabEntry"
                />
              </div>
            </div>

            <!-- Prescriptions -->
            <ApptPrescription
              :encounter-id="consultationId || 'temp'"
              :is-encounter-temp="!consultationId"
            />
          </div>

          <!-- Enhanced Sidebar -->
          <div class="w-full lg:w-1/2 xl:w-2/5 2xl:w-1/3 bg-white border-t lg:border-t-0 lg:border-l border-slate-200 p-4 lg:p-6 space-y-4 lg:space-y-6">
            <!-- Recent Vitals History -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
              <div class="px-4 py-3 bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200">
                <div class="flex items-center gap-2">
                  <div class="w-6 h-6 bg-teal-500 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                    </svg>
                  </div>
                  <h3 class="text-sm font-semibold text-teal-900">Recent Vitals History</h3>
                </div>
              </div>
              <div class="p-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-slate-50 rounded-lg p-3">
                    <div class="text-xs font-medium text-slate-600 mb-1">Temperature</div>
                    <div class="text-lg font-bold text-slate-900">12</div>
                    <div class="text-xs text-slate-500">Recorded 3 months ago</div>
                  </div>
                  <div class="bg-slate-50 rounded-lg p-3">
                    <div class="text-xs font-medium text-slate-600 mb-1">Pulse</div>
                    <div class="text-lg font-bold text-slate-900">12</div>
                    <div class="text-xs text-slate-500">Recorded 3 months ago</div>
                  </div>
                  <div class="bg-slate-50 rounded-lg p-3">
                    <div class="text-xs font-medium text-slate-600 mb-1">Blood Pressure</div>
                    <div class="text-lg font-bold text-slate-900">12</div>
                    <div class="text-xs text-slate-500">Recorded 3 months ago</div>
                  </div>
                  <div class="bg-slate-50 rounded-lg p-3">
                    <div class="text-xs font-medium text-slate-600 mb-1">Respiratory Rate</div>
                    <div class="text-lg font-bold text-slate-900">12</div>
                    <div class="text-xs text-slate-500">Recorded 3 months ago</div>
                  </div>
                  <div class="bg-slate-50 rounded-lg p-3 col-span-2">
                    <div class="text-xs font-medium text-slate-600 mb-1">Saturation</div>
                    <div class="text-lg font-bold text-slate-900">12</div>
                    <div class="text-xs text-slate-500">Recorded 3 months ago</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Previous Visits -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
              <div class="px-4 py-3 bg-gradient-to-r from-blue-50 to-blue-100 border-b border-blue-200">
                <div class="flex items-center gap-2">
                  <div class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M8 2v4"/>
                      <path d="M16 2v4"/>
                      <rect width="18" height="18" x="3" y="4" rx="2"/>
                      <path d="M3 10h18"/>
                    </svg>
                  </div>
                  <h3 class="text-sm font-semibold text-blue-900">Previous Visits</h3>
                </div>
              </div>
              <div class="p-4">
                <div class="bg-slate-50 rounded-lg p-3 hover:bg-slate-100 transition-colors cursor-pointer">
                  <div class="flex items-start justify-between">
                    <div>
                      <div class="font-medium text-slate-900 mb-1">General Visit</div>
                      <div class="text-sm text-slate-600 mb-1">Doctor: Raja Mohan</div>
                      <div class="text-xs text-slate-500">3 months ago</div>
                    </div>
                    <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Files -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
              <div class="px-4 py-3 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-purple-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                        <polyline points="14 2 14 8 20 8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                        <polyline points="10 9 9 9 8 9"/>
                      </svg>
                    </div>
                    <h3 class="text-sm font-semibold text-purple-900">Files</h3>
                  </div>
                  <div class="flex items-center gap-2">
                    <button class="text-xs text-teal-600 hover:text-teal-700 font-medium px-2 py-1 hover:bg-teal-50 rounded transition-colors">
                      Upload
                    </button>
                    <button class="text-xs text-teal-600 hover:text-teal-700 font-medium px-2 py-1 hover:bg-teal-50 rounded transition-colors">
                      Mobile Upload
                    </button>
                    <button class="text-xs bg-slate-900 hover:bg-slate-800 text-white px-3 py-1.5 rounded-lg font-medium transition-colors">
                      Generate Letter
                    </button>
                  </div>
                </div>
              </div>
              <div class="p-4">
                <div class="bg-slate-50 rounded-lg p-3 hover:bg-slate-100 transition-colors">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                      <div class="w-3 h-3 bg-emerald-500 rounded"></div>
                    </div>
                    <div class="flex-1">
                      <div class="font-medium text-slate-900">test</div>
                      <div class="text-xs text-slate-500">Lab Report</div>
                    </div>
                    <div class="flex items-center gap-2">
                      <button class="p-1.5 text-slate-400 hover:text-teal-600 hover:bg-teal-50 rounded transition-colors">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      </button>
                      <button class="p-1.5 text-slate-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                          <polyline points="7 10 12 15 17 10"/>
                          <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                      </button>
                      <button class="p-1.5 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="3 6 5 6 21 6"/>
                          <path d="m19 6-2 14a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2L5 6"/>
                          <path d="m10 11 6 6"/>
                          <path d="m16 11-6 6"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <PrescriptionModal
      v-if="showPrescriptionModal && consultationId"
      :consultation-id="consultationId"
      @close="closeModal('prescription')"
      @saved="() => { loadConsultation(); closeModal('prescription'); }"
    />

    <DocumentUploadModal
      v-if="showDocumentModal && consultationId"
      :consultation-id="consultationId"
      @close="closeModal('document')"
      @uploaded="() => { loadConsultation(); closeModal('document'); }"
    />

    <ScheduleFollowupModal
      :show="showFollowupModal"
      :patient-id="patientId"
      :consultation-id="consultationId"
      :provider-id="consultation?.provider?.id"
      @close="showFollowupModal = false"
      @scheduled="handleFollowupScheduled"
    />

    <!-- Lab Request Modal -->
    <div v-if="showLabRequestModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <LabRequestForm
          :consultation-id="consultationId ? Number(consultationId) : undefined"
          :patient-id="patientId ? Number(patientId) : undefined"
          @close="closeModal('labRequest')"
          @request-created="handleLabRequestCreated"
        />
      </div>
    </div>

    <!-- Consultation Completion Modal -->
    <div v-if="showCompletionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-md w-full mx-4">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Complete Consultation</h3>
            <button @click="showCompletionModal = false" class="text-gray-500 hover:text-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="mb-6">
            <p class="text-gray-600 mb-4">
              Are you ready to complete this consultation? This will mark the consultation as finished.
            </p>

            <div v-if="consultation?.patient?.user?.email" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-start gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-blue-600 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22 6 12 13 2 6"/>
                </svg>
                <div>
                  <h4 class="font-medium text-blue-900 mb-1">Send Summary to Patient</h4>
                  <p class="text-sm text-blue-700">
                    Would you like to send a consultation summary to {{ consultation.patient.user.email }}?
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="flex gap-3">
            <button
              @click="finalizeConsultation(false)"
              :disabled="saving"
              class="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50 transition-colors"
            >
              Complete Only
            </button>
            <button
              v-if="consultation?.patient?.user?.email"
              @click="finalizeConsultation(true)"
              :disabled="saving"
              class="flex-1 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              Complete & Send Summary
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Keyboard Shortcuts Help Modal -->
    <div v-if="showKeyboardHelp" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-md w-full mx-4">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Keyboard Shortcuts</h3>
            <button @click="showKeyboardHelp = false" class="text-gray-500 hover:text-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div>
              <h4 class="font-medium text-gray-900 mb-2">General Actions</h4>
              <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Save consultation</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+S</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Complete consultation</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+Enter</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Print consultation</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+P</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Close modals</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Esc</kbd>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-medium text-gray-900 mb-2">Quick Actions</h4>
              <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Schedule follow-up</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+F</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Order lab tests</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+L</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Focus medical forms</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+M</kbd>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-medium text-gray-900 mb-2">Navigation</h4>
              <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Vital signs</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+1</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Present concerns</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+2</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Present history</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+3</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Examination</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+4</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Assessment & plan</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+5</kbd>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Prescriptions</span>
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Alt+6</kbd>
                </div>
              </div>
            </div>

            <div class="pt-2 border-t">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Show this help</span>
                <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+?</kbd>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import { Head } from '@inertiajs/vue3'
import AppLayout from '../../layouts/AppLayout.vue'

// Import consultation components
import VitalSignsCard from '../../components/consultation/VitalSignsCard.vue'
import MedicalRecordForm from '../../components/consultation/MedicalRecordForm.vue'
import ApptPrescription from '../../components/consultation/ApptPrescription.vue'
import ConsultationAdditionalTabs from '../../components/consultation/ConsultationAdditionalTabs.vue'
import PrescriptionModal from '../../components/consultation/PrescriptionModal.vue'
import DocumentUploadModal from '../../components/consultation/DocumentUploadModal.vue'
import PreviousVisitsCard from '../../components/consultation/PreviousVisitsCard.vue'
import AIScribe from '../../components/consultation/AIScribe.vue'
import FileManagement from '../../components/consultation/FileManagement.vue'
import RecentVitals from '../../components/consultation/RecentVitals.vue'
import ScheduleFollowupModal from '../../components/consultation/ScheduleFollowupModal.vue'
import LabRequestForm from '../../components/TDL/LabRequestForm.vue'

// Import composables
import { useConsultationTemplate } from '../../composables/useConsultationTemplate'
import { useConsultationApi } from '../../composables/useConsultationApi'
import axios from 'axios'
import { useToast } from 'vue-toastification'

interface Props {
  consultationId?: string
  appointmentId?: string
  patientId?: string
}

interface Patient {
  id: number
  first_name?: string
  last_name?: string
  email?: string
  patient_unique_id?: string
  user?: {
    name: string
    email: string
  }
}

interface Provider {
  id: number
  user?: {
    name: string
    email: string
  }
}

interface ConsultationData {
  vital_signs?: Record<string, any>
  main_tabs?: Record<string, ConsultationEntry[]>
  additional_tabs?: Record<string, ConsultationEntry[]>
}

const props = defineProps<Props>()

// Initialize toast
const toast = useToast()

// Template and consultation data
const {
  template,
  loadTemplate,
  initializeConsultationData,
  addTabEntry,
  removeTabEntry,
  calculateBMI
} = useConsultationTemplate()

const {
  getConsultation,
  updateConsultation,
  createConsultation
} = useConsultationApi()

// Component state
const consultation = ref<{
  id?: number
  patient?: Patient
  provider?: Provider
  appointment_id?: number
  consultation_date?: string
  consultation_mode?: string
  is_telemedicine?: boolean
  status?: string
} | null>(null)

const consultationData = ref<ConsultationData>({
  vital_signs: {},
  main_tabs: {},
  additional_tabs: {}
})

const saving = ref(false)
const showPrescriptionModal = ref(false)
const showDocumentModal = ref(false)
const showFollowupModal = ref(false)
const showLabRequestModal = ref(false)
const showCompletionModal = ref(false)
const showKeyboardHelp = ref(false)

// Modal state management
const modalStates = ref({
  prescription: false,
  document: false,
  followup: false,
  labRequest: false
})

const openModal = (modalType: 'prescription' | 'document' | 'followup' | 'labRequest') => {
  // Close other modals first (but more efficiently)
  showPrescriptionModal.value = false
  showDocumentModal.value = false
  showFollowupModal.value = false
  showLabRequestModal.value = false
  showCompletionModal.value = false
  showKeyboardHelp.value = false

  // Reset all modal states
  modalStates.value.prescription = false
  modalStates.value.document = false
  modalStates.value.followup = false
  modalStates.value.labRequest = false

  // Open the requested modal
  switch (modalType) {
    case 'prescription':
      showPrescriptionModal.value = true
      modalStates.value.prescription = true
      break
    case 'document':
      showDocumentModal.value = true
      modalStates.value.document = true
      break
    case 'followup':
      showFollowupModal.value = true
      modalStates.value.followup = true
      break
    case 'labRequest':
      showLabRequestModal.value = true
      modalStates.value.labRequest = true
      break
  }
}

const closeModal = (modalType: 'prescription' | 'document' | 'followup' | 'labRequest') => {
  switch (modalType) {
    case 'prescription':
      showPrescriptionModal.value = false
      modalStates.value.prescription = false
      break
    case 'document':
      showDocumentModal.value = false
      modalStates.value.document = false
      break
    case 'followup':
      showFollowupModal.value = false
      modalStates.value.followup = false
      break
    case 'labRequest':
      showLabRequestModal.value = false
      modalStates.value.labRequest = false
      break
  }
}

const closeAllModals = () => {
  showPrescriptionModal.value = false
  showDocumentModal.value = false
  showFollowupModal.value = false
  showLabRequestModal.value = false
  showCompletionModal.value = false
  showKeyboardHelp.value = false

  // Update individual properties instead of reassigning the entire object
  modalStates.value.prescription = false
  modalStates.value.document = false
  modalStates.value.followup = false
  modalStates.value.labRequest = false
}

// Timer and auto-save functionality
const startTime = ref(new Date())
const currentTime = ref(new Date())
const lastSaved = ref<Date | null>(null)
const timerInterval = ref<any>(null)
// Track unsaved changes for user feedback
const hasUnsavedChanges = ref(false)

// Define types for consultation forms
interface ConsultationEntry {
  id: number
  content: string
  created_at: string
}

interface ConsultationForm {
  instanceId: string
  type: string
  title: string
  entries: ConsultationEntry[]
  templates: string[]
}

// Main consultation forms for medical record keeping
const mainConsultationForms = ref<ConsultationForm[]>([
  {
    instanceId: 'concerns-1',
    type: 'concerns',
    title: 'Present Concerns',
    entries: [],
    templates: ['Chest pain', 'Shortness of breath', 'Headache', 'Abdominal pain']
  },
  {
    instanceId: 'history-1',
    type: 'history',
    title: 'Present History',
    entries: [],
    templates: ['Acute onset', 'Gradual onset', 'Chronic condition']
  },
  {
    instanceId: 'examination-1',
    type: 'examination',
    title: 'Examination',
    entries: [],
    templates: [
      'CVS - Heart sounds, rhythm, peripheral pulses, edema',
      'Resp - Breath sounds, percussion, chest movement, respiratory effort',
      'Gastro - Abdomen inspection, palpation, bowel sounds, tenderness',
      'Neuro - Mental status, cranial nerves, motor function, sensory function, reflexes',
      'MSK - Range of motion, strength, tenderness, deformity',
      'Derm - Skin appearance, lesions, distribution, associated symptoms',
      'ENT - Ears, nose, throat, lymph nodes'
    ]
  },
  {
    instanceId: 'plan-1',
    type: 'plan',
    title: 'Assessment & Plan',
    entries: [],
    templates: ['Investigations', 'Treatment', 'Follow-up', 'Referral']
  }
])

const aiData = ref(null)
const approvedInstances = ref({})

// Computed
const isEditing = computed(() => !!props.consultationId)

const patientId = computed((): string | number | null => {
  if (!consultation.value) return props.patientId || null

  return consultation.value.patient?.id ||
         props.patientId ||
         null
})

// Progress tracking
const consultationSections = computed(() => [
  {
    key: 'vital_signs',
    title: 'Vital Signs',
    required: false,
    completed: hasVitalSigns.value
  },
  {
    key: 'concerns',
    title: 'Present Concerns',
    required: true,
    completed: hasConcerns.value
  },
  {
    key: 'history',
    title: 'Present History',
    required: false,
    completed: hasHistory.value
  },
  {
    key: 'examination',
    title: 'Examination',
    required: false,
    completed: hasExamination.value
  },
  {
    key: 'plan',
    title: 'Assessment & Plan',
    required: false,
    completed: hasPlan.value
  },
  {
    key: 'prescriptions',
    title: 'Prescriptions',
    required: false,
    completed: hasPrescriptions.value
  }
])

const consultationProgress = computed(() => {
  const totalSections = consultationSections.value.length
  const completedSections = consultationSections.value.filter(s => s.completed).length
  return Math.round((completedSections / totalSections) * 100)
})

// Section completion checks
const hasVitalSigns = computed(() => {
  const vitals = consultationData.value.vital_signs || {}
  return Object.values(vitals).some(value => value && value.toString().trim() !== '')
})

const hasConcerns = computed(() => {
  const concernsForm = mainConsultationForms.value.find(f => f.type === 'concerns')
  return concernsForm && concernsForm.entries.length > 0 &&
         concernsForm.entries.some(entry => entry.content && entry.content.trim() !== '')
})

const hasHistory = computed(() => {
  const historyForm = mainConsultationForms.value.find(f => f.type === 'history')
  return historyForm && historyForm.entries.length > 0 &&
         historyForm.entries.some(entry => entry.content && entry.content.trim() !== '')
})

const hasExamination = computed(() => {
  const examForm = mainConsultationForms.value.find(f => f.type === 'examination')
  return examForm && examForm.entries.length > 0 &&
         examForm.entries.some(entry => entry.content && entry.content.trim() !== '')
})

const hasPlan = computed(() => {
  const planForm = mainConsultationForms.value.find(f => f.type === 'plan')
  return planForm && planForm.entries.length > 0 &&
         planForm.entries.some(entry => entry.content && entry.content.trim() !== '')
})

const hasPrescriptions = computed(() => {
  // This would need to be connected to the prescription component's data
  // For now, we'll assume it's false until we can integrate with the prescription component
  return false
})

// Methods
const loadConsultation = async () => {
  if (props.consultationId) {
    try {
      const response = await getConsultation(Number(props.consultationId))

      if (!response?.data) {
        throw new Error('No consultation data received')
      }

      consultation.value = response.data
      consultationData.value = {
        vital_signs: response.data.vital_signs || {},
        main_tabs: response.data.main_tabs || {},
        additional_tabs: response.data.additional_tabs || {}
      }

      // Load main consultation forms data from main_tabs
      if (response.data.main_tabs) {
        Object.entries(response.data.main_tabs).forEach(([key, entries]: [string, any]) => {
          const form = mainConsultationForms.value.find(f => f.type === key)
          if (form && Array.isArray(entries)) {
            form.entries = entries
          }
        })
      }
    } catch (error: any) {
      console.error('Error loading consultation:', error)

      // Handle different types of errors
      let errorMessage = 'Failed to load consultation'

      if (error.response?.status === 404) {
        errorMessage = 'Consultation not found'
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to view this consultation'
      } else if (error.response?.status === 401) {
        errorMessage = 'Please log in to view this consultation'
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      toast.error(errorMessage)

      // Redirect to consultations list if consultation not found or access denied
      if (error.response?.status === 404 || error.response?.status === 403) {
        setTimeout(() => {
          window.location.href = '/consultations'
        }, 2000)
      }
    }
  } else if (props.appointmentId) {
    try {
      // Load appointment data to pre-populate consultation
      const response = await axios.get(`/appointments/${props.appointmentId}`)

      if (!response?.data?.data) {
        throw new Error('No appointment data received')
      }

      const appointment = response.data.data

      // Validate appointment data
      if (!appointment.patient) {
        throw new Error('Appointment does not have a valid patient')
      }

      // Create consultation structure with appointment data
      consultation.value = {
        appointment_id: appointment.id,
        patient: appointment.patient,
        provider: appointment.provider,
        consultation_date: appointment.appointment_date,
        consultation_mode: appointment.appointment_type === 'video' ? 'video' as const : 'in_person' as const,
        is_telemedicine: appointment.appointment_type === 'video'
      }

      consultationData.value = initializeConsultationData()
    } catch (error: any) {
      console.error('Error loading appointment:', error)

      let errorMessage = 'Failed to load appointment data'

      if (error.response?.status === 404) {
        errorMessage = 'Appointment not found'
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to access this appointment'
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      toast.error(errorMessage)
      consultationData.value = initializeConsultationData()

      // Redirect if appointment not found
      if (error.response?.status === 404) {
        setTimeout(() => {
          window.location.href = '/appointments'
        }, 2000)
      }
    }
  } else if (props.patientId) {
    try {
      // Load patient data to pre-populate consultation
      const response = await axios.get(`/patients/${props.patientId}`)
      const patient = response.data.data

      // Create consultation structure with patient data
      consultation.value = {
        patient: patient,
        consultation_date: new Date().toISOString().split('T')[0],
        consultation_mode: 'in_person' as const,
        is_telemedicine: false
      }

      consultationData.value = initializeConsultationData()
    } catch (error: any) {
      console.error('Error loading patient:', error)
      const errorMessage = error.response?.data?.message || 'Failed to load patient data'
      toast.error(errorMessage)
      consultationData.value = initializeConsultationData()
    }
  } else {
    // Initialize new consultation
    consultationData.value = initializeConsultationData()
  }
}

// Function to force update all form contents immediately
const forceUpdateAllFormContents = async () => {
  // Trigger blur event on all textareas to force immediate content update
  const textareas = document.querySelectorAll('textarea')
  textareas.forEach(textarea => {
    if (textarea.value && textarea.value.trim()) {
      // Trigger a blur event to force content update
      const event = new Event('blur', { bubbles: true })
      textarea.dispatchEvent(event)
    }
  })

  // Wait for the content updates to process
  await new Promise(resolve => setTimeout(resolve, 100))
}

// Function for explicit save (with validation) - called when user clicks save button
const saveConsultationExplicit = async () => {
  try {
    saving.value = true

    // Force update all form contents first
    await forceUpdateAllFormContents()

    // Calculate BMI if height and weight are provided
    calculateBMI(consultationData.value)

    // Prepare main tabs data from consultation forms
    const mainTabsData: Record<string, any> = {}
    mainConsultationForms.value.forEach(form => {
      if (form.entries.length > 0) {
        mainTabsData[form.type] = form.entries
      }
    })

    // Validate required fields
    const errors: string[] = []

    // Check if Present Concerns (concerns) has content
    const concernsForm = mainConsultationForms.value.find(f => f.type === 'concerns')
    const hasValidConcerns = concernsForm && concernsForm.entries.length > 0

    if (!hasValidConcerns) {
      errors.push('Present Concerns is required')
    }

    if (errors.length > 0) {
      errors.forEach(error => toast.error(error))
      return
    }

    // Proceed with save
    await saveConsultation(false)
    toast.success(isEditing.value ? 'Consultation updated successfully' : 'Consultation created successfully')

  } catch (err) {
    console.error('Error saving consultation:', err)
    toast.error('Failed to save consultation. Please try again.')
  } finally {
    saving.value = false
  }
}

// Function to complete consultation
const completeConsultation = async () => {
  // Show completion modal for options
  showCompletionModal.value = true
}

// Function to actually complete the consultation
const finalizeConsultation = async (sendToPatient = false) => {
  try {
    saving.value = true

    // First save the consultation
    await saveConsultationExplicit()

    // Then update status to completed
    if (props.consultationId) {
      const payload = {
        status: 'completed' as const,
        completed_at: new Date().toISOString()
      }

      await updateConsultation(Number(props.consultationId), payload)

      // If user wants to send to patient, generate and send consultation summary
      if (sendToPatient && consultation.value?.patient?.user?.email) {
        try {
          await axios.post(`/consultations/${props.consultationId}/send-summary`, {
            patient_email: consultation.value.patient.user.email
          })
          toast.success('Consultation completed and summary sent to patient')
        } catch (emailError) {
          console.error('Error sending summary:', emailError)
          toast.warning('Consultation completed but failed to send summary to patient')
        }
      } else {
        toast.success('Consultation completed successfully')
      }

      showCompletionModal.value = false

      // Redirect to consultations list
      router.visit('/consultations')
    }

  } catch (err) {
    console.error('Error completing consultation:', err)
    toast.error('Failed to complete consultation. Please try again.')
  } finally {
    saving.value = false
  }
}

// Function for auto-save (no validation, no toast messages)
const saveConsultation = async (isAutoSave = true) => {
  try {
    if (!isAutoSave) {
      saving.value = true
    }

    // Calculate BMI if height and weight are provided
    calculateBMI(consultationData.value)

    // Prepare main tabs data from consultation forms
    const mainTabsData: Record<string, any> = {}
    mainConsultationForms.value.forEach(form => {
      if (form.entries.length > 0) {
        mainTabsData[form.type] = form.entries
      }
    })

    const payload = {
      ...consultationData.value,
      main_tabs: mainTabsData,
      status: 'in_progress' as const,
      // Include appointment data if creating from appointment
      ...(props.appointmentId && {
        appointment_id: Number(props.appointmentId),
        patient_id: consultation.value?.patient?.id,
        consultation_date: consultation.value?.consultation_date,
        consultation_mode: consultation.value?.consultation_mode as 'video' | 'in_person' | 'phone' | undefined,
        is_telemedicine: consultation.value?.is_telemedicine
      }),
      // Include patient data if creating from patient (KiviCare style)
      ...(props.patientId && {
        patient_id: Number(props.patientId),
        consultation_date: consultation.value?.consultation_date,
        consultation_mode: consultation.value?.consultation_mode as 'video' | 'in_person' | 'phone' | undefined,
        is_telemedicine: consultation.value?.is_telemedicine
      })
    }

    if (isEditing.value && props.consultationId) {
      await updateConsultation(Number(props.consultationId), payload)
      if (!isAutoSave) {
        toast.success('Consultation updated successfully')
      }
    } else {
      const response = await createConsultation(payload)
      if (!isAutoSave) {
        toast.success('Consultation created successfully')
      }
      router.visit(`/consultations/${response.data.id}/edit`)
    }

    lastSaved.value = new Date()
    hasUnsavedChanges.value = false
  } catch (error: any) {
    console.error('Error saving consultation:', error)
    const errorMessage = error.response?.data?.message || 'Failed to save consultation'
    toast.error(errorMessage)
  } finally {
    saving.value = false
  }
}

// Computed properties
const formattedTime = computed(() => {
  // Ensure both times are valid dates
  if (!startTime.value || !currentTime.value ||
      isNaN(startTime.value.getTime()) || isNaN(currentTime.value.getTime())) {
    return '00:00'
  }

  const elapsed = Math.floor((currentTime.value.getTime() - startTime.value.getTime()) / 1000)

  // Ensure elapsed time is not negative or invalid
  if (elapsed < 0 || isNaN(elapsed) || !isFinite(elapsed)) {
    return '00:00'
  }

  const hours = Math.floor(elapsed / 3600)
  const minutes = Math.floor((elapsed % 3600) / 60)
  const seconds = elapsed % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// Helper methods
const formatDate = (date: any) => {
  return new Date(date).toLocaleDateString()
}

const formatConsultationStatus = (status: string) => {
  const statusMap = {
    'draft': 'Draft',
    'in_progress': 'In Progress',
    'completed': 'Completed',
    'cancelled': 'Cancelled'
  }
  return statusMap[status] || status
}

const getConsultationStatusClass = (status: string) => {
  const classMap = {
    'draft': 'bg-slate-100 text-slate-700 border border-slate-200',
    'in_progress': 'bg-teal-100 text-teal-700 border border-teal-200',
    'completed': 'bg-emerald-100 text-emerald-700 border border-emerald-200',
    'cancelled': 'bg-red-100 text-red-700 border border-red-200'
  }
  return classMap[status] || 'bg-slate-100 text-slate-700 border border-slate-200'
}

const getSectionStatusClass = (section: any) => {
  if (section.completed) {
    return 'bg-emerald-500 border-emerald-500'
  } else if (section.required) {
    return 'bg-red-400 border-red-400'
  } else {
    return 'bg-slate-300 border-slate-300'
  }
}

const formatTimeAgo = (date: Date) => {
  // Validate input date
  if (!date || isNaN(date.getTime())) {
    return 'unknown'
  }

  const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000)

  // Ensure seconds is valid
  if (isNaN(seconds) || !isFinite(seconds)) {
    return 'unknown'
  }

  if (seconds < 60) return 'just now'

  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) return `${minutes} min ago`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`

  return date.toLocaleDateString()
}

const printConsultation = () => {
  window.print()
}

// Appointment-related functions
const viewAppointment = (appointmentId: number) => {
  window.location.href = `/appointments/${appointmentId}`
}

// Event handlers for child components

const updateAdditionalTabs = (additionalTabs: Record<string, any>) => {
  consultationData.value.additional_tabs = additionalTabs
  hasUnsavedChanges.value = true
}

const addAdditionalTab = (tabKey: string) => {
  // Create a new object to trigger reactivity
  const currentTabs = consultationData.value.additional_tabs || {}
  const newTabs = { ...currentTabs }

  if (!newTabs[tabKey]) {
    newTabs[tabKey] = []
  }

  consultationData.value.additional_tabs = newTabs
  hasUnsavedChanges.value = true
}

const removeAdditionalTab = (tabKey: string) => {
  if (consultationData.value.additional_tabs) {
    delete consultationData.value.additional_tabs[tabKey]
    hasUnsavedChanges.value = true
  }
}

const addAdditionalTabEntry = (tabKey: string, content: string) => {
  addTabEntry(consultationData.value, 'additional_tabs', tabKey, content)
  hasUnsavedChanges.value = true
}

const removeAdditionalTabEntry = (tabKey: string, entryId: number) => {
  removeTabEntry(consultationData.value, 'additional_tabs', tabKey, entryId)
  hasUnsavedChanges.value = true
}

// Vital signs methods
const updateVitalSigns = (_instanceId: string, vitals: Record<string, any>) => {
  consultationData.value.vital_signs = vitals
  hasUnsavedChanges.value = true
}

const saveVitalSigns = (_instanceId: string, vitals: Record<string, any>) => {
  consultationData.value.vital_signs = vitals
  hasUnsavedChanges.value = true
}

const cloneVitalSigns = () => {
  // Create a copy of current vital signs with new timestamp
  const currentVitals = { ...consultationData.value.vital_signs }
  if (Object.keys(currentVitals).length > 0) {
    // Add timestamp to distinguish from original
    currentVitals.recorded_at = new Date().toISOString()
    consultationData.value.vital_signs = currentVitals
    hasUnsavedChanges.value = true
    toast.success('Vital signs cloned successfully')
  } else {
    toast.warning('No vital signs to clone')
  }
}

const removeVitalSigns = () => {
  // Clear vital signs data
  consultationData.value.vital_signs = {}
  hasUnsavedChanges.value = true
  toast.success('Vital signs removed')
}

const approveAIVitals = () => {
  // Mark AI vitals as approved and save
  if (consultationData.value.vital_signs) {
    consultationData.value.vital_signs.ai_approved = true
    consultationData.value.vital_signs.approved_at = new Date().toISOString()
    hasUnsavedChanges.value = true
  }
  toast.success('AI vitals approved')
}

const updateFormContent = (data: { instanceId: string; content: string }) => {
  const form = mainConsultationForms.value.find(f => f.instanceId === data.instanceId)
  if (form) {
    // Convert content to entry format for consistency
    if (data.content && data.content.trim()) {
      const existingEntry = form.entries.find(entry => entry.id === 1)
      if (existingEntry) {
        existingEntry.content = data.content
      } else {
        form.entries.push({
          id: 1,
          content: data.content,
          created_at: new Date().toISOString()
        })
      }
    }
    // Mark as having unsaved changes
    hasUnsavedChanges.value = true
  }
}

const saveFormContent = (data: { instanceId: string; content: string }) => {
  updateFormContent(data)
  hasUnsavedChanges.value = true
}

const cloneForm = (type: string) => {
  const existingForms = mainConsultationForms.value.filter(f => f.type === type)
  const newInstanceId = `${type}-${existingForms.length + 1}`

  const originalForm = mainConsultationForms.value.find(f => f.type === type)
  if (originalForm) {
    const newForm = {
      ...originalForm,
      instanceId: newInstanceId,
      entries: []
    }
    mainConsultationForms.value.push(newForm)
    toast.success(`New ${type} form added`)
  }
}

const approveAIContent = (data: { instanceId: string }) => {
  approvedInstances.value[data.instanceId] = true
  toast.success('AI content approved')
}

// Removed scheduleFollowUp - now using openModal('followup')

const handleFollowupScheduled = async (appointment: any) => {
  try {
    toast.success(`Follow-up appointment scheduled for ${appointment.appointment_date || 'selected date'}`)
    closeModal('followup')
    // Optionally refresh consultation data to show the new appointment
    if (appointment.id) {
      await loadConsultation()
    }
  } catch (error: any) {
    console.error('Error handling follow-up scheduling:', error)
    toast.error('Failed to schedule follow-up appointment. Please try again.')
  }
}

const handleLabRequestCreated = async (request: any) => {
  try {
    toast.success(`Lab request created successfully (Order: ${request.order_number})`)
    closeModal('labRequest')
    // Optionally refresh consultation data or show the new lab request
    if (request.id) {
      await loadConsultation()
    }
  } catch (error: any) {
    console.error('Error handling lab request created:', error)
    toast.error('Failed to create lab request. Please try again.')
  }
}

// AI Scribe event handlers
const handleAIRecordsUpdated = () => {
  toast.success('AI transcription records updated')
  // Refresh consultation data to get latest AI records
  if (props.consultationId) {
    loadConsultation()
  }
}

const handleAIPopulate = (data: { extractedData: any; rawData: any }) => {
  const populatedSections: string[] = []

  // Update aiData for form population
  aiData.value = data.extractedData

  // Update main consultation forms with AI data using entries structure
  const updateFormWithAIData = (formType: string, content: string) => {
    const form = mainConsultationForms.value.find(f => f.type === formType)
    if (form && content) {
      const existingEntry = form.entries.find(entry => entry.id === 1)
      if (existingEntry) {
        existingEntry.content = content
      } else {
        form.entries.push({
          id: 1,
          content: content,
          created_at: new Date().toISOString()
        })
      }
      populatedSections.push(form.title)
    }
  }

  if (data.extractedData.concerns) {
    updateFormWithAIData('concerns', data.extractedData.concerns)
  }

  if (data.extractedData.history) {
    updateFormWithAIData('history', data.extractedData.history)
  }

  if (data.extractedData.examination) {
    updateFormWithAIData('examination', data.extractedData.examination)
  }

  if (data.extractedData.plan) {
    updateFormWithAIData('plan', data.extractedData.plan)
  }

  // Update vital signs if available
  if (data.extractedData.vital_signs) {
    consultationData.value.vital_signs = {
      ...consultationData.value.vital_signs,
      ...data.extractedData.vital_signs
    }
    populatedSections.push('Vital Signs')
  }

  // Mark as having unsaved changes
  hasUnsavedChanges.value = true

  // Show success message with populated sections
  if (populatedSections.length > 0) {
    toast.success(`AI populated: ${populatedSections.join(', ')}`)
  } else {
    toast.info('AI processing completed, but no data was extracted')
  }
}

// File management event handlers
const handleFilesUpdated = (files: any[]) => {
  if (files && files.length > 0) {
    // Mark as having unsaved changes
    hasUnsavedChanges.value = true
  }
}

// Recent vitals event handlers
const handleVitalSelected = (vital: any) => {
  if (vital && typeof vital === 'object') {
    // Populate current vital signs form with selected vital
    consultationData.value.vital_signs = {
      ...consultationData.value.vital_signs,
      ...vital,
      selected_from_history: true,
      selected_at: new Date().toISOString()
    }
    hasUnsavedChanges.value = true
    toast.success('Previous vital signs loaded')
  } else {
    toast.warning('Invalid vital signs data')
  }
}

// Timer functionality - update every 5 seconds to reduce conflicts
const startTimer = () => {
  // Initialize start time if not set
  if (!startTime.value || isNaN(startTime.value.getTime())) {
    startTime.value = new Date()
  }

  // Update immediately
  currentTime.value = new Date()

  // Then update every 5 seconds instead of every second
  timerInterval.value = setInterval(() => {
    currentTime.value = new Date()
  }, 5000)
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

// Watch for changes in consultation data to track unsaved changes
watch(
  consultationData,
  () => {
    if (consultationData.value && Object.keys(consultationData.value).length > 0) {
      hasUnsavedChanges.value = true
    }
  },
  { deep: true }
)

// Enhanced keyboard event handler for better UX and accessibility
const handleKeydown = (event: KeyboardEvent) => {
  // Close modals on Escape key
  if (event.key === 'Escape') {
    closeAllModals()
    return
  }

  // Handle Ctrl/Cmd key combinations
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        saveConsultationExplicit()
        break
      case 'Enter':
        event.preventDefault()
        if (isEditing.value) {
          completeConsultation()
        }
        break
      case 'p':
        event.preventDefault()
        printConsultation()
        break
      case 'f':
        event.preventDefault()
        openModal('followup')
        break
      case 'l':
        event.preventDefault()
        openModal('labRequest')
        break
      case 'm':
        event.preventDefault()
        // Focus on first medical record form
        const firstTextarea = document.querySelector('textarea')
        if (firstTextarea) {
          firstTextarea.focus()
        }
        break
      case '/':
      case '?':
        event.preventDefault()
        showKeyboardHelp.value = !showKeyboardHelp.value
        break
    }
  }

  // Handle Alt key combinations for quick navigation
  if (event.altKey) {
    switch (event.key) {
      case '1':
        event.preventDefault()
        focusSection('vital-signs')
        break
      case '2':
        event.preventDefault()
        focusSection('concerns')
        break
      case '3':
        event.preventDefault()
        focusSection('history')
        break
      case '4':
        event.preventDefault()
        focusSection('examination')
        break
      case '5':
        event.preventDefault()
        focusSection('plan')
        break
      case '6':
        event.preventDefault()
        focusSection('prescriptions')
        break
    }
  }
}

// Helper function to focus on specific sections
const focusSection = (sectionType: string) => {
  let selector = ''

  switch (sectionType) {
    case 'vital-signs':
      selector = 'input[placeholder*="36.5"], input[placeholder*="72"]'
      break
    case 'concerns':
    case 'history':
    case 'examination':
    case 'plan':
      // Find the textarea for the specific form type
      const forms = document.querySelectorAll('textarea')
      const formTitles = document.querySelectorAll('h2')

      for (let i = 0; i < formTitles.length; i++) {
        const title = formTitles[i].textContent?.toLowerCase()
        if (title?.includes(sectionType) ||
            (sectionType === 'concerns' && title?.includes('present concerns')) ||
            (sectionType === 'history' && title?.includes('present history')) ||
            (sectionType === 'examination' && title?.includes('examination')) ||
            (sectionType === 'plan' && title?.includes('assessment'))) {
          const textarea = forms[i]
          if (textarea) {
            textarea.focus()
            return
          }
        }
      }
      break
    case 'prescriptions':
      // Focus on add medication button
      const addMedButton = document.querySelector('button[class*="bg-green-600"]') as HTMLElement
      if (addMedButton) {
        addMedButton.focus()
      }
      break
  }

  if (selector) {
    const element = document.querySelector(selector) as HTMLElement
    if (element) {
      element.focus()
    }
  }
}

// Initialize
onMounted(async () => {
  try {
    await loadTemplate()

    // Ensure additional_tabs is initialized
    if (!consultationData.value.additional_tabs) {
      consultationData.value.additional_tabs = {}
    }

    await loadConsultation()
    startTimer()

    // Add keyboard event listeners
    document.addEventListener('keydown', handleKeydown)
  } catch (error: any) {
    console.error('Error initializing consultation:', error)
    toast.error('Failed to initialize consultation. Please refresh the page.')
  }
})

onUnmounted(() => {
  stopTimer()
  // Close all modals to prevent memory leaks
  closeAllModals()
  // Clear any pending timeouts or intervals
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
  }
  // Remove keyboard event listeners
  document.removeEventListener('keydown', handleKeydown)
})
</script>


