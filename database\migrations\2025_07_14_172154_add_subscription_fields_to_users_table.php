<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('subscription_plan_id')->nullable()->after('password_change_required');
            $table->timestamp('subscription_started_at')->nullable()->after('subscription_plan_id');
            $table->timestamp('subscription_ends_at')->nullable()->after('subscription_started_at');
            $table->enum('subscription_status', ['active', 'inactive', 'cancelled', 'expired', 'trial'])->default('inactive')->after('subscription_ends_at');
            $table->json('usage_limits')->nullable()->after('subscription_status');
            $table->json('current_usage')->nullable()->after('usage_limits');
            $table->timestamp('usage_reset_at')->nullable()->after('current_usage');

            $table->foreign('subscription_plan_id')->references('id')->on('subscription_plans')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['subscription_plan_id']);
            $table->dropColumn([
                'subscription_plan_id',
                'subscription_started_at',
                'subscription_ends_at',
                'subscription_status',
                'usage_limits',
                'current_usage',
                'usage_reset_at'
            ]);
        });
    }
};
