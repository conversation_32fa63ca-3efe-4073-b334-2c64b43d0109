<script setup lang="ts">
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { computed } from 'vue';
import PublicLayout from '@/layouts/PublicLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SubscriptionPlan {
  id: number;
  name: string;
  slug: string;
  price: number;
  formatted_price: string;
  currency: string;
  interval: string;
  description: string;
  features: string[];
  is_free: boolean;
  rate_limits: {
    chat_messages_per_hour: number;
    chat_messages_per_day: number;
    appointments_per_month: number;
    api_requests_per_minute: number;
    api_requests_per_hour: number;
  };
}

interface Props {
  plan: SubscriptionPlan;
  currentSubscription?: any;
  isAuthenticated: boolean;
}

const props = defineProps<Props>();

// Check if user is on this plan
const isCurrentPlan = computed(() => {
  if (!props.currentSubscription || !props.currentSubscription.plan) return false;
  return props.currentSubscription.plan.slug === props.plan.slug;
});

// Get the checkout URL for the plan
const getCheckoutUrl = computed(() => {
  return route('membership.checkout', { slug: props.plan.slug });
});

// Get the appropriate button text based on authentication and current plan
const getButtonText = computed(() => {
  if (isCurrentPlan.value) return 'Current Plan';
  if (props.plan.is_free) return 'Get Started';
  return 'Subscribe Now';
});

// Group features into categories
const groupedFeatures = computed(() => {
  const groups = {
    'Chat & AI': [] as string[],
    'Appointments': [] as string[],
    'Health Records': [] as string[],
    'Support': [] as string[],
    'Other': [] as string[],
  };

  props.plan.features.forEach(feature => {
    if (feature.toLowerCase().includes('chat') || feature.toLowerCase().includes('ai')) {
      groups['Chat & AI'].push(feature);
    } else if (feature.toLowerCase().includes('appointment')) {
      groups['Appointments'].push(feature);
    } else if (feature.toLowerCase().includes('health') || feature.toLowerCase().includes('record')) {
      groups['Health Records'].push(feature);
    } else if (feature.toLowerCase().includes('support')) {
      groups['Support'].push(feature);
    } else {
      groups['Other'].push(feature);
    }
  });

  return groups;
});
</script>

<template>
  <PublicLayout>
    <Head :title="`${plan.name} - Membership Plan`" />

    <div class="container mx-auto py-12 px-4 sm:px-6 lg:px-8 max-w-5xl">
      <!-- Breadcrumb -->
      <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <Link :href="route('membership.index')" class="text-gray-500 hover:text-gray-700">
                Membership Plans
              </Link>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-gray-700 font-medium">{{ plan.name }}</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <!-- Plan Header -->
      <div class="bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg p-8 mb-12 text-white">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 class="text-3xl md:text-4xl font-bold mb-2">{{ plan.name }}</h1>
            <p class="text-teal-100 text-lg mb-4 md:mb-0">{{ plan.description }}</p>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold">{{ plan.formatted_price }}</div>
            <div class="text-teal-100">per {{ plan.interval }}</div>
          </div>
        </div>
      </div>

      <!-- Plan Details -->
      <div class="grid gap-8 lg:grid-cols-3 mb-12">
        <!-- Left Column: Features -->
        <div class="lg:col-span-2">
          <h2 class="text-2xl font-bold mb-6">Plan Features</h2>
          
          <div class="space-y-8">
            <div v-for="(features, category) in groupedFeatures" :key="category" v-if="features.length > 0">
              <h3 class="text-xl font-medium mb-4">{{ category }}</h3>
              <ul class="grid gap-3 md:grid-cols-2">
                <li v-for="(feature, index) in features" :key="index" class="flex items-start">
                  <svg class="h-5 w-5 text-teal-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ feature }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Usage Limits -->
          <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 class="text-xl font-medium mb-4">Usage Limits</h3>
            <div class="grid gap-4 md:grid-cols-2">
              <div>
                <div class="text-sm text-gray-500">Chat Messages</div>
                <div class="font-medium">{{ plan.rate_limits.chat_messages_per_day }} per day</div>
                <div class="text-sm text-gray-500">({{ plan.rate_limits.chat_messages_per_hour }} per hour)</div>
              </div>
              <div>
                <div class="text-sm text-gray-500">Appointments</div>
                <div class="font-medium">{{ plan.rate_limits.appointments_per_month }} per month</div>
              </div>
              <div>
                <div class="text-sm text-gray-500">API Requests</div>
                <div class="font-medium">{{ plan.rate_limits.api_requests_per_hour }} per hour</div>
                <div class="text-sm text-gray-500">({{ plan.rate_limits.api_requests_per_minute }} per minute)</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column: Summary & Action -->
        <div>
          <Card class="sticky top-8">
            <CardHeader>
              <CardTitle>{{ plan.name }}</CardTitle>
              <CardDescription>{{ plan.description }}</CardDescription>
              <div class="mt-2">
                <span class="text-3xl font-bold">{{ plan.formatted_price }}</span>
                <span class="text-gray-500 ml-1">per {{ plan.interval }}</span>
              </div>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- Plan Highlights -->
              <div>
                <h4 class="font-medium mb-3">Key Benefits:</h4>
                <ul class="space-y-2">
                  <li v-for="(feature, index) in plan.features.slice(0, 5)" :key="index" class="flex items-center text-sm">
                    <svg class="h-4 w-4 text-teal-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    {{ feature }}
                  </li>
                </ul>
              </div>

              <!-- Current Plan Badge -->
              <div v-if="isCurrentPlan" class="bg-teal-50 border border-teal-200 rounded-md p-3 text-center">
                <Badge variant="default">Current Plan</Badge>
                <p class="text-sm text-teal-700 mt-1">You are currently subscribed to this plan</p>
              </div>

              <!-- Action Button -->
              <Button 
                :as="isCurrentPlan ? 'div' : 'a'"
                :href="!isCurrentPlan ? getCheckoutUrl : undefined"
                :variant="isCurrentPlan ? 'outline' : 'default'"
                :disabled="isCurrentPlan"
                class="w-full"
                size="lg"
              >
                {{ getButtonText }}
              </Button>

              <!-- Security Notice -->
              <div class="text-center text-xs text-gray-500">
                <p>Secure payment processing</p>
                <p>Cancel anytime. No hidden fees.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="border-t pt-12">
        <h2 class="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
        <div class="grid gap-6 md:grid-cols-2">
          <div>
            <h3 class="font-medium text-lg">Can I change my plan later?</h3>
            <p class="text-gray-600 mt-1">Yes, you can upgrade, downgrade, or cancel your subscription at any time from your account settings.</p>
          </div>
          <div>
            <h3 class="font-medium text-lg">How does billing work?</h3>
            <p class="text-gray-600 mt-1">You'll be charged at the beginning of each billing cycle. You can cancel anytime before your next billing date.</p>
          </div>
          <div>
            <h3 class="font-medium text-lg">Is there a free trial?</h3>
            <p class="text-gray-600 mt-1">We offer a free plan with limited features. You can use it as long as you want before upgrading.</p>
          </div>
          <div>
            <h3 class="font-medium text-lg">What payment methods do you accept?</h3>
            <p class="text-gray-600 mt-1">We accept all major credit cards including Visa, Mastercard, and American Express.</p>
          </div>
        </div>
      </div>

      <!-- Compare Plans Link -->
      <div class="text-center mt-12">
        <p class="text-gray-600 mb-4">Not sure if this is the right plan for you?</p>
        <Button as="a" :href="route('membership.index')" variant="outline">
          Compare All Plans
        </Button>
      </div>
    </div>
  </PublicLayout>
</template>
