<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'bill_id',
        'service_id',
        'item_name',
        'description',
        'unit_price',
        'quantity',
        'total_price',
        'wp_bill_item_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'quantity' => 'integer',
        'total_price' => 'decimal:2',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($billItem) {
            $billItem->total_price = $billItem->unit_price * $billItem->quantity;
        });

        static::saved(function ($billItem) {
            // Recalculate bill totals when item is saved
            $billItem->bill->calculateTotals()->save();
        });

        static::deleted(function ($billItem) {
            // Recalculate bill totals when item is deleted
            if ($billItem->bill) {
                $billItem->bill->calculateTotals()->save();
            }
        });
    }

    /**
     * Get the bill that owns the bill item.
     */
    public function bill()
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * Get the service associated with the bill item.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get formatted unit price.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '£' . number_format($this->unit_price, 2);
    }

    /**
     * Get formatted total price.
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return '£' . number_format($this->total_price, 2);
    }
}
