<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TimezoneMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Set the timezone from user's timezone if available
        if ($request->user() && $request->user()->timezone) {
            config(['app.timezone' => $request->user()->timezone]);
            date_default_timezone_set($request->user()->timezone);
        }
        
        // Set timezone from header if provided (for API requests)
        if ($request->hasHeader('X-Timezone')) {
            $timezone = $request->header('X-Timezone');
            if ($this->isValidTimezone($timezone)) {
                config(['app.timezone' => $timezone]);
                date_default_timezone_set($timezone);
            }
        }

        return $next($request);
    }

    /**
     * Check if timezone is valid
     */
    private function isValidTimezone($timezone)
    {
        return in_array($timezone, timezone_identifiers_list());
    }
}