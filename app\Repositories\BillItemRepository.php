<?php

namespace App\Repositories;

use App\Models\BillItem;
use App\Repositories\Interfaces\BillItemRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class BillItemRepository implements BillItemRepositoryInterface
{
    public function getByBill(int $billId): Collection
    {
        return BillItem::with('service')
                       ->where('bill_id', $billId)
                       ->orderBy('created_at')
                       ->get();
    }

    public function create(array $data): BillItem
    {
        return BillItem::create($data);
    }

    public function update(BillItem $billItem, array $data): BillItem
    {
        $billItem->update($data);
        return $billItem->fresh();
    }

    public function delete(BillItem $billItem): bool
    {
        return $billItem->delete();
    }

    public function findById(int $id): ?BillItem
    {
        return BillItem::find($id);
    }
}
