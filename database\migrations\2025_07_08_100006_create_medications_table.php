<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medications', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Generic name
            $table->string('brand_name')->nullable(); // Brand/trade name
            $table->string('active_ingredient');
            $table->string('strength')->nullable(); // e.g., "500mg", "10mg/ml"
            $table->string('form'); // tablet, capsule, liquid, injection, etc.
            $table->string('route')->nullable(); // oral, topical, injection, etc.
            $table->text('description')->nullable();
            $table->json('indications')->nullable(); // What it's used for
            $table->json('contraindications')->nullable(); // When not to use
            $table->json('side_effects')->nullable(); // Common side effects
            $table->json('interactions')->nullable(); // Drug interactions
            $table->text('dosage_guidelines')->nullable(); // General dosage info
            $table->string('drug_class')->nullable(); // Antibiotic, analgesic, etc.
            $table->string('controlled_substance_schedule')->nullable(); // Schedule I-V
            $table->boolean('requires_prescription')->default(true);
            $table->boolean('is_active')->default(true);
            $table->string('regulatory_code')->nullable(); // BNF code, etc.
            $table->timestamps();

            // Indexes
            $table->index('name');
            $table->index('brand_name');
            $table->index('active_ingredient');
            $table->index('drug_class');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medications');
    }
};
