<template>
  <div class="w-full">
    <!-- Desktop Layout - Single Row with More Space -->
    <div class="hidden lg:flex items-center w-full px-6 py-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border-l-4 border-purple-500 gap-8">
      <!-- Left Side: Medroid Scribe Branding -->
      <div class="flex items-center gap-3">
        <img src="/m-scribe-logo 2.svg"
             alt="Scribe"
             :class="[
               'h-5 w-auto transition-all duration-300',
               status === 'recording' && !isPaused ? 'animate-pulse' : ''
             ]" />
        <span :class="[
          'text-lg font-bold transition-colors duration-300',
          status === 'recording' && !isPaused ? 'text-pink-600' : 'text-pink-500'
        ]">Scribe</span>
      </div>

      <!-- Center: Recording Animation and Status -->
      <div class="flex-1 flex items-center justify-center gap-4">
        <!-- Recording Animation Waves -->
        <div v-if="status === 'recording' && !isPaused" class="flex items-end space-x-1 h-10 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-lg">
          <div v-for="n in 5" :key="n"
               class="w-1 rounded-full transform transition-all duration-200"
               :class="[
                 `animate-wave-${n}`,
                 n % 2 === 0 ? 'bg-gradient-to-t from-teal-500 to-blue-500' : 'bg-gradient-to-t from-blue-500 to-purple-500'
               ]"
               :style="`animation-delay: ${n * 0.15}s; height: ${(Math.random() * 20) + 8}px`"
          ></div>
          <div class="ml-3 flex items-center space-x-1">
            <div class="w-2 h-2 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 animate-pulse"></div>
            <div class="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse" style="animation-delay: 0.3s"></div>
            <div class="w-1 h-1 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 animate-pulse" style="animation-delay: 0.6s"></div>
          </div>
        </div>

        <!-- Status Indicators -->
        <div v-if="status !== 'idle'" class="flex items-center gap-3">
          <!-- Recording Status -->
          <div v-if="status === 'recording'" class="flex items-center gap-3">
            <div v-if="!isPaused" class="px-4 py-2 bg-gradient-to-r from-red-100 to-pink-100 text-red-700 text-sm font-medium rounded-lg border border-red-200 flex items-center gap-2">
              🔴 Recording {{ formatTime(recordingDuration) }}
            </div>
            <div v-else class="px-4 py-2 bg-gradient-to-r from-amber-100 to-orange-100 text-amber-700 text-sm font-medium rounded-lg border border-amber-200">
              ⏸️ Paused {{ formatTime(recordingDuration) }}
            </div>
          </div>

          <!-- Processing Status -->
          <div v-else-if="status === 'processing'" class="flex items-center gap-3 text-sm text-teal-700 font-medium px-4 py-2 bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg border border-teal-200">
            <svg class="animate-spin h-5 w-5 text-teal-600" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ processingStatus || 'Processing audio...' }}</span>
          </div>

          <!-- Other Status -->
          <div v-else class="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-700 text-sm font-medium rounded-lg border border-blue-200">
            <span class="capitalize">{{ status }}</span>
          </div>
        </div>
      </div>

      <!-- Right Side: Action Buttons -->
      <div class="flex items-center gap-3">
        <!-- Main Recording Button -->
        <button
          @click="toggleRecording"
          :disabled="['processing', 'analyzing', 'populating'].includes(status)"
          :class="[
            'h-10 w-12 flex items-center justify-center rounded-lg transition-all duration-300 relative overflow-hidden shadow-sm font-medium',
            status === 'recording'
              ? (isPaused
                  ? 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white'
                  : 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white')
              : 'bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 text-white'
          ]"
        >
            <!-- Microphone Icon (when not recording) -->
            <svg v-if="status !== 'recording'"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 26 26"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" x2="12" y1="19" y2="22"></line>
            </svg>

            <!-- Stop Icon (when recording and not paused) -->
            <svg v-else-if="!isPaused"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <rect x="6" y="6" width="12" height="12"></rect>
            </svg>

            <!-- Play Icon (when recording is paused) -->
            <svg v-else
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>

            <!-- Pulsing Background Effect -->
            <div v-if="status === 'recording' && !isPaused"
              class="absolute inset-0 bg-red-400 opacity-30 animate-ping"
            ></div>
          </button>

          <!-- Pause/Resume Button (only shown when recording) -->
          <button
            v-if="status === 'recording'"
            @click="togglePause"
            class="h-10 w-10 flex items-center justify-center rounded-lg transition-all duration-300 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-500 hover:to-slate-600 text-white shadow-sm"
          >
            <!-- Pause Icon -->
            <svg v-if="!isPaused"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5"
            >
              <line x1="6" y1="4" x2="6" y2="20"></line>
              <line x1="18" y1="4" x2="18" y2="20"></line>
            </svg>

            <!-- Resume Icon -->
            <svg v-else
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5"
            >
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
          </button>

          <!-- Alternative Options (when idle) -->
          <div v-if="status === 'idle'" class="flex items-center gap-3">
            <!-- File Upload Button -->
            <label class="flex items-center justify-center gap-2 cursor-pointer px-4 py-2 h-10 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg text-sm font-medium shadow-sm transition-all duration-300 hover:shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" x2="12" y1="3" y2="15"></line>
              </svg>
              <span>Upload Audio</span>
              <input type="file" accept="audio/*, .mp3, .wav, .ogg, .webm, .m4a, .mp4, .aac" class="hidden" @change="handleFileUpload" />
            </label>

            <!-- Mobile Recording Button -->
            <button
              @click="showMobileRecordingModal"
              class="flex items-center justify-center gap-2 px-4 py-2 h-10 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg text-sm font-medium shadow-sm transition-all duration-300 hover:shadow-md"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                <line x1="12" y1="18" x2="12.01" y2="18"></line>
              </svg>
              <span>Mobile Recording</span>
            </button>

            <!-- Manual Transcript Entry Button -->
            <button
              @click="showManualTranscriptDialog"
              class="flex items-center justify-center gap-2 px-4 py-2 h-10 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-lg text-sm font-medium shadow-sm transition-all duration-300 hover:shadow-md"
            >
              <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
              <span>Manual Entry</span>
            </button>
          </div>

          <!-- Review Button (when transcribed) -->
          <button
            v-if="status === 'transcribed'"
            @click="showTranscriptDialog"
            class="px-4 py-2 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white rounded-lg text-sm font-medium flex items-center gap-2 shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
            </svg>
            Review Transcript
          </button>

          <!-- Audio Controls (when audio exists) -->
          <div v-if="audioUrl && status !== 'recording'" class="flex items-center gap-3 ml-4 pl-4 border-l border-slate-300">
            <!-- Audio Player -->
            <button
              @click="toggleAudioPlayback"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-blue-500 hover:bg-blue-600 transition-colors text-white shadow-sm"
            >
              <svg v-if="isAudioPlaying" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="6" y="5" width="4" height="14"></rect>
                <rect x="14" y="5" width="4" height="14"></rect>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            </button>

            <!-- Time Display and Progress -->
            <div class="flex flex-col w-48">
              <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
                <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300" :style="`width: ${audioProgress}%`"></div>
              </div>
              <div class="text-xs text-gray-600 flex justify-between">
                <span>{{ formatTime(audioCurrentTime) }}</span>
                <span>{{ formatTime(audioDuration) }}</span>
              </div>
            </div>

            <!-- Download Button -->
            <a :href="audioUrl"
               :download="getAudioFileName()"
               class="px-3 py-2 h-10 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg text-sm font-medium flex items-center gap-2 shadow-sm transition-all duration-300 hover:shadow-md"
               title="Download audio recording"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" x2="12" y1="15" y2="3"></line>
              </svg>
              <span>Download</span>
            </a>

            <!-- Restart Button -->
            <button
              @click="restartRecording"
              class="px-3 py-2 h-10 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-lg text-sm font-medium flex items-center gap-2 shadow-sm transition-all duration-300 hover:shadow-md"
              title="Start a new recording"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                <path d="M3 3v5h5"></path>
              </svg>
              <span>Restart</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Layout - Single Row -->
    <div class="lg:hidden w-full">
      <!-- Branding Row -->
      <div class="flex items-center justify-between w-full px-3 py-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg border-l-4 border-purple-500">
        <div class="flex items-center gap-2">
          <img src="/m-scribe-logo 2.svg"
               alt="Scribe"
               :class="[
                 'h-4 w-auto transition-all duration-300',
                 status === 'recording' && !isPaused ? 'animate-pulse' : ''
               ]" />
          <span :class="[
            'text-sm font-bold transition-colors duration-300',
            status === 'recording' && !isPaused ? 'text-pink-600' : 'text-pink-500'
          ]">Scribe</span>
        </div>

        <!-- Status Indicator for Mobile -->
        <div v-if="status !== 'idle'" class="text-xs">
          <div v-if="status === 'recording'" class="flex items-center gap-1">
            <div v-if="!isPaused" class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <div v-else class="w-2 h-2 bg-amber-500 rounded-full"></div>
            <span class="text-slate-600">
              {{ isPaused ? 'Paused' : 'Recording' }} {{ formatTime(recordingDuration) }}
            </span>
          </div>
          <div v-else-if="status === 'processing'" class="flex items-center gap-1">
            <svg class="animate-spin h-3 w-3 text-teal-600" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-slate-600">Processing...</span>
          </div>
          <div v-else class="flex items-center gap-1">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="text-slate-600 capitalize">{{ status }}</span>
          </div>
        </div>
      </div>

      <!-- Recording Animation Row (when recording) -->
      <div v-if="status === 'recording' && !isPaused" class="w-full px-3 py-2 bg-white/80 backdrop-blur-sm border-l-4 border-purple-500 flex justify-center">
        <div class="flex items-end space-x-1 h-8 px-3 py-1 bg-white/60 backdrop-blur-sm rounded-lg">
          <div v-for="n in 5" :key="n"
               class="w-1 rounded-full transform transition-all duration-200"
               :class="[
                 `animate-wave-${n}`,
                 n % 2 === 0 ? 'bg-gradient-to-t from-teal-500 to-blue-500' : 'bg-gradient-to-t from-blue-500 to-purple-500'
               ]"
               :style="`animation-delay: ${n * 0.15}s; height: ${(Math.random() * 20) + 8}px`"
          ></div>
          <div class="ml-2 flex items-center space-x-1">
            <div class="w-2 h-2 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 animate-pulse"></div>
            <div class="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse" style="animation-delay: 0.3s"></div>
            <div class="w-1 h-1 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 animate-pulse" style="animation-delay: 0.6s"></div>
          </div>
        </div>
      </div>

      <!-- Action Buttons Row -->
      <div class="w-full px-3 py-2 bg-white/60 rounded-b-lg border-l-4 border-purple-500">
        <div class="flex items-center gap-2">
          <!-- Main Recording Button -->
          <button
            @click="toggleRecording"
            :disabled="['processing', 'analyzing', 'populating'].includes(status)"
            :class="[
              'h-10 w-10 flex items-center justify-center rounded-lg transition-all duration-300 relative overflow-hidden shadow-sm font-medium',
              status === 'recording'
                ? (isPaused
                    ? 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white'
                    : 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white')
                : 'bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 text-white'
            ]"
          >
            <!-- Microphone Icon (when not recording) -->
            <svg v-if="status !== 'recording'"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 26 26"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" x2="12" y1="19" y2="22"></line>
            </svg>

            <!-- Stop Icon (when recording and not paused) -->
            <svg v-else-if="!isPaused"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <rect x="6" y="6" width="12" height="12"></rect>
            </svg>

            <!-- Play Icon (when recording is paused) -->
            <svg v-else
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5 text-white transition-all duration-300"
            >
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>

            <!-- Pulsing Background Effect -->
            <div v-if="status === 'recording' && !isPaused"
              class="absolute inset-0 bg-red-400 opacity-30 animate-ping"
            ></div>
          </button>

          <!-- Pause/Resume Button (only shown when recording) -->
          <button
            v-if="status === 'recording'"
            @click="togglePause"
            class="h-10 w-10 flex items-center justify-center rounded-lg transition-all duration-300 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-500 hover:to-slate-600 text-white shadow-sm"
          >
            <!-- Pause Icon -->
            <svg v-if="!isPaused"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-4 h-4"
            >
              <line x1="6" y1="4" x2="6" y2="20"></line>
              <line x1="18" y1="4" x2="18" y2="20"></line>
            </svg>

            <!-- Resume Icon -->
            <svg v-else
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-4 h-4"
            >
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
          </button>

          <!-- Alternative Options (when idle) -->
          <div v-if="status === 'idle'" class="flex items-center gap-2 flex-1">
            <!-- File Upload Button -->
            <label class="flex-1 h-10 flex items-center justify-center gap-1 cursor-pointer bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg text-xs font-medium shadow-sm transition-all duration-300 hover:shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" x2="12" y1="3" y2="15"></line>
              </svg>
              <span>Upload</span>
              <input type="file" accept="audio/*, .mp3, .wav, .ogg, .webm, .m4a, .mp4, .aac" class="hidden" @change="handleFileUpload" />
            </label>

            <!-- Mobile Recording Button -->
            <button
              @click="showMobileRecordingModal"
              class="flex-1 h-10 flex items-center justify-center gap-1 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg text-xs font-medium shadow-sm transition-all duration-300 hover:shadow-md"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                <line x1="12" y1="18" x2="12.01" y2="18"></line>
              </svg>
              <span>Mobile</span>
            </button>

            <!-- Manual Transcript Entry Button -->
            <button
              @click="showManualTranscriptDialog"
              class="flex-1 h-10 flex items-center justify-center gap-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-lg text-xs font-medium shadow-sm transition-all duration-300 hover:shadow-md"
            >
              <svg class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
              <span>Manual</span>
            </button>
          </div>

          <!-- Review Button (when transcribed) -->
          <button
            v-if="status === 'transcribed'"
            @click="showTranscriptDialog"
            class="flex-1 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white rounded-lg text-xs font-medium flex items-center justify-center gap-1 shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <svg class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
            </svg>
            <span>Review</span>
          </button>
        </div>
      </div>

      <!-- Audio Controls Section (Mobile) -->
      <div v-if="audioUrl && status !== 'recording'" class="mt-2 px-3 py-2 bg-white rounded-lg border border-slate-200 shadow-sm">
        <!-- Audio Player Row -->
        <div class="flex items-center gap-2 mb-2">
          <button
            @click="toggleAudioPlayback"
            class="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 hover:bg-blue-600 transition-colors text-white"
          >
            <svg v-if="isAudioPlaying" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="6" y="5" width="4" height="14"></rect>
              <rect x="14" y="5" width="4" height="14"></rect>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
          </button>

          <!-- Time Display and Progress -->
          <div class="flex flex-col flex-1">
            <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
              <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300" :style="`width: ${audioProgress}%`"></div>
            </div>
            <div class="text-xs text-gray-500 flex justify-between">
              <span>{{ formatTime(audioCurrentTime) }}</span>
              <span>{{ formatTime(audioDuration) }}</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons Row -->
        <div class="flex items-center gap-2">
          <!-- Download Button -->
          <a :href="audioUrl"
             :download="getAudioFileName()"
             class="flex-1 h-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded text-xs font-medium flex items-center justify-center gap-1 transition-all duration-300"
             title="Download audio recording"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" x2="12" y1="15" y2="3"></line>
            </svg>
            <span>Download</span>
          </a>

          <!-- Restart Button -->
          <button
            @click="restartRecording"
            class="flex-1 h-8 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded text-xs font-medium flex items-center justify-center gap-1 transition-all duration-300"
            title="Start a new recording"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
              <path d="M3 3v5h5"></path>
            </svg>
            <span>Restart</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message (if any) -->
    <div v-if="mediaError" class="mt-2 px-3 py-2 bg-red-50 text-red-700 text-xs rounded border border-red-200 flex items-center gap-2">
      <svg class="w-3 h-3 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="15" y1="9" x2="9" y2="15"></line>
        <line x1="9" y1="9" x2="15" y2="15"></line>
      </svg>
      {{ mediaError }}
    </div>

    <!-- Modals and Additional Components -->

    <!-- Manual Transcript Modal -->
    <div v-if="showManualModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
    <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Enter Consultation Transcript</h2>
        <button @click="closeManualModal" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="mb-4 flex-grow overflow-y-auto">
        <p class="text-sm text-gray-600 mb-2">Enter the conversation transcript below. Prefix each speaker with "Doctor:" or "Patient:" to help with analysis.</p>
        <textarea
          v-model="transcript"
          class="w-full min-h-[300px] p-3 border rounded"
          placeholder="Doctor: Hello, how are you feeling today?
Patient: I've been having headaches for the past week.
Doctor: Can you tell me more about these headaches?"
          style="height: 402px;"
        ></textarea>
      </div>

      <div class="flex justify-end gap-2 mt-2">
        <button
          @click="closeManualModal"
          class="px-3 py-1.5 border rounded hover:bg-gray-50 text-sm"
        >
          Cancel
        </button>
        <button
          @click="processManualTranscript"
          class="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded text-sm"
          :disabled="!transcript || transcript.trim() === ''"
        >
          Analyze Transcript
        </button>
      </div>
    </div>
  </div>

  <!-- Transcript Review Modal -->
  <div v-if="showTranscriptModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
    <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Review Transcript</h2>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Content area with scrolling -->
      <div class="flex-grow overflow-y-auto pr-2">
        <!-- Diarized Transcript Display -->
        <div v-if="diarizedTranscript && diarizedTranscript.length > 0" class="mb-4">
          <h3 class="text-md font-medium mb-2">Speaker Diarization</h3>
          <div class="space-y-2 mb-4">
            <div v-for="(utterance, index) in diarizedTranscript" :key="index"
                class="p-3 rounded-lg"
                :class="utterance.speaker === 0 ? 'bg-blue-50' : 'bg-green-50'">
              <div class="font-semibold mb-1">
                Speaker {{ utterance.speaker === 0 ? '1 (Doctor)' : '2 (Patient)' }}
              </div>
              <div>{{ utterance.text }}</div>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h3 class="text-md font-medium mb-2">Complete Transcript</h3>
          <textarea
            v-model="transcript"
            class="w-full min-h-[200px] p-3 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
            placeholder="Transcript will appear here..."
          ></textarea>
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-2">
        <button
          @click="closeModal"
          class="px-3 py-1.5 border rounded hover:bg-gray-50 text-sm"
        >
          Cancel
        </button>
        <button
          @click="analyzeTranscript"
          class="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded text-sm"
        >
          Extract Medical Data
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Recording Modal -->
  <div v-if="showMobileDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
    <div class="bg-white rounded-lg p-6 w-full max-w-md my-4 flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Record With Mobile Device</h2>
        <button @click="closeMobileModal" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="text-center mb-4">
        <div v-if="qrCodeUrl" class="mx-auto mb-4 p-2 border rounded-lg bg-white w-64 h-64 flex items-center justify-center">
          <img :src="qrCodeUrl" alt="QR Code for mobile recording" class="max-w-full max-h-full" />
        </div>

        <p class="text-sm text-gray-600 mb-2">
          Scan this QR code with your mobile device to record audio for this consultation.
        </p>

        <div v-if="sessionStatus" class="text-xs text-gray-500">
          Session expires in {{ sessionTimeRemaining }}
        </div>
      </div>

      <div v-if="mobileRecordingError" class="bg-red-50 p-3 rounded-md mb-4">
        <p class="text-sm text-red-600">{{ mobileRecordingError }}</p>
      </div>

      <div v-if="mobileRecordingSuccess" class="bg-green-50 p-3 rounded-md mb-4">
        <p class="text-sm text-green-600">{{ mobileRecordingSuccess }}</p>
      </div>

      <div class="flex justify-between">
        <button @click="closeMobileModal" class="px-4 py-2 border rounded hover:bg-gray-50">
          Cancel
        </button>

        <button
          v-if="sessionProcessed"
          @click="retrieveTranscript"
          class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Load Transcript
        </button>

        <button
          v-else-if="sessionId && !sessionProcessed"
          @click="checkSessionStatus"
          class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded"
        >
          Check Status
        </button>

        <button
          v-else
          @click="createMobileRecordingSession"
          :disabled="isCreatingSession"
          class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isCreatingSession" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
          </span>
          <span v-else>Generate QR Code</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Results Modal -->
  <div v-if="showResultsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
    <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Extracted Medical Information</h2>
        <button @click="closeResultsModal" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-grow overflow-y-auto pr-2">
        <div class="space-y-4">
          <div v-for="(content, key) in analysisResults" :key="key" class="border p-4 rounded-lg">
            <h3 class="font-medium capitalize mb-2">{{ formatKey(key) }}</h3>
            <div class="text-gray-700">{{ content }}</div>
          </div>
        </div>
      </div>

      <div class="flex justify-end mt-4">
        <button
          @click="populateRecords"
          class="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded text-sm"
          :disabled="status === 'populating'"
        >
          Populate Medical Records
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import axios from 'axios'

interface Props {
  encounterId: string | number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'records-updated': []
  'ai-populate': [{ extractedData: any; rawData: any }]
}>()

// Component state
const status = ref<'idle' | 'recording' | 'processing' | 'analyzing' | 'transcribed' | 'populating' | 'completed'>('idle')
const transcript = ref('')
const diarizedTranscript = ref<Array<{ speaker: number; text: string; start?: number; end?: number }>>([])
const showTranscriptModal = ref(false)
const showResultsModal = ref(false)
const showManualModal = ref(false)
const analysisResults = ref<Record<string, string> | null>(null)

// Mobile recording state
const showMobileDialog = ref(false)
const isCreatingSession = ref(false)
const sessionId = ref<string | null>(null)
const qrCodeUrl = ref<string | null>(null)
const recordUrl = ref<string | null>(null)
const sessionStatus = ref<string | null>(null)
const sessionTimeRemaining = ref('')
const sessionProcessed = ref(false)
const mobileRecordingError = ref<string | null>(null)
const mobileRecordingSuccess = ref<string | null>(null)
const sessionCheckTimer = ref<number | null>(null)
const recordingDocumentId = ref<string | null>(null)

// Recording state
const recorder = ref<MediaRecorder | null>(null)
const audioChunks = ref<Blob[]>([])
const audioBlob = ref<Blob | null>(null)
const audioUrl = ref<string | null>(null)
const audioElement = ref<HTMLAudioElement | null>(null)
const isAudioPlaying = ref(false)
const audioProgress = ref(0)
const audioCurrentTime = ref(0)
const audioDuration = ref(0)
const audioUpdateTimer = ref<number | null>(null)

// Recording timing
const recordingStartTime = ref<number | null>(null)
const recordingTimer = ref<number | null>(null)
const recordingDuration = ref(0)
const isPaused = ref(false)
const pauseStartTime = ref<number | null>(null)
const totalPausedDuration = ref(0)
const maxRecordingTime = ref(45 * 60) // 45 minutes in seconds

// Error and processing state
const mediaError = ref<string | null>(null)
const canRecord = ref(true)
const processingStatus = ref('')
const processingProgress = ref(0)
const downloadCounter = ref(1)

// Methods
const formatTime = (seconds: number): string => {
  seconds = Math.round(parseFloat(seconds.toString()) || 0)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatKey = (key: string): string => {
  return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
}

const getAudioFileName = (): string => {
  const now = new Date()
  const datePart = now.toISOString().split('T')[0]
  const patientId = props.encounterId || 'unknown'
  const extension = getAudioExtension()
  return `patient_${patientId}_${datePart}_${downloadCounter.value}.${extension}`
}

const getAudioExtension = (): string => {
  if (!audioBlob.value) return 'wav'

  if (audioBlob.value.type.includes('webm')) return 'webm'
  if (audioBlob.value.type.includes('wav')) return 'wav'
  if (audioBlob.value.type.includes('mp3')) return 'mp3'
  if (audioBlob.value.type.includes('ogg')) return 'ogg'
  if (audioBlob.value.type.includes('m4a')) return 'm4a'

  return 'wav'
}

const checkRecordingAvailability = async (): Promise<void> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    stream.getTracks().forEach(track => track.stop())
    canRecord.value = true
    console.log('Recording is available')
  } catch (error) {
    console.error('Recording is not available:', error)
    canRecord.value = false
    mediaError.value = 'Recording not supported in this browser. Please use file upload or manual entry.'
  }
}

const toggleRecording = (): void => {
  console.log("Toggle recording clicked, current status:", status.value)

  if (status.value === 'idle') {
    startRecording()
  } else if (status.value === 'recording') {
    stopRecording()
  }
}

const startRecording = async (): Promise<void> => {
  try {
    mediaError.value = null
    console.log("Starting recording")
    status.value = 'recording'
    audioChunks.value = []

    // Start recording timer
    recordingStartTime.value = Date.now()
    recordingDuration.value = 0
    recordingTimer.value = setInterval(() => {
      recordingDuration.value = Math.floor((Date.now() - recordingStartTime.value!) / 1000)

      if (recordingDuration.value >= maxRecordingTime.value) {
        stopRecording()
      }
    }, 1000)

    // Get user media
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

    // Create MediaRecorder with preferred format
    const options: MediaRecorderOptions = {}

    // Try to use a format that's compatible with Deepgram
    if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
      options.mimeType = 'audio/webm;codecs=opus'
    } else if (MediaRecorder.isTypeSupported('audio/webm')) {
      options.mimeType = 'audio/webm'
    } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
      options.mimeType = 'audio/mp4'
    }

    console.log('Using MediaRecorder with options:', options)
    recorder.value = new MediaRecorder(stream, options)

    recorder.value.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }

    recorder.value.onstop = () => {
      processRecording()
    }

    recorder.value.start()
    console.log("Recording started successfully")
  } catch (error) {
    console.error('Error starting recording:', error)
    mediaError.value = 'Failed to start recording: ' + (error instanceof Error ? error.message : 'Unknown error')
    canRecord.value = false
    status.value = 'idle'
  }
}

const stopRecording = (): void => {
  if (recorder.value) {
    try {
      recorder.value.stop()

      // Stop all tracks
      if (recorder.value.stream) {
        recorder.value.stream.getTracks().forEach(track => track.stop())
      }

      // Clear the recording timer
      if (recordingTimer.value) {
        clearInterval(recordingTimer.value)
        recordingTimer.value = null
      }

      status.value = 'processing'
    } catch (error) {
      console.error('Error stopping recording:', error)
      mediaError.value = 'Error stopping recording'
      status.value = 'idle'
    }
  }
}

const processRecording = async (): Promise<void> => {
  try {
    if (!audioChunks.value || audioChunks.value.length === 0) {
      throw new Error('No audio data captured')
    }

    // Create audio blob
    let mimeType = 'audio/webm'
    if (recorder.value && recorder.value.mimeType) {
      mimeType = recorder.value.mimeType
    }

    audioBlob.value = new Blob(audioChunks.value, { type: mimeType })

    if (audioBlob.value.size === 0) {
      throw new Error('Recorded audio is empty')
    }

    // Create URL for audio playback
    if (audioUrl.value) {
      URL.revokeObjectURL(audioUrl.value)
    }
    audioUrl.value = URL.createObjectURL(audioBlob.value)

    // Initialize audio player
    initAudioPlayer()

    // Process with AI transcription
    await transcribeAudio()

    status.value = 'transcribed'
  } catch (error) {
    console.error('Error processing recording:', error)
    mediaError.value = `Failed to process recording: ${error instanceof Error ? error.message : 'Unknown error'}`
    status.value = 'idle'
  }
}

const transcribeAudio = async (): Promise<void> => {
  try {
    status.value = 'processing'
    processingStatus.value = 'Transcribing audio...'
    processingProgress.value = 10

    // Debug audio blob
    console.log('Audio blob details:', {
      size: audioBlob.value?.size,
      type: audioBlob.value?.type,
      chunks: audioChunks.value.length
    })

    // Create FormData for file upload
    const formData = new FormData()
    const extension = getAudioExtension()
    const filename = `recording.${extension}`
    console.log('Sending file with name:', filename)
    formData.append('audio_file', audioBlob.value!, filename)
    formData.append('encounter_id', props.encounterId.toString())

    // Simulate progress
    const progressInterval = setInterval(() => {
      if (processingProgress.value < 85) {
        processingProgress.value += 2
      }
    }, 500)

    // Call transcription API
    const response = await axios.post('/consultations/transcribe-audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    clearInterval(progressInterval)
    processingProgress.value = 100

    if (response.data.success) {
      transcript.value = response.data.data.transcript || ''
      diarizedTranscript.value = response.data.data.diarized_transcript || []

      // Auto-open transcript review dialog
      setTimeout(() => {
        showTranscriptDialog()
      }, 500)
    } else {
      throw new Error(response.data.message || 'Transcription failed')
    }
  } catch (error) {
    console.error('Error transcribing audio:', error)
    mediaError.value = `Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    status.value = 'idle'
  }
}

const handleFileUpload = async (event: Event): Promise<void> => {
  try {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (!file) return

    mediaError.value = null
    status.value = 'processing'

    // Check if file is audio
    const isAudioFile =
      file.type.startsWith('audio/') ||
      file.type.startsWith('video/webm') ||
      file.name.toLowerCase().endsWith('.mp3') ||
      file.name.toLowerCase().endsWith('.wav') ||
      file.name.toLowerCase().endsWith('.ogg') ||
      file.name.toLowerCase().endsWith('.webm') ||
      file.name.toLowerCase().endsWith('.m4a') ||
      file.name.toLowerCase().endsWith('.mp4') ||
      file.name.toLowerCase().endsWith('.aac')

    if (!isAudioFile) {
      throw new Error('Please upload an audio file (.mp3, .wav, .webm, .ogg, .m4a, etc)')
    }

    // Save the audio file
    audioBlob.value = file

    // Create URL for audio playback
    if (audioUrl.value) {
      URL.revokeObjectURL(audioUrl.value)
    }
    audioUrl.value = URL.createObjectURL(file)

    // Initialize audio player
    initAudioPlayer()

    // Process with transcription
    await transcribeUploadedFile(file)

    status.value = 'transcribed'
  } catch (error) {
    console.error('Error uploading file:', error)
    mediaError.value = `File upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    status.value = 'idle'
  }
}

const transcribeUploadedFile = async (file: File): Promise<void> => {
  try {
    processingStatus.value = 'Processing uploaded file...'
    processingProgress.value = 10

    const formData = new FormData()
    formData.append('audio_file', file)
    formData.append('encounter_id', props.encounterId.toString())

    const progressInterval = setInterval(() => {
      if (processingProgress.value < 85) {
        processingProgress.value += 1
      }
    }, 1000)

    const response = await axios.post('/consultations/transcribe-audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    clearInterval(progressInterval)
    processingProgress.value = 100

    if (response.data.success) {
      transcript.value = response.data.data.transcript || ''
      diarizedTranscript.value = response.data.data.diarized_transcript || []

      setTimeout(() => {
        showTranscriptDialog()
      }, 500)
    } else {
      throw new Error(response.data.message || 'Transcription failed')
    }
  } catch (error) {
    console.error('Error transcribing uploaded file:', error)
    throw error
  }
}

const togglePause = (): void => {
  if (!recorder.value || status.value !== 'recording') return

  if (isPaused.value) {
    resumeRecording()
  } else {
    pauseRecording()
  }
}

const pauseRecording = (): void => {
  if (!recorder.value || status.value !== 'recording' || isPaused.value) return

  try {
    if (recorder.value.state === 'recording') {
      recorder.value.pause()
    }

    isPaused.value = true
    pauseStartTime.value = Date.now()

    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }

    console.log('Recording paused')
  } catch (error) {
    console.error('Error pausing recording:', error)
  }
}

const resumeRecording = (): void => {
  if (!recorder.value || status.value !== 'recording' || !isPaused.value) return

  try {
    if (recorder.value.state === 'paused') {
      recorder.value.resume()
    }

    if (pauseStartTime.value) {
      totalPausedDuration.value += (Date.now() - pauseStartTime.value) / 1000
      pauseStartTime.value = null
    }

    recordingTimer.value = setInterval(() => {
      recordingDuration.value = Math.floor((Date.now() - recordingStartTime.value!) / 1000) - Math.floor(totalPausedDuration.value)

      if (recordingDuration.value >= maxRecordingTime.value) {
        stopRecording()
      }
    }, 1000)

    isPaused.value = false
    console.log('Recording resumed')
  } catch (error) {
    console.error('Error resuming recording:', error)
  }
}

const restartRecording = (): void => {
  cleanup()

  status.value = 'idle'
  transcript.value = ''
  diarizedTranscript.value = []
  analysisResults.value = null
  mediaError.value = null
  processingProgress.value = 0
  processingStatus.value = ''

  downloadCounter.value++
}

const initAudioPlayer = (): void => {
  if (!audioUrl.value) return

  cleanupAudioPlayer()

  audioElement.value = new Audio(audioUrl.value)

  audioElement.value.addEventListener('loadedmetadata', () => {
    audioDuration.value = audioElement.value?.duration || 0
  })

  audioElement.value.addEventListener('timeupdate', () => {
    if (audioElement.value) {
      audioCurrentTime.value = audioElement.value.currentTime
      audioProgress.value = (audioCurrentTime.value / audioDuration.value) * 100
    }
  })

  audioElement.value.addEventListener('ended', () => {
    isAudioPlaying.value = false
    audioProgress.value = 0
    audioCurrentTime.value = 0
  })
}

const toggleAudioPlayback = (): void => {
  if (!audioElement.value) return

  if (isAudioPlaying.value) {
    audioElement.value.pause()
    isAudioPlaying.value = false
  } else {
    audioElement.value.play()
    isAudioPlaying.value = true
  }
}

const cleanupAudioPlayer = (): void => {
  if (audioElement.value) {
    audioElement.value.pause()
    audioElement.value = null
  }

  if (audioUpdateTimer.value) {
    clearInterval(audioUpdateTimer.value)
    audioUpdateTimer.value = null
  }

  isAudioPlaying.value = false
  audioProgress.value = 0
  audioCurrentTime.value = 0
  audioDuration.value = 0
}

const cleanup = (): void => {
  if (recorder.value) {
    if (status.value === 'recording') {
      recorder.value.stop()
    }
    recorder.value = null
  }

  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }

  if (sessionCheckTimer.value) {
    clearInterval(sessionCheckTimer.value)
    sessionCheckTimer.value = null
  }

  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value)
    audioUrl.value = null
  }

  cleanupAudioPlayer()
}

const showManualTranscriptDialog = (): void => {
  showManualModal.value = true
  transcript.value = ''
}

const closeManualModal = (): void => {
  showManualModal.value = false
}

const processManualTranscript = async (): Promise<void> => {
  if (!transcript.value.trim()) return

  closeManualModal()
  await analyzeTranscript()
}

const showTranscriptDialog = (): void => {
  showTranscriptModal.value = true
}

const closeModal = (): void => {
  showTranscriptModal.value = false
}

const closeResultsModal = (): void => {
  showResultsModal.value = false
}

const analyzeTranscript = async (): Promise<void> => {
  try {
    status.value = 'analyzing'
    closeModal()

    const response = await axios.post('/consultations/analyze-transcript', {
      transcript: transcript.value,
      encounter_id: props.encounterId
    })

    if (response.data.success) {
      analysisResults.value = response.data.data.extracted_data || {}
      showResultsModal.value = true
      status.value = 'transcribed'
    } else {
      throw new Error(response.data.message || 'Analysis failed')
    }
  } catch (error) {
    console.error('Error analyzing transcript:', error)
    mediaError.value = `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    status.value = 'transcribed'
  }
}

const populateRecords = async (): Promise<void> => {
  try {
    status.value = 'populating'
    closeResultsModal()

    // Call the backend AI populate endpoint
    const response = await axios.post('/consultations/ai-populate', {
      encounter_id: props.encounterId,
      extracted_data: analysisResults.value || {},
      raw_data: {
        transcript: transcript.value,
        diarized_transcript: diarizedTranscript.value
      }
    })

    if (response.data.success) {
      // Emit the AI populate event with extracted data for frontend updates
      emit('ai-populate', {
        extractedData: analysisResults.value || {},
        rawData: {
          transcript: transcript.value,
          diarizedTranscript: diarizedTranscript.value
        }
      })

      status.value = 'completed'
      emit('records-updated')

      // Reset to idle after showing completion
      setTimeout(() => {
        status.value = 'idle'
      }, 3000)
    } else {
      throw new Error(response.data.message || 'Failed to populate records')
    }
  } catch (error) {
    console.error('Error populating records:', error)
    mediaError.value = `Failed to populate records: ${error instanceof Error ? error.message : 'Unknown error'}`
    status.value = 'transcribed'
  }
}

// Mobile Recording Methods
const showMobileRecordingModal = (): void => {
  showMobileDialog.value = true
  mobileRecordingError.value = null
  mobileRecordingSuccess.value = null
  createMobileRecordingSession()
}

const closeMobileModal = (): void => {
  showMobileDialog.value = false
  if (sessionCheckTimer.value) {
    clearInterval(sessionCheckTimer.value)
    sessionCheckTimer.value = null
  }
}

const createMobileRecordingSession = async (): Promise<void> => {
  isCreatingSession.value = true
  mobileRecordingError.value = null
  mobileRecordingSuccess.value = null

  try {
    const response = await axios.post('/consultations/mobile-recording/create', {
      encounter_id: props.encounterId,
      expire_minutes: 30
    })

    if (response.data.success) {
      const sessionData = response.data.data
      sessionId.value = sessionData.session_id
      qrCodeUrl.value = sessionData.qr_code_url
      recordUrl.value = sessionData.record_url

      startSessionStatusCheck()
      mobileRecordingSuccess.value = 'QR code generated successfully. Scan with your mobile device to record audio.'
    } else {
      throw new Error(response.data.message || 'Failed to create mobile recording session')
    }
  } catch (error) {
    console.error('Error creating mobile recording session:', error)
    mobileRecordingError.value = `Error: ${error instanceof Error ? error.message : 'Failed to create recording session'}`
  } finally {
    isCreatingSession.value = false
  }
}

const startSessionStatusCheck = (): void => {
  if (sessionCheckTimer.value) {
    clearInterval(sessionCheckTimer.value)
  }

  sessionCheckTimer.value = setInterval(() => {
    checkSessionStatus()
  }, 10000) // Check every 10 seconds
}

const checkSessionStatus = async (): Promise<void> => {
  if (!sessionId.value) return

  try {
    const response = await axios.post('/consultations/mobile-recording/status', {
      session_id: sessionId.value
    })

    if (response.data.success) {
      const statusData = response.data.data
      sessionStatus.value = statusData.status
      sessionTimeRemaining.value = formatTimeRemaining(statusData.time_remaining)
      sessionProcessed.value = statusData.processed

      if (statusData.processed && statusData.transcription_ready) {
        mobileRecordingSuccess.value = 'Audio recording has been uploaded! Click "Load Transcript" to continue.'

        if (sessionCheckTimer.value) {
          clearInterval(sessionCheckTimer.value)
          sessionCheckTimer.value = null
        }

        recordingDocumentId.value = statusData.document_id
      }
    }
  } catch (error) {
    console.error('Error checking session status:', error)
    if (!mobileRecordingSuccess.value) {
      mobileRecordingError.value = 'Could not verify recording status. Try refreshing the page.'
    }
  }
}

const formatTimeRemaining = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes <= 0 && remainingSeconds <= 0) {
    return 'Expired'
  }

  return `${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`
}

const retrieveTranscript = async (): Promise<void> => {
  if (!sessionId.value) {
    mobileRecordingError.value = 'Missing session ID. Please try again.'
    return
  }

  if (!sessionProcessed.value) {
    mobileRecordingError.value = 'Recording not yet processed. Please wait and try again.'
    return
  }

  mobileRecordingError.value = null
  mediaError.value = null

  try {
    mobileRecordingSuccess.value = 'Processing mobile recording...'

    showMobileDialog.value = false
    status.value = 'processing'
    processingStatus.value = 'Processing mobile recording transcript...'
    processingProgress.value = 20

    const response = await axios.post('/consultations/mobile-recording/process', {
      session_id: sessionId.value,
      encounter_id: props.encounterId
    })

    processingProgress.value = 80

    if (response.data.success) {
      const transcriptData = response.data.data
      transcript.value = transcriptData.transcript || ''
      diarizedTranscript.value = transcriptData.diarized_transcript || []

      processingProgress.value = 100
      status.value = 'transcribed'

      setTimeout(() => {
        showTranscriptDialog()
      }, 500)
    } else {
      throw new Error(response.data.message || 'Failed to process mobile recording')
    }
  } catch (error) {
    console.error('Error retrieving transcript:', error)
    mobileRecordingError.value = `Error: ${error instanceof Error ? error.message : 'Failed to retrieve transcript'}`
    status.value = 'idle'
    processingProgress.value = 0
  }
}

// Lifecycle hooks
onMounted(() => {
  checkRecordingAvailability()
})

onUnmounted(() => {
  cleanup()
})

// Watch for encounter ID changes
watch(() => props.encounterId, (newId) => {
  console.log('AIScribe encounterId updated:', newId)
})
</script>

<style scoped>
/* Animated gradient background */
@keyframes gradient-x {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease-in-out infinite;
  background-size: 200% 200%;
}

/* Wave animations for recording indicator */
@keyframes wave-1 {
  0%, 100% { height: 8px; }
  50% { height: 24px; }
}

@keyframes wave-2 {
  0%, 100% { height: 12px; }
  50% { height: 20px; }
}

@keyframes wave-3 {
  0%, 100% { height: 16px; }
  50% { height: 28px; }
}

@keyframes wave-4 {
  0%, 100% { height: 10px; }
  50% { height: 22px; }
}

@keyframes wave-5 {
  0%, 100% { height: 14px; }
  50% { height: 26px; }
}

.animate-wave-1 {
  animation: wave-1 1.2s ease-in-out infinite;
}

.animate-wave-2 {
  animation: wave-2 1.1s ease-in-out infinite;
}

.animate-wave-3 {
  animation: wave-3 1.3s ease-in-out infinite;
}

.animate-wave-4 {
  animation: wave-4 1.0s ease-in-out infinite;
}

.animate-wave-5 {
  animation: wave-5 1.4s ease-in-out infinite;
}

/* Subtle glow effect for recording state */
.recording-glow {
  box-shadow: 0 0 20px rgba(20, 184, 166, 0.3);
}
</style>
