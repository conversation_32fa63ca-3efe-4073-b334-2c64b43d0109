<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import axios from 'axios';

interface SubscriptionPlan {
  id: number;
  name: string;
  slug: string;
  price: number;
  formatted_price: string;
  currency: string;
  interval: string;
  description: string;
  features: string[];
  is_free: boolean;
}

interface CurrentSubscription {
  plan: SubscriptionPlan | null;
  status: string;
  started_at: string | null;
  ends_at: string | null;
  is_active: boolean;
}

const currentSubscription = ref<CurrentSubscription | null>(null);
const loading = ref(true);

// Fetch current subscription data
const fetchSubscription = async () => {
  try {
    const response = await axios.get('/web-api/subscription/current');
    if (response.data.success) {
      currentSubscription.value = response.data.data; // Fix: use 'data' not 'subscription'
    }
  } catch (error) {
    console.log('No subscription found or error:', error);
    currentSubscription.value = null;
  } finally {
    loading.value = false;
  }
};

// Plan info computed property
const planInfo = computed(() => {
  if (!currentSubscription.value || !currentSubscription.value.plan || currentSubscription.value.plan.is_free) {
    return {
      name: 'Free Plan',
      status: 'Free',
      icon: '🆓',
      description: 'Basic features included',
      isFreePlan: true,
      buttonText: 'Upgrade',
      buttonVariant: 'default' as const
    };
  }

  const plan = currentSubscription.value.plan;
  const subscription = currentSubscription.value;
  
  return {
    name: plan.name,
    status: subscription.is_active ? 'Active' : 'Inactive',
    icon: '✨',
    description: plan.formatted_price + ' ' + plan.interval,
    isFreePlan: false,
    buttonText: 'Manage',
    buttonVariant: 'outline' as const
  };
});

onMounted(() => {
  fetchSubscription();
});
</script>

<template>
  <div v-if="!loading" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6 shadow-sm">
    <div class="flex items-center justify-between">
      <!-- Plan Info -->
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-lg">
          {{ planInfo.icon }}
        </div>
        <div>
          <div class="flex items-center space-x-2">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ planInfo.name }}</h3>
            <span 
              :class="[
                'px-2 py-0.5 text-xs font-medium rounded-full',
                planInfo.isFreePlan 
                  ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' 
                  : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              ]"
            >
              {{ planInfo.status }}
            </span>
          </div>
          <p class="text-xs text-gray-600 dark:text-gray-400">{{ planInfo.description }}</p>
        </div>
      </div>

      <!-- Action Button -->
      <Link href="/settings/subscription">
        <Button 
          size="sm" 
          :variant="planInfo.buttonVariant"
          :class="[
            'text-xs px-4 py-2',
            planInfo.isFreePlan 
              ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0' 
              : 'border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700'
          ]"
        >
          {{ planInfo.buttonText }}
        </Button>
      </Link>
    </div>
  </div>
  
  <!-- Loading state -->
  <div v-else class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6 shadow-sm">
    <div class="flex items-center space-x-3">
      <div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
      <div class="space-y-2">
        <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
        <div class="h-2 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
      </div>
    </div>
  </div>
</template>