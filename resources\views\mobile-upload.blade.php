<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Mobile Upload - Medroid EHR</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-blue-600 text-white rounded-lg p-4 mb-4">
                <h1 class="text-xl font-bold">Medroid EHR</h1>
                <p class="text-blue-100">Mobile Upload</p>
            </div>
            <p class="text-gray-600 text-sm">Upload documents or photos for your consultation</p>
        </div>

        <!-- Session Status -->
        <div id="session-status" class="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Session Status:</span>
                <span id="status-indicator" class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Checking...
                </span>
            </div>
            <div id="session-timer" class="text-xs text-gray-500 mt-2"></div>
        </div>

        <!-- Upload Interface -->
        <div id="upload-interface" class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h3 class="font-medium text-gray-900 mb-4">Upload Files</h3>
            
            <!-- File Upload Area -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
                <input type="file" id="file-upload" multiple accept="image/*,.pdf,.doc,.docx" class="hidden">
                <label for="file-upload" class="cursor-pointer">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="text-sm text-gray-600 mb-2">Click to select files or take a photo</p>
                    <p class="text-xs text-gray-500">Images, PDFs, Documents</p>
                </label>
            </div>

            <!-- Camera Capture -->
            <div class="grid grid-cols-2 gap-3 mb-4">
                <button id="camera-btn" class="flex items-center justify-center gap-2 p-3 border rounded-lg hover:bg-gray-50">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="text-sm">Camera</span>
                </button>
                <button id="gallery-btn" class="flex items-center justify-center gap-2 p-3 border rounded-lg hover:bg-gray-50">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span class="text-sm">Gallery</span>
                </button>
            </div>

            <!-- Selected Files -->
            <div id="selected-files" class="space-y-2 mb-4"></div>

            <!-- Upload Progress -->
            <div id="upload-progress" class="hidden mb-4">
                <div class="bg-gray-200 rounded-full h-2 mb-2">
                    <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-600 text-center">Uploading files...</p>
            </div>

            <!-- Upload Button -->
            <button id="upload-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                Upload Files
            </button>
        </div>

        <!-- Messages -->
        <div id="messages" class="space-y-2"></div>

        <!-- Footer -->
        <div class="text-center text-xs text-gray-500 mt-8">
            <p>Secure upload session</p>
            <p>Session ID: {{ $sessionId }}</p>
        </div>
    </div>

    <script>
        // Session state
        let sessionId = '{{ $sessionId }}';
        let selectedFiles = [];
        let sessionCheckTimer = null;

        // DOM elements
        const fileUpload = document.getElementById('file-upload');
        const cameraBtn = document.getElementById('camera-btn');
        const galleryBtn = document.getElementById('gallery-btn');
        const selectedFilesContainer = document.getElementById('selected-files');
        const uploadBtn = document.getElementById('upload-btn');
        const uploadProgress = document.getElementById('upload-progress');
        const progressBar = document.getElementById('progress-bar');
        const statusIndicator = document.getElementById('status-indicator');
        const sessionTimer = document.getElementById('session-timer');
        const messagesContainer = document.getElementById('messages');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkSessionStatus();
            startSessionMonitoring();
            setupEventListeners();
        });

        function setupEventListeners() {
            fileUpload.addEventListener('change', handleFileSelection);
            cameraBtn.addEventListener('click', openCamera);
            galleryBtn.addEventListener('click', openGallery);
            uploadBtn.addEventListener('click', uploadFiles);
        }

        function checkSessionStatus() {
            fetch('/check-mobile-upload-session', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ session_id: sessionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateSessionStatus(data.data);
                } else {
                    showMessage('Session expired or invalid', 'error');
                    statusIndicator.textContent = 'Expired';
                    statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
                    disableUpload();
                }
            })
            .catch(error => {
                console.error('Error checking session:', error);
                showMessage('Failed to check session status', 'error');
            });
        }

        function updateSessionStatus(sessionData) {
            if (sessionData.is_expired) {
                statusIndicator.textContent = 'Expired';
                statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
                sessionTimer.textContent = 'Session has expired';
                disableUpload();
            } else {
                statusIndicator.textContent = 'Active';
                statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
                sessionTimer.textContent = `Session expires at ${new Date(sessionData.expires_at).toLocaleTimeString()}`;
            }
        }

        function startSessionMonitoring() {
            sessionCheckTimer = setInterval(checkSessionStatus, 60000); // Check every minute
        }

        function handleFileSelection(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function openCamera() {
            // Create a file input that opens camera
            const cameraInput = document.createElement('input');
            cameraInput.type = 'file';
            cameraInput.accept = 'image/*';
            cameraInput.capture = 'environment'; // Use rear camera
            cameraInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                addFiles(files);
            });
            cameraInput.click();
        }

        function openGallery() {
            fileUpload.click();
        }

        function addFiles(files) {
            files.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });
            updateSelectedFilesDisplay();
            updateUploadButton();
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateSelectedFilesDisplay();
            updateUploadButton();
        }

        function updateSelectedFilesDisplay() {
            selectedFilesContainer.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
                
                fileItem.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">${file.name}</p>
                            <p class="text-xs text-gray-500">${formatFileSize(file.size)}</p>
                        </div>
                    </div>
                    <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                
                selectedFilesContainer.appendChild(fileItem);
            });
        }

        function updateUploadButton() {
            uploadBtn.disabled = selectedFiles.length === 0;
            uploadBtn.textContent = selectedFiles.length > 0 ? `Upload ${selectedFiles.length} file(s)` : 'Upload Files';
        }

        function uploadFiles() {
            if (selectedFiles.length === 0) return;

            const formData = new FormData();
            formData.append('session_id', sessionId);
            
            selectedFiles.forEach((file, index) => {
                formData.append(`files[${index}]`, file);
            });

            uploadProgress.classList.remove('hidden');
            uploadBtn.disabled = true;

            fetch('/consultations/mobile-upload', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`Successfully uploaded ${selectedFiles.length} file(s)!`, 'success');
                    selectedFiles = [];
                    updateSelectedFilesDisplay();
                    updateUploadButton();
                } else {
                    showMessage('Failed to upload files: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showMessage('Failed to upload files', 'error');
            })
            .finally(() => {
                uploadProgress.classList.add('hidden');
                progressBar.style.width = '0%';
                uploadBtn.disabled = false;
            });
        }

        function disableUpload() {
            uploadBtn.disabled = true;
            fileUpload.disabled = true;
            cameraBtn.disabled = true;
            galleryBtn.disabled = true;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showMessage(message, type) {
            const messageEl = document.createElement('div');
            const bgColor = type === 'error' ? 'bg-red-100 text-red-800' : 
                           type === 'success' ? 'bg-green-100 text-green-800' : 
                           'bg-blue-100 text-blue-800';
            
            messageEl.className = `p-3 rounded-lg ${bgColor} text-sm`;
            messageEl.textContent = message;
            
            messagesContainer.appendChild(messageEl);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                messageEl.remove();
            }, 5000);
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (sessionCheckTimer) clearInterval(sessionCheckTimer);
        });

        // Make removeFile function global
        window.removeFile = removeFile;
    </script>
</body>
</html>
