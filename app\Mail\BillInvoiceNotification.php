<?php

namespace App\Mail;

use App\Models\Bill;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class BillInvoiceNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Bill $bill,
        public string $paymentLink,
        public ?string $invoicePath = null
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Invoice #{$this->bill->bill_number} from {$this->bill->clinic->name}",
            from: [
                'address' => config('mail.from.address'),
                'name' => $this->bill->clinic->name,
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.bills.invoice',
            with: [
                'bill' => $this->bill,
                'clinic' => $this->bill->clinic,
                'patient' => $this->bill->patient,
                'paymentLink' => $this->paymentLink,
                'invoiceNumber' => $this->bill->bill_number,
                'totalAmount' => $this->bill->total_amount,
                'dueDate' => $this->bill->due_date,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];

        if ($this->invoicePath && Storage::disk('local')->exists($this->invoicePath)) {
            $attachments[] = Attachment::fromStorageDisk('local', $this->invoicePath)
                ->as("invoice-{$this->bill->bill_number}.pdf")
                ->withMime('application/pdf');
        }

        return $attachments;
    }
}
