<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescription_refill_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('original_prescription_id')->constrained('prescriptions')->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('requested_by')->constrained('users')->onDelete('cascade'); // Patient or caregiver
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null'); // Clinician
            $table->foreignId('new_prescription_id')->nullable()->constrained('prescriptions')->onDelete('set null');
            $table->enum('status', ['pending', 'approved', 'rejected', 'expired'])->default('pending');
            $table->text('request_reason')->nullable(); // Why refill is needed
            $table->text('patient_notes')->nullable(); // Patient's additional notes
            $table->json('requested_items')->nullable(); // Which items to refill
            $table->datetime('requested_at');
            $table->datetime('reviewed_at')->nullable();
            $table->text('review_notes')->nullable(); // Clinician's notes
            $table->text('rejection_reason')->nullable();
            $table->boolean('requires_consultation')->default(false); // Need appointment first
            $table->date('expires_at')->nullable(); // Request expiry date
            $table->timestamps();

            // Indexes
            $table->index(['patient_id', 'status']);
            $table->index(['reviewed_by', 'status']);
            $table->index('status');
            $table->index('requested_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescription_refill_requests');
    }
};
