<template>
    <AppLayout>
        <Head title="Consultation Dashboard" />
        
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Consultation Dashboard</h1>
                        <p class="text-gray-600" v-if="dashboardData.patient">
                            {{ dashboardData.patient.user?.first_name }} {{ dashboardData.patient.user?.last_name }}
                        </p>
                    </div>
                    <div class="flex space-x-3">
                        <button 
                            @click="goBack"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            Back to Consultations
                        </button>
                        <button 
                            @click="startNewConsultation"
                            v-if="dashboardData.patient"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                        >
                            Start New Consultation
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>

            <!-- Dashboard Content -->
            <div v-else-if="dashboardData.patient" class="space-y-6">
                <!-- Patient Header -->
                <div class="bg-white border-b border-gray-200 shadow-sm">
                    <div class="px-8 py-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6">
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900">
                                        {{ dashboardData.patient.first_name || dashboardData.patient.user?.first_name }} {{ dashboardData.patient.last_name || dashboardData.patient.user?.last_name }}
                                    </h1>
                                    <p class="text-sm text-gray-600 mt-1 font-medium">{{ dashboardData.patient.patient_unique_id || dashboardData.patient.id }}</p>
                                </div>

                            </div>
                            <div class="flex items-center space-x-3">
                                <button
                                    @click="openEditPatientModal"
                                    class="inline-flex items-center px-4 py-2.5 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="bg-white border-b border-gray-200 shadow-sm">
                    <nav class="flex space-x-8 px-8" aria-label="Tabs">
                        <button
                            v-for="tab in tabs"
                            :key="tab.name"
                            @click="activeTab = tab.name"
                            :class="[
                                activeTab === tab.name
                                    ? 'border-blue-500 text-blue-600 font-semibold'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
                            ]"
                        >
                            {{ tab.label }}
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="bg-white shadow-sm">
                    <!-- Summary Tab - Shows consultation details (current or last) -->
                    <div v-if="activeTab === 'summary'" class="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Present Concerns -->
                            <div v-if="getSelectedTabContent('concerns').length > 0" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Present Concerns</h3>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div v-for="entry in getSelectedTabContent('concerns')" :key="entry.id" class="mb-2 last:mb-0">
                                        <p class="text-sm text-gray-800">• {{ entry.content }}</p>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('concerns')[0]?.created_at) }}</p>
                            </div>

                            <!-- Plan -->
                            <div v-if="getSelectedTabContent('plan').length > 0" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Plan</h3>
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <div v-for="entry in getSelectedTabContent('plan')" :key="entry.id" class="mb-2 last:mb-0">
                                        <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('plan')[0]?.created_at) }}</p>
                            </div>

                            <!-- History -->
                            <div v-if="getSelectedTabContent('history').length > 0" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">History</h3>
                                <div class="bg-yellow-50 rounded-lg p-4">
                                    <div v-for="entry in getSelectedTabContent('history')" :key="entry.id" class="mb-2 last:mb-0">
                                        <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('history')[0]?.created_at) }}</p>
                            </div>

                            <!-- Allergies -->
                            <div v-if="getSelectedTabContent('allergies').length > 0 || dashboardData.patient?.allergies" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Allergies</h3>
                                <div class="bg-red-50 rounded-lg p-4">
                                    <div v-if="getSelectedTabContent('allergies').length > 0">
                                        <div v-for="entry in getSelectedTabContent('allergies')" :key="entry.id" class="mb-2 last:mb-0">
                                            <p class="text-sm text-red-800">• {{ entry.content }}</p>
                                        </div>
                                    </div>
                                    <div v-else-if="dashboardData.patient?.allergies">
                                        <p v-for="allergy in dashboardData.patient.allergies" :key="allergy" class="text-sm text-red-800 mb-1 last:mb-0">• {{ allergy }}</p>
                                    </div>
                                    <div v-else>
                                        <p class="text-sm text-red-800">• No known drug allergies</p>
                                    </div>
                                </div>
                                <p v-if="getSelectedTabContent('allergies')?.[0]" class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('allergies')[0].created_at) }}</p>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Examination -->
                            <div v-if="getSelectedTabContent('examination').length > 0" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Examination</h3>
                                <div class="bg-green-50 rounded-lg p-4">
                                    <div v-for="entry in getSelectedTabContent('examination')" :key="entry.id" class="mb-2 last:mb-0">
                                        <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('examination')[0]?.created_at) }}</p>
                            </div>

                            <!-- Medical History -->
                            <div v-if="getSelectedTabContent('medical_history').length > 0 || dashboardData.patient?.medical_history" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Medical History</h3>
                                <div class="bg-purple-50 rounded-lg p-4">
                                    <div v-if="getSelectedTabContent('medical_history').length > 0">
                                        <div v-for="entry in getSelectedTabContent('medical_history')" :key="entry.id" class="mb-2 last:mb-0">
                                            <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                        </div>
                                    </div>
                                    <div v-else-if="dashboardData.patient?.medical_history">
                                        <p class="text-sm text-gray-800 leading-relaxed">{{ dashboardData.patient.medical_history }}</p>
                                    </div>
                                </div>
                                <p v-if="getSelectedTabContent('medical_history')?.[0]" class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('medical_history')[0].created_at) }}</p>
                            </div>

                            <!-- Current Medications -->
                            <div v-if="getSelectedTabContent('medications').length > 0 || dashboardData.patient?.current_medications" class="pb-6 border-b border-gray-100">
                                <h3 class="text-base font-semibold text-gray-900 mb-3">Current Medications</h3>
                                <div class="bg-indigo-50 rounded-lg p-4">
                                    <div v-if="getSelectedTabContent('medications').length > 0">
                                        <div v-for="entry in getSelectedTabContent('medications')" :key="entry.id" class="mb-2 last:mb-0">
                                            <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                        </div>
                                    </div>
                                    <div v-else-if="dashboardData.patient?.current_medications">
                                        <p class="text-sm text-gray-800 leading-relaxed">{{ dashboardData.patient.current_medications }}</p>
                                    </div>
                                </div>
                                <p v-if="getSelectedTabContent('medications')?.[0]" class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('medications')[0].created_at) }}</p>
                            </div>
                        </div>

                        <!-- Show message if no data available -->
                        <div v-if="!hasAnyConsultationData" class="col-span-2 text-center py-12">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Consultation Data</h3>
                            <p class="text-gray-500">No consultation details available for this patient</p>
                        </div>
                    </div>

                    <!-- Consultation Tab -->
                    <div v-else-if="activeTab === 'consultation'" class="flex h-[calc(100vh-300px)]">
                        <!-- Left Sidebar - Consultation History -->
                        <div class="w-80 border-r border-gray-200 bg-gray-50">
                            <div class="p-4">
                                <div v-if="dashboardData.recent_consultations.length === 0" class="text-center py-8 text-gray-500">
                                    No consultations found
                                </div>
                                <div v-else class="space-y-3">
                                    <div
                                        v-for="consultation in dashboardData.recent_consultations"
                                        :key="consultation.id"
                                        @click="selectConsultation(consultation.id)"
                                        :class="[
                                            'p-4 border rounded-lg cursor-pointer transition-colors duration-200',
                                            selectedConsultationId === consultation.id
                                                ? 'border-blue-500 bg-blue-50'
                                                : 'border-gray-200 hover:border-gray-300 hover:bg-white bg-white'
                                        ]"
                                    >
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-semibold text-gray-900 text-sm">
                                                {{ consultation.provider?.user?.first_name }} {{ consultation.provider?.user?.last_name }}
                                            </span>
                                            <div class="flex items-center space-x-2">
                                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                                    </svg>
                                                </button>
                                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-500">{{ formatDate(consultation.created_at) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Panel - Consultation Details -->
                        <div class="flex-1 bg-white overflow-y-auto">
                            <div v-if="selectedConsultation" class="p-6">
                                <!-- Provider Header -->
                                <div class="mb-6 pb-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h2 class="text-xl font-bold text-gray-900">
                                                {{ selectedConsultation.provider?.user?.first_name }} {{ selectedConsultation.provider?.user?.last_name }}
                                            </h2>
                                            <p class="text-sm text-gray-600">{{ formatDate(selectedConsultation.created_at) }}</p>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Consultation Content Grid -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Left Column -->
                                    <div class="space-y-6">
                                        <!-- Present Concerns -->
                                        <div v-if="getSelectedTabContent('concerns').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">Present Concerns</h3>
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('concerns')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">• {{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('concerns')[0]?.created_at) }}</p>
                                        </div>

                                        <!-- Plan -->
                                        <div v-if="getSelectedTabContent('plan').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">Plan</h3>
                                            <div class="bg-blue-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('plan')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('plan')[0]?.created_at) }}</p>
                                        </div>

                                        <!-- History -->
                                        <div v-if="getSelectedTabContent('history').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">History</h3>
                                            <div class="bg-yellow-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('history')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('history')[0]?.created_at) }}</p>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="space-y-6">
                                        <!-- Examination -->
                                        <div v-if="getSelectedTabContent('examination').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">Examination</h3>
                                            <div class="bg-green-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('examination')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('examination')[0]?.created_at) }}</p>
                                        </div>

                                        <!-- Medical History -->
                                        <div v-if="getSelectedTabContent('medical_history').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">Medical History</h3>
                                            <div class="bg-purple-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('medical_history')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('medical_history')[0]?.created_at) }}</p>
                                        </div>

                                        <!-- Allergies -->
                                        <div v-if="getSelectedTabContent('allergies').length > 0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-3">Allergies</h3>
                                            <div class="bg-red-50 rounded-lg p-4">
                                                <div v-for="entry in getSelectedTabContent('allergies')" :key="entry.id" class="mb-2 last:mb-0">
                                                    <p class="text-sm text-gray-800">{{ entry.content }}</p>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">Recorded: {{ formatDate(getSelectedTabContent('allergies')[0]?.created_at) }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- No Consultation Selected -->
                            <div v-else class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Consultation</h3>
                                    <p class="text-gray-500">Choose a consultation from the list to view details</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Prescription Tab -->
                    <div v-else-if="activeTab === 'prescription'" class="flex h-[calc(100vh-300px)]">
                        <!-- Left Sidebar - Prescription List -->
                        <div class="w-80 border-r border-gray-200 bg-gray-50">
                            <div class="p-4">
                                <div v-if="getConsultationPrescriptions().length === 0" class="text-center py-8 text-gray-500">
                                    No prescriptions found for this consultation
                                </div>
                                <div v-else class="space-y-3">
                                    <div
                                        v-for="prescription in getConsultationPrescriptions()"
                                        :key="prescription.id"
                                        @click="selectPrescription(prescription.id)"
                                        :class="[
                                            'p-4 border rounded-lg cursor-pointer transition-colors duration-200',
                                            selectedPrescriptionId === prescription.id
                                                ? 'border-blue-500 bg-blue-50'
                                                : 'border-gray-200 hover:border-gray-300 hover:bg-white bg-white'
                                        ]"
                                    >
                                        <div class="mb-2">
                                            <span class="font-semibold text-gray-900 text-sm">
                                                {{ prescription.prescription_number ? `Prescription #${prescription.prescription_number}` : prescription.medication_name }}
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500 mb-1">{{ formatDate(prescription.prescribed_date || prescription.created_at) }}</p>
                                        <p class="text-xs text-gray-600">
                                            {{ prescription.total_items ? `${prescription.total_items} medicine prescribed` : prescription.dosage }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Panel - Prescription Details -->
                        <div class="flex-1 bg-white overflow-y-auto">
                            <div v-if="selectedPrescription" class="p-6">
                                <!-- Prescription Header -->
                                <div class="mb-6 pb-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h2 class="text-xl font-bold text-gray-900">
                                                Prescription #{{ selectedPrescription.prescription_number }}
                                            </h2>
                                            <p class="text-sm text-gray-600">{{ formatDate(selectedPrescription.prescribed_date) }}</p>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                                </svg>
                                            </button>
                                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Prescription Details -->
                                <div class="space-y-6">
                                    <!-- Medication Items -->
                                    <div v-if="selectedPrescription.items && selectedPrescription.items.length > 0">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Medications</h3>
                                        <div class="space-y-4">
                                            <div
                                                v-for="item in selectedPrescription.items"
                                                :key="item.id"
                                                class="bg-gray-50 rounded-lg p-4 border border-gray-200"
                                            >
                                                <div class="flex items-start justify-between mb-3">
                                                    <div>
                                                        <h4 class="font-semibold text-gray-900">{{ item.medication_name }}</h4>
                                                        <p class="text-sm text-gray-600">{{ item.strength }} - {{ item.form }}</p>
                                                    </div>
                                                    <div class="text-right">
                                                        <span class="text-sm font-medium text-gray-900">Qty: {{ item.quantity }}</span>
                                                        <p class="text-xs text-gray-500">{{ item.quantity_unit }}</p>
                                                    </div>
                                                </div>

                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                                    <div>
                                                        <span class="font-medium text-gray-700">Dose:</span>
                                                        <span class="text-gray-600 ml-1">{{ item.dosage }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="font-medium text-gray-700">Route:</span>
                                                        <span class="text-gray-600 ml-1">{{ item.route }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="font-medium text-gray-700">Frequency:</span>
                                                        <span class="text-gray-600 ml-1">{{ item.frequency }}</span>
                                                    </div>
                                                    <div v-if="item.duration_days">
                                                        <span class="font-medium text-gray-700">Duration:</span>
                                                        <span class="text-gray-600 ml-1">{{ item.duration_days }} days</span>
                                                    </div>
                                                </div>

                                                <div v-if="item.directions_for_use" class="mt-3">
                                                    <span class="font-medium text-gray-700">Directions:</span>
                                                    <p class="text-gray-600 mt-1">{{ item.directions_for_use }}</p>
                                                </div>

                                                <div v-if="item.additional_instructions" class="mt-2">
                                                    <span class="font-medium text-gray-700">Additional Instructions:</span>
                                                    <p class="text-gray-600 mt-1">{{ item.additional_instructions }}</p>
                                                </div>

                                                <div v-if="item.warnings" class="mt-2">
                                                    <span class="font-medium text-red-700">Warnings:</span>
                                                    <p class="text-red-600 mt-1">{{ item.warnings }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Clinical Information -->
                                    <div v-if="selectedPrescription.clinical_indication || selectedPrescription.additional_instructions">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Clinical Information</h3>
                                        <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                            <div v-if="selectedPrescription.clinical_indication" class="mb-3">
                                                <span class="font-medium text-gray-700">Clinical Indication:</span>
                                                <p class="text-gray-600 mt-1">{{ selectedPrescription.clinical_indication }}</p>
                                            </div>
                                            <div v-if="selectedPrescription.additional_instructions">
                                                <span class="font-medium text-gray-700">Additional Instructions:</span>
                                                <p class="text-gray-600 mt-1">{{ selectedPrescription.additional_instructions }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Prescription Status & Info -->
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Prescription Information</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <div class="space-y-2 text-sm">
                                                    <div>
                                                        <span class="font-medium text-gray-700">Status:</span>
                                                        <span :class="[
                                                            'ml-2 px-2 py-1 rounded-full text-xs font-medium',
                                                            selectedPrescription.status === 'active' ? 'bg-green-100 text-green-800' :
                                                            selectedPrescription.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                                                            selectedPrescription.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                                            'bg-gray-100 text-gray-800'
                                                        ]">
                                                            {{ selectedPrescription.status }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="font-medium text-gray-700">Type:</span>
                                                        <span class="text-gray-600 ml-1">{{ selectedPrescription.type }}</span>
                                                    </div>
                                                    <div v-if="selectedPrescription.valid_until">
                                                        <span class="font-medium text-gray-700">Valid Until:</span>
                                                        <span class="text-gray-600 ml-1">{{ formatDate(selectedPrescription.valid_until) }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="selectedPrescription.pharmacy_name" class="bg-gray-50 rounded-lg p-4">
                                                <div class="space-y-2 text-sm">
                                                    <div>
                                                        <span class="font-medium text-gray-700">Pharmacy:</span>
                                                        <p class="text-gray-600 mt-1">{{ selectedPrescription.pharmacy_name }}</p>
                                                    </div>
                                                    <div v-if="selectedPrescription.pharmacy_address">
                                                        <span class="font-medium text-gray-700">Address:</span>
                                                        <p class="text-gray-600 mt-1">{{ selectedPrescription.pharmacy_address }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- No Prescription Selected -->
                            <div v-else class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Prescription</h3>
                                    <p class="text-gray-500">Choose a prescription from the list to view details</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div v-else-if="activeTab === 'documents'" class="p-6">
                        <div class="mb-6">
                            <h2 class="text-xl font-bold text-gray-900">Documents</h2>
                        </div>

                        <div v-if="getConsultationDocuments().length === 0" class="text-center py-12">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Documents</h3>
                            <p class="text-gray-500">No documents found for this consultation</p>
                        </div>

                        <div v-else class="space-y-3">
                            <div
                                v-for="document in getConsultationDocuments()"
                                :key="document.id"
                                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                            >
                                <div class="flex items-center space-x-3">
                                    <!-- Document Icon -->
                                    <div class="flex-shrink-0">
                                        <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>

                                    <!-- Document Details -->
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ document.title || document.file_name }}</h4>
                                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                                            <span>{{ document.document_type || 'Document' }}</span>
                                            <span>•</span>
                                            <span>{{ formatDate(document.created_at) }}</span>
                                            <span v-if="document.file_size">•</span>
                                            <span v-if="document.file_size">{{ formatFileSize(document.file_size) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex items-center space-x-2">
                                    <button
                                        @click="viewDocument(document)"
                                        class="p-2 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                                        title="View Document"
                                    >
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                    <button
                                        @click="downloadDocument(document)"
                                        class="p-2 text-gray-400 hover:text-green-600 transition-colors duration-200"
                                        title="Download Document"
                                    >
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="text-center py-12">
                <p class="text-red-600">{{ error }}</p>
                <button
                    @click="fetchDashboardData"
                    class="mt-4 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                    Retry
                </button>
            </div>
        </div>

        <!-- Patient Edit Modal -->
        <PatientEditModal
            :is-open="showEditPatientModal"
            :patient-id="dashboardData.patient?.id"
            @close="closeEditPatientModal"
            @saved="handlePatientSaved"
        />
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '../../layouts/AppLayout.vue'
import PatientEditModal from '../../components/PatientEditModal.vue'
import { Head, router } from '@inertiajs/vue3'
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'

interface Props {
    patientId: string | number
}

interface TabEntry {
    id: number
    content: string
    created_at: string
}

interface Consultation {
    id: number
    patient_id: number
    provider_id: number
    status: string
    created_at: string
    main_tabs?: Record<string, TabEntry[]>
    additional_tabs?: Record<string, TabEntry[]>
    provider?: any
    main_prescriptions?: any[]
    prescriptions?: any[]
    documents?: any[]
}

interface Patient {
    id: number
    first_name?: string
    last_name?: string
    patient_unique_id?: string
    allergies?: string[]
    medical_history?: string
    current_medications?: string
    user?: {
        first_name: string
        last_name: string
    }
}

interface DashboardData {
    patient: Patient | null
    consultation: Consultation | null
    recent_consultations: Consultation[]
    upcoming_appointments: any[]
}

const props = defineProps<Props>()

const loading = ref(true)
const error = ref('')
const activeTab = ref('summary')
const selectedConsultationId = ref<number | null>(null)
const selectedConsultation = ref<Consultation | null>(null)
const selectedPrescriptionId = ref<number | null>(null)
const selectedPrescription = ref<any | null>(null)
const showEditPatientModal = ref(false)
const dashboardData = ref<DashboardData>({
    patient: null,
    consultation: null,
    recent_consultations: [],
    upcoming_appointments: []
})

const tabs = [
    { name: 'summary', label: 'Summary' },
    { name: 'consultation', label: 'Consultation' },
    { name: 'prescription', label: 'Prescription' },
    { name: 'documents', label: 'Documents' }
]

const fetchDashboardData = async () => {
    loading.value = true
    error.value = ''

    try {
        const params = new URLSearchParams(window.location.search)
        const consultationId = params.get('consultation_id')

        const queryParams = new URLSearchParams({
            patient_id: props.patientId.toString()
        })

        if (consultationId) {
            queryParams.append('consultation_id', consultationId)
        }

        const response = await axios.get(`/consultation-dashboard-data?${queryParams}`)

        if (response.data.success) {
            dashboardData.value = response.data.data

            // Set the consultation for summary tab
            // If consultation_id was provided, use that consultation
            // Otherwise, use the most recent consultation
            if (consultationId && dashboardData.value.consultation) {
                // Consultation from URL parameter
                selectedConsultation.value = dashboardData.value.consultation
                selectedConsultationId.value = dashboardData.value.consultation.id
            } else if (dashboardData.value.recent_consultations.length > 0) {
                // Most recent consultation
                selectedConsultation.value = dashboardData.value.recent_consultations[0]
                selectedConsultationId.value = dashboardData.value.recent_consultations[0].id
            }
        } else {
            error.value = response.data.message || 'Failed to load dashboard data'
        }
    } catch (err: any) {
        error.value = err.response?.data?.message || 'Failed to load dashboard data'

    } finally {
        loading.value = false
    }
}

const formatDate = (date: string) => {
    if (!date) return ''
    try {
        return new Date(date).toLocaleDateString()
    } catch {
        return date
    }
}





const getSelectedTabContent = (tabType: string) => {
    if (!selectedConsultation.value) return []

    // Check main_tabs first
    if (selectedConsultation.value.main_tabs?.[tabType]) {
        return selectedConsultation.value.main_tabs[tabType]
    }

    // Check additional_tabs
    if (selectedConsultation.value.additional_tabs?.[tabType]) {
        return selectedConsultation.value.additional_tabs[tabType]
    }

    return []
}

const selectConsultation = (consultationId: number) => {
    selectedConsultationId.value = consultationId
    selectedConsultation.value = dashboardData.value.recent_consultations.find(c => c.id === consultationId) || null

    // Auto-select first prescription when consultation is selected
    const consultationPrescriptions = getConsultationPrescriptions()
    if (consultationPrescriptions.length > 0) {
        selectPrescription(consultationPrescriptions[0].id)
    } else {
        selectedPrescriptionId.value = null
        selectedPrescription.value = null
    }
}

const selectPrescription = (prescriptionId: number) => {
    selectedPrescriptionId.value = prescriptionId
    const consultationPrescriptions = getConsultationPrescriptions()
    selectedPrescription.value = consultationPrescriptions.find(p => p.id === prescriptionId) || null
}

const getConsultationPrescriptions = () => {
    if (!selectedConsultation.value) return []

    // Combine both main prescriptions and consultation prescriptions
    const mainPrescriptions = selectedConsultation.value.main_prescriptions || []
    const consultationPrescriptions = selectedConsultation.value.prescriptions || []

    return [...mainPrescriptions, ...consultationPrescriptions]
}

const getConsultationDocuments = () => {
    if (!selectedConsultation.value) return []
    return selectedConsultation.value.documents || []
}

const viewDocument = (document: any) => {
    try {
        if (document.file_path || document.url) {
            window.open(document.file_path || document.url, '_blank')
        }
    } catch {
        // Handle error silently
    }
}

const downloadDocument = (document: any) => {
    try {
        if (document.file_path || document.url) {
            const link = document.createElement('a')
            link.href = document.file_path || document.url
            link.download = document.file_name || document.title || 'document'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        }
    } catch {
        // Handle error silently
    }
}

const formatFileSize = (bytes: number) => {
    if (!bytes) return ''
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const goBack = () => {
    router.visit('/consultations')
}

const startNewConsultation = () => {
    router.visit(`/consultations/create/${props.patientId}`)
}

const openEditPatientModal = () => {
    showEditPatientModal.value = true
}

const closeEditPatientModal = () => {
    showEditPatientModal.value = false
}

const handlePatientSaved = () => {
    closeEditPatientModal()
    // Refresh dashboard data to show updated patient info
    fetchDashboardData()
}

// Computed property to check if any consultation data exists
const hasAnyConsultationData = computed(() => {
    if (!selectedConsultation.value) return false

    const tabTypes = ['concerns', 'plan', 'history', 'allergies', 'examination', 'medical_history', 'medications']

    // Check if any tab has content
    const hasTabContent = tabTypes.some(tabType => getSelectedTabContent(tabType).length > 0)

    // Check if patient has any basic data
    const hasPatientData = dashboardData.value.patient?.allergies ||
                          dashboardData.value.patient?.medical_history ||
                          dashboardData.value.patient?.current_medications

    return hasTabContent || hasPatientData
})

onMounted(async () => {
    await fetchDashboardData()
})
</script>
