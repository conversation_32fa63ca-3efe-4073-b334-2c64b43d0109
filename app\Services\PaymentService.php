<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Appointment;
use App\Models\PaymentSettings;
use App\Repositories\PaymentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Exception\ApiErrorException;

class PaymentService
{
    protected PaymentRepository $paymentRepository;

    public function __construct(PaymentRepository $paymentRepository)
    {
        $this->paymentRepository = $paymentRepository;
    }

    /**
     * Get payments with filters and pagination
     */
    public function getPaymentsWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->paymentRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Create payment intent for appointment
     */
    public function createPaymentIntent(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $appointment = Appointment::findOrFail($data['appointment_id']);
            $amount = $data['amount'];
            $currency = $data['currency'] ?? 'usd';

            // Get payment settings for the provider
            $paymentSettings = $this->getPaymentSettings($appointment->provider_id);
            
            if (!$paymentSettings || !$paymentSettings->stripe_secret_key) {
                throw new \Exception('Payment settings not configured for this provider');
            }

            // Set Stripe API key
            Stripe::setApiKey($paymentSettings->stripe_secret_key);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount * 100, // Convert to cents
                'currency' => $currency,
                'metadata' => [
                    'appointment_id' => $appointment->id,
                    'patient_id' => $appointment->patient_id,
                    'provider_id' => $appointment->provider_id,
                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            // Create payment record
            $payment = $this->paymentRepository->create([
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'provider_id' => $appointment->provider_id,
                'amount' => $amount,
                'currency' => $currency,
                'payment_method' => 'stripe',
                'stripe_payment_intent_id' => $paymentIntent->id,
                'status' => 'pending',
                'payment_date' => now(),
            ]);

            return [
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'payment' => $payment,
            ];
        });
    }

    /**
     * Process web payment
     */
    public function processWebPayment(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $appointment = Appointment::findOrFail($data['appointment_id']);
            $paymentIntentId = $data['payment_intent_id'];

            // Get payment settings
            $paymentSettings = $this->getPaymentSettings($appointment->provider_id);
            Stripe::setApiKey($paymentSettings->stripe_secret_key);

            // Retrieve payment intent from Stripe
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            if ($paymentIntent->status === 'succeeded') {
                // Update payment record
                $payment = $this->paymentRepository->findByStripePaymentIntent($paymentIntentId);
                
                if ($payment) {
                    $this->paymentRepository->update($payment->id, [
                        'status' => 'completed',
                        'stripe_charge_id' => $paymentIntent->latest_charge,
                        'processed_at' => now(),
                    ]);

                    // Update appointment status
                    $appointment->update([
                        'status' => 'scheduled',
                        'payment_status' => 'paid',
                    ]);

                    return [
                        'success' => true,
                        'payment' => $payment->fresh(),
                        'appointment' => $appointment->fresh(),
                    ];
                }
            }

            throw new \Exception('Payment processing failed');
        });
    }

    /**
     * Process refund
     */
    public function processRefund(int $paymentId, ?float $amount = null, ?string $reason = null): array
    {
        return DB::transaction(function () use ($paymentId, $amount, $reason) {
            $payment = $this->paymentRepository->find($paymentId);
            
            if (!$payment || $payment->status !== 'completed') {
                throw new \Exception('Payment not found or not eligible for refund');
            }

            // Get payment settings
            $paymentSettings = $this->getPaymentSettings($payment->provider_id);
            Stripe::setApiKey($paymentSettings->stripe_secret_key);

            // Process refund with Stripe
            $refundAmount = $amount ? ($amount * 100) : null; // Convert to cents
            
            $refund = Refund::create([
                'charge' => $payment->stripe_charge_id,
                'amount' => $refundAmount,
                'reason' => $reason ?? 'requested_by_customer',
                'metadata' => [
                    'payment_id' => $payment->id,
                    'appointment_id' => $payment->appointment_id,
                ],
            ]);

            // Update payment record
            $refundedAmount = $refund->amount / 100; // Convert back to dollars
            $this->paymentRepository->update($payment->id, [
                'status' => $refundedAmount >= $payment->amount ? 'refunded' : 'partially_refunded',
                'refunded_amount' => ($payment->refunded_amount ?? 0) + $refundedAmount,
                'stripe_refund_id' => $refund->id,
                'refunded_at' => now(),
                'refund_reason' => $reason,
            ]);

            return [
                'success' => true,
                'refund' => $refund,
                'payment' => $payment->fresh(),
            ];
        });
    }

    /**
     * Get payment settings for provider
     */
    private function getPaymentSettings(int $providerId): ?PaymentSettings
    {
        $provider = \App\Models\Provider::find($providerId);
        
        if (!$provider) {
            return null;
        }

        // Try to get provider-specific settings first
        $paymentSettings = PaymentSettings::where('clinic_id', $provider->clinic_id)->first();
        
        // Fallback to system settings if no clinic-specific settings
        if (!$paymentSettings) {
            $paymentSettings = PaymentSettings::whereNull('clinic_id')->first();
        }

        return $paymentSettings;
    }

    /**
     * Test Stripe connection
     */
    public function testStripeConnection(array $credentials): array
    {
        try {
            Stripe::setApiKey($credentials['stripe_secret_key']);

            Log::info('Testing Stripe connection with card details', [
                'card_number_length' => strlen($credentials['card_number']),
                'exp_month' => $credentials['exp_month'],
                'exp_year' => $credentials['exp_year'],
                'cvc_length' => strlen($credentials['cvc']),
                'stripe_key_prefix' => substr($credentials['stripe_secret_key'], 0, 7),
            ]);

            // Try to create a payment method to test the card
            $paymentMethod = \Stripe\PaymentMethod::create([
                'type' => 'card',
                'card' => [
                    'number' => $credentials['card_number'],
                    'exp_month' => $credentials['exp_month'],
                    'exp_year' => $credentials['exp_year'],
                    'cvc' => $credentials['cvc'],
                ],
                'billing_details' => [
                    'name' => $credentials['cardholder_name'],
                ],
            ]);

            return [
                'success' => true,
                'message' => 'Stripe connection and card validation successful',
                'payment_method_id' => $paymentMethod->id,
                'card_brand' => $paymentMethod->card->brand ?? 'unknown',
                'card_last4' => $paymentMethod->card->last4 ?? 'unknown',
                'stripe_mode' => strpos($credentials['stripe_secret_key'], 'sk_live_') === 0 ? 'live' : 'test',
            ];

        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe test failed:', [
                'message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'stripe_type' => $e->getError()->type ?? 'unknown',
                'stripe_param' => $e->getError()->param ?? 'unknown',
                'full_error' => $e->getJsonBody(),
            ]);

            return [
                'success' => false,
                'message' => 'Stripe test failed: ' . $e->getMessage(),
                'stripe_error' => [
                    'code' => $e->getStripeCode(),
                    'type' => $e->getError()->type ?? 'unknown',
                    'param' => $e->getError()->param ?? 'unknown',
                    'decline_code' => $e->getError()->decline_code ?? null,
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Unexpected error in Stripe test:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Unexpected error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment details
     */
    public function getPaymentDetails(int $paymentId): ?Payment
    {
        return Payment::with([
            'user',
            'appointment.patient.user',
            'appointment.provider.user',
            'appointment.service'
        ])->find($paymentId);
    }

    /**
     * Get payment history
     */
    public function getPaymentHistory(array $filters = []): Collection
    {
        $query = Payment::query();

        // Apply user filter
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        // Apply other filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('payment_date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('payment_date', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'payment_date';
        $sortDir = $filters['sort_dir'] ?? 'desc';
        $query->orderBy($sortBy, $sortDir);

        // Include relationships
        $query->with(['appointment.provider.user', 'appointment.service']);

        return $query->get();
    }

    /**
     * Confirm payment
     */
    public function confirmPayment(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $paymentIntentId = $data['payment_intent_id'];
            $appointmentId = $data['appointment_id'];
            $userId = $data['user_id'];

            $appointment = Appointment::findOrFail($appointmentId);

            // Get payment settings and check with Stripe
            $paymentSettings = $this->getPaymentSettings($appointment->provider_id);
            Stripe::setApiKey($paymentSettings->stripe_secret_key);

            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            if ($paymentIntent->status !== 'succeeded') {
                return [
                    'success' => false,
                    'message' => 'Payment has not been completed yet',
                    'status' => $paymentIntent->status,
                ];
            }

            // Check if the slot is still available
            $provider = $appointment->provider;
            $isAvailable = $provider->isAvailable(
                $appointment->date->format('Y-m-d'),
                $appointment->time_slot,
                $appointment->id
            );

            if (!$isAvailable) {
                Log::warning('Slot is no longer available for appointment #' . $appointmentId);

                // Refund the payment
                try {
                    $refund = Refund::create([
                        'payment_intent' => $paymentIntentId,
                    ]);

                    return [
                        'success' => false,
                        'message' => 'This time slot is no longer available. Your payment has been refunded. Please choose another time.',
                    ];
                } catch (\Exception $e) {
                    Log::error('Error refunding payment: ' . $e->getMessage());
                    return [
                        'success' => false,
                        'message' => 'This time slot is no longer available. Please contact support for a refund.',
                    ];
                }
            }

            // Update appointment
            $appointment->update([
                'payment_status' => 'paid',
                'paid_at' => now(),
                'status' => $appointment->status === 'pending_payment' ? 'scheduled' : $appointment->status,
            ]);

            // Create payment record
            $payment = $this->paymentRepository->create([
                'user_id' => $userId,
                'appointment_id' => $appointmentId,
                'patient_id' => $appointment->patient_id,
                'provider_id' => $appointment->provider_id,
                'payment_id' => $paymentIntentId,
                'amount' => $appointment->amount,
                'currency' => $paymentIntent->currency,
                'payment_method' => 'stripe',
                'payment_method_type' => $paymentIntent->payment_method_types[0] ?? null,
                'payment_method_details' => $paymentIntent->charges->data[0]->payment_method_details->card->last4 ?? null,
                'status' => 'completed',
                'stripe_payment_intent_id' => $paymentIntentId,
                'description' => 'Payment for appointment #' . $appointmentId,
                'payment_date' => now(),
                'processed_at' => now(),
            ]);

            // Send notifications
            $this->sendPaymentConfirmationNotifications($appointment, $payment);

            return [
                'success' => true,
                'payment' => $payment,
                'appointment' => $appointment->fresh(),
            ];
        });
    }

    /**
     * Send payment confirmation notifications
     */
    private function sendPaymentConfirmationNotifications(Appointment $appointment, Payment $payment): void
    {
        try {
            $patient = \App\Models\Patient::findOrFail($appointment->patient_id);
            $provider = \App\Models\Provider::findOrFail($appointment->provider_id);

            // Send notification to patient
            $patient->user->notify(new \App\Notifications\AppointmentConfirmedNotification(
                $appointment,
                $payment,
                false
            ));

            // Send notification to provider
            $provider->user->notify(new \App\Notifications\AppointmentConfirmedNotification(
                $appointment,
                $payment,
                true
            ));

            Log::info('Payment confirmation notifications sent', [
                'appointment_id' => $appointment->id,
                'payment_id' => $payment->id,
                'amount' => $payment->amount
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation notification', [
                'appointment_id' => $appointment->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create appointment with payment
     */
    public function createAppointmentWithPayment(array $data): array
    {
        return DB::transaction(function () use ($data) {
            // Create appointment first
            $appointment = Appointment::create([
                'patient_id' => $data['patient_id'],
                'provider_id' => $data['provider_id'],
                'service_id' => $data['service_id'] ?? null,
                'date' => $data['date'],
                'time_slot' => $data['time_slot'],
                'reason' => $data['reason'],
                'notes' => $data['notes'] ?? '',
                'status' => 'pending_payment',
                'payment_status' => 'unpaid',
                'amount' => $data['amount'],
                'is_telemedicine' => $data['is_telemedicine'] ?? false,
            ]);

            // Create payment intent
            $paymentData = [
                'appointment_id' => $appointment->id,
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'usd',
            ];

            $paymentResult = $this->createPaymentIntent($paymentData);

            return [
                'appointment' => $appointment,
                'payment_intent' => $paymentResult,
            ];
        });
    }
}
