<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Provider;

echo "=== Fixing Provider Availability ===\n";

$providers = Provider::where('verification_status', 'verified')
    ->with(['user', 'services'])
    ->get();

echo "Total verified providers: " . $providers->count() . "\n\n";

// Default availability structure
$defaultAvailability = [
    ['day' => 'Monday', 'slots' => [
        ['start_time' => '09:00', 'end_time' => '12:00'],
        ['start_time' => '14:00', 'end_time' => '17:00']
    ]],
    ['day' => 'Tuesday', 'slots' => [
        ['start_time' => '09:00', 'end_time' => '12:00'],
        ['start_time' => '14:00', 'end_time' => '17:00']
    ]],
    ['day' => 'Wednesday', 'slots' => [
        ['start_time' => '09:00', 'end_time' => '12:00'],
        ['start_time' => '14:00', 'end_time' => '17:00']
    ]],
    ['day' => 'Thursday', 'slots' => [
        ['start_time' => '09:00', 'end_time' => '12:00'],
        ['start_time' => '14:00', 'end_time' => '17:00']
    ]],
    ['day' => 'Friday', 'slots' => [
        ['start_time' => '09:00', 'end_time' => '12:00'],
        ['start_time' => '14:00', 'end_time' => '17:00']
    ]],
    ['day' => 'Saturday', 'slots' => []],
    ['day' => 'Sunday', 'slots' => []]
];

foreach ($providers as $provider) {
    echo "Provider ID: " . $provider->id . " - " . ($provider->user->name ?? 'No name') . "\n";

    // Check if provider has availability
    $availability = $provider->getAvailabilityData();

    if (empty($availability)) {
        echo "  Setting up default availability...\n";

        // Update the provider with default availability
        $provider->weekly_availability = $defaultAvailability;
        $provider->save();

        echo "  ✓ Default availability set\n";
    } else {
        echo "  Already has availability configured\n";
    }

    // Test getting slots for today
    $today = date('Y-m-d');
    $slots = $provider->getAvailableTimeSlots($today);
    echo "  Available slots for today ($today): " . count($slots) . "\n";

    echo "---\n";
}

echo "\n=== Testing Chat Appointment Slots ===\n";

// Test the same logic that the chat system uses
$availableSlots = [];
$today = \Carbon\Carbon::now();
$daysAhead = 7;

$providers = Provider::where('verification_status', 'verified')
    ->whereHas('services', function($query) {
        $query->where('active', true);
    })
    ->get();

echo "Providers with active services: " . $providers->count() . "\n";

foreach ($providers as $provider) {
    for ($i = 0; $i < $daysAhead; $i++) {
        $date = $today->copy()->addDays($i)->format('Y-m-d');
        $slots = $provider->getAvailableTimeSlots($date);

        if (!empty($slots)) {
            $service = $provider->services()->where('active', true)->first();
            if ($service) {
                $availableSlots[] = [
                    'provider' => $provider,
                    'service' => $service,
                    'date' => $date,
                    'slots' => $slots
                ];
                echo "Provider {$provider->id} has " . count($slots) . " slots on $date\n";
                break; // Found slots, move to next provider
            }
        }
    }
}

echo "\nTotal slot groups found: " . count($availableSlots) . "\n";
