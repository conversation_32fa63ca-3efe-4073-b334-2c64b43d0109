<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import ImageUpload from '@/components/ImageUpload.vue';
import MultiCountryPricing from '@/components/MultiCountryPricing.vue';

// Get current user
const page = usePage();
const currentUser = computed(() => page.props.auth?.user);

// Get provider's country code
const providerCountry = computed(() => {
    return currentUser.value?.country_code || null;
});

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'My Products', href: '/provider/products' },
    { title: 'Create Product', href: '/provider/products/create' },
];

const loading = ref(false);
const categories = ref([]);
const form = ref({
    name: '',
    description: '',
    short_description: '',
    type: 'physical',
    category_id: '',
    price: '',
    sale_price: '',
    sku: '',
    stock_quantity: '',
    manage_stock: true,
    weight: '',
    dimensions: '',
    is_featured: false,
    is_active: true,
    digital_files: [],
    download_limit: '',
    download_expiry_days: '',
    countries: [] // Multi-country pricing data
});

const featuredImageFile = ref(null);
const galleryImageFiles = ref([]);
const errors = ref({});

const fetchCategories = async () => {
    try {
        const response = await window.axios.get('/provider/products/create');
        categories.value = response.data.categories || [];
    } catch (error) {
        console.error('Error fetching categories:', error);
    }
};

const submitForm = async () => {
    loading.value = true;
    errors.value = {};

    try {
        // Create FormData for file uploads
        const formData = new FormData();

        // Add all form fields
        Object.keys(form.value).forEach(key => {
            if (key === 'countries') {
                formData.append(key, JSON.stringify(form.value[key]));
            } else if (form.value[key] !== null && form.value[key] !== '') {
                // Convert boolean values to 1/0 for Laravel validation
                if (typeof form.value[key] === 'boolean') {
                    formData.append(key, form.value[key] ? '1' : '0');
                } else {
                    formData.append(key, form.value[key]);
                }
            } else if (key === 'stock_quantity' && form.value.type === 'physical') {
                // Ensure stock_quantity is always sent for physical products
                formData.append(key, form.value[key] || '0');
            }
        });

        // Add image files
        if (featuredImageFile.value) {
            formData.append('featured_image', featuredImageFile.value);
            console.log('Added featured image:', featuredImageFile.value.name);
        }

        if (galleryImageFiles.value && galleryImageFiles.value.length > 0) {
            galleryImageFiles.value.forEach((file, index) => {
                formData.append(`gallery_images[${index}]`, file);
                console.log(`Added gallery image ${index}:`, file.name);
            });
        }

        // Debug: Log all form data
        console.log('Form data being sent:');
        for (const [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`${key}: File(${value.name}, ${value.size} bytes)`);
            } else {
                console.log(`${key}: ${value}`);
            }
        }

        const response = await window.axios.post('/provider/products', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        if (response.data.success) {
            // Redirect to products list with success message
            window.location.href = '/provider/products?success=Product created successfully';
        }
    } catch (error) {
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            console.error('Validation errors:', error.response.data);
            console.error('Form data being sent:', Object.fromEntries(formData.entries()));
        } else {
            console.error('Error creating product:', error);
            console.error('Full error response:', error.response);
            alert('Error creating product. Please try again.');
        }
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchCategories();
});
</script>

<template>
    <Head title="Create Product" />

    <AppLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <nav class="flex" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" :href="breadcrumb.href" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <form @submit.prevent="submitForm" class="space-y-6">
                            <!-- Product Name -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Product Name *</label>
                                <input 
                                    v-model="form.name"
                                    type="text" 
                                    required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                >
                                <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name[0] }}</p>
                            </div>

                            <!-- SKU -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">SKU *</label>
                                <input 
                                    v-model="form.sku"
                                    type="text" 
                                    required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                >
                                <p v-if="errors.sku" class="mt-1 text-sm text-red-600">{{ errors.sku[0] }}</p>
                            </div>

                            <!-- Category and Type -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Category *</label>
                                    <select 
                                        v-model="form.category_id"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Select Category</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.name }}
                                        </option>
                                    </select>
                                    <p v-if="errors.category_id" class="mt-1 text-sm text-red-600">{{ errors.category_id[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Type *</label>
                                    <select 
                                        v-model="form.type"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="physical">Physical</option>
                                        <option value="digital">Digital</option>
                                    </select>
                                    <p v-if="errors.type" class="mt-1 text-sm text-red-600">{{ errors.type[0] }}</p>
                                </div>
                            </div>

                            <!-- Price and Sale Price -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Default Price ($) *</label>
                                    <input 
                                        v-model="form.price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                    <p class="mt-1 text-xs text-gray-500">This will be used as the base price for all countries</p>
                                    <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Sale Price ($)</label>
                                    <input 
                                        v-model="form.sale_price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    >
                                    <p v-if="errors.sale_price" class="mt-1 text-sm text-red-600">{{ errors.sale_price[0] }}</p>
                                </div>
                            </div>

                            <!-- Multi-Country Pricing -->
                            <div class="border border-gray-200 rounded-lg p-6">
                                <MultiCountryPricing
                                    v-model="form.countries"
                                    :default-price="form.price"
                                    :show-sale-price="true"
                                    :provider-country="providerCountry"
                                />
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Description *</label>
                                <textarea 
                                    v-model="form.description"
                                    rows="4"
                                    required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                ></textarea>
                                <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description[0] }}</p>
                            </div>

                            <!-- Short Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Short Description</label>
                                <textarea
                                    v-model="form.short_description"
                                    rows="2"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                ></textarea>
                                <p v-if="errors.short_description" class="mt-1 text-sm text-red-600">{{ errors.short_description[0] }}</p>
                            </div>

                            <!-- Product Images -->
                            <div class="space-y-6">
                                <h3 class="text-lg font-medium text-gray-900">Product Images</h3>

                                <!-- Featured Image -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-3">Featured Image</label>
                                    <ImageUpload
                                        v-model="featuredImageFile"
                                        :multiple="false"
                                        accepted-types="image/*"
                                        accepted-types-text="PNG, JPG, GIF up to 10MB"
                                    />
                                    <p v-if="errors.featured_image" class="mt-2 text-sm text-red-600">{{ errors.featured_image[0] }}</p>
                                </div>

                                <!-- Gallery Images -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-3">Gallery Images</label>
                                    <ImageUpload
                                        v-model="galleryImageFiles"
                                        :multiple="true"
                                        accepted-types="image/*"
                                        accepted-types-text="PNG, JPG, GIF up to 10MB each. Select multiple images for product gallery."
                                    />
                                    <p v-if="errors.gallery_images" class="mt-2 text-sm text-red-600">{{ errors.gallery_images[0] }}</p>
                                </div>
                            </div>

                            <!-- Physical Product Fields -->
                            <div v-if="form.type === 'physical'" class="space-y-4">
                                <div class="grid grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Stock Quantity *</label>
                                        <input 
                                            v-model="form.stock_quantity"
                                            type="number" 
                                            min="0"
                                            required
                                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                        <p v-if="errors.stock_quantity" class="mt-1 text-sm text-red-600">{{ errors.stock_quantity[0] }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Weight (kg)</label>
                                        <input 
                                            v-model="form.weight"
                                            type="number" 
                                            step="0.01"
                                            min="0"
                                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                        <p v-if="errors.weight" class="mt-1 text-sm text-red-600">{{ errors.weight[0] }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Dimensions</label>
                                        <input 
                                            v-model="form.dimensions"
                                            type="text" 
                                            placeholder="L x W x H"
                                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        >
                                        <p v-if="errors.dimensions" class="mt-1 text-sm text-red-600">{{ errors.dimensions[0] }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Product Status -->
                            <div class="flex items-center space-x-6">
                                <div class="flex items-center">
                                    <input
                                        v-model="form.is_featured"
                                        type="checkbox"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label class="ml-2 block text-sm text-gray-900">
                                        Featured Product
                                    </label>
                                </div>

                                <div class="flex items-center">
                                    <input
                                        v-model="form.is_active"
                                        type="checkbox"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label class="ml-2 block text-sm text-gray-900">
                                        Product is Active
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3">
                                <Link href="/provider/products" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="loading"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                                >
                                    {{ loading ? 'Creating...' : 'Create Product' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
