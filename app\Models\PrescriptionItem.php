<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrescriptionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_id',
        'medication_id',
        'medication_name',
        'strength',
        'form',
        'dosage',
        'frequency',
        'route',
        'quantity',
        'quantity_unit',
        'duration_days',
        'directions_for_use',
        'additional_instructions',
        'take_with_food',
        'avoid_alcohol',
        'warnings',
        'unit_cost',
        'total_cost',
        'status',
        'quantity_dispensed',
        'dispensed_at',
        'dispensing_notes',
        'is_repeat_eligible',
        'repeats_allowed',
        'repeats_used',
    ];

    protected $casts = [
        'take_with_food' => 'boolean',
        'avoid_alcohol' => 'boolean',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'dispensed_at' => 'datetime',
        'is_repeat_eligible' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            // Calculate total cost if unit cost and quantity are set
            if ($item->unit_cost && $item->quantity) {
                $item->total_cost = $item->unit_cost * $item->quantity;
            }
        });

        static::saved(function ($item) {
            // Update prescription total cost
            $item->prescription->updateTotalCost();
        });
    }

    /**
     * Get the prescription that owns the item.
     */
    public function prescription()
    {
        return $this->belongsTo(Prescription::class);
    }

    /**
     * Get the medication associated with the item.
     */
    public function medication()
    {
        return $this->belongsTo(Medication::class);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending items.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get dispensed items.
     */
    public function scopeDispensed($query)
    {
        return $query->where('status', 'dispensed');
    }

    /**
     * Scope to get repeat eligible items.
     */
    public function scopeRepeatEligible($query)
    {
        return $query->where('is_repeat_eligible', true);
    }

    /**
     * Check if item is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if item is dispensed.
     */
    public function isDispensed()
    {
        return $this->status === 'dispensed';
    }

    /**
     * Check if item is partially dispensed.
     */
    public function isPartiallyDispensed()
    {
        return $this->status === 'partially_dispensed';
    }

    /**
     * Check if item is cancelled.
     */
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if item can be repeated.
     */
    public function canBeRepeated()
    {
        return $this->is_repeat_eligible && $this->repeats_used < $this->repeats_allowed;
    }

    /**
     * Get remaining repeats.
     */
    public function getRemainingRepeats()
    {
        if (!$this->is_repeat_eligible) {
            return 0;
        }

        return max(0, $this->repeats_allowed - $this->repeats_used);
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'dispensed' => 'Dispensed',
            'partially_dispensed' => 'Partially Dispensed',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the full medication display name.
     */
    public function getFullMedicationNameAttribute()
    {
        $name = $this->medication_name;
        if ($this->strength) {
            $name .= ' ' . $this->strength;
        }
        if ($this->form) {
            $name .= ' (' . $this->form . ')';
        }
        return $name;
    }

    /**
     * Get the dosage instructions display.
     */
    public function getDosageInstructionsAttribute()
    {
        $instructions = [];
        
        if ($this->dosage) {
            $instructions[] = $this->dosage;
        }
        
        if ($this->frequency) {
            $instructions[] = $this->frequency;
        }
        
        if ($this->route && $this->route !== 'oral') {
            $instructions[] = 'via ' . $this->route;
        }
        
        return implode(', ', $instructions);
    }

    /**
     * Get the quantity display.
     */
    public function getQuantityDisplayAttribute()
    {
        return $this->quantity . ' ' . $this->quantity_unit;
    }

    /**
     * Get the duration display.
     */
    public function getDurationDisplayAttribute()
    {
        if (!$this->duration_days) {
            return null;
        }

        if ($this->duration_days == 1) {
            return '1 day';
        }

        if ($this->duration_days <= 7) {
            return $this->duration_days . ' days';
        }

        $weeks = floor($this->duration_days / 7);
        $remainingDays = $this->duration_days % 7;

        $display = $weeks . ' week' . ($weeks > 1 ? 's' : '');
        
        if ($remainingDays > 0) {
            $display .= ' and ' . $remainingDays . ' day' . ($remainingDays > 1 ? 's' : '');
        }

        return $display;
    }

    /**
     * Get special instructions array.
     */
    public function getSpecialInstructionsAttribute()
    {
        $instructions = [];
        
        if ($this->take_with_food) {
            $instructions[] = 'Take with food';
        }
        
        if ($this->avoid_alcohol) {
            $instructions[] = 'Avoid alcohol';
        }
        
        if ($this->additional_instructions) {
            $instructions[] = $this->additional_instructions;
        }
        
        return $instructions;
    }

    /**
     * Mark item as dispensed.
     */
    public function markAsDispensed($quantity = null, $notes = null)
    {
        $this->status = 'dispensed';
        $this->quantity_dispensed = $quantity ?? $this->quantity;
        $this->dispensed_at = now();
        
        if ($notes) {
            $this->dispensing_notes = $notes;
        }
        
        $this->save();
    }

    /**
     * Use a repeat.
     */
    public function useRepeat()
    {
        if ($this->canBeRepeated()) {
            $this->repeats_used++;
            $this->save();
            return true;
        }
        
        return false;
    }
}
