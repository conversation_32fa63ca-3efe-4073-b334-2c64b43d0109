Execute below commands once release v1.0 goes to production
===========================================================

php artisan migrate:status

php artisan migrate

php artisan permissions:verify

php artisan db:seed --class=ProductionSafeRolesPermissionsSeeder

php artisan db:seed --class=EmailTemplatePermissionsSeeder 

php artisan email:migrate-to-slots --dry-run

php artisan email:migrate-to-slots --force 

php artisan email:fix-icons --dry-run

php artisan email:fix-icons

# Clinic Admin Role & Appointment Filtering Implementation
# ========================================================

# Fix clinic admin users - assign proper Spatie roles and permissions
php artisan fix:clinic-admin-roles

# Populate missing clinic_id for appointments (if any)
php artisan appointments:populate-clinic-ids --dry-run
php artisan appointments:populate-clinic-ids

# Verify appointment clinic filtering is working
php artisan tinker --execute="echo 'Appointments without clinic_id: ' . App\Models\Appointment::whereNull('clinic_id')->count() . PHP_EOL;"

# Clear caches after role/permission changes
php artisan cache:clear
php artisan config:clear

# Consultation Clinic Filtering & Icon Fixes
# ===========================================

# Verify consultation clinic filtering is working
php artisan tinker --execute="echo 'Total consultations: ' . App\Models\Consultation::count() . PHP_EOL; echo 'Consultations with clinic_id: ' . App\Models\Consultation::whereNotNull('clinic_id')->count() . PHP_EOL;"

# Note: FontAwesome icons in consultations interface have been replaced with Lucide icons
# - All action buttons (view, edit, delete) now use proper Lucide icons
# - Table headers use Lucide icons
# - Status badges use Lucide icons
# - No additional commands needed - frontend changes only

# Start Consultation Navigation Fix
# ================================
# - "Start Consultation" button now properly redirects to /consultations/{id}/edit
# - Uses correct consultation API endpoint: /consultations/from-appointment/{appointmentId}
# - Fixed API call to use useConsultationApi composable instead of generic useApi
# - No additional commands needed - frontend changes only

# Consultation Clinic Filtering Implementation
# ============================================

# Verify consultation clinic filtering is working
php artisan tinker --execute="echo 'Total consultations: ' . App\Models\Consultation::count() . PHP_EOL; echo 'Consultations with clinic_id: ' . App\Models\Consultation::whereNotNull('clinic_id')->count() . PHP_EOL;"

 php artisan fix:provider-clinic-mapping --force

 php artisan fix:consultation-status --force

 php artisan db:seed --class=CountrySeeder
 
php artisan db:seed --class=SubscriptionPlanSeeder