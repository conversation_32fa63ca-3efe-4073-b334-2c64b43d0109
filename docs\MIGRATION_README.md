# WordPress to Laravel EHR Migration System
## HIPAA/GDPR Compliant | Email-Based Tracking | Real-Time Progress

## Quick Start

### 1. Setup Configuration
```bash
# Configure WordPress connection in .env
WP_API_URL=http://your-wordpress-site.com/wp-admin/admin-ajax.php
WP_API_AUTH="your_wordpress_logged_in_cookie"
WP_API_NONCE="your_wordpress_nonce"
```

### 2. Test Base Migration
```bash
php artisan migratewp:test-base --clinic=1
```

### 3. Run Migration (Step-by-Step Approach - RECOMMENDED)
```bash

# Step 1: Clinics Second (Foundation)
php artisan migratewp:clinics --dry-run
php artisan migratewp:clinics

# Step 2: Users Third (Depends on Clinics)
php artisan migratewp:users --clinic=all --dry-run
php artisan migratewp:users --clinic=all

# Step 3: Categories First (Global, Independent)
php artisan migratewp:categories --dry-run
php artisan migratewp:categories

# Step 4: Services Fourth (Depends on Categories and Clinics)
php artisan migratewp:services --clinic=all --dry-run
php artisan migratewp:services --clinic=all

# Step 5: Static Data Fifth (specializations, etc. - categories handled separately)
php artisan migratewp:staticdata --dry-run
php artisan migratewp:staticdata

# Step 6: Appointments Sixth (Depends on Services and Users)
php artisan migratewp:appointments --clinic=all --dry-run
php artisan migratewp:appointments --clinic=all

# Step 7: Consultations Seventh (Depends on Appointments)
php artisan migratewp:consultations --clinic=all --dry-run
php artisan migratewp:consultations --clinic=all

# Step 8: Prescriptions Eighth (Depends on Consultations)
php artisan migratewp:prescriptions --clinic=all --dry-run
php artisan migratewp:prescriptions --clinic=all

# Step 9: Encounter Tabs Ninth (Depends on Consultations)
php artisan migratewp:encountertabs --clinic=all --dry-run
php artisan migratewp:encountertabs --clinic=all

# Step 10: Encounter Vitals Tenth (Depends on Consultations)
php artisan migratewp:encountervitals --clinic=all --dry-run
php artisan migratewp:encountervitals --clinic=all

# Step 11: Patient Encounter Summary Documents Eleventh (Depends on Consultations)
php artisan migratewp:encountersummarydocuments --clinic=all --dry-run
php artisan migratewp:encountersummarydocuments --clinic=all

# Step 12: Patient Documents Twelfth (Depends on Users)
php artisan migratewp:patientdocuments --clinic=all --dry-run
php artisan migratewp:patientdocuments --clinic=all

# Step 13: Patient Reports Thirteenth (Depends on Users) skip this step while migrating
#php artisan migratewp:patientreports --clinic=all --dry-run
#php artisan migratewp:patientreports --clinic=all

# Step 14: Bills Last (Depends on Appointments)
php artisan migratewp:bills --clinic=all --dry-run
php artisan migratewp:bills --clinic=all

# Step 15: Bill Items/Services (Bill Line Items)
php artisan migratewp:bill-items --clinic=all --dry-run
php artisan migratewp:bill-items --clinic=all

# Step 16: Clinic Sessions & Schedules (Provider Availability & Absences)
php artisan migratewp:clinic-sessions --clinic=all --dry-run
php artisan migratewp:clinic-sessions --clinic=all
```

### Alternative: Combined Commands (Legacy)
```bash
# Foundation Data (Clinics, Users, Services combined)
php artisan migratewp:setupbasedata --clinic=all --dry-run

# Clinical Data (Appointments, Consultations, Prescriptions)
php artisan migratewp:clinicaldata --clinic=all --dry-run
```

## Available Commands

### 🎯 Step-by-Step Migration Commands (RECOMMENDED)

**Foundation Data:**
- `migratewp:categories` - Migrate global service categories (49+ categories)
- `migratewp:clinics` - Migrate clinics only
- `migratewp:users` - Migrate users only
- `migratewp:services` - Migrate services only (depends on categories and clinics)
- `migratewp:staticdata` - Migrate static data only (specializations, etc. - excludes categories)

**Clinical Data:**
- `migratewp:appointments` - Migrate appointments only
- `migratewp:consultations` - Migrate consultations/encounters only
- `migratewp:prescriptions` - Migrate prescriptions only
- `migratewp:encountertabs` - Migrate encounter tabs only
- `migratewp:encountervitals` - Migrate encounter vitals only
- `migratewp:encountersummarydocuments` - Migrate patient encounter summary documents only
- `migratewp:patientdocuments` - Migrate patient documents only
- `migratewp:patientreports` - Migrate patient reports only

**Scheduling Data:**
- `migratewp:clinic-sessions` - Migrate clinic sessions (availability) and schedules (absences)

**Financial Data:**
- `migratewp:bills` - Migrate bills/invoices only

### 📦 Combined Migration Commands (Legacy)
- `migratewp:test-base` - Test API connection and data structure
- `migratewp:setupbasedata` - Migrate foundation data (Clinics, Users, Services)
- `migratewp:clinicaldata` - Migrate clinical data (Appointments, Consultations, Prescriptions, Encounter Tabs, Encounter Vitals, Patient Documents, Patient Reports)

## Detailed Command Documentation

### Categories Migration
```bash
# Migrate global service categories
php artisan migratewp:categories --dry-run
php artisan migratewp:categories
```

**Features:**
- ✅ Migrates 49+ global service categories from WordPress
- ✅ Categories are clinic-independent (global scope)
- ✅ Automatic WordPress category ID mapping (`wp_category_id`)
- ✅ Handles both array and object data formats
- ✅ Comprehensive logging and error handling
- ✅ Uses WordPress static data API endpoint

**Options:**
- `--dry-run` - Preview what would be migrated without executing
- `--force` - Skip confirmation prompts

**Dependencies:** None (independent migration)

### Services Migration
```bash
# Migrate services for specific clinic
php artisan migratewp:services --clinic=1 --dry-run
php artisan migratewp:services --clinic=1

# Migrate services for all clinics
php artisan migratewp:services --clinic=all --dry-run
php artisan migratewp:services --clinic=all
```

**Features:**
- ✅ Automatic category mapping to migrated categories
- ✅ WordPress service ID tracking (`wp_service_id`)
- ✅ Price, duration, and type field mapping
- ✅ Clinic-specific service migration
- ✅ Provider mapping (when providers are migrated)

**Dependencies:** Categories and Clinics must be migrated first

### Medical Records & Clinical Data Migration

#### Encounter Tabs Migration
```bash
# Migrate encounter tabs for specific clinic
php artisan migratewp:encountertabs --clinic=1 --dry-run
php artisan migratewp:encountertabs --clinic=1

# Migrate encounter tabs for all clinics
php artisan migratewp:encountertabs --clinic=all --dry-run
php artisan migratewp:encountertabs --clinic=all
```

**Features:**
- ✅ Migrates encounter tabs data including medical history, allergies, medications, safeguarding, and other clinical notes
- ✅ WordPress encounter tab ID tracking (`wp_encounter_tab_id`)
- ✅ Stores data in consultation's `encounter_tabs` JSON field
- ✅ Automatic consultation mapping via WordPress encounter ID
- ✅ Comprehensive logging and error handling

**Dependencies:** Consultations must be migrated first

#### Encounter Vitals Migration
```bash
# Migrate encounter vitals for specific clinic
php artisan migratewp:encountervitals --clinic=1 --dry-run
php artisan migratewp:encountervitals --clinic=1

# Migrate encounter vitals for all clinics
php artisan migratewp:encountervitals --clinic=all --dry-run
php artisan migratewp:encountervitals --clinic=all
```

**Features:**
- ✅ Migrates encounter vitals data including vital signs like temperature, pulse, blood pressure, etc.
- ✅ WordPress encounter vital ID tracking (`wp_encounter_vital_id`)
- ✅ Stores data in consultation's `vital_signs` JSON field
- ✅ Automatic consultation mapping via WordPress encounter ID
- ✅ Comprehensive logging and error handling

**Dependencies:** Consultations must be migrated first

#### Patient Encounter Summary Documents Migration
```bash
# Migrate patient encounter summary documents for specific clinic
php artisan migratewp:encountersummarydocuments --clinic=1 --dry-run
php artisan migratewp:encountersummarydocuments --clinic=1

# Migrate patient encounter summary documents for all clinics
php artisan migratewp:encountersummarydocuments --clinic=all --dry-run
php artisan migratewp:encountersummarydocuments --clinic=all
```

**Features:**
- ✅ Migrates patient encounter summary documents including consultation summaries and generated reports
- ✅ WordPress encounter summary document ID tracking (`wp_encounter_summary_document_id`)
- ✅ Stores data in consultation's `summary_documents` JSON field
- ✅ Automatic consultation mapping via WordPress encounter ID
- ✅ Comprehensive logging and error handling

**Dependencies:** Consultations must be migrated first

#### Patient Reports Migration
```bash
# Migrate patient reports for specific clinic
php artisan migratewp:patientreports --clinic=1 --dry-run
php artisan migratewp:patientreports --clinic=1

# Migrate patient reports for all clinics
php artisan migratewp:patientreports --clinic=all --dry-run
php artisan migratewp:patientreports --clinic=all
```

**Features:**
- ✅ Migrates patient reports including lab results, diagnostic reports, and other patient-specific documents
- ✅ WordPress patient report ID tracking (`wp_patient_report_id`)
- ✅ Stores data in patient's `reports` JSON field
- ✅ Email-based patient mapping for reliable data association
- ✅ Comprehensive logging and error handling

**Dependencies:** Users must be migrated first

### Bills Migration
```bash
# Migrate bills for specific clinic
php artisan migratewp:bills --clinic=1 --dry-run
php artisan migratewp:bills --clinic=1

# Migrate bills for all clinics
php artisan migratewp:bills --clinic=all --dry-run
php artisan migratewp:bills --clinic=all
```

**Features:**
- ✅ Migrates bills/invoices from WordPress to Laravel
- ✅ WordPress bill ID tracking (`wp_bill_id`)
- ✅ Automatic patient and provider mapping
- ✅ Consultation and appointment linking
- ✅ Payment status and financial data migration
- ✅ Comprehensive logging and error handling

**Dependencies:** Users, Patients, Providers, Consultations, and Appointments must be migrated first

### Bill Items Migration
```bash
# Migrate bill items for specific clinic
php artisan migratewp:bill-items --clinic=1 --dry-run
php artisan migratewp:bill-items --clinic=1

# Migrate bill items for all clinics
php artisan migratewp:bill-items --clinic=all --dry-run
php artisan migratewp:bill-items --clinic=all

# Migrate bill items for specific bill only
php artisan migratewp:bill-items --bill-id=123 --dry-run
php artisan migratewp:bill-items --bill-id=123
```

**Features:**
- ✅ Migrates bill line items/services from WordPress to Laravel
- ✅ WordPress bill item ID tracking (`wp_bill_item_id`)
- ✅ Automatic service mapping and creation
- ✅ Price, quantity, and total calculations
- ✅ Service linking with WordPress service ID mapping
- ✅ Automatic bill total recalculation after item migration
- ✅ Comprehensive logging and error handling

**Options:**
- `--clinic=` - Specific clinic ID, comma-separated IDs, or "all" for all clinics
- `--bill-id=` - Migrate items for specific bill ID only
- `--dry-run` - Preview what would be migrated without executing
- `--force` - Skip confirmation prompts

**Dependencies:** Bills, Services, and Clinics must be migrated first

## Common Options
- `--clinic=ID` - Specific clinic ID, comma-separated IDs, or "all"
- `--dry-run` - Preview without executing (RECOMMENDED FIRST)
- `--force` - Skip confirmation prompts
- `--batch-size=500` - Records per batch

## 📁 Dedicated Logging Structure

Each migration command creates dedicated log files:

```
storage/logs/migration/
├── categories/
│   ├── categories_2025_01_21.log       # Success logs
│   └── categories_errors_2025_01_21.log # Error logs
├── clinics/
│   ├── clinics_2025_01_21.log          # Success logs
│   └── clinics_errors_2025_01_21.log   # Error logs
├── users/
│   ├── users_2025_01_21.log
│   └── users_errors_2025_01_21.log
├── services/
│   ├── services_2025_01_21.log
│   └── services_errors_2025_01_21.log
├── static_data/
│   ├── static_data_2025_01_21.log
│   └── static_data_errors_2025_01_21.log
├── appointments/
│   ├── appointments_2025_01_21.log
│   └── appointments_errors_2025_01_21.log
├── consultations/
│   ├── consultations_2025_01_21.log
│   └── consultations_errors_2025_01_21.log
├── prescriptions/
│   ├── prescriptions_2025_01_21.log
│   └── prescriptions_errors_2025_01_21.log
├── encounter_tabs/
│   ├── encounter_tabs_2025_01_21.log
│   └── encounter_tabs_errors_2025_01_21.log
├── encounter_vitals/
│   ├── encounter_vitals_2025_01_21.log
│   └── encounter_vitals_errors_2025_01_21.log
├── encounter_summary_documents/
│   ├── encounter_summary_documents_2025_01_21.log
│   └── encounter_summary_documents_errors_2025_01_21.log
├── patient_documents/
│   ├── patient_documents_2025_01_21.log
│   └── patient_documents_errors_2025_01_21.log
├── patient_reports/
│   ├── patient_reports_2025_01_21.log
│   └── patient_reports_errors_2025_01_21.log
└── bills/
    ├── bills_2025_01_21.log
    └── bills_errors_2025_01_21.log
```

### Benefits of Step-by-Step Approach:
- ✅ **Complete Control** - Run one step at a time
- ✅ **Clear Visibility** - See exactly what each step does
- ✅ **Easy Troubleshooting** - Isolate problems to specific data types
- ✅ **Flexible Execution** - Test single clinics or all at once
- ✅ **Detailed Logging** - Separate logs for each data type
- ✅ **Role Detection** - Shows proper WordPress → Laravel role mapping

## Key Features

### 🔒 HIPAA/GDPR Compliance
- **Comprehensive audit trails** - Every data transformation logged
- **CSV export reports** - For compliance verification
- **Sensitive field detection** - Automatic identification of PHI/PII
- **Session tracking** - Unique session IDs for each migration

### 📧 Email-Based Tracking
- **Primary identifier** - Uses email addresses for data mapping
- **Duplicate detection** - Identifies duplicate email addresses
- **Validation** - Ensures email format compliance
- **Cross-reference** - WordPress ID ↔ Laravel ID ↔ Email mapping

### 📊 Real-Time Progress
- **Live progress bars** - See migration progress in real-time
- **Count tracking** - Processed/Created/Updated/Errors
- **Preview tables** - See data before migration (dry-run)
- **Success rates** - Percentage success for each module

## Getting WordPress Authentication

See `docs/GET_WORDPRESS_AUTH.md` for detailed instructions on obtaining the required WordPress cookie and nonce values.

## WordPress Setup

See `docs/WORDPRESS_SETUP_GUIDE.md` for WordPress plugin configuration.

## Production Execution

### Pre-Migration Checklist
- [ ] Laravel application deployed and tested
- [ ] Fresh WordPress authentication credentials obtained
- [ ] Backup storage space available (2x current DB size)
- [ ] Maintenance window scheduled (2-4 hours)
- [ ] Staging migration tested successfully

### WordPress Authentication Setup
```bash
# Get fresh authentication credentials (see docs/GET_WORDPRESS_AUTH.md)
WP_API_URL=https://your-production-wp.com/wp-admin/admin-ajax.php
WP_API_AUTH="fresh_wordpress_logged_in_cookie"
WP_API_NONCE="current_nonce_value"
```

### Pre-Migration Steps
1. **Enable Maintenance Mode**
   ```bash
   php artisan down --message="System migration in progress"
   ```

2. **Create Full Backup**
   ```bash
   php artisan migratewp:backup --type=full --compress
   ```

3. **Test API Connectivity**
   ```bash
   php artisan migratewp:test-api
   ```

### Production Migration Process (Simplified)
```bash
# 1. Final backup
php artisan migratewp:backup --type=full

# 2. Test base migration
php artisan migratewp:test-base --clinic=1

# 3. Migrate in recommended order (DRY RUN FIRST)
# Step 1: Foundation Data (MUST RUN FIRST)
php artisan migratewp:setupbasedata --clinic=all --dry-run
php artisan migratewp:setupbasedata --clinic=all --force

# Step 2: Clinical Data (Appointments, Consultations, Prescriptions)
php artisan migratewp:clinicaldata --clinic=all --dry-run
php artisan migratewp:clinicaldata --clinic=all --force

# 4. Verify audit reports
# Check: storage/app/migration_audits/
```

### Post-Migration Steps
```bash
# 1. Disable maintenance mode
php artisan up

# 2. Clear all caches
php artisan config:clear && php artisan cache:clear

# 3. Verify critical functionality
# - Test login system
# - Test appointment booking
# - Test payment processing
# - Verify email notifications

# 4. Monitor logs
tail -f storage/logs/laravel.log
```

### Rollback Plan (If Issues Occur)
```bash
# Stop migration and restore from backup
php artisan migratewp:rollback --backup=backup_file.sql

# Or rollback specific module/clinic
php artisan migratewp:rollback --clinic=1 --module=appointments
```

## Migration Output Examples

### Test Command Output
```bash
php artisan migratewp:test-base --clinic=1
```

```
=== TESTING BASE MIGRATION ===

--- TEST 1: API CONNECTION ---
✅ API Connection: SUCCESS
   Response status: OK
   Data count: 3

--- TEST 2: CLINICS DATA ---
✅ Found 3 clinics
✅ Clinic data structure: VALID
   Sample clinic: Downtown Medical (<EMAIL>)

--- TEST 3: USERS DATA ---
✅ Found 25 users for clinic 1
✅ User data structure: VALID
   Sample user: <EMAIL> (ID: 101)
   Role distribution: {"clinic_admin":2,"doctor":8,"patient":12,"receptionist":3}

--- TEST 4: SERVICES DATA ---
✅ Found 15 services for clinic 1
✅ Service data structure: VALID
   Sample service: General Consultation (ID: 101)
   Price: 150

=== BASE MIGRATION TEST COMPLETED ===
```

### Dry Run Output
```bash
php artisan migratewp:setupbasedata --clinic=1 --dry-run
```

```
=== MIGRATING SETUP BASE DATA ===
Session ID: migration_2025_01_21_14_30_45_abc123
DRY RUN MODE - Previewing migration without making changes

--- STEP 1: MIGRATING CLINICS ---
✓ Would migrate 3 clinics

┌─────────────────────────────────────────────────────────────┐
│ WP ID │ Name                │ Email                │ Status │
├─────────────────────────────────────────────────────────────┤
│ 1     │ Downtown Medical    │ <EMAIL>   │ Active │
│ 2     │ Uptown Clinic      │ <EMAIL>      │ Active │
│ 3     │ Westside Health    │ <EMAIL> │ Active │
└─────────────────────────────────────────────────────────────┘

--- STEP 2: MIGRATING USERS ---
[████████████████████] 100% (25/25) | Current: <EMAIL>
✓ Would migrate 25 users

=== EMAIL TRACKING SUMMARY ===
Unique Emails Found: 25
Duplicate Emails: 0
Invalid Emails: 0
HIPAA Sensitive Fields: 47
```

## Troubleshooting

### API Connection Issues
1. Verify WordPress URL is correct
2. Check authentication cookie is valid
3. Ensure nonce is current
4. Test with: `php artisan migratewp:test-api`

### Migration Errors
1. Check logs in `storage/logs/migration.log`
2. Use `--dry-run` to preview changes
3. Start with small batches using `--batch-size=50`

### Multi-Level Data Issues
1. Verify `level` field mapping in WordPress data
2. Check `clinic_id` associations are correct
3. Test inheritance rules after migration

### Common Production Issues & Solutions

**Authentication Expires During Migration**
```bash
# Get fresh credentials and update .env
# Resume migration from last successful point
```

**Memory/Timeout Issues**
```bash
# Reduce batch size for large datasets
php artisan migratewp:appointments --batch-size=100

# Increase timeout in config/migration.php
'timeout' => 300, // 5 minutes
```

**Data Conflicts**
```bash
# Use dry-run to identify conflicts first
php artisan migratewp:services --clinic=1 --dry-run
```

## Audit Reports Generated

After each migration, comprehensive audit reports are generated:

```
storage/app/migration_audits/
├── migration_audit_[session_id].csv      # Complete audit log
├── migration_mappings_[session_id].csv   # WordPress ID ↔ Laravel ID ↔ Email mappings
├── migration_errors_[session_id].csv     # Any errors encountered
└── migration_report_[session_id].json    # Final summary report
```

### CSV Content Example (Data Mappings)
```csv
Session ID,Timestamp,Module,WordPress ID,Laravel ID,Email,WP Data Hash,Laravel Data Hash,Transformations Applied,WP Data Size,Laravel Data Size,Sensitive Fields Count,Email Valid,Sensitive Fields Details
migration_2025_01_21_14_30_45_abc123,2025-01-21T14:30:45Z,users,101,1,<EMAIL>,abc123def,def456ghi,"[""transform_user_data"",""role_mapping""]",1024,856,3,Yes,"[{""field"":""email"",""type"":""email"",""has_data"":true}]"
```

## Simplified File Structure

```
app/Console/Commands/Migration/
├── BaseMigrationCommand.php          # Simple batch processing with transaction safety
├── TestBaseMigration.php             # Test API connection and data structure
│
├── Step-by-Step Commands (RECOMMENDED):
├── MigrateClinics.php                # Clinics only
├── MigrateUsers.php                  # Users only
├── MigrateServices.php               # Services only
├── MigrateStaticData.php             # Static data only
├── MigrateAppointments.php           # Appointments only
├── MigrateConsultations.php          # Consultations/encounters only
├── MigratePrescriptions.php          # Prescriptions only
├── MigrateEncounterTabs.php          # Encounter tabs only
├── MigrateEncounterVitals.php        # Encounter vitals only
├── MigratePatientEncounterSummaryDocuments.php # Patient encounter summary documents only
├── MigratePatientDocuments.php       # Patient documents only
├── MigratePatientReports.php         # Patient reports only
├── MigrateBills.php                  # Bills/invoices only
├── MigrateBillItems.php              # Bill items/services only
│
├── Combined Commands (Legacy):
├── MigrateSetupBaseData.php          # Foundation data (Clinics, Users, Services)
└── MigrateClinicalData.php           # Clinical data (Appointments, Consultations, Prescriptions)

app/Services/Migration/
└── DataTransformer.php               # Data transformation methods

config/migration.php                  # Migration configuration
docs/MIGRATION_README.md              # This documentation
```

This simplified system provides:
- ✅ **14 focused commands** for complete step-by-step control
- ✅ **Email-based tracking** for reliable data mapping
- ✅ **Transaction safety** with batch processing and rollback
- ✅ **Dedicated logging** with separate files for each data type
- ✅ **Skip existing emails** to avoid duplicates
- ✅ **Complete visibility** of what each step does
- ✅ **Easy troubleshooting** with isolated data type processing
- ✅ **Full clinical data coverage** - appointments, consultations, prescriptions, encounter tabs, encounter vitals, patient encounter summary documents, patient documents, patient reports, bills
