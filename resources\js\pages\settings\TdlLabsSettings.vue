<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head } from '@inertiajs/vue3';
import TdlSettings from '@/components/TDL/TdlSettings.vue';

const breadcrumbs = [
    {
        title: 'TDL Labs Settings',
        href: '/settings/tdl-labs',
    },
];
</script>

<template>
    <Head title="TDL Labs Settings" />

    <AppLayout>
        <SettingsLayout :breadcrumbs="breadcrumbs">
            <div class="space-y-6">
                <!-- Header -->
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">TDL Labs Settings</h1>
                    <p class="text-sm text-gray-600 mt-1">Configure TDL laboratory integration for your clinic</p>
                </div>

                <!-- TDL Settings Component -->
                <TdlSettings />
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
