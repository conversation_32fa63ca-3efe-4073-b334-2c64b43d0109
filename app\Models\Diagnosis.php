<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Diagnosis extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'diagnosis_code',
        'diagnosis_system',
        'diagnosis_name',
        'description',
        'type',
        'status',
        'onset_date',
        'resolved_date',
        'notes',
        'severity',
        'icd10_code',
        'icd10_description',
        'diagnosed_date',
    ];

    protected $casts = [
        'onset_date' => 'date',
        'resolved_date' => 'date',
        'diagnosed_date' => 'date',
    ];

    /**
     * Get the consultation that owns the diagnosis.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the ICD-10 code associated with this diagnosis.
     */
    public function icd10Code()
    {
        return $this->belongsTo(Icd10Code::class, 'icd10_code', 'code');
    }

    /**
     * Scope to filter by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get primary diagnoses.
     */
    public function scopePrimary($query)
    {
        return $query->where('type', 'primary');
    }

    /**
     * Scope to get secondary diagnoses.
     */
    public function scopeSecondary($query)
    {
        return $query->where('type', 'secondary');
    }

    /**
     * Scope to get differential diagnoses.
     */
    public function scopeDifferential($query)
    {
        return $query->where('type', 'differential');
    }

    /**
     * Scope to get active diagnoses.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Check if diagnosis is primary.
     */
    public function isPrimary()
    {
        return $this->type === 'primary';
    }

    /**
     * Check if diagnosis is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if diagnosis is resolved.
     */
    public function isResolved()
    {
        return $this->status === 'resolved';
    }

    /**
     * Get the type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'primary' => 'Primary Diagnosis',
            'secondary' => 'Secondary Diagnosis',
            'differential' => 'Differential Diagnosis',
        ];

        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'active' => 'Active',
            'resolved' => 'Resolved',
            'chronic' => 'Chronic',
            'suspected' => 'Suspected',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the severity display.
     */
    public function getSeverityDisplayAttribute()
    {
        if (!$this->severity) {
            return null;
        }

        $severityLabels = [
            1 => 'Very Mild',
            2 => 'Mild',
            3 => 'Mild-Moderate',
            4 => 'Moderate',
            5 => 'Moderate',
            6 => 'Moderate-Severe',
            7 => 'Severe',
            8 => 'Severe',
            9 => 'Very Severe',
            10 => 'Critical',
        ];

        return $severityLabels[$this->severity] ?? 'Level ' . $this->severity;
    }

    /**
     * Get the full diagnosis display with code.
     */
    public function getFullDisplayAttribute()
    {
        $display = $this->diagnosis_name;
        
        if ($this->diagnosis_code) {
            $display .= ' (' . $this->diagnosis_code . ')';
        }

        return $display;
    }
}
