<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClinicTdlSetting extends Model
{
    use HasFactory;

    protected $table = 'clinic_tdl_settings';

    protected $fillable = [
        'clinic_id',
        'azure_connection_string',
        'tdl_account_id',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    protected $hidden = [
        'azure_connection_string', // Hide sensitive data
    ];

    /**
     * Get the clinic that owns this TDL setting
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Check if TDL is properly configured for this clinic
     */
    public function isConfigured(): bool
    {
        return $this->is_active && 
               !empty($this->azure_connection_string) && 
               !empty($this->tdl_account_id);
    }

    /**
     * Get default settings structure
     */
    public function getDefaultSettings(): array
    {
        return [
            'default_turnaround_time' => '24-48 hours',
            'auto_send_requests' => true,
            'notification_email' => null,
            'polling_frequency' => 15, // minutes
            'auto_process_results' => true,
            'require_physician_review' => true,
        ];
    }

    /**
     * Get setting value with fallback to default
     */
    public function getSetting(string $key, $default = null)
    {
        $settings = $this->settings ?? [];
        $defaultSettings = $this->getDefaultSettings();
        
        return $settings[$key] ?? $defaultSettings[$key] ?? $default;
    }

    /**
     * Update a specific setting
     */
    public function updateSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->update(['settings' => $settings]);
    }
}
