<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Populate patient-provider relationships from existing appointments
        DB::statement("
            INSERT INTO patient_provider (patient_id, provider_id, relationship_type, assigned_by_user_id, assignment_reason, is_active, assigned_at, created_at, updated_at)
            SELECT DISTINCT
                a.patient_id,
                a.provider_id,
                'appointment' as relationship_type,
                NULL as assigned_by_user_id,
                'Migrated from existing appointment' as assignment_reason,
                true as is_active,
                MIN(a.created_at) as assigned_at,
                NOW() as created_at,
                NOW() as updated_at
            FROM appointments a
            WHERE NOT EXISTS (
                SELECT 1 FROM patient_provider pp
                WHERE pp.patient_id = a.patient_id
                AND pp.provider_id = a.provider_id
                AND pp.relationship_type = 'appointment'
            )
            GROUP BY a.patient_id, a.provider_id
        ");

        // Populate patient-provider relationships from created_by_provider_id
        DB::statement("
            INSERT INTO patient_provider (patient_id, provider_id, relationship_type, assigned_by_user_id, assignment_reason, is_active, assigned_at, created_at, updated_at)
            SELECT DISTINCT
                p.id as patient_id,
                p.created_by_provider_id as provider_id,
                'created' as relationship_type,
                pr.user_id as assigned_by_user_id,
                'Migrated from created_by_provider_id' as assignment_reason,
                true as is_active,
                p.created_at as assigned_at,
                NOW() as created_at,
                NOW() as updated_at
            FROM patients p
            JOIN providers pr ON p.created_by_provider_id = pr.id
            WHERE p.created_by_provider_id IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM patient_provider pp
                WHERE pp.patient_id = p.id
                AND pp.provider_id = p.created_by_provider_id
                AND pp.relationship_type = 'created'
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove migrated relationships
        DB::statement("
            DELETE FROM patient_provider
            WHERE assignment_reason IN (
                'Migrated from existing appointment',
                'Migrated from created_by_provider_id'
            )
        ");
    }
};
