import { ref } from 'vue'
import { useToast } from 'vue-toastification'

const toast = useToast()

// Global error state
const globalError = ref(null)
const isOnline = ref(navigator.onLine)

// Error types
const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown'
}

// Error severity levels
const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

export function useErrorHandling() {
  
  /**
   * Parse error into standardized format
   */
  const parseError = (error, context = 'Unknown') => {
    const errorInfo = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      context,
      type: ERROR_TYPES.UNKNOWN,
      severity: ERROR_SEVERITY.MEDIUM,
      title: 'Something went wrong',
      message: 'An unexpected error occurred. Please try again.',
      details: null,
      canRetry: true,
      canReport: true,
      actions: []
    }

    // HTTP/Axios errors
    if (error?.response) {
      const status = error.response.status
      errorInfo.type = getHttpErrorType(status)
      errorInfo.severity = getHttpErrorSeverity(status)
      errorInfo.title = getHttpErrorTitle(status)
      errorInfo.message = error.response.data?.message || getHttpErrorMessage(status)
      errorInfo.details = error.response.data
      errorInfo.canRetry = isRetryableHttpError(status)
    }
    // Network errors
    else if (error?.code === 'NETWORK_ERROR' || !isOnline.value) {
      errorInfo.type = ERROR_TYPES.NETWORK
      errorInfo.severity = ERROR_SEVERITY.HIGH
      errorInfo.title = 'Connection Problem'
      errorInfo.message = 'Please check your internet connection and try again.'
      errorInfo.canRetry = true
    }
    // Validation errors
    else if (error?.name === 'ValidationError' || error?.errors) {
      errorInfo.type = ERROR_TYPES.VALIDATION
      errorInfo.severity = ERROR_SEVERITY.LOW
      errorInfo.title = 'Validation Error'
      errorInfo.message = 'Please check your input and try again.'
      errorInfo.details = error.errors || error.details
      errorInfo.canRetry = true
    }
    // JavaScript errors
    else if (error instanceof Error) {
      errorInfo.type = ERROR_TYPES.CLIENT
      errorInfo.severity = ERROR_SEVERITY.MEDIUM
      errorInfo.title = error.name || 'Application Error'
      errorInfo.message = error.message || 'An unexpected error occurred.'
      errorInfo.details = error.stack
      errorInfo.canRetry = false
    }
    // String errors
    else if (typeof error === 'string') {
      errorInfo.message = error
      errorInfo.canRetry = true
    }

    return errorInfo
  }

  /**
   * Handle error with appropriate user feedback
   */
  const handleError = (error, context = 'Unknown', options = {}) => {
    const errorInfo = parseError(error, context)
    
    // Log error for debugging
    console.error(`[${context}]`, errorInfo)

    // Show user notification unless silent
    if (!options.silent) {
      showErrorToast(errorInfo, options)
    }

    // Set global error for modal display
    if (options.showModal) {
      globalError.value = errorInfo
    }

    // Report error if enabled
    if (options.report !== false && errorInfo.canReport) {
      reportError(errorInfo)
    }

    return errorInfo
  }

  /**
   * Show error toast notification
   */
  const showErrorToast = (errorInfo, options = {}) => {
    const toastOptions = {
      timeout: options.timeout || getSeverityTimeout(errorInfo.severity),
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false
    }

    switch (errorInfo.severity) {
      case ERROR_SEVERITY.CRITICAL:
      case ERROR_SEVERITY.HIGH:
        toast.error(errorInfo.message, toastOptions)
        break
      case ERROR_SEVERITY.MEDIUM:
        toast.warning(errorInfo.message, toastOptions)
        break
      case ERROR_SEVERITY.LOW:
        toast.info(errorInfo.message, toastOptions)
        break
      default:
        toast.error(errorInfo.message, toastOptions)
    }
  }

  /**
   * Show success message
   */
  const showSuccess = (message, options = {}) => {
    toast.success(message, {
      timeout: options.timeout || 5000,
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false,
      ...options
    })
  }

  /**
   * Show info message
   */
  const showInfo = (message, options = {}) => {
    toast.info(message, {
      timeout: options.timeout || 4000,
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false,
      ...options
    })
  }

  /**
   * Report error to backend
   */
  const reportError = async (errorInfo) => {
    try {
      // Only report in production or when explicitly enabled
      if (import.meta.env.DEV && !window.ENABLE_ERROR_REPORTING) {
        return
      }

      const reportData = {
        ...errorInfo,
        url: window.location.href,
        userAgent: navigator.userAgent,
        userId: window.user?.id || null,
        clinicId: window.user?.clinic_id || null
      }

      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify(reportData)
      })
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * Clear global error
   */
  const clearError = () => {
    globalError.value = null
  }

  /**
   * Retry wrapper for functions
   */
  const withRetry = async (fn, maxRetries = 3, delay = 1000) => {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  }

  // Helper functions
  const getHttpErrorType = (status) => {
    if (status === 401) return ERROR_TYPES.AUTHENTICATION
    if (status === 403) return ERROR_TYPES.AUTHORIZATION
    if (status === 422) return ERROR_TYPES.VALIDATION
    if (status >= 500) return ERROR_TYPES.SERVER
    if (status >= 400) return ERROR_TYPES.CLIENT
    return ERROR_TYPES.UNKNOWN
  }

  const getHttpErrorSeverity = (status) => {
    if (status >= 500) return ERROR_SEVERITY.HIGH
    if (status === 401 || status === 403) return ERROR_SEVERITY.MEDIUM
    if (status === 422) return ERROR_SEVERITY.LOW
    return ERROR_SEVERITY.MEDIUM
  }

  const getHttpErrorTitle = (status) => {
    const titles = {
      400: 'Bad Request',
      401: 'Authentication Required',
      403: 'Access Denied',
      404: 'Not Found',
      422: 'Validation Error',
      429: 'Too Many Requests',
      500: 'Server Error',
      502: 'Service Unavailable',
      503: 'Service Unavailable',
      504: 'Request Timeout'
    }
    return titles[status] || 'Request Failed'
  }

  const getHttpErrorMessage = (status) => {
    const messages = {
      400: 'The request was invalid. Please check your input.',
      401: 'Please log in to continue.',
      403: 'You don\'t have permission to perform this action.',
      404: 'The requested resource was not found.',
      422: 'Please check your input and try again.',
      429: 'Too many requests. Please wait a moment and try again.',
      500: 'A server error occurred. Please try again later.',
      502: 'The service is temporarily unavailable.',
      503: 'The service is temporarily unavailable.',
      504: 'The request timed out. Please try again.'
    }
    return messages[status] || 'An error occurred while processing your request.'
  }

  const isRetryableHttpError = (status) => {
    return [408, 429, 500, 502, 503, 504].includes(status)
  }

  const getSeverityTimeout = (severity) => {
    switch (severity) {
      case ERROR_SEVERITY.CRITICAL: return 10000
      case ERROR_SEVERITY.HIGH: return 8000
      case ERROR_SEVERITY.MEDIUM: return 6000
      case ERROR_SEVERITY.LOW: return 4000
      default: return 6000
    }
  }

  // Network status monitoring
  window.addEventListener('online', () => {
    isOnline.value = true
    showSuccess('Connection restored')
  })

  window.addEventListener('offline', () => {
    isOnline.value = false
    showInfo('Connection lost. Some features may not work.', { timeout: 8000 })
  })

  return {
    // State
    globalError,
    isOnline,
    
    // Methods
    handleError,
    showSuccess,
    showInfo,
    clearError,
    withRetry,
    reportError,
    
    // Constants
    ERROR_TYPES,
    ERROR_SEVERITY
  }
}
