<?php

namespace App\Services;

use App\Models\Patient;
use App\Models\User;
use App\Repositories\PatientRepository;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class PatientService
{
    protected PatientRepository $patientRepository;
    protected UserRepository $userRepository;

    public function __construct(
        PatientRepository $patientRepository,
        UserRepository $userRepository
    ) {
        $this->patientRepository = $patientRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Create a new patient with business logic
     */
    public function createPatient(array $userData, array $patientData): Patient
    {
        return DB::transaction(function () use ($userData, $patientData) {
            // Business logic: Create user first
            $userData['role'] = 'patient';
            $userData['is_active'] = $userData['is_active'] ?? true;
            
            if (isset($userData['password'])) {
                $userData['password'] = Hash::make($userData['password']);
            }

            $user = $this->userRepository->create($userData);
            
            // Business logic: Set patient defaults
            $patientData['user_id'] = $user->id;
            $patientData['patient_id'] = $patientData['patient_id'] ?? $this->generatePatientId();
            $patientData['registration_date'] = $patientData['registration_date'] ?? now();
            
            return $this->patientRepository->create($patientData);
        });
    }

    /**
     * Update patient information
     */
    public function updatePatient(int $patientId, array $data): ?Patient
    {
        // Business logic: Handle user data separately
        if (isset($data['user_data'])) {
            $patient = $this->patientRepository->find($patientId);
            if ($patient && $patient->user_id) {
                $this->userRepository->update($patient->user_id, $data['user_data']);
            }
            unset($data['user_data']);
        }

        return $this->patientRepository->update($patientId, $data);
    }

    /**
     * Get patient with full details
     */
    public function getPatientDetails(int $patientId): ?Patient
    {
        $patient = $this->patientRepository->find($patientId);
        
        if (!$patient) {
            return null;
        }

        // Load all relationships
        $patient->load([
            'user',
            'clinic',
            'appointments',
            'healthRecords'
        ]);

        return $patient;
    }

    /**
     * Search patients with business logic
     */
    public function searchPatients(string $search, array $filters = []): Collection
    {
        return $this->patientRepository->search($search, $filters['clinic_id'] ?? null);
    }

    /**
     * Get patients with filters and pagination
     */
    public function getPatientsWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->patientRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Get patient dashboard data
     */
    public function getPatientDashboard(int $patientId): array
    {
        $patient = $this->getPatientDetails($patientId);
        
        if (!$patient) {
            return [];
        }

        return [
            'patient' => $patient,
            'upcoming_appointments' => $this->getUpcomingAppointments($patientId),
            'recent_consultations' => $this->getRecentConsultations($patientId),
            'active_prescriptions' => $this->getActivePrescriptions($patientId),
            'health_metrics' => $this->getHealthMetrics($patientId),
            'alerts' => $this->getPatientAlerts($patientId)
        ];
    }

    /**
     * Get patients by clinic
     */
    public function getPatientsByClinic(int $clinicId): Collection
    {
        return $this->patientRepository->findByClinic($clinicId);
    }

    /**
     * Get patients by provider
     */
    public function getPatientsByProvider(int $providerId): Collection
    {
        return $this->patientRepository->findByProvider($providerId);
    }

    /**
     * Get patient statistics
     */
    public function getPatientStatistics(int $clinicId): array
    {
        return $this->patientRepository->getStatistics($clinicId);
    }

    /**
     * Update patient medical history
     */
    public function updateMedicalHistory(int $patientId, array $medicalHistory): bool
    {
        $patient = $this->patientRepository->update($patientId, [
            'medical_history' => $medicalHistory
        ]);
        
        return $patient !== null;
    }

    /**
     * Update patient preferences
     */
    public function updatePatientPreferences(int $patientId, array $preferences): bool
    {
        $patient = $this->patientRepository->update($patientId, [
            'preferences' => $preferences
        ]);
        
        return $patient !== null;
    }

    /**
     * Generate unique patient ID
     */
    private function generatePatientId(): string
    {
        do {
            $patientId = 'P' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while ($this->patientRepository->findOneBy(['patient_id' => $patientId]));

        return $patientId;
    }

    /**
     * Get upcoming appointments for patient
     */
    private function getUpcomingAppointments(int $patientId): Collection
    {
        // This would use AppointmentService when available
        return collect([]);
    }

    /**
     * Get recent consultations for patient
     */
    private function getRecentConsultations(int $patientId): Collection
    {
        // This would use ConsultationService
        return collect([]);
    }

    /**
     * Get active prescriptions for patient
     */
    private function getActivePrescriptions(int $patientId): Collection
    {
        // This would use PrescriptionService when available
        return collect([]);
    }

    /**
     * Get health metrics for patient
     */
    private function getHealthMetrics(int $patientId): array
    {
        // Business logic for health metrics
        return [
            'vitals' => [],
            'lab_results' => [],
            'measurements' => []
        ];
    }

    /**
     * Get patient alerts
     */
    private function getPatientAlerts(int $patientId): array
    {
        // Business logic for patient alerts
        return [
            'medication_reminders' => [],
            'appointment_reminders' => [],
            'health_alerts' => []
        ];
    }
}
