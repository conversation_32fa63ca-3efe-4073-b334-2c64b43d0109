<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Consolidated patients table WordPress migration enhancements
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Add WordPress tracking fields (check if they exist first)
            if (!Schema::hasColumn('patients', 'wp_patient_id')) {
                $table->integer('wp_patient_id')->nullable()->index()->after('id');
            }
            if (!Schema::hasColumn('patients', 'wp_user_id')) {
                $table->integer('wp_user_id')->nullable()->after('wp_patient_id');
                $table->index('wp_user_id');
            }
            if (!Schema::hasColumn('patients', 'wp_clinic_id')) {
                $table->integer('wp_clinic_id')->nullable()->after('wp_user_id');
                $table->index('wp_clinic_id');
            }

            // Add basic patient fields (check if they exist first - they may have been added by previous migrations)
            if (!Schema::hasColumn('patients', 'first_name')) {
                $table->string('first_name')->nullable()->after('created_by_provider_id');
            }
            if (!Schema::hasColumn('patients', 'last_name')) {
                $table->string('last_name')->nullable()->after('first_name');
            }
            if (!Schema::hasColumn('patients', 'email')) {
                $table->string('email')->nullable()->after('last_name');
            }
            if (!Schema::hasColumn('patients', 'registration_date')) {
                $table->timestamp('registration_date')->nullable()->after('email');
            }

            // Add additional WordPress tracking fields
            if (!Schema::hasColumn('patients', 'wp_created_date')) {
                $table->datetime('wp_created_date')->nullable()->after('registration_date');
            }
            if (!Schema::hasColumn('patients', 'wp_status')) {
                $table->string('wp_status')->nullable()->after('wp_created_date');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Only drop indexes and columns that exist and were added by this migration
            $indexesToDrop = ['wp_patient_id', 'wp_user_id', 'wp_clinic_id'];
            foreach ($indexesToDrop as $index) {
                if (Schema::hasColumn('patients', $index)) {
                    try {
                        $table->dropIndex([$index]);
                    } catch (Exception $e) {
                        // Index might not exist, continue
                    }
                }
            }

            $columnsToCheck = [
                'wp_patient_id', 'first_name', 'last_name', 'email', 'registration_date',
                'wp_user_id', 'wp_clinic_id', 'wp_created_date', 'wp_status'
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('patients', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
