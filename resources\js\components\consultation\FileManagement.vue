<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <!-- Header -->
    <div class="bg-white px-4 py-3 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h2 class="font-medium text-gray-900">Files</h2>
        <div class="flex gap-1">
          <button
            class="flex items-center gap-1 px-2 py-1 border border-gray-300 rounded text-xs hover:bg-gray-50 relative group"
            @click="fetchPdfPassword"
            title="Show Password"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12" class="w-3 h-3">
              <circle cx="8" cy="12" r="3.5" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M11 12H20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M16 12V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M19 12V10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">Show Password</span>
          </button>
          <button class="flex items-center gap-1 px-2 py-1 border border-gray-300 rounded text-xs hover:bg-gray-50"
            @click="showUploadModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3 h-3">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" x2="12" y1="3" y2="15"></line>
            </svg>
            Upload
          </button>
          <button class="flex items-center gap-1 px-2 py-1 border border-gray-300 rounded text-xs hover:bg-gray-50"
            @click="showMobileUploadModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3 h-3">
              <rect x="7" y="4" width="10" height="16" rx="1" ry="1"></rect>
              <path d="M11 5h2"></path>
              <path d="M12 17v.01"></path>
            </svg>
            Mobile
          </button>
          <button class="flex items-center gap-1 px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
            @click="showTemplateModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3 h-3">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 12h8"></path>
              <path d="M12 8v8"></path>
            </svg>
            Letter
          </button>
        </div>
      </div>
    </div>

    <!-- File List -->
    <div class="p-4">
      <div v-if="loading" class="flex items-center justify-center py-8">
        <svg class="animate-spin h-6 w-6 text-blue-600" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="ml-2 text-gray-600">Loading files...</span>
      </div>

      <div v-else-if="files.length === 0" class="text-center py-8 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto mb-2 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14 2 14 8 20 8"/>
        </svg>
        <p>No files uploaded yet</p>
        <p class="text-sm">Upload documents, images, or take photos</p>
      </div>

      <div v-else class="space-y-2">
        <div v-for="file in files" :key="file.id" class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
          <div class="flex items-center gap-3">
            <!-- File Icon -->
            <div class="w-10 h-10 rounded-lg flex items-center justify-center" :class="getFileIconClass(file.type)">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14 2 14 8 20 8"/>
              </svg>
            </div>
            
            <!-- File Info -->
            <div>
              <h3 class="font-medium text-sm">{{ file.name }}</h3>
              <div class="flex items-center gap-2 text-xs text-gray-500">
                <span>{{ formatFileSize(file.size) }}</span>
                <span>•</span>
                <span>{{ formatDate(file.created_at) }}</span>
                <span v-if="file.uploaded_by">•</span>
                <span v-if="file.uploaded_by">{{ file.uploaded_by }}</span>
              </div>
            </div>
          </div>

          <!-- File Actions -->
          <div class="flex items-center gap-1">
            <!-- View/Download Button -->
            <button @click="viewFile(file)" class="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded" title="View">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
            </button>

            <!-- Download Button -->
            <a :href="file.download_url" :download="file.name" class="p-1.5 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded" title="Download">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7 10 12 15 17 10"/>
                <line x1="12" x2="12" y1="15" y2="3"/>
              </svg>
            </a>

            <!-- Share Button -->
            <button @click="shareFile(file)" class="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded" title="Share">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                <polyline points="16 6 12 2 8 6"/>
                <line x1="12" x2="12" y1="2" y2="15"/>
              </svg>
            </button>

            <!-- Delete Button -->
            <button v-if="canDelete" @click="deleteFile(file)" class="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded" title="Delete">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3 6 5 6 21 6"/>
                <path d="m19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                <line x1="10" y1="11" x2="10" y2="17"/>
                <line x1="14" y1="11" x2="14" y2="17"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploading" class="p-4 border-t bg-gray-50">
      <div class="flex items-center gap-3">
        <svg class="animate-spin h-4 w-4 text-blue-600" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm text-gray-600">Uploading files...</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="`width: ${uploadProgress}%`"></div>
      </div>
    </div>

    <!-- Camera Modal -->
    <div v-if="showCamera" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Take Photo</h3>
          <button @click="closeCamera" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <video ref="videoElement" autoplay class="w-full rounded-lg bg-gray-100"></video>
          <canvas ref="canvasElement" class="hidden"></canvas>
          
          <div class="flex justify-center gap-3">
            <button @click="capturePhoto" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Capture Photo
            </button>
            <button @click="closeCamera" class="px-4 py-2 border rounded hover:bg-gray-50">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- File Viewer Modal -->
    <div v-if="viewingFile" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">{{ viewingFile.name }}</h3>
          <button @click="closeViewer" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="text-center">
          <img v-if="isImage(viewingFile.type)" :src="viewingFile.url" :alt="viewingFile.name" class="max-w-full max-h-96 mx-auto rounded-lg" />
          <iframe v-else-if="isPDF(viewingFile.type)" :src="viewingFile.url" class="w-full h-96 border rounded-lg"></iframe>
          <div v-else class="py-8 text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mx-auto mb-4 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14 2 14 8 20 8"/>
            </svg>
            <p>Preview not available</p>
            <a :href="viewingFile.download_url" :download="viewingFile.name" class="text-blue-600 hover:underline">Download to view</a>
          </div>
        </div>
      </div>
    </div>

    <!-- New Modals -->
    <LetterTemplateModal
      v-if="showTemplateDialog"
      :show="showTemplateDialog"
      :encounter-id="encounterId"
      :consultation-data="consultationData"
      :patient-details="patientDetails"
      @close="showTemplateDialog = false"
      @select="handleTemplateSelect"
      @reload-files="loadFiles"
    />

    <MobileUploadModal
      v-if="showMobileUploadDialog"
      :show="showMobileUploadDialog"
      :encounter-id="encounterId"
      :patient-details="patientDetails"
      @close="showMobileUploadDialog = false"
      @upload-detected="handleMobileUpload"
    />

    <!-- Upload Modal -->
    <UploadModal
      v-if="showUploadDialog"
      :show="showUploadDialog"
      :encounter-id="encounterId"
      :patient-details="patientDetails"
      @close="closeUploadModal"
      @upload-success="handleUploadSuccess"
    />

    <!-- Share Modal -->
    <ShareModal
      v-if="selectedFileForShare"
      :show="showShareModal"
      :share-type="'document'"
      :item-id="selectedFileForShare.id"
      :consultation-id="consultationId"
      @close="closeShareModal"
      @shared="handleFileShared"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useNotifications } from '../../composables/useNotifications'
import axios from 'axios'
import LetterTemplateModal from './LetterTemplateModal.vue'
import MobileUploadModal from './MobileUploadModal.vue'
import UploadModal from './UploadModal.vue'
import ShareModal from './ShareModal.vue'

interface FileItem {
  id: number
  name: string
  type: string
  size: number
  url: string
  download_url: string
  created_at: string
  uploaded_by?: string
}

interface Props {
  encounterId: string | number
  patientId?: string | number
  patientDetails?: {
    patient_id: number
    name: string
    email?: string
  }
  consultationData?: Record<string, any>
  canUpload?: boolean
  canDelete?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canUpload: true,
  canDelete: true,
  patientDetails: () => ({ patient_id: 0, name: '' }),
  consultationData: () => ({})
})

const emit = defineEmits<{
  'files-updated': [files: FileItem[]]
}>()

// Component state
const files = ref<FileItem[]>([])
const loading = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const showCamera = ref(false)
const viewingFile = ref<FileItem | null>(null)

// New modal states
const showUploadDialog = ref(false)
const showTemplateDialog = ref(false)
const showMobileUploadDialog = ref(false)
const showShareModal = ref(false)
const selectedFileForShare = ref<FileItem | null>(null)

// Camera refs
const videoElement = ref<HTMLVideoElement | null>(null)
const canvasElement = ref<HTMLCanvasElement | null>(null)
const mediaStream = ref<MediaStream | null>(null)

// Methods
const loadFiles = async (): Promise<void> => {
  try {
    loading.value = true
    const response = await axios.get(`/consultations/${props.encounterId}/files`)

    if (response.data.success) {
      files.value = response.data.data || []
      emit('files-updated', files.value)
    }
  } catch (error) {
    console.error('Error loading files:', error)
  } finally {
    loading.value = false
  }
}

const handleFileUpload = async (event: Event): Promise<void> => {
  const target = event.target as HTMLInputElement
  const selectedFiles = target.files
  
  if (!selectedFiles || selectedFiles.length === 0) return
  
  try {
    uploading.value = true
    uploadProgress.value = 0
    
    const formData = new FormData()
    formData.append('encounter_id', props.encounterId.toString())
    
    Array.from(selectedFiles).forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    const response = await axios.post('/consultations/upload-files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })
    
    if (response.data.success) {
      await loadFiles()
    }
  } catch (error) {
    console.error('Error uploading files:', error)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
    // Reset file input
    target.value = ''
  }
}

const deleteFile = async (file: FileItem): Promise<void> => {
  // Show delete confirmation dialog
  const confirmed = await showDeleteConfirm(file.name, 'file')

  if (!confirmed) return

  try {
    const response = await axios.delete(`/consultation-files/${file.id}`)

    if (response.data.success) {
      // Reload files from server to ensure we have the latest data
      await loadFiles()
      emit('files-updated', files.value)

      // Show success toast
      showSuccess('File deleted successfully')
    }
  } catch (error) {
    console.error('Error deleting file:', error)

    // Show error toast
    showError('Failed to delete file. Please try again.')
  }
}

const viewFile = (file: FileItem): void => {
  viewingFile.value = file
}

const closeViewer = (): void => {
  viewingFile.value = null
}

const takePhoto = async (): Promise<void> => {
  try {
    showCamera.value = true
    mediaStream.value = await navigator.mediaDevices.getUserMedia({ video: true })
    
    if (videoElement.value) {
      videoElement.value.srcObject = mediaStream.value
    }
  } catch (error) {
    console.error('Error accessing camera:', error)
    showCamera.value = false
  }
}

const capturePhoto = (): void => {
  if (!videoElement.value || !canvasElement.value) return
  
  const canvas = canvasElement.value
  const video = videoElement.value
  
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.drawImage(video, 0, 0)
    
    canvas.toBlob(async (blob) => {
      if (blob) {
        const file = new File([blob], `photo_${Date.now()}.jpg`, { type: 'image/jpeg' })
        
        // Create a fake file input event
        const fakeEvent = {
          target: {
            files: [file]
          }
        } as any
        
        await handleFileUpload(fakeEvent)
      }
    }, 'image/jpeg', 0.8)
  }
  
  closeCamera()
}

const closeCamera = (): void => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
    mediaStream.value = null
  }
  showCamera.value = false
}

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>`
  }
  if (fileType === 'application/pdf') {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/></svg>`
  }
  return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/></svg>`
}

const getFileIconClass = (fileType: string | undefined): string => {
  if (!fileType) return 'bg-gray-100 text-gray-600'
  if (fileType.startsWith('image/')) return 'bg-green-100 text-green-600'
  if (fileType === 'application/pdf') return 'bg-red-100 text-red-600'
  return 'bg-gray-100 text-gray-600'
}

const isImage = (fileType: string | undefined): boolean => {
  return fileType ? fileType.startsWith('image/') : false
}

const isPDF = (fileType: string | undefined): boolean => {
  return fileType === 'application/pdf'
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// New methods for plugin features
const fetchPdfPassword = async (): Promise<void> => {
  try {
    const response = await axios.post('/get-document-access-key', {
      patient_id: props.patientDetails?.patient_id,
    })

    if (response.data?.success) {
      const password = response.data.data.password
      await showAlert('Document Password', `The password for this document is: ${password}`, 'info')
    } else {
      throw new Error(response.data?.message || 'Failed to fetch password')
    }
  } catch (error: any) {
    console.error('Error fetching PDF password:', error)
    await showAlert('Error', 'Failed to fetch document password', 'error')
  }
}

const showUploadModal = (): void => {
  showUploadDialog.value = true
}

const closeUploadModal = (): void => {
  showUploadDialog.value = false
}

const handleUploadSuccess = async (): Promise<void> => {
  // Reload files after successful upload
  await loadFiles()
  closeUploadModal()
}

const showTemplateModal = (): void => {
  showTemplateDialog.value = true
}

const showMobileUploadModal = (): void => {
  showMobileUploadDialog.value = true
}

const handleTemplateSelect = (template: any): void => {
  console.log('Selected template:', template)
  showTemplateDialog.value = false
}

const handleMobileUpload = (): void => {
  loadFiles()
}

// Share functions
const shareFile = (file: FileItem): void => {
  selectedFileForShare.value = file
  showShareModal.value = true
}

const closeShareModal = (): void => {
  showShareModal.value = false
  selectedFileForShare.value = null
}

const handleFileShared = (result: any): void => {
  showSuccess('File shared successfully!')
  closeShareModal()
}

// Add consultationId computed property
const consultationId = computed(() => props.encounterId)

// Initialize notifications
const { showDeleteConfirm, showSuccess, showError, showAlert } = useNotifications()

// Lifecycle
onMounted(() => {
  loadFiles()
})

onUnmounted(() => {
  closeCamera()
})
</script>
