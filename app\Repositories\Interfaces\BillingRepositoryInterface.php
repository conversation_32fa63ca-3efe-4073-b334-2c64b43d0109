<?php

namespace App\Repositories\Interfaces;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface BillingRepositoryInterface
{
    public function getBillsWithDetailsForClinic(int $clinicId, int $perPage = 20, array $params = []): LengthAwarePaginator;
    public function getBillWithFullDetails(int $billId): ?object;
    public function getBillingReportData(int $clinicId, array $filters = []): Collection;
    public function getUnpaidBillsWithPatientDetails(int $clinicId): Collection;
    public function getBillsByPatientWithDetails(int $patientId): Collection;
    public function getBillsByProviderWithDetails(int $providerId): Collection;
    public function getBillingStatsForClinic(int $clinicId): array;
}
