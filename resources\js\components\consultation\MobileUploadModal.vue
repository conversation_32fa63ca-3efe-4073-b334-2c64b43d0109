<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">Upload from Mobile Device</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="text-center mb-4">
        <p class="text-sm mb-4">Scan this QR code with your mobile device to upload photos or documents directly from
          your phone.</p>

        <div v-if="qrCodeUrl"
          class="mx-auto mb-4 p-2 border rounded-lg bg-white w-64 h-64 flex items-center justify-center">
          <img :src="qrCodeUrl" alt="QR Code for mobile upload" class="max-w-full max-h-full" />
        </div>
        <div v-else class="animate-pulse bg-gray-200 mx-auto w-64 h-64 rounded-lg flex items-center justify-center">
          <span class="text-gray-500">Generating QR code...</span>
        </div>
      </div>

      <div class="flex flex-col space-y-3">
        <div class="p-3 bg-blue-50 text-blue-700 rounded-lg text-sm">
          <strong>Instructions:</strong>
          <ol class="list-decimal pl-4 mt-1">
            <li>Scan the QR code with your phone's camera</li>
            <li>Follow the link to open the upload page</li>
            <li>Take a photo or select a file from your gallery</li>
            <li>Click upload to add the file to this patient's records</li>
          </ol>
        </div>

        <p class="text-center text-sm text-gray-500">
          Mobile upload session will expire in {{ expireMinutes }} minutes
        </p>

        <div v-if="mobileUploadStatus" class="p-2 rounded-lg text-sm" :class="{
          'bg-green-50 text-green-700': mobileUploadStatus.type === 'success',
          'bg-yellow-50 text-yellow-700': mobileUploadStatus.type === 'pending',
          'bg-red-50 text-red-700': mobileUploadStatus.type === 'error'
        }">
          {{ mobileUploadStatus.message }}
        </div>

        <div class="flex justify-end pt-2">
          <button @click="refreshQRCode"
            class="mr-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg">
            Refresh QR
          </button>
          <button @click="$emit('close')" class="px-4 py-2 bg-black text-white rounded-lg">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useToast } from 'vue-toastification'
import axios from 'axios'

interface Props {
  show: boolean
  encounterId: string | number
  patientDetails: {
    patient_id: number
    name: string
    email?: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'close': []
  'upload-detected': []
}>()

const toast = useToast()

// State
const qrCodeUrl = ref('')
const uploadSessionId = ref('')
const mobileUploadStatus = ref<{
  type: 'success' | 'pending' | 'error'
  message: string
} | null>(null)
const mobileUploadPollingInterval = ref<NodeJS.Timeout | null>(null)
const expireMinutes = ref(5)

// Methods
const generateQRCode = async (): Promise<void> => {
  try {
    qrCodeUrl.value = ''
    mobileUploadStatus.value = {
      type: 'pending',
      message: 'Generating secure upload link...'
    }

    // Generate a new upload session
    const response = await axios.post('/create-mobile-upload-session', {
      encounter_id: props.encounterId,
      patient_id: props.patientDetails.patient_id,
      expire_minutes: expireMinutes.value,
    })

    if (response.data?.success) {
      uploadSessionId.value = response.data.data.session_id
      qrCodeUrl.value = response.data.data.qr_code_url
      expireMinutes.value = response.data.data.expire_minutes

      // Start polling for mobile uploads using this session
      startMobileUploadPolling()

      mobileUploadStatus.value = {
        type: 'success',
        message: 'QR code ready. Scan with your mobile device to upload.'
      }
    } else {
      throw new Error(response.data?.message || 'Failed to create upload session')
    }
  } catch (error: any) {
    console.error('Error generating QR code:', error)
    mobileUploadStatus.value = {
      type: 'error',
      message: 'Failed to generate QR code. Please try again.'
    }
  }
}

const refreshQRCode = (): void => {
  // Stop current polling
  stopMobileUploadPolling()
  
  // Generate a new QR code
  generateQRCode()
}

const startMobileUploadPolling = (): void => {
  // Clear any existing polling interval
  stopMobileUploadPolling()

  // Start a new polling interval (check every 3 seconds)
  mobileUploadPollingInterval.value = setInterval(() => {
    checkForMobileUploads()
  }, 3000)
}

const stopMobileUploadPolling = (): void => {
  if (mobileUploadPollingInterval.value) {
    clearInterval(mobileUploadPollingInterval.value)
    mobileUploadPollingInterval.value = null
  }
}

const checkForMobileUploads = async (): Promise<void> => {
  if (!uploadSessionId.value) return

  try {
    const response = await axios.get('/check-mobile-upload-session', {
      params: {
        session_id: uploadSessionId.value
      }
    })

    if (response.data?.success && response.data.data?.uploads) {
      // If new uploads detected
      const uploads = response.data.data.uploads
      if (uploads.length > 0) {
        // Update status
        mobileUploadStatus.value = {
          type: 'success',
          message: `${uploads.length} new file(s) uploaded from mobile device!`
        }

        // Emit upload detected event
        emit('upload-detected')
      }
    }
  } catch (error: any) {
    console.error('Error checking for mobile uploads:', error)
    // Don't update the UI for polling errors to avoid confusing the user
  }
}

// Lifecycle
onMounted(() => {
  if (props.show) {
    generateQRCode()
  }
})

onUnmounted(() => {
  stopMobileUploadPolling()
})
</script>
