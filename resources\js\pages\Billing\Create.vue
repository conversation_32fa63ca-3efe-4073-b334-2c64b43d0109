<template>
    <AppLayout title="Create Bill">
        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">Create New Bill</h2>
                                <p class="text-gray-600 mt-1">Generate a bill for patient services</p>
                            </div>
                            <Link :href="route('bills.index')" 
                                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Back to Bills
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Bill Form -->
                <form @submit.prevent="createBill" class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Patient *</label>
                                    <select v-model="billForm.patient_id" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Select Patient</option>
                                        <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                                            {{ patient.name }} ({{ patient.email }})
                                        </option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Provider *</label>
                                    <select v-model="billForm.provider_id" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Select Provider</option>
                                        <option v-for="provider in providers" :key="provider.id" :value="provider.id">
                                            {{ provider.name }}
                                        </option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                                    <input v-model="billForm.title" 
                                           type="text" 
                                           placeholder="Bill title or description"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                                    <input v-model="billForm.due_date" 
                                           type="date" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>

                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                                <textarea v-model="billForm.notes" 
                                          rows="3"
                                          placeholder="Additional notes or instructions"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Bill Items -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Bill Items</h3>
                                <button type="button" 
                                        @click="addBillItem"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                    Add Item
                                </button>
                            </div>

                            <div class="space-y-4">
                                <div v-for="(item, index) in billForm.items" :key="index" 
                                     class="border border-gray-200 rounded-lg p-4">
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                                            <select v-model="item.service_id" 
                                                    @change="updateItemFromService(index)"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="">Select Service</option>
                                                <option v-for="service in services" :key="service.id" :value="service.id">
                                                    {{ service.name }} (£{{ service.price }})
                                                </option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                                            <input v-model="item.item_name" 
                                                   type="text" 
                                                   required
                                                   placeholder="Item or service name"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price *</label>
                                            <input v-model="item.unit_price" 
                                                   type="number" 
                                                   step="0.01"
                                                   min="0"
                                                   required
                                                   @input="calculateItemTotal(index)"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Quantity *</label>
                                            <div class="flex">
                                                <input v-model="item.quantity" 
                                                       type="number" 
                                                       min="1"
                                                       required
                                                       @input="calculateItemTotal(index)"
                                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <button type="button" 
                                                        @click="removeBillItem(index)"
                                                        class="px-3 py-2 bg-red-600 text-white rounded-r-md hover:bg-red-700 transition-colors">
                                                    ×
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                        <input v-model="item.description" 
                                               type="text" 
                                               placeholder="Item description"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div class="mt-2 text-right">
                                        <span class="text-sm font-medium text-gray-700">
                                            Total: £{{ (item.unit_price * item.quantity).toFixed(2) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bill Summary -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Bill Summary</h3>
                            
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span class="font-medium">£{{ subtotal.toFixed(2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Discount:</span>
                                    <div class="flex items-center space-x-2">
                                        <span>£</span>
                                        <input v-model="billForm.discount" 
                                               type="number" 
                                               step="0.01"
                                               min="0"
                                               :max="subtotal"
                                               class="w-20 px-2 py-1 border border-gray-300 rounded text-right">
                                    </div>
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t pt-2">
                                    <span>Total:</span>
                                    <span>£{{ total.toFixed(2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end space-x-3">
                        <Link :href="route('bills.index')" 
                              class="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                            Cancel
                        </Link>
                        <button type="submit" 
                                :disabled="loading || billForm.items.length === 0"
                                class="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50">
                            {{ loading ? 'Creating...' : 'Create Bill' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import axios from 'axios'

const loading = ref(false)
const patients = ref([])
const providers = ref([])
const services = ref([])

const billForm = reactive({
    patient_id: '',
    provider_id: '',
    title: '',
    due_date: '',
    notes: '',
    discount: 0,
    items: []
})

const subtotal = computed(() => {
    return billForm.items.reduce((sum, item) => {
        return sum + (parseFloat(item.unit_price || 0) * parseInt(item.quantity || 0))
    }, 0)
})

const total = computed(() => {
    return Math.max(0, subtotal.value - parseFloat(billForm.discount || 0))
})

const addBillItem = () => {
    billForm.items.push({
        service_id: '',
        item_name: '',
        description: '',
        unit_price: 0,
        quantity: 1
    })
}

const removeBillItem = (index) => {
    billForm.items.splice(index, 1)
}

const updateItemFromService = (index) => {
    const item = billForm.items[index]
    const service = services.value.find(s => s.id == item.service_id)
    if (service) {
        item.item_name = service.name
        item.unit_price = service.price
    }
}

const calculateItemTotal = (index) => {
    // This will trigger reactivity for the computed total
}

const loadFormData = async () => {
    try {
        const response = await axios.get('/bills/create')
        const data = response.data.data
        patients.value = data.patients
        providers.value = data.providers
        services.value = data.services
    } catch (error) {
        console.error('Error loading form data:', error)
    }
}

const createBill = async () => {
    loading.value = true
    try {
        const response = await axios.post('/bills', billForm)
        router.visit(route('bills.show', response.data.data.id))
    } catch (error) {
        console.error('Error creating bill:', error)
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    loadFormData()
    addBillItem() // Add one item by default
})
</script>
