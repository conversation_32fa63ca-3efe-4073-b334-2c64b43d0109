<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bills', function (Blueprint $table) {
            $table->id();
            $table->string('bill_number')->unique(); // Auto-generated bill number
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->foreignId('consultation_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');

            $table->string('title')->nullable(); // Bill title/description
            $table->decimal('subtotal', 10, 2); // Sum of all bill items
            $table->json('tax_data')->nullable(); // Applied taxes with calculations
            $table->decimal('tax_amount', 10, 2)->default(0); // Total tax amount
            $table->decimal('discount', 10, 2)->default(0); // Discount amount
            $table->decimal('total_amount', 10, 2); // Final amount (subtotal + tax - discount)

            $table->enum('payment_status', ['unpaid', 'paid', 'partially_paid', 'sent_to_patient'])->default('unpaid');
            $table->enum('status', ['draft', 'sent', 'paid', 'cancelled'])->default('draft');

            $table->timestamp('bill_date')->useCurrent();
            $table->timestamp('due_date')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();

            $table->index(['clinic_id', 'payment_status']);
            $table->index(['patient_id', 'status']);
            $table->index(['provider_id', 'bill_date']);
            $table->index('bill_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bills');
    }
};
