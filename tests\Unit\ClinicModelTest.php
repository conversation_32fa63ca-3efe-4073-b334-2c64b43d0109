<?php

namespace Tests\Unit;

use App\Models\Clinic;
use App\Models\User;
use App\Models\Provider;
use App\Models\Patient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class ClinicModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'clinic_admin']);
        Role::create(['name' => 'provider']);
        Role::create(['name' => 'patient']);
    }

    /** @test */
    public function clinic_can_be_created_with_required_fields()
    {
        $clinic = Clinic::factory()->create([
            'name' => 'Test Clinic',
            'email' => '<EMAIL>',
            'phone' => '************'
        ]);

        $this->assertDatabaseHas('clinics', [
            'name' => 'Test Clinic',
            'email' => '<EMAIL>',
            'phone' => '************'
        ]);
    }

    /** @test */
    public function clinic_has_providers_relationship()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create();
        $provider = Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $user->id
        ]);

        $this->assertTrue($clinic->providers->contains($provider));
        $this->assertEquals($clinic->id, $provider->clinic_id);
    }

    /** @test */
    public function clinic_has_patients_relationship()
    {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create();
        $patient = Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $user->id
        ]);

        $this->assertTrue($clinic->patients->contains($patient));
        $this->assertEquals($clinic->id, $patient->clinic_id);
    }

    /** @test */
    public function clinic_can_get_all_associated_users()
    {
        $clinic = Clinic::factory()->create();
        
        // Create provider user
        $providerUser = User::factory()->create();
        Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $providerUser->id
        ]);
        
        // Create patient user
        $patientUser = User::factory()->create();
        Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $patientUser->id
        ]);

        $users = $clinic->users();

        $this->assertCount(2, $users);
        $this->assertTrue($users->contains($providerUser));
        $this->assertTrue($users->contains($patientUser));
    }

    /** @test */
    public function clinic_can_get_provider_users_only()
    {
        $clinic = Clinic::factory()->create();
        
        // Create provider user
        $providerUser = User::factory()->create();
        Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $providerUser->id
        ]);
        
        // Create patient user
        $patientUser = User::factory()->create();
        Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $patientUser->id
        ]);

        $providerUsers = $clinic->getProviderUsers();

        $this->assertCount(1, $providerUsers);
        $this->assertTrue($providerUsers->contains($providerUser));
        $this->assertFalse($providerUsers->contains($patientUser));
    }

    /** @test */
    public function clinic_can_get_patient_users_only()
    {
        $clinic = Clinic::factory()->create();
        
        // Create provider user
        $providerUser = User::factory()->create();
        Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $providerUser->id
        ]);
        
        // Create patient user
        $patientUser = User::factory()->create();
        Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $patientUser->id
        ]);

        $patientUsers = $clinic->getPatientUsers();

        $this->assertCount(1, $patientUsers);
        $this->assertTrue($patientUsers->contains($patientUser));
        $this->assertFalse($patientUsers->contains($providerUser));
    }

    /** @test */
    public function clinic_fillable_attributes_work_correctly()
    {
        $data = [
            'name' => 'Test Clinic',
            'description' => 'A test clinic',
            'email' => '<EMAIL>',
            'phone' => '************',
            'website' => 'https://testclinic.com',
            'address' => '123 Test St',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
            'license_number' => 'LIC123',
            'tax_id' => 'TAX456',
            'is_active' => true,
            'accepts_new_patients' => true,
            'telemedicine_enabled' => true
        ];

        $clinic = Clinic::create($data);

        foreach ($data as $key => $value) {
            $this->assertEquals($value, $clinic->$key);
        }
    }

    /** @test */
    public function clinic_casts_work_correctly()
    {
        $clinic = Clinic::factory()->create([
            'is_active' => 1,
            'accepts_new_patients' => 0,
            'telemedicine_enabled' => 1,
            'operating_hours' => ['monday' => '9:00-17:00'],
            'services_offered' => ['consultation', 'surgery'],
            'insurance_accepted' => ['insurance1', 'insurance2']
        ]);

        // Test boolean casts
        $this->assertIsBool($clinic->is_active);
        $this->assertTrue($clinic->is_active);
        $this->assertIsBool($clinic->accepts_new_patients);
        $this->assertFalse($clinic->accepts_new_patients);
        $this->assertIsBool($clinic->telemedicine_enabled);
        $this->assertTrue($clinic->telemedicine_enabled);

        // Test array casts
        $this->assertIsArray($clinic->operating_hours);
        $this->assertEquals(['monday' => '9:00-17:00'], $clinic->operating_hours);
        $this->assertIsArray($clinic->services_offered);
        $this->assertEquals(['consultation', 'surgery'], $clinic->services_offered);
        $this->assertIsArray($clinic->insurance_accepted);
        $this->assertEquals(['insurance1', 'insurance2'], $clinic->insurance_accepted);
    }

    /** @test */
    public function only_admin_can_modify_slug()
    {
        $clinic = Clinic::factory()->create();
        
        // Test admin user
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $this->assertTrue($clinic->canModifySlug($admin));
        
        // Test clinic admin user
        $clinicAdmin = User::factory()->create();
        $clinicAdmin->assignRole('clinic_admin');
        
        $this->assertFalse($clinic->canModifySlug($clinicAdmin));
        
        // Test provider user
        $provider = User::factory()->create();
        $provider->assignRole('provider');
        
        $this->assertFalse($clinic->canModifySlug($provider));
        
        // Test null user
        $this->assertFalse($clinic->canModifySlug(null));
    }

    /** @test */
    public function clinic_stats_include_correct_counts()
    {
        $clinic = Clinic::factory()->create();
        
        // Create active and inactive providers
        $activeProviderUser = User::factory()->create(['is_active' => true]);
        $inactiveProviderUser = User::factory()->create(['is_active' => false]);
        
        Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $activeProviderUser->id
        ]);
        Provider::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $inactiveProviderUser->id
        ]);
        
        // Create active and inactive patients
        $activePatientUser = User::factory()->create(['is_active' => true]);
        $inactivePatientUser = User::factory()->create(['is_active' => false]);
        
        Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $activePatientUser->id,
            'is_active' => true
        ]);
        Patient::factory()->create([
            'clinic_id' => $clinic->id,
            'user_id' => $inactivePatientUser->id,
            'is_active' => false
        ]);

        $stats = $clinic->stats;

        $this->assertEquals(2, $stats['total_providers']);
        $this->assertEquals(1, $stats['active_providers']);
        $this->assertEquals(2, $stats['total_patients']);
        $this->assertEquals(1, $stats['active_patients']);
    }
}
