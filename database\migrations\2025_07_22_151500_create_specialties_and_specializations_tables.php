<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Consolidated specialties and specializations tables creation
     */
    public function up(): void
    {
        // Create specialties table first (parent table)
        Schema::create('specialties', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., "internal_medicine"
            $table->string('label'); // e.g., "Internal Medicine"
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('wp_specialty_id')->nullable()->unique();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index('is_active');
            $table->index('sort_order');
            $table->index('wp_specialty_id');
        });

        // Create specializations table (child table)
        Schema::create('specializations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., "cardiology"
            $table->string('label'); // e.g., "Cardiology"
            $table->text('description')->nullable();
            $table->foreignId('specialty_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->integer('wp_specialization_id')->nullable()->unique();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index('is_active');
            $table->index('sort_order');
            $table->index('wp_specialization_id');
            $table->index('specialty_id');
        });

        // Add specialty_id to specializations table (this was in a separate migration)
        // This is already included in the specializations table creation above
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('specializations');
        Schema::dropIfExists('specialties');
    }
};
