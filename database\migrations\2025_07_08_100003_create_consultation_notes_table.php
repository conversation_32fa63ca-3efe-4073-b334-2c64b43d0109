<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultation_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->string('note_type')->default('general'); // general, follow_up, amendment, addendum
            $table->text('content');
            $table->json('attachments')->nullable();
            $table->boolean('is_private')->default(false); // Private notes not visible to patient
            $table->datetime('note_date');
            $table->timestamps();

            // Indexes
            $table->index(['consultation_id', 'note_date']);
            $table->index('note_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultation_notes');
    }
};
