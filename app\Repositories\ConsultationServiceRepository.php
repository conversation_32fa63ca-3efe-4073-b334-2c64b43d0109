<?php

namespace App\Repositories;

use App\Models\ConsultationService;
use App\Repositories\Interfaces\ConsultationServiceRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ConsultationServiceRepository implements ConsultationServiceRepositoryInterface
{
    public function getByConsultation(int $consultationId): Collection
    {
        return ConsultationService::with('service')
                                 ->where('consultation_id', $consultationId)
                                 ->orderBy('created_at')
                                 ->get();
    }

    public function getBillableByConsultation(int $consultationId): Collection
    {
        return ConsultationService::with('service')
                                 ->where('consultation_id', $consultationId)
                                 ->billable()
                                 ->orderBy('created_at')
                                 ->get();
    }

    public function getUnbilledByConsultation(int $consultationId): Collection
    {
        return ConsultationService::with('service')
                                 ->where('consultation_id', $consultationId)
                                 ->unbilled()
                                 ->orderBy('created_at')
                                 ->get();
    }

    public function create(array $data): ConsultationService
    {
        return ConsultationService::create($data);
    }

    public function update(ConsultationService $consultationService, array $data): ConsultationService
    {
        $consultationService->update($data);
        return $consultationService->fresh();
    }

    public function delete(ConsultationService $consultationService): bool
    {
        return $consultationService->delete();
    }

    public function findById(int $id): ?ConsultationService
    {
        return ConsultationService::with('service')->find($id);
    }

    public function markAsBilled(int $consultationServiceId, int $billId): bool
    {
        return ConsultationService::where('id', $consultationServiceId)
                                 ->update([
                                     'is_billed' => true,
                                     'bill_id' => $billId,
                                 ]);
    }

    public function getUnbilledByClinic(int $clinicId): Collection
    {
        return ConsultationService::with(['consultation.patient.user', 'consultation.provider.user', 'service'])
                                 ->whereHas('consultation', function ($query) use ($clinicId) {
                                     $query->where('clinic_id', $clinicId);
                                 })
                                 ->unbilled()
                                 ->orderBy('created_at', 'desc')
                                 ->get();
    }
}
