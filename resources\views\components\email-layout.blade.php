<!DOCTYPE html>
<html>
<head>
    <title>{{ $title ?? "Medroid" }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
        }
        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .container {
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 25px 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .header img {
            max-width: 24px;
            height: 24px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .content h1 {
            color: #1f2937;
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: 700;
        }
        .content h2 {
            color: #374151;
            font-size: 22px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        .content h3 {
            color: #4b5563;
            font-size: 18px;
            margin-bottom: 12px;
            font-weight: 600;
        }
        .content p {
            margin-bottom: 16px;
            color: #4b5563;
        }
        .content a {
            color: #2563eb;
            text-decoration: none;
        }
        .content a:hover {
            text-decoration: underline;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px 0;
        }
        .btn:hover {
            opacity: 0.9;
            text-decoration: none;
        }
        .alert {
            padding: 16px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .alert.info {
            background-color: #dbeafe;
            border-left: 4px solid #3b82f6;
            color: #1e40af;
        }
        .alert.success {
            background-color: #dcfce7;
            border-left: 4px solid #22c55e;
            color: #166534;
        }
        .alert.warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            color: #92400e;
        }
        .alert.error {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
            color: #991b1b;
        }
        .highlight {
            background-color: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        .footer {
            background-color: #f9fafb;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 8px 0;
            color: #6b7280;
            font-size: 14px;
        }
        .social-links {
            margin-bottom: 20px;
        }
        .social-links a {
            color: #6b7280;
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
        }
        .social-links a:hover {
            color: #374151;
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            .email-wrapper {
                margin: 0;
                box-shadow: none;
            }
            .content {
                padding: 30px 20px;
            }
            .header {
                padding: 20px 15px;
                gap: 6px;
            }
            .header img {
                max-width: 18px;
                height: 18px;
            }
            .header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="content">
                {!! $slot !!}
            </div>
        </div>
    </div>
</body>
</html>
