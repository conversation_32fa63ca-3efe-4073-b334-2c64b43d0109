<?php

namespace App\Services;

use App\Models\Country;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PriceFormatterService
{
    /**
     * Format price based on user's country
     */
    public function formatPrice(float $price, ?string $countryCode = null): string
    {
        // Get the country code
        if (!$countryCode) {
            $user = Auth::user();
            $countryCode = $user ? $user->country_code : 'GB';
        }

        // Default to GB if no country code
        if (!$countryCode) {
            $countryCode = 'GB';
        }

        // Get country information
        $country = Country::where('code', $countryCode)->first();
        
        if (!$country) {
            // Fallback to GB
            $country = Country::where('code', 'GB')->first();
        }

        if (!$country) {
            // Ultimate fallback
            return '£' . number_format($price, 2);
        }

        // Convert price to country currency (simplified conversion)
        $convertedPrice = $this->convertPrice($price, 'GBP', $country->currency_code);

        // Format with country's currency symbol
        return $country->currency_symbol . number_format($convertedPrice, 2);
    }

    /**
     * Convert price between currencies
     * This is a simplified conversion - in production, you'd use a real exchange rate API
     */
    private function convertPrice(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        // Simple conversion rates (in production, use a real currency API)
        $conversionRates = [
            'GBP' => [
                'USD' => 1.27,
                'EUR' => 1.08,
                'INR' => 105.0,
                'CAD' => 1.71,
                'AUD' => 1.90,
                'NGN' => 1900.0,
                'ZAR' => 23.5,
            ],
            'USD' => [
                'GBP' => 0.79,
                'EUR' => 0.85,
                'INR' => 83.0,
                'CAD' => 1.35,
                'AUD' => 1.50,
                'NGN' => 1500.0,
                'ZAR' => 18.5,
            ],
            'INR' => [
                'GBP' => 0.0095,
                'USD' => 0.012,
                'EUR' => 0.010,
                'CAD' => 0.016,
                'AUD' => 0.018,
                'NGN' => 18.0,
                'ZAR' => 0.22,
            ],
        ];

        $rate = $conversionRates[$fromCurrency][$toCurrency] ?? 1.0;
        return $amount * $rate;
    }

    /**
     * Get currency information for a country
     */
    public function getCurrencyInfo(?string $countryCode = null): array
    {
        // Get the country code
        if (!$countryCode) {
            $user = Auth::user();
            $countryCode = $user ? $user->country_code : 'GB';
        }

        // Default to GB if no country code
        if (!$countryCode) {
            $countryCode = 'GB';
        }

        // Get country information
        $country = Country::where('code', $countryCode)->first();
        
        if (!$country) {
            // Fallback to GB
            $country = Country::where('code', 'GB')->first();
        }

        if (!$country) {
            // Ultimate fallback
            return [
                'code' => 'GBP',
                'symbol' => '£',
                'name' => 'British Pound',
            ];
        }

        return [
            'code' => $country->currency_code,
            'symbol' => $country->currency_symbol,
            'name' => $country->currency_name,
        ];
    }
}