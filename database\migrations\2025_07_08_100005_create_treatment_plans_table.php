<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('treatment_plans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->string('plan_type')->default('treatment'); // treatment, investigation, referral, lifestyle
            $table->string('title');
            $table->text('description');
            $table->text('instructions')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['planned', 'active', 'completed', 'cancelled', 'on_hold'])->default('planned');
            $table->text('outcome')->nullable();
            $table->date('review_date')->nullable();
            $table->json('goals')->nullable(); // Treatment goals and targets
            $table->timestamps();

            // Indexes
            $table->index(['consultation_id', 'plan_type']);
            $table->index('status');
            $table->index('review_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('treatment_plans');
    }
};
