<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class LabTestCatalog extends Model
{
    use HasFactory;

    protected $table = 'lab_test_catalog';

    protected $fillable = [
        'test_code',
        'test_name',
        'category',
        'price',
        'description',
        'requirements',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'requirements' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active tests
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to search tests by name or code
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('test_name', 'like', "%{$search}%")
              ->orWhere('test_code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Get all available categories
     */
    public static function getCategories(): array
    {
        return self::active()
            ->distinct()
            ->pluck('category')
            ->filter()
            ->sort()
            ->values()
            ->toArray();
    }

    /**
     * Check if test requires fasting
     */
    public function requiresFasting(): bool
    {
        return $this->getRequirement('fasting_required', false);
    }

    /**
     * Get fasting hours required
     */
    public function getFastingHours(): ?int
    {
        return $this->getRequirement('fasting_hours');
    }

    /**
     * Get sample type required
     */
    public function getSampleType(): ?string
    {
        return $this->getRequirement('sample_type');
    }

    /**
     * Get turnaround time
     */
    public function getTurnaroundTime(): ?string
    {
        return $this->getRequirement('turnaround_time');
    }

    /**
     * Get special instructions
     */
    public function getSpecialInstructions(): ?string
    {
        return $this->getRequirement('special_instructions');
    }

    /**
     * Get a specific requirement value
     */
    public function getRequirement(string $key, $default = null)
    {
        $requirements = $this->requirements ?? [];
        return $requirements[$key] ?? $default;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->price ? '£' . number_format($this->price, 2) : 'POA';
    }

    /**
     * Get test summary for display
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'test_code' => $this->test_code,
            'test_name' => $this->test_name,
            'category' => $this->category,
            'price' => $this->price,
            'formatted_price' => $this->formatted_price,
            'requires_fasting' => $this->requiresFasting(),
            'fasting_hours' => $this->getFastingHours(),
            'sample_type' => $this->getSampleType(),
            'turnaround_time' => $this->getTurnaroundTime(),
            'special_instructions' => $this->getSpecialInstructions(),
        ];
    }
}
