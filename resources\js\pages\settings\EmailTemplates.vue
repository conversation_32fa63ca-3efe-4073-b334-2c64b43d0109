<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import EmailEditor from '@/components/EmailEditor.vue';
import { useNotifications } from '@/composables/useNotifications';

const breadcrumbs = [
    {
        title: 'Email Templates',
        href: '/settings/email-templates',
    },
];

const loading = ref(false);
const templates = ref([]);
const showCustomizeModal = ref(false);
const showPreviewModal = ref(false);
const selectedTemplate = ref(null);
const customizeForm = ref({
    subject: '',
    content: '',
    is_active: true
});
const previewData = ref(null);
const saving = ref(false);

// Testing functionality
const showTestModal = ref(false);
const testEmail = ref('');
const testingTemplate = ref(null);
const sendingTest = ref(false);
const testResults = ref([]);

// Initialize notifications
const { showSuccess, showError } = useNotifications();

const fetchTemplates = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/settings/email-templates/data');
        templates.value = response.data.templates || {};
    } catch (error) {
        console.error('Error fetching clinic email templates:', error);
        templates.value = {};
    } finally {
        loading.value = false;
    }
};

const openCustomizeModal = (template) => {
    selectedTemplate.value = template;
    customizeForm.value = {
        subject: template.custom_subject || template.system_subject,
        content: template.custom_content || template.system_content,
        is_active: template.custom_is_active !== undefined ? template.custom_is_active : true
    };
    showCustomizeModal.value = true;
};

const closeCustomizeModal = () => {
    showCustomizeModal.value = false;
    selectedTemplate.value = null;
    customizeForm.value = {
        subject: '',
        content: '',
        is_active: true
    };
};

const saveCustomization = async () => {
    if (!selectedTemplate.value) return;

    saving.value = true;
    try {
        await window.axios.post(`/settings/email-templates/${selectedTemplate.value.id}`, customizeForm.value);

        // Show success message
        alert('Template customization saved successfully!');

        // Refresh templates
        await fetchTemplates();

        // Close modal
        closeCustomizeModal();
    } catch (error) {
        console.error('Error saving customization:', error);
        alert('Error saving customization. Please try again.');
    } finally {
        saving.value = false;
    }
};

const openPreviewModal = async (template) => {
    selectedTemplate.value = template;
    previewData.value = null;
    showPreviewModal.value = true;

    try {
        const response = await window.axios.get(`/settings/email-templates/${template.id}/preview`);
        console.log('Preview response:', response.data);
        previewData.value = response.data;
    } catch (error) {
        console.error('Error loading preview:', error);
        console.error('Error details:', error.response?.data);
        previewData.value = { error: 'Failed to load preview: ' + (error.response?.data?.message || error.message) };
    }
};

const closePreviewModal = () => {
    showPreviewModal.value = false;
    selectedTemplate.value = null;
    previewData.value = null;
};

const getPreviewContent = () => {
    if (!customizeForm.value.content) return '<p class="text-gray-400">Start typing your message to see the preview...</p>';

    // Replace common variables with placeholder data for preview
    let content = customizeForm.value.content;
    content = content.replace(/\{\{\s*patientName\s*\}\}/g, '<strong>[Patient Name]</strong>');
    content = content.replace(/\{\{\s*providerName\s*\}\}/g, '<strong>[Provider Name]</strong>');
    content = content.replace(/\{\{\s*appointmentDate\s*\}\}/g, '<strong>[Appointment Date]</strong>');
    content = content.replace(/\{\{\s*appointmentTime\s*\}\}/g, '<strong>[Appointment Time]</strong>');
    content = content.replace(/\{\{\s*serviceName\s*\}\}/g, '<strong>[Service Name]</strong>');
    content = content.replace(/\{\{\s*clinicName\s*\}\}/g, '<strong>[Clinic Name]</strong>');

    return content;
};

// Testing functions
const openTestModal = (template) => {
    testingTemplate.value = template;
    testEmail.value = '';
    testResults.value = [];
    showTestModal.value = true;
};

const closeTestModal = () => {
    showTestModal.value = false;
    testingTemplate.value = null;
    testEmail.value = '';
    testResults.value = [];
};

const sendTestEmail = async () => {
    if (!testEmail.value || !testingTemplate.value) return;

    sendingTest.value = true;
    try {
        const response = await window.axios.post(`/settings/email-templates/${testingTemplate.value.id}/test`, {
            test_email: testEmail.value
        });

        testResults.value.push({
            template: testingTemplate.value.name,
            status: 'success',
            message: 'Test email sent successfully!',
            timestamp: new Date().toLocaleTimeString()
        });

        showSuccess('Test email sent successfully!');
    } catch (error) {
        console.error('Error sending test email:', error);
        testResults.value.push({
            template: testingTemplate.value.name,
            status: 'error',
            message: error.response?.data?.message || 'Failed to send test email',
            timestamp: new Date().toLocaleTimeString()
        });
        showError('Error sending test email: ' + (error.response?.data?.message || error.message));
    } finally {
        sendingTest.value = false;
    }
};

const sendAllTestEmails = async () => {
    if (!testEmail.value) return;

    sendingTest.value = true;
    testResults.value = [];

    // Get all customized templates
    const customizedTemplates = [];
    Object.values(templates.value).forEach(typeTemplates => {
        typeTemplates.forEach(template => {
            if (template.is_customized) {
                customizedTemplates.push(template);
            }
        });
    });

    if (customizedTemplates.length === 0) {
        alert('No customized templates found to test.');
        sendingTest.value = false;
        return;
    }

    // Send test emails one by one
    for (const template of customizedTemplates) {
        try {
            await window.axios.post(`/settings/email-templates/${template.id}/test`, {
                test_email: testEmail.value
            });

            testResults.value.push({
                template: template.name,
                status: 'success',
                message: 'Test email sent successfully!',
                timestamp: new Date().toLocaleTimeString()
            });
        } catch (error) {
            console.error(`Error sending test email for ${template.name}:`, error);
            testResults.value.push({
                template: template.name,
                status: 'error',
                message: error.response?.data?.message || 'Failed to send test email',
                timestamp: new Date().toLocaleTimeString()
            });
        }

        // Small delay between emails
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    sendingTest.value = false;
    alert(`Test completed! Sent ${testResults.value.filter(r => r.status === 'success').length} of ${customizedTemplates.length} emails successfully.`);
};

onMounted(() => {
    fetchTemplates();
});
</script>

<template>
    <Head title="Email Templates" />

    <AppLayout>
        <SettingsLayout :breadcrumbs="breadcrumbs">
            <div class="space-y-6">
                <!-- Header -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Email Templates</h1>
                        <p class="text-sm text-gray-600 mt-1">Customize email templates for your clinic</p>
                    </div>
                    <button @click="openTestModal(null)"
                            class="px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        Test All Templates
                    </button>
                </div>

                <!-- Content -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6">
                        <div v-if="loading" class="text-center">
                            <p class="text-gray-500">Loading templates...</p>
                        </div>
                        
                        <div v-else-if="Object.keys(templates).length === 0" class="text-center">
                            <p class="text-gray-500">No templates found.</p>
                        </div>
                        
                        <div v-else>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Templates</h3>
                            <div v-for="(typeTemplates, type) in templates" :key="type" class="mb-6">
                                <h4 class="text-md font-medium text-gray-700 mb-2 capitalize">{{ type }} Templates</h4>
                                <div class="space-y-2">
                                    <div v-for="template in typeTemplates" :key="template.id" 
                                         class="p-4 border border-gray-200 rounded-lg">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h5 class="font-medium text-gray-900">{{ template.name }}</h5>
                                                <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
                                                <p class="text-xs text-gray-400 mt-1">
                                                    Status: 
                                                    <span v-if="template.is_customized" class="text-blue-600">Customized</span>
                                                    <span v-else class="text-gray-600">Using Default</span>
                                                </p>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button @click="openCustomizeModal(template)"
                                                        class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                                    Customize
                                                </button>
                                                <button @click="openPreviewModal(template)"
                                                        class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200 transition-colors">
                                                    Preview
                                                </button>
                                                <button @click="openTestModal(template)"
                                                        class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors">
                                                    Test
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>

    <!-- Customize Modal -->
    <div v-if="showCustomizeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-lg bg-white max-h-screen overflow-y-auto">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900">
                            Customize Email Template
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">{{ selectedTemplate?.name }}</p>
                    </div>
                    <button @click="closeCustomizeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Form Section -->
                    <div class="space-y-6">
                        <form @submit.prevent="saveCustomization" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Email Subject Line
                                </label>
                                <input v-model="customizeForm.subject"
                                       type="text"
                                       placeholder="Enter the email subject line"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       required>
                                <p class="text-xs text-gray-500 mt-1">This will appear in the recipient's inbox</p>
                            </div>

                            <EmailEditor v-model="customizeForm.content" />

                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-blue-900 mb-2">Available Variables</h4>
                                <p class="text-xs text-blue-700 mb-2">You can use these placeholders in your message:</p>
                                <div class="grid grid-cols-2 gap-2 text-xs">
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} patientName {{ '}' }}{{ '}' }}</code> - Patient's name</div>
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} providerName {{ '}' }}{{ '}' }}</code> - Doctor's name</div>
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} appointmentDate {{ '}' }}{{ '}' }}</code> - Appointment date</div>
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} appointmentTime {{ '}' }}{{ '}' }}</code> - Appointment time</div>
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} serviceName {{ '}' }}{{ '}' }}</code> - Service type</div>
                                    <div><code class="bg-blue-100 px-1 rounded">{{ '{' }}{{ '{' }} clinicName {{ '}' }}{{ '}' }}</code> - Your clinic name</div>
                                </div>
                            </div>

                            <div class="flex items-center bg-gray-50 p-3 rounded-lg">
                                <input v-model="customizeForm.is_active"
                                       type="checkbox"
                                       id="is_active"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-3 block text-sm text-gray-900">
                                    <span class="font-medium">Active Template</span>
                                    <span class="block text-xs text-gray-500">Enable this customized template for your clinic</span>
                                </label>
                            </div>

                            <div class="flex justify-end space-x-3 pt-4 border-t">
                                <button type="button"
                                        @click="closeCustomizeModal"
                                        class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                    Cancel
                                </button>
                                <button type="submit"
                                        :disabled="saving"
                                        class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                    {{ saving ? 'Saving...' : 'Save Template' }}
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Preview Section -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Live Preview</h4>
                        <div class="bg-white border rounded-lg p-4 min-h-96">
                            <div class="border-b pb-2 mb-3">
                                <p class="text-xs text-gray-500">Subject:</p>
                                <p class="font-medium">{{ customizeForm.subject || 'Email Subject' }}</p>
                            </div>
                            <div class="prose prose-sm max-w-none" v-html="getPreviewContent()"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">This preview shows how your email will look to recipients</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        Preview: {{ selectedTemplate?.name }}
                    </h3>
                    <button @click="closePreviewModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div v-if="!previewData" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-2 text-gray-500">Loading preview...</p>
                </div>

                <div v-else-if="previewData.error" class="text-center py-8">
                    <p class="text-red-600">{{ previewData.error }}</p>
                </div>

                <div v-else class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-2">Email Preview</h4>
                        <div class="bg-white border rounded p-4 min-h-96">
                            <!-- Subject Line -->
                            <div class="border-b pb-3 mb-4" v-if="previewData.preview && previewData.preview.subject">
                                <p class="text-xs text-gray-500 uppercase tracking-wide">Subject:</p>
                                <p class="font-semibold text-gray-900 mt-1">{{ previewData.preview.subject }}</p>
                            </div>

                            <!-- Email Content -->
                            <div class="prose prose-sm max-w-none">
                                <div v-if="previewData.preview && previewData.preview.content"
                                     v-html="previewData.preview.content"></div>
                                <div v-else-if="previewData.preview"
                                     v-html="previewData.preview"></div>
                                <div v-else class="text-gray-500 italic">
                                    No preview content available
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button @click="closePreviewModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div v-if="showTestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ testingTemplate ? `Test Template: ${testingTemplate.name}` : 'Test All Customized Templates' }}
                    </h3>
                    <button @click="closeTestModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="space-y-4">
                    <!-- Email Input -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Test Email Address
                        </label>
                        <input v-model="testEmail"
                               type="email"
                               placeholder="Enter email address to receive test emails"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               required>
                        <p class="text-xs text-gray-500 mt-1">Test emails will be sent to this address</p>
                    </div>

                    <!-- Test Results -->
                    <div v-if="testResults.length > 0" class="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Test Results</h4>
                        <div class="space-y-2">
                            <div v-for="result in testResults" :key="`${result.template}-${result.timestamp}`"
                                 class="flex items-center justify-between p-2 bg-white rounded border">
                                <div class="flex items-center space-x-2">
                                    <div :class="result.status === 'success' ? 'w-2 h-2 bg-green-500 rounded-full' : 'w-2 h-2 bg-red-500 rounded-full'"></div>
                                    <span class="text-sm font-medium">{{ result.template }}</span>
                                </div>
                                <div class="text-right">
                                    <p :class="result.status === 'success' ? 'text-green-600 text-xs' : 'text-red-600 text-xs'">
                                        {{ result.message }}
                                    </p>
                                    <p class="text-xs text-gray-400">{{ result.timestamp }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button @click="closeTestModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                            Cancel
                        </button>
                        <button v-if="testingTemplate"
                                @click="sendTestEmail"
                                :disabled="!testEmail || sendingTest"
                                class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ sendingTest ? 'Sending...' : 'Send Test Email' }}
                        </button>
                        <button v-else
                                @click="sendAllTestEmails"
                                :disabled="!testEmail || sendingTest"
                                class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ sendingTest ? 'Sending...' : 'Send All Test Emails' }}
                        </button>
                    </div>

                    <!-- Info Box -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex">
                            <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-blue-700">
                                <p class="font-medium mb-1">Testing Information</p>
                                <ul class="text-xs space-y-1">
                                    <li v-if="testingTemplate">• This will send a test email for the selected template only</li>
                                    <li v-else>• This will send test emails for all your customized templates</li>
                                    <li>• Test emails use sample data (John Doe, Dr. Smith, etc.)</li>
                                    <li>• Only customized templates will be tested</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
