<template>
    <AppLayout title="Bills">
        <div class="py-6">
            <div class="mx-auto max-w-7xl sm:px-4 lg:px-6">
                <!-- Header Section -->
                <div class="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 mb-4">
                    <div class="p-6 border-b border-gray-100 rounded-t-2xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 mb-1">Billing Management</h2>
                                <p class="text-gray-600">Manage bills, invoices, and payments</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button
                                    @click="exportBills"
                                    class="inline-flex items-center px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 transition-all duration-200"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export
                                </button>
                                <Link
                                    href="/bills/create"
                                    class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 transition-all duration-200"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Bill
                                </Link>
                                <button
                                    @click="loadBills(1)"
                                    class="inline-flex items-center px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 transition-all duration-200"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filters Section -->
                    <div class="p-6 bg-gradient-to-r from-gray-50 to-white">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Search</label>
                                <div class="relative">
                                    <input
                                        v-model="filters.search"
                                        @input="debouncedSearch"
                                        type="text"
                                        placeholder="Search bills..."
                                        class="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
                                    />
                                    <svg class="absolute left-3 top-3 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Doctor</label>
                                <select
                                    v-model="filters.doctor"
                                    @change="applyFilters"
                                    class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
                                    style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                >
                                    <option value="">All Doctors</option>
                                    <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
                                        {{ doctor.name }}
                                    </option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Status</label>
                                <select
                                    v-model="filters.status"
                                    @change="applyFilters"
                                    class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
                                    style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                >
                                    <option value="">All Status</option>
                                    <option value="paid">Paid</option>
                                    <option value="unpaid">Unpaid</option>
                                    <option value="partially_paid">Partially Paid</option>
                                    <option value="sent_to_patient">Sent to Patient</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Show</label>
                                <select
                                    v-model="pagination.per_page"
                                    @change="changePerPage"
                                    class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
                                    style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                >
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bills Table -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <div class="flex items-center space-x-4">
                                <h3 class="text-lg font-medium text-gray-900">Bills</h3>
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox"
                                           v-model="selectAll"
                                           @change="toggleSelectAll"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="text-sm text-gray-500">Select All</span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button v-if="selectedBills.length > 0"
                                        @click="bulkAction"
                                        class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded">
                                    Actions ({{ selectedBills.length }})
                                </button>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div v-if="loading" class="flex justify-center items-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>

                        <!-- Bills Table -->
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox"
                                                   v-model="selectAll"
                                                   @change="toggleSelectAll"
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor/Clinic</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient/Service</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Details</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="bill in bills.data" :key="bill.id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox"
                                                   v-model="selectedBills"
                                                   :value="bill.id"
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ bill.bill_number }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ bill.provider?.user?.name }}</div>
                                            <div class="text-sm text-gray-500">{{ bill.clinic?.name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ bill.patient?.user?.name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Total: £{{ bill.total_amount }}</div>
                                            <div v-if="shouldShowDueAmount(bill)" class="text-sm text-gray-500">
                                                Due: £{{ calculateDueAmount(bill) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ formatDate(bill.bill_date) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusClass(bill.payment_status)"
                                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ getStatusLabel(bill.payment_status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <!-- View Details Button - Always available -->
                                                <button @click="viewBill(bill)"
                                                        class="text-blue-600 hover:text-blue-900"
                                                        title="View Details">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                </button>

                                                <!-- Edit Button - Only for unpaid/partially paid bills -->
                                                <button v-if="canEditBill(bill)"
                                                        @click="editBill(bill)"
                                                        class="text-gray-600 hover:text-gray-900"
                                                        title="Edit Bill">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                </button>

                                                <!-- Send to Patient Button - Only for unpaid bills -->
                                                <button v-if="canSendToPatient(bill)"
                                                        @click="sendBillToPatient(bill)"
                                                        class="text-green-600 hover:text-green-900"
                                                        title="Send to Patient">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                    </svg>
                                                </button>

                                                <!-- Mark as Paid Button - Only for unpaid/partially paid bills -->
                                                <button v-if="canMarkAsPaid(bill)"
                                                        @click="markBillAsPaid(bill)"
                                                        class="text-purple-600 hover:text-purple-900"
                                                        title="Mark as Paid">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </button>

                                                <!-- Download Receipt Button - Only for paid bills -->
                                                <button v-if="canDownloadReceipt(bill)"
                                                        @click="downloadReceipt(bill)"
                                                        class="text-indigo-600 hover:text-indigo-900"
                                                        title="Download Receipt">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- No bills message -->
                        <div v-if="!loading && bills.data && bills.data.length === 0" class="text-center py-8">
                            <p class="text-gray-500">No bills found.</p>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div v-if="!loading && bills.data && bills.data.length > 0" class="bg-white border-t border-gray-200">
                        <Pagination
                            :currentPage="pagination.current_page"
                            :lastPage="pagination.last_page"
                            :total="pagination.total"
                            :perPage="pagination.per_page"
                            :from="pagination.from"
                            :to="pagination.to"
                            @page-changed="changePage"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Bill Details Modal -->
        <BillDetailsModal
            :isOpen="showBillDetailsModal"
            :billId="selectedBillId"
            @close="closeBillDetailsModal"
            @sent="onBillSent"
        />
    </AppLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import Pagination from '@/components/Pagination.vue'
import BillDetailsModal from '@/components/BillDetailsModal.vue'
import axios from 'axios'
import { debounce } from 'lodash'

const props = defineProps({
    stats: Object
})

const stats = ref(props.stats || {})
const bills = ref({ data: [], links: [], total: 0 })
const loading = ref(false)
const selectedBills = ref([])
const selectAll = ref(false)
const doctors = ref([])

// Bill details modal state
const showBillDetailsModal = ref(false)
const selectedBillId = ref(null)

// Pagination
const pagination = reactive({
    current_page: 1,
    last_page: 1,
    per_page: 20,
    total: 0,
    from: 0,
    to: 0
})

const filters = ref({
    search: '',
    doctor: '',
    status: '',
    per_page: 20
})

// Load bills from API
const loadBills = async (page = 1) => {
    loading.value = true
    try {
        const params = {
            ...filters.value,
            page: page,
            per_page: pagination.per_page
        }

        const response = await axios.get('/bills/list', { params })

        if (response.data.success) {
            const paginatedData = response.data.data
            bills.value = paginatedData

            // Update pagination object
            Object.assign(pagination, {
                current_page: paginatedData.current_page || 1,
                last_page: paginatedData.last_page || 1,
                per_page: paginatedData.per_page || 20,
                total: paginatedData.total || 0,
                from: paginatedData.from || 0,
                to: paginatedData.to || 0
            })
        } else {
            bills.value = { data: [], links: [], total: 0 }
        }
    } catch (error) {
        console.error('Error loading bills:', error)
        bills.value = { data: [], links: [], total: 0 }
    } finally {
        loading.value = false
    }
}

// Change page
const changePage = (page) => {
    // Ensure page is a number, not an event object
    const pageNumber = typeof page === 'number' ? page : parseInt(page) || 1
    loadBills(pageNumber)
}

// Change per page
const changePerPage = () => {
    loadBills(1) // Reset to page 1 when changing per page
}

// Apply filters
const applyFilters = () => {
    loadBills(1) // Reset to page 1 when applying filters
}

// Load providers for filter
const loadProviders = async () => {
    try {
        const response = await axios.get('/providers-list-dropdown')
        doctors.value = response.data.data || []
    } catch (error) {
        console.error('Error loading providers:', error)
    }
}

// Debounced search
const debouncedSearch = debounce(() => {
    loadBills(1) // Reset to page 1 when searching
}, 300)

// Select all functionality
const toggleSelectAll = () => {
    if (selectAll.value) {
        selectedBills.value = bills.value.data.map(bill => bill.id)
    } else {
        selectedBills.value = []
    }
}

// Utility functions
const getStatusClass = (status) => {
    const classes = {
        'paid': 'bg-green-100 text-green-800',
        'unpaid': 'bg-red-100 text-red-800',
        'partially_paid': 'bg-yellow-100 text-yellow-800',
        'sent_to_patient': 'bg-blue-100 text-blue-800'
    }
    return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status) => {
    const labels = {
        'paid': 'Paid',
        'unpaid': 'Unpaid',
        'partially_paid': 'Partially Paid',
        'sent_to_patient': 'Sent to Patient'
    }
    return labels[status] || status
}

// Business logic functions
const shouldShowDueAmount = (bill) => {
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const calculateDueAmount = (bill) => {
    if (bill.payment_status === 'paid') return 0

    // If there's a specific due amount, use it; otherwise use total amount
    const dueAmount = bill.due_amount || bill.total_amount
    const paidAmount = bill.paid_amount || 0

    return Math.max(0, dueAmount - paidAmount)
}

const canEditBill = (bill) => {
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const canSendToPatient = (bill) => {
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const canMarkAsPaid = (bill) => {
    return bill.payment_status === 'unpaid' || bill.payment_status === 'partially_paid'
}

const canDownloadReceipt = (bill) => {
    return bill.payment_status === 'paid'
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

// Action methods
const viewBill = (bill) => {
    viewBillDetails(bill.id)
}

const editBill = (bill) => {
    router.visit(`/bills/${bill.id}/edit`)
}

const sendBillToPatient = async (bill) => {
    try {
        const response = await axios.post(`/bills/${bill.id}/send-to-patient`)

        if (response.data.success) {
            if (window.toastify) {
                window.toastify('Bill sent to patient successfully!', 'success')
            }
            // Refresh the bills list
            loadBills(pagination.current_page)
        } else {
            if (window.toastify) {
                window.toastify(response.data.message || 'Failed to send bill to patient', 'error')
            }
        }
    } catch (error) {
        const message = error.response?.data?.message || 'Failed to send bill to patient'
        if (window.toastify) {
            window.toastify(message, 'error')
        }
    }
}

const markBillAsPaid = async (bill) => {
    try {
        const response = await axios.post(`/bills/${bill.id}/mark-paid`)

        if (response.data.success) {
            if (window.toastify) {
                window.toastify('Bill marked as paid successfully!', 'success')
            }
            // Refresh the bills list
            loadBills(pagination.current_page)
        } else {
            if (window.toastify) {
                window.toastify(response.data.message || 'Failed to mark bill as paid', 'error')
            }
        }
    } catch (error) {
        const message = error.response?.data?.message || 'Failed to mark bill as paid'
        if (window.toastify) {
            window.toastify(message, 'error')
        }
    }
}

const downloadReceipt = async (bill) => {
    try {
        const response = await axios.get(`/bills/${bill.id}/download-receipt`, {
            responseType: 'blob'
        })

        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `receipt-${bill.bill_number}.pdf`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        if (window.toastify) {
            window.toastify('Receipt downloaded successfully!', 'success')
        }
    } catch (error) {
        const message = error.response?.data?.message || 'Failed to download receipt'
        if (window.toastify) {
            window.toastify(message, 'error')
        }
    }
}

const bulkAction = () => {
    // Implement bulk actions
    console.log('Bulk action for bills:', selectedBills.value)
}

const exportBills = async () => {
    try {
        const response = await axios.get('/bills/export', {
            params: filters.value,
            responseType: 'blob'
        })

        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'bills-export.csv')
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
    } catch (error) {
        console.error('Error exporting bills:', error)
        alert('Failed to export bills')
    }
}

// Bill details modal functions
const viewBillDetails = (billId) => {
    selectedBillId.value = billId
    showBillDetailsModal.value = true
}

const closeBillDetailsModal = () => {
    showBillDetailsModal.value = false
    selectedBillId.value = null
}

const onBillSent = (bill) => {
    // Refresh the bills list to show updated status
    loadBills(pagination.current_page)
}

onMounted(async () => {
    await loadBills()
    await loadProviders()
})
</script>

<style scoped>
/* Add any custom styles if needed */
</style>
