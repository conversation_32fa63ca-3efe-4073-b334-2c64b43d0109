<?php

namespace App\Observers;

use App\Models\Patient;
use App\Models\AuditLog;

class PatientObserver
{
    /**
     * Handle the Patient "created" event.
     */
    public function created(Patient $patient): void
    {
        AuditLog::logActivity([
            'action' => 'create',
            'resource_type' => 'patient',
            'resource_id' => $patient->id,
            'patient_id' => $patient->id,
            'description' => 'Patient record created',
            'is_sensitive' => true,
            'severity' => 'medium',
            'new_values' => $patient->toArray(),
        ]);
    }

    /**
     * Handle the Patient "updated" event.
     */
    public function updated(Patient $patient): void
    {
        $changes = $patient->getChanges();
        $original = $patient->getOriginal();

        // Get only the changed fields
        $changedFields = array_keys($changes);

        // Create a shorter description to avoid database field length limits
        $fieldCount = count($changedFields);
        $description = "Patient record updated ({$fieldCount} fields)";

        // If only a few fields, list them; otherwise just show count
        if ($fieldCount <= 5) {
            $description = 'Patient record updated: ' . implode(', ', $changedFields);
        }

        AuditLog::logActivity([
            'action' => 'update',
            'resource_type' => 'patient',
            'resource_id' => $patient->id,
            'patient_id' => $patient->id,
            'description' => $description,
            'accessed_fields' => $changedFields,
            'is_sensitive' => true,
            'severity' => 'high',
            'old_values' => array_intersect_key($original, $changes),
            'new_values' => $changes,
        ]);
    }

    /**
     * Handle the Patient "deleted" event.
     */
    public function deleted(Patient $patient): void
    {
        AuditLog::logActivity([
            'action' => 'delete',
            'resource_type' => 'patient',
            'resource_id' => $patient->id,
            'patient_id' => $patient->id,
            'description' => 'Patient record deleted',
            'is_sensitive' => true,
            'severity' => 'critical',
            'old_values' => $patient->toArray(),
        ]);
    }

    /**
     * Handle the Patient "restored" event.
     */
    public function restored(Patient $patient): void
    {
        AuditLog::logActivity([
            'action' => 'restore',
            'resource_type' => 'patient',
            'resource_id' => $patient->id,
            'patient_id' => $patient->id,
            'description' => 'Patient record restored',
            'is_sensitive' => true,
            'severity' => 'high',
            'new_values' => $patient->toArray(),
        ]);
    }

    /**
     * Handle the Patient "force deleted" event.
     */
    public function forceDeleted(Patient $patient): void
    {
        AuditLog::logActivity([
            'action' => 'force_delete',
            'resource_type' => 'patient',
            'resource_id' => $patient->id,
            'patient_id' => $patient->id,
            'description' => 'Patient record permanently deleted',
            'is_sensitive' => true,
            'severity' => 'critical',
            'old_values' => $patient->toArray(),
        ]);
    }
}
