<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="closeModal"></div>

            <div class="inline-block w-full max-w-4xl p-8 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Edit Patient Details</h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="savePatient" v-if="!loading">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- First Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <input
                                v-model="form.first_name"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.first_name }"
                            />
                            <p v-if="errors.first_name" class="text-red-500 text-xs mt-1">{{ errors.first_name[0] }}</p>
                        </div>

                        <!-- Last Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <input
                                v-model="form.last_name"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.last_name }"
                            />
                            <p v-if="errors.last_name" class="text-red-500 text-xs mt-1">{{ errors.last_name[0] }}</p>
                        </div>

                        <!-- Email -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                v-model="form.email"
                                type="email"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.email }"
                            />
                            <p v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email[0] }}</p>
                        </div>

                        <!-- Mobile Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                            <div class="flex">
                                <select v-model="form.country_code" class="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50">
                                    <option value="+44">+44</option>
                                    <option value="+1">+1</option>
                                    <option value="+91">+91</option>
                                </select>
                                <input
                                    v-model="form.phone_number"
                                    type="text"
                                    class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    :class="{ 'border-red-500': errors.phone_number }"
                                />
                            </div>
                            <p v-if="errors.phone_number" class="text-red-500 text-xs mt-1">{{ errors.phone_number[0] }}</p>
                        </div>

                        <!-- Date of Birth -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                            <input
                                v-model="form.date_of_birth"
                                type="date"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.date_of_birth }"
                            />
                            <p v-if="errors.date_of_birth" class="text-red-500 text-xs mt-1">{{ errors.date_of_birth[0] }}</p>
                        </div>

                        <!-- Gender -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                            <select
                                v-model="form.gender"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.gender }"
                            >
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                            <p v-if="errors.gender" class="text-red-500 text-xs mt-1">{{ errors.gender[0] }}</p>
                        </div>

                        <!-- NHS Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">NHS Number</label>
                            <input
                                v-model="form.nhs_number"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.nhs_number }"
                            />
                            <p v-if="errors.nhs_number" class="text-red-500 text-xs mt-1">{{ errors.nhs_number[0] }}</p>
                        </div>

                        <!-- Country -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                            <select
                                v-model="form.country"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.country }"
                            >
                                <option value="">Select Country</option>
                                <option value="United Kingdom">United Kingdom</option>
                                <option value="United States">United States</option>
                                <option value="Canada">Canada</option>
                                <option value="Australia">Australia</option>
                            </select>
                            <p v-if="errors.country" class="text-red-500 text-xs mt-1">{{ errors.country[0] }}</p>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea
                            v-model="form.address"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            :class="{ 'border-red-500': errors.address }"
                        ></textarea>
                        <p v-if="errors.address" class="text-red-500 text-xs mt-1">{{ errors.address[0] }}</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <!-- City -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                            <input
                                v-model="form.city"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.city }"
                            />
                            <p v-if="errors.city" class="text-red-500 text-xs mt-1">{{ errors.city[0] }}</p>
                        </div>

                        <!-- Post Code -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Post Code</label>
                            <input
                                v-model="form.post_code"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.post_code }"
                            />
                            <p v-if="errors.post_code" class="text-red-500 text-xs mt-1">{{ errors.post_code[0] }}</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3 mt-6">
                        <button
                            type="button"
                            @click="closeModal"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="saving"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {{ saving ? 'Saving...' : 'Save Changes' }}
                        </button>
                    </div>
                </form>

                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Loading patient data...</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useNotifications } from '@/composables/useNotifications';

const props = defineProps({
    isOpen: Boolean,
    patientId: [String, Number]
});

const emit = defineEmits(['close', 'saved']);

const { showSuccess, showError } = useNotifications();

// Form data
const form = ref({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    country_code: '+44',
    date_of_birth: '',
    gender: '',
    nhs_number: '',
    country: '',
    address: '',
    city: '',
    post_code: ''
});

const loading = ref(false);
const saving = ref(false);
const errors = ref({});

// Methods
const fetchPatientData = async () => {
    if (!props.patientId) return;
    
    loading.value = true;
    try {
        const response = await window.axios.get(`/patients/${props.patientId}`);
        if (response.data.success) {
            const patient = response.data.data;
            form.value = {
                first_name: patient.first_name || '',
                last_name: patient.last_name || '',
                email: patient.email || '',
                phone_number: patient.phone_number || '',
                country_code: patient.country_code || '+44',
                date_of_birth: patient.date_of_birth || '',
                gender: patient.gender || '',
                nhs_number: patient.nhs_number || '',
                country: patient.country || '',
                address: patient.address || '',
                city: patient.city || '',
                post_code: patient.post_code || ''
            };
        }
    } catch (error) {
        console.error('Error fetching patient:', error);
        showError('Failed to load patient data. Please try again.');
    } finally {
        loading.value = false;
    }
};

const savePatient = async () => {
    saving.value = true;
    errors.value = {};
    
    try {
        const response = await window.axios.put(`/update-patient/${props.patientId}`, form.value);
        
        if (response.data.success || response.status === 200) {
            showSuccess('Patient updated successfully!');
            emit('saved');
            closeModal();
        }
    } catch (error) {
        console.error('Error saving patient:', error);
        
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
            showError('Please fix the validation errors and try again.');
        } else {
            showError('Failed to update patient. Please try again.');
        }
    } finally {
        saving.value = false;
    }
};

const closeModal = () => {
    emit('close');
    // Reset form and errors
    form.value = {
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        country_code: '+44',
        date_of_birth: '',
        gender: '',
        nhs_number: '',
        country: '',
        address: '',
        city: '',
        post_code: ''
    };
    errors.value = {};
};

// Watch for modal open/close
watch(() => props.isOpen, (newValue) => {
    if (newValue && props.patientId) {
        fetchPatientData();
    }
});
</script>
