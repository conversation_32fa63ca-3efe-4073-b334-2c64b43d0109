<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\Prescription;
use App\Models\MedicalLetter;
use Barryvdh\DomPDF\Facade\Pdf;
use Spatie\LaravelPdf\Facades\Pdf as SpatiePdf;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Storage;

/**
 * Medical PDF Service with Modern Styling Support
 * Supports CSS3, Flexbox, Grid, and responsive design
 */
class MedicalPdfService
{
    private FhirService $fhirService;

    public function __construct(FhirService $fhirService)
    {
        $this->fhirService = $fhirService;
    }

    /**
     * Generate prescription PDF with modern styling
     */
    public function generatePrescriptionPdf(Prescription $prescription): string
    {
        $data = [
            'prescription' => $prescription,
            'patient' => $prescription->patient,
            'provider' => $prescription->provider,
            'consultation' => $prescription->consultation,
            'items' => $prescription->items()->with('medication')->get(),
            'fhir_data' => $this->fhirService->patientToFhir($prescription->patient),
            'generated_at' => now(),
            'clinic' => $prescription->patient->clinic,
        ];

        // Use Spatie PDF for better CSS support
        $pdf = SpatiePdf::view('pdf.prescription', $data)
            ->format('A4')
            ->margins(15, 15, 15, 15)
            ->headerHtml(view('pdf.partials.header', $data))
            ->footerHtml(view('pdf.partials.footer', $data))
            ->showBackground()
            ->showBrowserHeaderAndFooter()
            ->emulateMedia('print');

        $filename = "prescription_{$prescription->id}_{$prescription->created_at->format('Y-m-d')}.pdf";
        $path = "prescriptions/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->string());
        
        return $path;
    }

    /**
     * Generate consultation report PDF
     */
    public function generateConsultationPdf(Consultation $consultation): string
    {
        $data = [
            'consultation' => $consultation,
            'patient' => $consultation->patient,
            'provider' => $consultation->provider,
            'diagnoses' => $consultation->diagnoses()->with('icd10Code')->get(),
            'prescriptions' => $consultation->prescriptions()->with('items.medication')->get(),
            'treatment_plans' => $consultation->treatmentPlans,
            'vital_signs' => $consultation->vitalSigns,
            'fhir_encounter' => $this->fhirService->consultationToFhir($consultation),
            'generated_at' => now(),
            'clinic' => $consultation->patient->clinic,
        ];

        $pdf = SpatiePdf::view('pdf.consultation-report', $data)
            ->format('A4')
            ->margins(20, 20, 20, 20)
            ->headerHtml(view('pdf.partials.medical-header', $data))
            ->footerHtml(view('pdf.partials.medical-footer', $data))
            ->showBackground()
            ->showBrowserHeaderAndFooter()
            ->emulateMedia('print');

        $filename = "consultation_{$consultation->id}_{$consultation->consultation_date->format('Y-m-d')}.pdf";
        $path = "consultations/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->string());
        
        return $path;
    }

    /**
     * Generate medical letter PDF
     */
    public function generateMedicalLetterPdf(MedicalLetter $letter): string
    {
        $data = [
            'letter' => $letter,
            'patient' => $letter->patient,
            'provider' => $letter->provider,
            'consultation' => $letter->consultation,
            'generated_at' => now(),
            'clinic' => $letter->patient->clinic,
            'digital_signature' => $this->getDigitalSignature($letter->provider),
        ];

        $pdf = SpatiePdf::view('pdf.medical-letter', $data)
            ->format('A4')
            ->margins(25, 25, 25, 25)
            ->headerHtml(view('pdf.partials.letterhead', $data))
            ->footerHtml(view('pdf.partials.letter-footer', $data))
            ->showBackground()
            ->showBrowserHeaderAndFooter()
            ->emulateMedia('print');

        $filename = "letter_{$letter->id}_{$letter->created_at->format('Y-m-d')}.pdf";
        $path = "letters/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->string());
        
        return $path;
    }

    /**
     * Generate patient summary PDF
     */
    public function generatePatientSummaryPdf($patientId, $dateFrom = null, $dateTo = null): string
    {
        $patient = \App\Models\Patient::with([
            'user',
            'clinic',
            'consultations.diagnoses.icd10Code',
            'consultations.prescriptions.items.medication',
            'consultations.treatmentPlans',
            'consultations.vitalSigns'
        ])->findOrFail($patientId);

        $consultationsQuery = $patient->consultations();
        
        if ($dateFrom) {
            $consultationsQuery->where('consultation_date', '>=', $dateFrom);
        }
        
        if ($dateTo) {
            $consultationsQuery->where('consultation_date', '<=', $dateTo);
        }

        $consultations = $consultationsQuery->orderBy('consultation_date', 'desc')->get();

        $data = [
            'patient' => $patient,
            'consultations' => $consultations,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'fhir_patient' => $this->fhirService->patientToFhir($patient),
            'generated_at' => now(),
            'clinic' => $patient->clinic,
        ];

        $pdf = SpatiePdf::view('pdf.patient-summary', $data)
            ->format('A4')
            ->margins(20, 20, 20, 20)
            ->headerHtml(view('pdf.partials.medical-header', $data))
            ->footerHtml(view('pdf.partials.medical-footer', $data))
            ->showBackground()
            ->showBrowserHeaderAndFooter()
            ->emulateMedia('print');

        $filename = "patient_summary_{$patient->id}_{now()->format('Y-m-d')}.pdf";
        $path = "summaries/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->string());
        
        return $path;
    }

    /**
     * Get digital signature for provider
     */
    private function getDigitalSignature($provider): ?string
    {
        if ($provider && $provider->digital_signature_path) {
            return Storage::disk('public')->url($provider->digital_signature_path);
        }
        
        return null;
    }

    /**
     * Generate batch PDFs for multiple prescriptions
     */
    public function generateBatchPrescriptionPdfs(array $prescriptionIds): array
    {
        $paths = [];
        
        foreach ($prescriptionIds as $id) {
            $prescription = Prescription::findOrFail($id);
            $paths[] = $this->generatePrescriptionPdf($prescription);
        }
        
        return $paths;
    }

    /**
     * Get PDF configuration for modern styling
     */
    private function getPdfConfig(): array
    {
        return [
            'format' => 'A4',
            'orientation' => 'portrait',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 20,
            'margin_bottom' => 20,
            'margin_header' => 10,
            'margin_footer' => 10,
            'default_font_size' => 11,
            'default_font' => 'Arial',
            'display_mode' => 'fullpage',
            'tempDir' => storage_path('app/temp'),
            'enable_php' => false,
            'enable_javascript' => true,
            'enable_html5_parser' => true,
            'enable_remote' => true,
        ];
    }
}
