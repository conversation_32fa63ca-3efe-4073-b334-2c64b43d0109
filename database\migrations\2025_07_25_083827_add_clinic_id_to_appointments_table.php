<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // Add clinic_id column if it doesn't exist
            if (!Schema::hasColumn('appointments', 'clinic_id')) {
                $table->unsignedBigInteger('clinic_id')->nullable()->after('provider_id');
                $table->foreign('clinic_id')->references('id')->on('clinics')->onDelete('set null');
                $table->index('clinic_id');
            }
        });

        // Populate clinic_id from provider's clinic_id for existing appointments
        \DB::statement('
            UPDATE appointments a
            INNER JOIN providers p ON a.provider_id = p.id
            SET a.clinic_id = p.clinic_id
            WHERE a.clinic_id IS NULL AND p.clinic_id IS NOT NULL
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            if (Schema::hasColumn('appointments', 'clinic_id')) {
                $table->dropForeign(['clinic_id']);
                $table->dropIndex(['clinic_id']);
                $table->dropColumn('clinic_id');
            }
        });
    }
};
