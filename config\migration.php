<?php

return [

    /*
    |--------------------------------------------------------------------------
    | WordPress API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for connecting to WordPress KiviCare plugin API
    | for data migration purposes.
    |
    */

    'wordpress_api_url' => env('WP_API_URL'),
    'wordpress_auth' => env('WP_API_AUTH'),
    'wordpress_nonce' => env('WP_API_NONCE'),

    /*
    |--------------------------------------------------------------------------
    | Migration Settings
    |--------------------------------------------------------------------------
    |
    | General settings for the migration process
    |
    */

    'batch_size' => env('MIGRATION_BATCH_SIZE', 500),
    'timeout' => env('MIGRATION_TIMEOUT', 300),
    'backup_path' => env('MIGRATION_BACKUP_PATH', storage_path('backups')),
    'backup_keep_days' => env('MIGRATION_BACKUP_KEEP_DAYS', 30),
    'backup_compress' => env('MIGRATION_BACKUP_COMPRESS', true),

    /*
    |--------------------------------------------------------------------------
    | WordPress Database Configuration (if needed for direct access)
    |--------------------------------------------------------------------------
    */

    'wordpress_database' => [
        'host' => env('WP_DB_HOST', '127.0.0.1'),
        'port' => env('WP_DB_PORT', 3306),
        'database' => env('WP_DB_DATABASE'),
        'username' => env('WP_DB_USERNAME'),
        'password' => env('WP_DB_PASSWORD'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Logging
    |--------------------------------------------------------------------------
    */

    'log_channel' => 'migration',
    'log_level' => 'info',

    /*
    |--------------------------------------------------------------------------
    | Role Mapping (WordPress to Laravel)
    |--------------------------------------------------------------------------
    */

    'role_mapping' => [
        'kiviCare_doctor' => 'provider',
        'kiviCare_receptionist' => 'staff',
        'kiviCare_patient' => 'patient',
        'kiviCare_clinic_admin' => 'clinic_admin',
        'doctor' => 'provider',
        'receptionist' => 'staff',
        'patient' => 'patient',
        'clinic_admin' => 'clinic_admin',
        'administrator' => 'admin',
        'admin' => 'admin',
    ],

];
