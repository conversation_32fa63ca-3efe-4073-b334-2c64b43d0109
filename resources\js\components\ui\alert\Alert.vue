<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface Props {
  variant?: 'default' | 'destructive';
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
});

const alertVariants = computed(() => {
  const variants = {
    default: 'bg-background text-foreground border-border',
    destructive: 'border-destructive/50 text-destructive [&>svg]:text-destructive',
  };
  
  return cn(
    'relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7',
    variants[props.variant],
    props.class
  );
});
</script>

<template>
  <div :class="alertVariants" role="alert">
    <slot />
  </div>
</template>
