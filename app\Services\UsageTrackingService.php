<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class UsageTrackingService
{
    /**
     * Track chat message usage for a user
     */
    public function trackChatMessage(User $user): bool
    {
        try {
            // Reset usage counters if needed
            $user->resetUsageIfNeeded();
            
            // Check if user has exceeded limits before tracking
            if ($user->hasExceededLimit('chat_messages_hour') || $user->hasExceededLimit('chat_messages_day')) {
                return false;
            }

            // Increment usage
            $user->incrementUsage('chat_message');
            
            Log::info('Chat message usage tracked', [
                'user_id' => $user->id,
                'current_usage' => $user->getCurrentUsage()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to track chat message usage', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Track appointment creation usage for a user
     */
    public function trackAppointment(User $user): bool
    {
        try {
            // Reset usage counters if needed
            $user->resetUsageIfNeeded();
            
            // Check if user has exceeded limits before tracking
            if ($user->hasExceededLimit('appointments_month')) {
                return false;
            }

            // Increment usage
            $user->incrementUsage('appointment');
            
            Log::info('Appointment usage tracked', [
                'user_id' => $user->id,
                'current_usage' => $user->getCurrentUsage()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to track appointment usage', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Track API request usage for a user
     */
    public function trackApiRequest(User $user): bool
    {
        try {
            // Reset usage counters if needed
            $user->resetUsageIfNeeded();
            
            // Check if user has exceeded limits before tracking
            if ($user->hasExceededLimit('api_requests_minute') || $user->hasExceededLimit('api_requests_hour')) {
                return false;
            }

            // Increment usage
            $user->incrementUsage('api_request');
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to track API request usage', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get usage statistics for a user
     */
    public function getUserUsageStats(User $user): array
    {
        $user->resetUsageIfNeeded();
        
        $limits = $user->getUsageLimits();
        $usage = $user->getCurrentUsage();
        
        return [
            'limits' => $limits,
            'current_usage' => $usage,
            'percentages' => [
                'chat_messages_day' => $this->calculatePercentage(
                    $usage['chat_messages_today'] ?? 0,
                    $limits['chat_messages_per_day'] ?? 1
                ),
                'chat_messages_hour' => $this->calculatePercentage(
                    $usage['chat_messages_this_hour'] ?? 0,
                    $limits['chat_messages_per_hour'] ?? 1
                ),
                'appointments_month' => $this->calculatePercentage(
                    $usage['appointments_this_month'] ?? 0,
                    $limits['appointments_per_month'] ?? 1
                ),
                'api_requests_minute' => $this->calculatePercentage(
                    $usage['api_requests_this_minute'] ?? 0,
                    $limits['api_requests_per_minute'] ?? 1
                ),
                'api_requests_hour' => $this->calculatePercentage(
                    $usage['api_requests_this_hour'] ?? 0,
                    $limits['api_requests_per_hour'] ?? 1
                ),
            ],
            'warnings' => $this->getUsageWarnings($usage, $limits),
        ];
    }

    /**
     * Calculate percentage usage
     */
    private function calculatePercentage(int $current, int $limit): int
    {
        if ($limit <= 0) return 0;
        return min(100, round(($current / $limit) * 100));
    }

    /**
     * Get usage warnings for the user
     */
    private function getUsageWarnings(array $usage, array $limits): array
    {
        $warnings = [];
        
        // Check chat messages (daily)
        $chatDayPercentage = $this->calculatePercentage(
            $usage['chat_messages_today'] ?? 0,
            $limits['chat_messages_per_day'] ?? 1
        );
        
        if ($chatDayPercentage >= 90) {
            $warnings[] = [
                'type' => 'chat_messages_day',
                'message' => 'You\'ve used ' . $chatDayPercentage . '% of your daily chat messages.',
                'severity' => 'high'
            ];
        } elseif ($chatDayPercentage >= 75) {
            $warnings[] = [
                'type' => 'chat_messages_day',
                'message' => 'You\'ve used ' . $chatDayPercentage . '% of your daily chat messages.',
                'severity' => 'medium'
            ];
        }
        
        // Check appointments (monthly)
        $appointmentPercentage = $this->calculatePercentage(
            $usage['appointments_this_month'] ?? 0,
            $limits['appointments_per_month'] ?? 1
        );
        
        if ($appointmentPercentage >= 90) {
            $warnings[] = [
                'type' => 'appointments_month',
                'message' => 'You\'ve used ' . $appointmentPercentage . '% of your monthly appointments.',
                'severity' => 'high'
            ];
        } elseif ($appointmentPercentage >= 75) {
            $warnings[] = [
                'type' => 'appointments_month',
                'message' => 'You\'ve used ' . $appointmentPercentage . '% of your monthly appointments.',
                'severity' => 'medium'
            ];
        }
        
        return $warnings;
    }

    /**
     * Check if user can perform action and track usage
     */
    public function canPerformAndTrack(User $user, string $action): array
    {
        $user->resetUsageIfNeeded();
        
        $canPerform = !$user->hasExceededLimit($action);
        
        if ($canPerform) {
            // Track the usage based on action type
            switch ($action) {
                case 'chat_messages_hour':
                case 'chat_messages_day':
                    $this->trackChatMessage($user);
                    break;
                case 'appointments_month':
                    $this->trackAppointment($user);
                    break;
                case 'api_requests_minute':
                case 'api_requests_hour':
                    $this->trackApiRequest($user);
                    break;
            }
        }
        
        return [
            'can_perform' => $canPerform,
            'usage_stats' => $this->getUserUsageStats($user),
            'upgrade_required' => $user->isOnFreePlan() && !$canPerform,
        ];
    }

    /**
     * Reset usage counters for all users (for scheduled tasks)
     */
    public function resetAllUsageCounters(): void
    {
        $now = Carbon::now();
        
        // Reset daily counters at midnight
        if ($now->hour === 0 && $now->minute === 0) {
            User::whereNotNull('subscription_plan_id')->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $usage = $user->getCurrentUsage();
                    $usage['chat_messages_today'] = 0;
                    $user->update(['current_usage' => $usage]);
                }
            });
        }
        
        // Reset hourly counters at the top of each hour
        if ($now->minute === 0) {
            User::whereNotNull('subscription_plan_id')->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $usage = $user->getCurrentUsage();
                    $usage['chat_messages_this_hour'] = 0;
                    $usage['api_requests_this_hour'] = 0;
                    $user->update(['current_usage' => $usage]);
                }
            });
        }
        
        // Reset minute counters every minute
        User::whereNotNull('subscription_plan_id')->chunk(100, function ($users) {
            foreach ($users as $user) {
                $usage = $user->getCurrentUsage();
                $usage['api_requests_this_minute'] = 0;
                $user->update(['current_usage' => $usage]);
            }
        });
        
        // Reset monthly counters on the first day of each month
        if ($now->day === 1 && $now->hour === 0 && $now->minute === 0) {
            User::whereNotNull('subscription_plan_id')->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $usage = $user->getCurrentUsage();
                    $usage['appointments_this_month'] = 0;
                    $user->update(['current_usage' => $usage]);
                }
            });
        }
    }
}
