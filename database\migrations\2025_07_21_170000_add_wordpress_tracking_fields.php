<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Consolidated WordPress tracking fields migration for all core tables
     */
    public function up(): void
    {
        // Add WordPress tracking fields to core tables
        Schema::table('clinics', function (Blueprint $table) {
            $table->integer('wp_clinic_id')->nullable()->unique()->after('id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->integer('wp_user_id')->nullable()->unique()->after('id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->integer('wp_service_id')->nullable()->unique()->after('id');
        });

        Schema::table('appointments', function (Blueprint $table) {
            $table->integer('wp_appointment_id')->nullable()->unique()->after('id');
        });

        Schema::table('consultations', function (Blueprint $table) {
            $table->integer('wp_encounter_id')->nullable()->unique()->after('id');
        });

        Schema::table('prescriptions', function (Blueprint $table) {
            $table->integer('wp_prescription_id')->nullable()->unique()->after('id');
        });

        // Add WordPress tracking to additional tables
        if (Schema::hasTable('bills')) {
            Schema::table('bills', function (Blueprint $table) {
                if (!Schema::hasColumn('bills', 'wp_bill_id')) {
                    $table->integer('wp_bill_id')->nullable()->unique()->after('id');
                }
            });
        }

        if (Schema::hasTable('bill_items')) {
            Schema::table('bill_items', function (Blueprint $table) {
                if (!Schema::hasColumn('bill_items', 'wp_bill_item_id')) {
                    $table->integer('wp_bill_item_id')->nullable()->unique()->after('id');
                }
            });
        }

        if (Schema::hasTable('health_records')) {
            Schema::table('health_records', function (Blueprint $table) {
                if (!Schema::hasColumn('health_records', 'wp_medical_problem_id')) {
                    $table->integer('wp_medical_problem_id')->nullable()->unique()->after('id');
                }
            });
        }

        if (Schema::hasTable('consultation_documents')) {
            Schema::table('consultation_documents', function (Blueprint $table) {
                if (!Schema::hasColumn('consultation_documents', 'wp_document_id')) {
                    $table->integer('wp_document_id')->nullable()->unique()->after('id');
                }
            });
        }

        if (Schema::hasTable('categories')) {
            Schema::table('categories', function (Blueprint $table) {
                if (!Schema::hasColumn('categories', 'wp_category_id')) {
                    $table->integer('wp_category_id')->nullable()->unique()->after('id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->dropColumn('wp_clinic_id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('wp_user_id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('wp_service_id');
        });

        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn('wp_appointment_id');
        });

        Schema::table('consultations', function (Blueprint $table) {
            $table->dropColumn('wp_encounter_id');
        });

        Schema::table('prescriptions', function (Blueprint $table) {
            $table->dropColumn('wp_prescription_id');
        });

        if (Schema::hasTable('bills') && Schema::hasColumn('bills', 'wp_bill_id')) {
            Schema::table('bills', function (Blueprint $table) {
                $table->dropColumn('wp_bill_id');
            });
        }

        if (Schema::hasTable('bill_items') && Schema::hasColumn('bill_items', 'wp_bill_item_id')) {
            Schema::table('bill_items', function (Blueprint $table) {
                $table->dropColumn('wp_bill_item_id');
            });
        }

        if (Schema::hasTable('health_records') && Schema::hasColumn('health_records', 'wp_medical_problem_id')) {
            Schema::table('health_records', function (Blueprint $table) {
                $table->dropColumn('wp_medical_problem_id');
            });
        }

        if (Schema::hasTable('consultation_documents') && Schema::hasColumn('consultation_documents', 'wp_document_id')) {
            Schema::table('consultation_documents', function (Blueprint $table) {
                $table->dropColumn('wp_document_id');
            });
        }

        if (Schema::hasTable('categories') && Schema::hasColumn('categories', 'wp_category_id')) {
            Schema::table('categories', function (Blueprint $table) {
                $table->dropColumn('wp_category_id');
            });
        }
    }
};
