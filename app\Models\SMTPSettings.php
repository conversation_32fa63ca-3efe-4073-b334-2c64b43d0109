<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class SMTPSettings extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'smtp_settings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'email_provider',
        'details',
        'additional_details',
        'is_active',
        'is_verified',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'details' => 'array',
        'additional_details' => 'array',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
    ];

    /**
     * Get the clinic that owns the SMTP settings.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get SMTP settings for a specific clinic and provider.
     */
    public static function getForClinic($clinicId, $provider = 'smtp')
    {
        return static::where('clinic_id', $clinicId)
            ->where('email_provider', $provider)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get email configuration for a clinic.
     * SMTP settings from system, only sender name/email can be clinic-specific.
     */
    public static function getEmailConfig($clinicId = null)
    {
        // Start with system configuration
        $config = [
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
            'source' => 'system',
            'clinic_id' => $clinicId
        ];

        // If no clinic ID provided, return system configuration
        if (!$clinicId) {
            return $config;
        }

        try {
            $settings = static::getForClinic($clinicId, 'smtp');

            // If clinic has valid, active settings, use clinic-specific sender details
            if ($settings && $settings->is_active && $settings->isConfigured()) {
                $config['from_address'] = $settings->details['sender_email'];
                $config['from_name'] = $settings->details['sender_name'];
                $config['source'] = 'clinic_specific';
            } else {
                $config['source'] = 'system_fallback';
            }
        } catch (\Exception $e) {
            \Log::warning("Error loading SMTP settings for clinic {$clinicId}: " . $e->getMessage());
            $config['source'] = 'system_error_fallback';
        }

        return $config;
    }

    /**
     * Scope to get active SMTP settings.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get settings by provider.
     */
    public function scopeByProvider($query, $provider)
    {
        return $query->where('email_provider', $provider);
    }

    /**
     * Check if the SMTP settings are properly configured.
     */
    public function isConfigured()
    {
        if (!$this->details || !$this->is_active) {
            return false;
        }

        switch ($this->email_provider) {
            case 'smtp':
                return isset($this->details['sender_name']) &&
                       isset($this->details['sender_email']) &&
                       !empty($this->details['sender_name']) &&
                       !empty($this->details['sender_email']);

            default:
                return !empty($this->details);
        }
    }

    /**
     * Get validation rules for email provider details.
     */
    public static function getValidationRules($provider)
    {
        switch ($provider) {
            case 'smtp':
                return [
                    'details.sender_name' => 'required|string|max:255',
                    'details.sender_email' => 'required|email',
                ];

            default:
                return [
                    'details' => 'required|array',
                ];
        }
    }

    /**
     * Get domain from additional_details
     */
    public function getDomain()
    {
        return $this->additional_details['domain'] ?? null;
    }

    /**
     * Check if domain is fully authenticated
     */
    public function isDomainFullyAuthenticated()
    {
        if (!$this->additional_details) {
            return false;
        }

        $details = $this->additional_details;
        return ($details['domain_verified'] ?? false) &&
               ($details['domain_authenticated'] ?? false) &&
               ($details['dkim_verified'] ?? false) &&
               ($details['spf_verified'] ?? false) &&
               ($details['dmarc_verified'] ?? false);
    }

    /**
     * Get domain verification progress percentage
     */
    public function getDomainVerificationProgress()
    {
        if (!$this->getDomain()) {
            return 0;
        }

        $details = $this->additional_details ?? [];
        $checks = [
            $details['domain_verified'] ?? false,
            $details['domain_authenticated'] ?? false,
            $details['dkim_verified'] ?? false,
            $details['spf_verified'] ?? false,
            $details['dmarc_verified'] ?? false
        ];

        $completed = count(array_filter($checks));
        return round(($completed / count($checks)) * 100);
    }

    /**
     * Get verification status text
     */
    public function getDomainVerificationStatusText()
    {
        if ($this->isDomainFullyAuthenticated()) {
            return 'Fully Authenticated';
        }

        $status = $this->additional_details['verification_status'] ?? 'pending';

        switch ($status) {
            case 'failed':
                return 'Verification Failed';
            case 'in_progress':
                return 'Verification In Progress';
            case 'verified':
                return 'Partially Verified';
            case 'authenticated':
                return 'Authenticated';
            default:
                return 'Pending Verification';
        }
    }

    /**
     * Get formatted DNS records for display
     */
    public function getFormattedDnsRecords()
    {
        $dnsRecords = $this->additional_details['dns_records'] ?? [];

        if (empty($dnsRecords)) {
            return [];
        }

        $formatted = [];
        $details = $this->additional_details ?? [];

        if (isset($dnsRecords['dkim_record'])) {
            $formatted[] = [
                'type' => 'DKIM Authentication',
                'record_type' => $dnsRecords['dkim_record']['type'],
                'host' => $dnsRecords['dkim_record']['host_name'],
                'value' => $dnsRecords['dkim_record']['value'],
                'status' => $details['dkim_verified'] ?? false,
                'description' => 'Authenticates emails sent from your domain'
            ];
        }

        if (isset($dnsRecords['spf_record'])) {
            $formatted[] = [
                'type' => 'SPF Record',
                'record_type' => $dnsRecords['spf_record']['type'],
                'host' => $dnsRecords['spf_record']['host_name'],
                'value' => $dnsRecords['spf_record']['value'],
                'status' => $details['spf_verified'] ?? false,
                'description' => 'Verifies domain ownership'
            ];
        }

        if (isset($dnsRecords['dmarc_record'])) {
            $formatted[] = [
                'type' => 'DMARC Policy',
                'record_type' => $dnsRecords['dmarc_record']['type'],
                'host' => $dnsRecords['dmarc_record']['host_name'],
                'value' => $dnsRecords['dmarc_record']['value'],
                'status' => $details['dmarc_verified'] ?? false,
                'description' => 'Provides email authentication policy'
            ];
        }

        return $formatted;
    }

    /**
     * Update verification status from email service API response
     */
    public function updateVerificationStatus($serviceResponse)
    {
        $additionalDetails = $this->additional_details ?? [];

        // Update DNS record statuses
        if (isset($serviceResponse['dns_records'])) {
            $additionalDetails['dkim_verified'] = $serviceResponse['dns_records']['dkim_record']['status'] ?? false;
            $additionalDetails['spf_verified'] = $serviceResponse['dns_records']['spf_record']['status'] ?? false;
            $additionalDetails['dmarc_verified'] = $serviceResponse['dns_records']['dmarc_record']['status'] ?? false;
        }

        // Update domain verification status
        $additionalDetails['domain_verified'] = $serviceResponse['verified'] ?? false;
        $additionalDetails['domain_authenticated'] = $serviceResponse['authenticated'] ?? false;
        $additionalDetails['last_verification_check'] = now()->toISOString();

        // Update overall verification status
        if ($this->isDomainFullyAuthenticated()) {
            $additionalDetails['verification_status'] = 'authenticated';
            $additionalDetails['fully_authenticated_at'] = now()->toISOString();
            $this->is_verified = true;
        } elseif ($additionalDetails['domain_verified']) {
            $additionalDetails['verification_status'] = 'verified';
        } else {
            $additionalDetails['verification_status'] = 'in_progress';
        }

        $this->additional_details = $additionalDetails;
        $this->save();
    }

    /**
     * Check if domain needs verification check
     */
    public function needsVerificationCheck()
    {
        // If already fully authenticated, no need to check frequently
        if ($this->isDomainFullyAuthenticated()) {
            return false;
        }

        $lastCheck = $this->additional_details['last_verification_check'] ?? null;

        // If never checked or last check was more than 5 minutes ago
        if (!$lastCheck) {
            return true;
        }

        try {
            $lastCheckTime = \Carbon\Carbon::parse($lastCheck);
            return $lastCheckTime->diffInMinutes(now()) >= 5;
        } catch (\Exception $e) {
            // If parsing fails, assume we need to check
            return true;
        }
    }

    /**
     * Get system SMTP configuration
     */
    public static function getSmtpConfig()
    {
        return [
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'username' => config('mail.mailers.smtp.username'),
            'password' => config('mail.mailers.smtp.password'),
            'encryption' => config('mail.mailers.smtp.encryption'),
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
        ];
    }

    /**
     * Remove domain configuration
     */
    public function removeDomain()
    {
        $additionalDetails = $this->additional_details ?? [];

        // Clear domain-related data but keep the structure
        $additionalDetails['domain'] = null;
        $additionalDetails['domain_id'] = null;
        $additionalDetails['dns_records'] = null;
        $additionalDetails['verification_status'] = 'pending';
        $additionalDetails['domain_verified'] = false;
        $additionalDetails['domain_authenticated'] = false;
        $additionalDetails['dkim_verified'] = false;
        $additionalDetails['spf_verified'] = false;
        $additionalDetails['dmarc_verified'] = false;
        $additionalDetails['last_verification_check'] = null;
        $additionalDetails['domain_setup_at'] = null;
        $additionalDetails['fully_authenticated_at'] = null;
        $additionalDetails['verification_error_message'] = null;

        $this->additional_details = $additionalDetails;
        $this->is_verified = false;
        $this->save();
    }
}
