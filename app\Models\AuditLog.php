<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_type',
        'session_id',
        'action',
        'resource_type',
        'resource_id',
        'description',
        'patient_id',
        'accessed_fields',
        'ip_address',
        'user_agent',
        'request_method',
        'request_url',
        'old_values',
        'new_values',
        'severity',
        'is_sensitive',
        'compliance_reason',
        'occurred_at',
    ];

    protected $casts = [
        'accessed_fields' => 'array',
        'old_values' => 'array',
        'new_values' => 'array',
        'is_sensitive' => 'boolean',
        'occurred_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Static method to create audit log entries
     */
    public static function logActivity(array $data)
    {
        $user = Auth::user();
        $request = request();

        // Check if we're running in console (artisan command)
        $isConsole = app()->runningInConsole();

        $logData = array_merge([
            'user_id' => $user?->id,
            'user_type' => $user ? self::getUserType($user) : null,
            'session_id' => $isConsole ? null : $request->session()?->getId(),
            'ip_address' => $isConsole ? '127.0.0.1' : $request->ip(),
            'user_agent' => $isConsole ? 'Console/Artisan' : $request->userAgent(),
            'request_method' => $isConsole ? 'CONSOLE' : $request->method(),
            'request_url' => $isConsole ? 'console://artisan' : $request->fullUrl(),
            'occurred_at' => now(),
        ], $data);

        return static::create($logData);
    }

    /**
     * Log patient data access (HIPAA requirement)
     */
    public static function logPatientAccess($patientId, $action, $accessedFields = null, $description = null)
    {
        return static::logActivity([
            'action' => $action,
            'resource_type' => 'patient',
            'resource_id' => $patientId,
            'patient_id' => $patientId,
            'accessed_fields' => $accessedFields,
            'description' => $description ?: "Patient data {$action}",
            'is_sensitive' => true,
            'severity' => 'high',
        ]);
    }

    /**
     * Log patient-provider relationship changes
     */
    public static function logPatientProviderRelationship($patientId, $providerId, $action, $relationshipType, $reason = null)
    {
        return static::logActivity([
            'action' => $action,
            'resource_type' => 'patient_provider_relationship',
            'resource_id' => "{$patientId}-{$providerId}",
            'patient_id' => $patientId,
            'description' => "Patient-provider relationship {$action}: {$relationshipType}",
            'compliance_reason' => $reason,
            'is_sensitive' => true,
            'severity' => 'medium',
            'new_values' => [
                'patient_id' => $patientId,
                'provider_id' => $providerId,
                'relationship_type' => $relationshipType,
                'action' => $action,
            ],
        ]);
    }

    /**
     * Get user type for audit logging
     */
    private static function getUserType($user)
    {
        if ($user->hasRole('admin')) return 'admin';
        if ($user->hasRole('clinic_admin')) return 'clinic_admin';
        if ($user->hasRole('provider')) return 'provider';
        if ($user->hasRole('patient')) return 'patient';
        return 'unknown';
    }

    /**
     * Scopes for compliance queries
     */
    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeSensitive($query)
    {
        return $query->where('is_sensitive', true);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('occurred_at', [$startDate, $endDate]);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }
}
