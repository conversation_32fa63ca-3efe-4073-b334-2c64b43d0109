<template>
  <div class="max-w-4xl">
    <!-- Result Header -->
    <div class="mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h3 class="text-xl font-semibold text-gray-900">Lab Result Details</h3>
          <div class="flex items-center space-x-4 text-sm text-gray-500 mt-2">
            <span>Order: {{ result.order_number }}</span>
            <span>Lab Ref: {{ result.lab_reference_id }}</span>
            <span>Received: {{ formatDate(result.received_at) }}</span>
          </div>
        </div>
        <span
          class="px-3 py-1 text-sm font-medium rounded-full"
          :class="getStatusClass(result.status)"
        >
          {{ result.status_label }}
        </span>
      </div>
    </div>

    <!-- Patient Information -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
      <h4 class="font-semibold text-gray-900 mb-3">Patient Information</h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div>
          <span class="font-medium text-gray-700">Name:</span>
          <span class="ml-2">{{ patientInfo.name || 'N/A' }}</span>
        </div>
        <div>
          <span class="font-medium text-gray-700">DOB:</span>
          <span class="ml-2">{{ patientInfo.dob || 'N/A' }}</span>
        </div>
        <div>
          <span class="font-medium text-gray-700">Gender:</span>
          <span class="ml-2">{{ patientInfo.gender || 'N/A' }}</span>
        </div>
      </div>
    </div>

    <!-- Test Results -->
    <div class="space-y-6">
      <div
        v-for="test in testResults"
        :key="test.test_code"
        class="bg-white border rounded-lg p-4"
      >
        <div class="flex justify-between items-center mb-4">
          <h4 class="text-lg font-semibold text-gray-900">{{ test.test_name }}</h4>
          <span class="text-sm text-gray-500">{{ test.test_code }}</span>
        </div>

        <!-- Biomarkers -->
        <div v-if="test.biomarkers && test.biomarkers.length > 0" class="space-y-3">
          <div
            v-for="biomarker in test.biomarkers"
            :key="biomarker.name"
            class="grid grid-cols-1 md:grid-cols-5 gap-4 py-3 border-b border-gray-100 last:border-b-0"
          >
            <div class="md:col-span-2">
              <span class="font-medium text-gray-900">{{ biomarker.name }}</span>
            </div>
            <div class="flex items-center">
              <span
                class="font-semibold"
                :class="getValueClass(biomarker.abnormal_flag)"
              >
                {{ biomarker.value }}
              </span>
              <span v-if="biomarker.units" class="ml-1 text-sm text-gray-500">
                {{ biomarker.units }}
              </span>
            </div>
            <div class="text-sm text-gray-600">
              {{ biomarker.reference_range || 'N/A' }}
            </div>
            <div class="flex items-center">
              <span
                v-if="biomarker.abnormal_flag && biomarker.abnormal_flag !== 'N'"
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getFlagClass(biomarker.abnormal_flag)"
              >
                {{ getFlagLabel(biomarker.abnormal_flag) }}
              </span>
              <span v-else class="text-sm text-green-600">Normal</span>
            </div>
          </div>
        </div>

        <!-- No biomarkers message -->
        <div v-else class="text-center py-4 text-gray-500">
          <i class="fas fa-info-circle mr-2"></i>
          No detailed biomarker data available for this test
        </div>
      </div>
    </div>

    <!-- No tests message -->
    <div v-if="testResults.length === 0" class="text-center py-8 text-gray-500">
      <i class="fas fa-flask text-4xl mb-4"></i>
      <p>No test results available</p>
    </div>

    <!-- Review Information -->
    <div v-if="result.reviewed_at" class="bg-blue-50 rounded-lg p-4 mt-6">
      <h4 class="font-semibold text-blue-900 mb-2">Review Information</h4>
      <div class="text-sm text-blue-800">
        <p><strong>Reviewed by:</strong> {{ result.reviewer?.first_name }} {{ result.reviewer?.last_name }}</p>
        <p><strong>Reviewed at:</strong> {{ formatDate(result.reviewed_at) }}</p>
        <p v-if="result.physician_notes" class="mt-2">
          <strong>Notes:</strong> {{ result.physician_notes }}
        </p>
      </div>
    </div>

    <!-- Raw Data Toggle -->
    <div class="mt-6 border-t pt-4">
      <button
        @click="showRawData = !showRawData"
        class="text-sm text-gray-600 hover:text-gray-800 transition-colors"
      >
        <i class="fas fa-code mr-2"></i>
        {{ showRawData ? 'Hide' : 'Show' }} Raw Data
      </button>
      
      <div v-if="showRawData" class="mt-4">
        <pre class="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-64">{{ JSON.stringify(result.result_data, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

// Reactive data
const showRawData = ref(false)

// Computed
const patientInfo = computed(() => {
  return props.result.result_data?.patient_info || {}
})

const testResults = computed(() => {
  return props.result.result_data?.tests || []
})

// Methods
const getStatusClass = (status) => {
  const classes = {
    received: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    reviewed: 'bg-purple-100 text-purple-800',
    archived: 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getValueClass = (abnormalFlag) => {
  if (abnormalFlag && abnormalFlag !== 'N') {
    return 'text-red-600'
  }
  return 'text-gray-900'
}

const getFlagClass = (flag) => {
  const classes = {
    'H': 'bg-red-100 text-red-800',
    'L': 'bg-yellow-100 text-yellow-800',
    'HH': 'bg-red-200 text-red-900',
    'LL': 'bg-yellow-200 text-yellow-900',
    'A': 'bg-orange-100 text-orange-800'
  }
  return classes[flag] || 'bg-gray-100 text-gray-800'
}

const getFlagLabel = (flag) => {
  const labels = {
    'H': 'High',
    'L': 'Low',
    'HH': 'Very High',
    'LL': 'Very Low',
    'A': 'Abnormal'
  }
  return labels[flag] || flag
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>


