<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\Appointment;
use App\Repositories\ConsultationRepository;
use App\Repositories\ConsultationNoteRepository;
use App\Repositories\DiagnosisRepository;
use App\Repositories\ConsultationDocumentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ConsultationService
{
    protected ConsultationRepository $consultationRepository;
    protected ConsultationNoteRepository $noteRepository;
    protected DiagnosisRepository $diagnosisRepository;
    protected ConsultationDocumentRepository $documentRepository;

    public function __construct(
        ConsultationRepository $consultationRepository,
        ConsultationNoteRepository $noteRepository,
        DiagnosisRepository $diagnosisRepository,
        ConsultationDocumentRepository $documentRepository
    ) {
        $this->consultationRepository = $consultationRepository;
        $this->noteRepository = $noteRepository;
        $this->diagnosisRepository = $diagnosisRepository;
        $this->documentRepository = $documentRepository;
    }

    /**
     * Create a new consultation with business logic
     */
    public function createConsultation(array $data): Consultation
    {
        // Business logic: Set defaults and validate
        $data['status'] = $data['status'] ?? 'draft';
        $data['consultation_date'] = $data['consultation_date'] ?? now();
        $data['consultation_mode'] = $data['consultation_mode'] ?? 'in_person';
        $data['is_telemedicine'] = $data['is_telemedicine'] ?? false;
        
        // Get clinic_id from provider if not provided
        if (empty($data['clinic_id']) && !empty($data['provider_id'])) {
            $provider = \App\Models\Provider::find($data['provider_id']);
            if ($provider) {
                $data['clinic_id'] = $provider->clinic_id;
            }
        }

        return DB::transaction(function () use ($data) {
            $consultation = $this->consultationRepository->create($data);
            
            // Business logic: Create initial note if content provided
            if (!empty($data['initial_note'])) {
                $this->addNote($consultation->id, [
                    'content' => $data['initial_note'],
                    'note_type' => 'general',
                    'note_date' => now(),
                    'created_by' => Auth::id()
                ]);
            }
            
            return $consultation;
        });
    }

    /**
     * Create consultation from appointment
     */
    public function createFromAppointment(int $appointmentId, array $additionalData = []): Consultation
    {
        $appointment = Appointment::with(['patient', 'provider', 'service'])->findOrFail($appointmentId);
        
        // Check if consultation already exists
        $existingConsultation = $this->consultationRepository->findByAppointment($appointmentId);
        if ($existingConsultation) {
            return $existingConsultation;
        }

        $consultationData = array_merge([
            'appointment_id' => $appointmentId,
            'patient_id' => $appointment->patient_id,
            'provider_id' => $appointment->provider_id,
            'clinic_id' => $appointment->provider->clinic_id ?? null,
            'consultation_type' => $appointment->service->name ?? 'General Consultation',
            'consultation_date' => $appointment->scheduled_at,
            'is_telemedicine' => $appointment->is_telemedicine ?? false,
            'consultation_mode' => $appointment->is_telemedicine ? 'video' : 'in_person',
            'status' => 'draft'
        ], $additionalData);

        return $this->createConsultation($consultationData);
    }

    /**
     * Update consultation with business logic
     */
    public function updateConsultation(int $consultationId, array $data): ?Consultation
    {
        // Business logic: Handle status changes
        if (isset($data['status'])) {
            $consultation = $this->consultationRepository->find($consultationId);
            if ($consultation && $consultation->status !== $data['status']) {
                $this->handleStatusChange($consultation, $data['status']);
            }
        }

        return $this->consultationRepository->update($consultationId, $data);
    }

    /**
     * Start consultation
     */
    public function startConsultation(int $consultationId): bool
    {
        return $this->consultationRepository->updateStatus($consultationId, 'in_progress');
    }

    /**
     * Complete consultation
     */
    public function completeConsultation(int $consultationId, array $completionData = []): bool
    {
        $data = array_merge($completionData, [
            'status' => 'completed',
            'completed_at' => now()
        ]);

        // Calculate duration if started_at exists
        $consultation = $this->consultationRepository->find($consultationId);
        if ($consultation && $consultation->started_at) {
            $data['duration_minutes'] = $consultation->started_at->diffInMinutes(now());
        }

        $updated = $this->consultationRepository->update($consultationId, $data);
        
        // Business logic: Update related appointment status
        if ($updated && $consultation->appointment_id) {
            \App\Models\Appointment::where('id', $consultation->appointment_id)
                ->update(['status' => 'completed']);
        }

        return $updated !== null;
    }

    /**
     * Get consultation with all related data
     */
    public function getConsultationDetails(int $consultationId): ?Consultation
    {
        $consultation = $this->consultationRepository->find($consultationId);
        
        if (!$consultation) {
            return null;
        }

        // Load all relationships
        $consultation->load([
            'patient.user',
            'provider.user',
            'appointment',
            'notes.creator',
            'diagnoses',
            'prescriptions',
            'documents.uploader',
            'treatmentPlans',
            'tabs'
        ]);

        return $consultation;
    }

    /**
     * Delete consultation with business logic
     */
    public function deleteConsultation(int $consultationId, ?int $providerId = null): bool
    {
        $consultation = $this->consultationRepository->find($consultationId);

        if (!$consultation) {
            return false;
        }

        // Business logic: Check if user has access to this consultation
        // If providerId is null, it means admin user can delete any consultation
        if ($providerId !== null && $consultation->provider_id !== $providerId) {
            throw new \Exception('Access denied. You can only delete your own consultations.', 403);
        }

        // Business logic: Only allow deletion of non-completed consultations
        if ($consultation->status === 'completed') {
            throw new \Exception('Completed consultations cannot be deleted.', 400);
        }

        return $this->consultationRepository->delete($consultationId);
    }

    /**
     * Add note to consultation
     */
    public function addNote(int $consultationId, array $noteData): \App\Models\ConsultationNote
    {
        $noteData['consultation_id'] = $consultationId;
        $noteData['created_by'] = $noteData['created_by'] ?? Auth::id();
        $noteData['note_date'] = $noteData['note_date'] ?? now();
        $noteData['is_private'] = $noteData['is_private'] ?? false;

        return $this->noteRepository->create($noteData);
    }

    /**
     * Add diagnosis to consultation
     */
    public function addDiagnosis(int $consultationId, array $diagnosisData): \App\Models\Diagnosis
    {
        $diagnosisData['consultation_id'] = $consultationId;
        $diagnosisData['diagnosed_date'] = $diagnosisData['diagnosed_date'] ?? now();
        $diagnosisData['status'] = $diagnosisData['status'] ?? 'active';

        return $this->diagnosisRepository->create($diagnosisData);
    }

    /**
     * Get patient's consultation history
     */
    public function getPatientHistory(int $patientId, int $limit = 10): Collection
    {
        return $this->consultationRepository->findRecentByPatient($patientId, $limit);
    }

    /**
     * Get provider's today consultations
     */
    public function getProviderTodayConsultations(int $providerId): Collection
    {
        return $this->consultationRepository->findTodayByProvider($providerId);
    }

    /**
     * Get consultations with filters and pagination
     */
    public function getConsultationsWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->consultationRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Get consultation statistics
     */
    public function getStatistics(?int $clinicId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        return $this->consultationRepository->getStatistics($clinicId, $startDate, $endDate);
    }

    /**
     * Analyze transcript with AI (business logic)
     */
    public function analyzeTranscript(string $transcript): array
    {
        // Business logic: Process transcript with AI
        // This would integrate with AI service to extract:
        // - Key symptoms
        // - Potential diagnoses
        // - Recommended actions
        // - Follow-up requirements
        
        return [
            'symptoms' => $this->extractSymptoms($transcript),
            'potential_diagnoses' => $this->suggestDiagnoses($transcript),
            'recommendations' => $this->generateRecommendations($transcript),
            'follow_up_required' => $this->assessFollowUpNeed($transcript)
        ];
    }

    /**
     * Handle status change business logic
     */
    private function handleStatusChange(Consultation $consultation, string $newStatus): void
    {
        switch ($newStatus) {
            case 'in_progress':
                if (!$consultation->started_at) {
                    $this->consultationRepository->update($consultation->id, ['started_at' => now()]);
                }
                break;
                
            case 'completed':
                // Business logic for completion
                break;
                
            case 'cancelled':
                // Business logic for cancellation
                break;
        }
    }

    /**
     * Extract symptoms from transcript (placeholder for AI integration)
     */
    private function extractSymptoms(string $transcript): array
    {
        // Placeholder - would integrate with AI service
        return [];
    }

    /**
     * Suggest diagnoses from transcript (placeholder for AI integration)
     */
    private function suggestDiagnoses(string $transcript): array
    {
        // Placeholder - would integrate with AI service
        return [];
    }

    /**
     * Generate recommendations from transcript (placeholder for AI integration)
     */
    private function generateRecommendations(string $transcript): array
    {
        // Placeholder - would integrate with AI service
        return [];
    }

    /**
     * Assess follow-up need from transcript (placeholder for AI integration)
     */
    private function assessFollowUpNeed(string $transcript): bool
    {
        // Placeholder - would integrate with AI service
        return false;
    }
}
