<?php

namespace Database\Seeders;

use App\Models\Icd10Code;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class Icd10CodesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $codes = [
            // Common General Conditions
            [
                'code' => 'Z00.00',
                'category' => 'Z00',
                'short_description' => 'Encounter for general adult medical examination without abnormal findings',
                'long_description' => 'Encounter for general adult medical examination without abnormal findings',
                'chapter' => 'Factors influencing health status and contact with health services',
                'section' => 'Persons encountering health services for examinations',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'I10',
                'category' => 'I10',
                'short_description' => 'Essential hypertension',
                'long_description' => 'Essential (primary) hypertension',
                'chapter' => 'Diseases of the circulatory system',
                'section' => 'Hypertensive diseases',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'E11.9',
                'category' => 'E11',
                'short_description' => 'Type 2 diabetes mellitus without complications',
                'long_description' => 'Type 2 diabetes mellitus without complications',
                'chapter' => 'Endocrine, nutritional and metabolic diseases',
                'section' => 'Diabetes mellitus',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'R51',
                'category' => 'R51',
                'short_description' => 'Headache',
                'long_description' => 'Headache',
                'chapter' => 'Symptoms, signs and abnormal clinical and laboratory findings',
                'section' => 'General symptoms and signs',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'R50.9',
                'category' => 'R50',
                'short_description' => 'Fever, unspecified',
                'long_description' => 'Fever, unspecified',
                'chapter' => 'Symptoms, signs and abnormal clinical and laboratory findings',
                'section' => 'General symptoms and signs',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'J06.9',
                'category' => 'J06',
                'short_description' => 'Acute upper respiratory infection, unspecified',
                'long_description' => 'Acute upper respiratory infection, unspecified',
                'chapter' => 'Diseases of the respiratory system',
                'section' => 'Acute upper respiratory infections',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'K59.00',
                'category' => 'K59',
                'short_description' => 'Constipation, unspecified',
                'long_description' => 'Constipation, unspecified',
                'chapter' => 'Diseases of the digestive system',
                'section' => 'Other diseases of intestines',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'M25.50',
                'category' => 'M25',
                'short_description' => 'Pain in unspecified joint',
                'long_description' => 'Pain in unspecified joint',
                'chapter' => 'Diseases of the musculoskeletal system and connective tissue',
                'section' => 'Arthropathies',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'R06.02',
                'category' => 'R06',
                'short_description' => 'Shortness of breath',
                'long_description' => 'Shortness of breath',
                'chapter' => 'Symptoms, signs and abnormal clinical and laboratory findings',
                'section' => 'Symptoms and signs involving the circulatory and respiratory systems',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'M79.3',
                'category' => 'M79',
                'short_description' => 'Panniculitis, unspecified',
                'long_description' => 'Panniculitis, unspecified',
                'chapter' => 'Diseases of the musculoskeletal system and connective tissue',
                'section' => 'Other soft tissue disorders',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            // Mental Health Conditions
            [
                'code' => 'F32.9',
                'category' => 'F32',
                'short_description' => 'Major depressive disorder, single episode, unspecified',
                'long_description' => 'Major depressive disorder, single episode, unspecified',
                'chapter' => 'Mental, Behavioral and Neurodevelopmental disorders',
                'section' => 'Mood [affective] disorders',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            [
                'code' => 'F41.9',
                'category' => 'F41',
                'short_description' => 'Anxiety disorder, unspecified',
                'long_description' => 'Anxiety disorder, unspecified',
                'chapter' => 'Mental, Behavioral and Neurodevelopmental disorders',
                'section' => 'Anxiety, dissociative, stress-related, somatoform and other nonpsychotic mental disorders',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            // Skin Conditions
            [
                'code' => 'L30.9',
                'category' => 'L30',
                'short_description' => 'Dermatitis, unspecified',
                'long_description' => 'Dermatitis, unspecified',
                'chapter' => 'Diseases of the skin and subcutaneous tissue',
                'section' => 'Dermatitis and eczema',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            // Cardiovascular
            [
                'code' => 'I25.10',
                'category' => 'I25',
                'short_description' => 'Atherosclerotic heart disease of native coronary artery without angina pectoris',
                'long_description' => 'Atherosclerotic heart disease of native coronary artery without angina pectoris',
                'chapter' => 'Diseases of the circulatory system',
                'section' => 'Ischemic heart diseases',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
            // Gastrointestinal
            [
                'code' => 'K21.9',
                'category' => 'K21',
                'short_description' => 'Gastro-esophageal reflux disease without esophagitis',
                'long_description' => 'Gastro-esophageal reflux disease without esophagitis',
                'chapter' => 'Diseases of the digestive system',
                'section' => 'Diseases of esophagus, stomach and duodenum',
                'is_billable' => true,
                'is_valid_for_submission' => true,
            ],
        ];

        foreach ($codes as $codeData) {
            Icd10Code::updateOrCreate(
                ['code' => $codeData['code']],
                $codeData
            );
        }
    }
}
