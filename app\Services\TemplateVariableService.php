<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use App\Models\User;
use Carbon\Carbon;

class TemplateVariableService
{
    /**
     * Process template content with variable replacement.
     */
    public function processTemplate(string $content, array $data = []): string
    {
        $variables = $this->buildVariableMap($data);
        
        // Replace variables in the format [Variable Name]
        return preg_replace_callback('/\[([^\]]+)\]/', function ($matches) use ($variables) {
            $variableName = $matches[1];
            return $variables[$variableName] ?? $matches[0]; // Return original if not found
        }, $content);
    }

    /**
     * Build comprehensive variable map from provided data.
     */
    private function buildVariableMap(array $data): array
    {
        $variables = [];

        // Add consultation data
        if (isset($data['consultation']) && $data['consultation'] instanceof Consultation) {
            $variables = array_merge($variables, $this->getConsultationVariables($data['consultation']));
        }

        // Add patient data
        if (isset($data['patient']) && $data['patient'] instanceof Patient) {
            $variables = array_merge($variables, $this->getPatientVariables($data['patient']));
        }

        // Add provider data
        if (isset($data['provider']) && $data['provider'] instanceof Provider) {
            $variables = array_merge($variables, $this->getProviderVariables($data['provider']));
        }

        // Add clinic data
        if (isset($data['clinic']) && $data['clinic'] instanceof Clinic) {
            $variables = array_merge($variables, $this->getClinicVariables($data['clinic']));
        }

        // Add consultation form data
        if (isset($data['consultation_data']) && is_array($data['consultation_data'])) {
            $variables = array_merge($variables, $this->getConsultationFormVariables($data['consultation_data']));
        }

        // Add current date/time variables
        $variables = array_merge($variables, $this->getDateTimeVariables());

        // Add custom variables from data
        if (isset($data['custom_variables']) && is_array($data['custom_variables'])) {
            $variables = array_merge($variables, $data['custom_variables']);
        }

        return $variables;
    }

    /**
     * Get consultation-related variables.
     */
    private function getConsultationVariables(Consultation $consultation): array
    {
        return [
            'Consultation Date' => $consultation->consultation_date ? 
                Carbon::parse($consultation->consultation_date)->format('d/m/Y') : '',
            'Consultation Time' => $consultation->consultation_date ? 
                Carbon::parse($consultation->consultation_date)->format('H:i') : '',
            'Consultation Mode' => ucfirst($consultation->consultation_mode ?? ''),
            'Consultation Status' => ucfirst($consultation->status ?? ''),
            'Chief Complaint' => $consultation->chief_complaint ?? '',
            'History of Present Illness' => $consultation->history_present_illness ?? '',
            'Assessment' => $consultation->assessment ?? '',
            'Plan' => $consultation->plan ?? '',
            'Follow Up Instructions' => $consultation->follow_up_instructions ?? '',
        ];
    }

    /**
     * Get patient-related variables.
     */
    private function getPatientVariables(Patient $patient): array
    {
        $user = $patient->user;
        $birthDate = $patient->date_of_birth ? Carbon::parse($patient->date_of_birth) : null;
        
        return [
            'Full Name' => $user->name ?? '',
            'First Name' => explode(' ', $user->name ?? '')[0] ?? '',
            'Last Name' => substr(strrchr($user->name ?? '', ' '), 1) ?: '',
            'Patient ID' => $patient->patient_id ?? '',
            'NHS/ID Number' => $patient->patient_id ?? '',
            'Date of Birth' => $birthDate ? $birthDate->format('d/m/Y') : '',
            'DD/MM/YYYY' => $birthDate ? $birthDate->format('d/m/Y') : '',
            'Age' => $birthDate ? $birthDate->age . ' years' : '',
            'Gender' => ucfirst($patient->gender ?? ''),
            'Phone Number' => $patient->phone ?? $user->phone ?? '',
            'Email' => $user->email ?? '',
            'Patient Address' => $this->formatAddress($patient),
            'Emergency Contact' => $patient->emergency_contact_name ?? '',
            'Emergency Phone' => $patient->emergency_contact_phone ?? '',
            'Blood Group' => $patient->blood_group ?? '',
            'Allergies' => $patient->allergies ?? 'No known allergies',
            'Medical History' => $patient->medical_history ?? '',
        ];
    }

    /**
     * Get provider-related variables.
     */
    private function getProviderVariables(Provider $provider): array
    {
        $user = $provider->user;
        $doctorSettings = $user->doctorSettings;
        
        return [
            'Doctor\'s Full Name' => $user->name ?? '',
            'Doctor Name' => $user->name ?? '',
            'Referring Doctor\'s Name' => $user->name ?? '',
            'Primary Surgeon\'s Name' => $user->name ?? '',
            'Provider Name' => $user->name ?? '',
            'Doctor Title' => $doctorSettings->title ?? 'Dr.',
            'Doctor Specialization' => $provider->specialization ?? '',
            'Doctor Qualification' => $doctorSettings->qualification ?? '',
            'Doctor Registration Number' => $doctorSettings->registration_number ?? '',
            'Doctor Phone' => $provider->phone ?? $user->phone ?? '',
            'Doctor Email' => $user->email ?? '',
        ];
    }

    /**
     * Get clinic-related variables.
     */
    private function getClinicVariables(Clinic $clinic): array
    {
        return [
            'Clinic Name' => $clinic->name ?? '',
            'Hospital Name' => $clinic->name ?? '',
            'Clinic Address' => $this->formatClinicAddress($clinic),
            'Hospital Address' => $this->formatClinicAddress($clinic),
            'Practice Address' => $this->formatClinicAddress($clinic),
            'Clinic Phone' => $clinic->phone ?? '',
            'Clinic Email' => $clinic->email ?? '',
            'Phone, Email' => trim(($clinic->phone ?? '') . ', ' . ($clinic->email ?? ''), ', '),
            'Contact Information' => trim(($clinic->phone ?? '') . ', ' . ($clinic->email ?? ''), ', '),
        ];
    }

    /**
     * Get consultation form data variables.
     */
    private function getConsultationFormVariables(array $consultationData): array
    {
        $variables = [];

        // Process vital signs
        if (isset($consultationData['vital_signs']) && is_array($consultationData['vital_signs'])) {
            foreach ($consultationData['vital_signs'] as $key => $value) {
                $variables[ucfirst(str_replace('_', ' ', $key))] = $value;
            }
        }

        // Process main tabs data
        if (isset($consultationData['main_tabs']) && is_array($consultationData['main_tabs'])) {
            foreach ($consultationData['main_tabs'] as $tabName => $entries) {
                if (is_array($entries)) {
                    $content = [];
                    foreach ($entries as $entry) {
                        if (isset($entry['content'])) {
                            $content[] = $entry['content'];
                        }
                    }
                    $variables[ucfirst(str_replace('_', ' ', $tabName))] = implode("\n", $content);
                }
            }
        }

        // Process additional tabs data
        if (isset($consultationData['additional_tabs']) && is_array($consultationData['additional_tabs'])) {
            foreach ($consultationData['additional_tabs'] as $tabName => $entries) {
                if (is_array($entries)) {
                    $content = [];
                    foreach ($entries as $entry) {
                        if (isset($entry['content'])) {
                            $content[] = $entry['content'];
                        }
                    }
                    $variables[ucfirst(str_replace('_', ' ', $tabName))] = implode("\n", $content);
                }
            }
        }

        return $variables;
    }

    /**
     * Get date/time variables.
     */
    private function getDateTimeVariables(): array
    {
        $now = Carbon::now();
        
        return [
            'Current Date' => $now->format('d/m/Y'),
            'Current Time' => $now->format('H:i'),
            'Date of Letter' => $now->format('d/m/Y'),
            'Date of Issue' => $now->format('d/m/Y'),
            'Date of Referral' => $now->format('d/m/Y'),
            'Date of Procedure' => $now->format('d/m/Y'),
            'Date of Visit' => $now->format('d/m/Y'),
            'Date of Consultation' => $now->format('d/m/Y'),
        ];
    }

    /**
     * Format patient address.
     */
    private function formatAddress(Patient $patient): string
    {
        $addressParts = array_filter([
            $patient->address_line_1,
            $patient->address_line_2,
            $patient->city,
            $patient->state,
            $patient->postal_code,
            $patient->country,
        ]);

        return implode(', ', $addressParts);
    }

    /**
     * Format clinic address.
     */
    private function formatClinicAddress(Clinic $clinic): string
    {
        $addressParts = array_filter([
            $clinic->address,
            $clinic->city,
            $clinic->state,
            $clinic->postal_code,
            $clinic->country,
        ]);

        return implode(', ', $addressParts);
    }

    /**
     * Get available variables for a template.
     */
    public function getAvailableVariables(): array
    {
        return [
            'Patient Information' => [
                'Full Name', 'First Name', 'Last Name', 'Patient ID', 'NHS/ID Number',
                'Date of Birth', 'DD/MM/YYYY', 'Age', 'Gender', 'Phone Number', 'Email',
                'Patient Address', 'Emergency Contact', 'Emergency Phone', 'Blood Group',
                'Allergies', 'Medical History'
            ],
            'Doctor Information' => [
                'Doctor\'s Full Name', 'Doctor Name', 'Referring Doctor\'s Name',
                'Primary Surgeon\'s Name', 'Provider Name', 'Doctor Title',
                'Doctor Specialization', 'Doctor Qualification', 'Doctor Registration Number',
                'Doctor Phone', 'Doctor Email'
            ],
            'Clinic Information' => [
                'Clinic Name', 'Hospital Name', 'Clinic Address', 'Hospital Address',
                'Practice Address', 'Clinic Phone', 'Clinic Email', 'Phone, Email',
                'Contact Information'
            ],
            'Consultation Information' => [
                'Consultation Date', 'Consultation Time', 'Consultation Mode',
                'Consultation Status', 'Chief Complaint', 'History of Present Illness',
                'Assessment', 'Plan', 'Follow Up Instructions'
            ],
            'Date/Time' => [
                'Current Date', 'Current Time', 'Date of Letter', 'Date of Issue',
                'Date of Referral', 'Date of Procedure', 'Date of Visit',
                'Date of Consultation'
            ]
        ];
    }
}
