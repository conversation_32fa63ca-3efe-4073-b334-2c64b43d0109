<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full h-full max-w-6xl max-h-[95vh] mx-4 overflow-hidden flex flex-col">
      <!-- Header with Actions -->
      <div class="flex items-center justify-between p-4 border-b bg-gray-50">
        <div>
          <h3 class="text-lg font-medium">Prescription Preview</h3>
          <p class="text-sm text-gray-500">{{ prescription?.patient?.name || 'Patient' }}</p>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex items-center gap-2">
          <!-- Mobile-friendly buttons -->
          <div class="hidden sm:flex items-center gap-2">
            <button 
              @click="printPrescription"
              class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              title="Print"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a1 1 0 001-1v-4a1 1 0 00-1-1H9a1 1 0 00-1 1v4a1 1 0 001 1zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
              </svg>
              Print
            </button>
            
            <button 
              @click="emailPrescription"
              :disabled="emailLoading"
              class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50"
              title="Email"
            >
              <svg v-if="emailLoading" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              {{ emailLoading ? 'Sending...' : 'Email' }}
            </button>
            
            <button 
              @click="downloadPDF"
              :disabled="pdfLoading"
              class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 disabled:opacity-50"
              title="Download PDF"
            >
              <svg v-if="pdfLoading" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              {{ pdfLoading ? 'Generating...' : 'PDF' }}
            </button>
            
            <button 
              @click="sharePrescription"
              class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100"
              title="Share"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
              </svg>
              Share
            </button>
          </div>
          
          <!-- Mobile dropdown menu -->
          <div class="sm:hidden relative">
            <button 
              @click="showMobileMenu = !showMobileMenu"
              class="p-2 text-gray-500 hover:text-gray-700"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
            
            <div v-if="showMobileMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <button @click="printPrescription; showMobileMenu = false" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                🖨️ Print
              </button>
              <button @click="emailPrescription; showMobileMenu = false" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                📧 Email
              </button>
              <button @click="downloadPDF; showMobileMenu = false" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                📄 Download PDF
              </button>
              <button @click="sharePrescription; showMobileMenu = false" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                🔗 Share
              </button>
            </div>
          </div>
          
          <button 
            @click="$emit('close')"
            class="p-2 text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Progress Bar -->
      <div v-if="pdfProgress > 0 && pdfProgress < 100" class="w-full bg-gray-200 h-1">
        <div 
          class="bg-green-500 h-1 transition-all duration-300" 
          :style="{width: pdfProgress + '%'}"
        ></div>
      </div>

      <!-- Content -->
      <div class="flex-1 overflow-auto p-6 bg-gray-50">
        <div class="max-w-4xl mx-auto">
          <div v-if="loading" class="flex items-center justify-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
          
          <div v-else-if="error" class="text-center py-12">
            <div class="text-red-500 mb-4">
              <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ error.title || 'Error Loading Prescription' }}</h3>
            <p class="text-gray-600 mb-4">{{ error.message || 'Failed to load prescription details' }}</p>
            <div class="space-x-3">
              <button @click="retryLoad" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90">
                Try Again
              </button>
              <button @click="$emit('close')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                Close
              </button>
            </div>
          </div>
          
          <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 print:shadow-none print:border-none">
            <div v-html="prescriptionHtml" class="prescription-content"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Modal -->
    <div v-if="showEmailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-medium mb-4">Email Prescription</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              <input 
                v-model="emailAddress"
                type="email" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                :placeholder="prescription?.patient?.email || 'Enter email address'"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Message (Optional)</label>
              <textarea 
                v-model="emailMessage"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Add a personal message..."
              ></textarea>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button @click="showEmailModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button @click="sendEmail" :disabled="!emailAddress || emailSending" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50">
              {{ emailSending ? 'Sending...' : 'Send Email' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Share Modal -->
    <div v-if="showShareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-medium mb-4">Share Prescription</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Share Link</label>
              <div class="flex">
                <input 
                  :value="shareLink"
                  readonly
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50"
                />
                <button 
                  @click="copyShareLink"
                  class="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary/90"
                >
                  {{ linkCopied ? 'Copied!' : 'Copy' }}
                </button>
              </div>
            </div>
            <div class="text-sm text-gray-500">
              This link will expire in 24 hours for security.
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button @click="showShareModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Error Modal -->
    <ErrorHandler
      :show="showErrorModal"
      :error="error"
      :can-retry="true"
      :can-report="true"
      @retry="retryLastAction"
      @report="reportError"
      @dismiss="showErrorModal = false"
    />

    <!-- Progress Modal -->
    <ProgressIndicator
      :show="showProgressModal"
      :title="progressData.title"
      :message="progressData.message"
      :progress="progressData.progress"
      :steps="progressData.steps"
      :current-operation="progressData.currentOperation"
      :can-cancel="progressData.canCancel"
      @cancel="cancelOperation"
    />

    <!-- Success Modal -->
    <SuccessModal
      :show="showSuccessModal"
      :title="successData.title"
      :message="successData.message"
      :details="successData.details"
      :downloads="successData.downloads"
      :auto-close="true"
      :auto-close-delay="8"
      @close="showSuccessModal = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useToast } from 'vue-toastification'
import axios from 'axios'
import ErrorHandler from '../common/ErrorHandler.vue'
import ProgressIndicator from '../common/ProgressIndicator.vue'
import SuccessModal from '../common/SuccessModal.vue'
import { useErrorHandling } from '@/composables/useErrorHandling'
import { useProgress } from '@/composables/useProgress'

const props = defineProps({
  show: Boolean,
  prescriptionId: [String, Number],
  prescription: Object
})

const emit = defineEmits(['close'])

const toast = useToast()
const { handleError, showSuccess, showInfo, globalError, clearError } = useErrorHandling()
const { createStepProgress, globalProgress, showProgress, hideProgress } = useProgress()

// State
const loading = ref(false)
const error = ref(null)
const prescriptionHtml = ref('')
const pdfLoading = ref(false)
const pdfProgress = ref(0)
const emailLoading = ref(false)
const showMobileMenu = ref(false)

// Enhanced error and progress state
const showErrorModal = ref(false)
const showProgressModal = ref(false)
const showSuccessModal = ref(false)
const progressData = ref({
  title: '',
  message: '',
  progress: -1,
  steps: [],
  currentOperation: '',
  canCancel: false
})
const successData = ref({
  title: '',
  message: '',
  details: [],
  downloads: []
})

// Email modal
const showEmailModal = ref(false)
const emailAddress = ref('')
const emailMessage = ref('')
const emailSending = ref(false)

// Share modal
const showShareModal = ref(false)
const shareLink = ref('')
const linkCopied = ref(false)

// Methods
const loadPrescription = async () => {
  if (!props.prescriptionId) return
  
  try {
    loading.value = true
    error.value = null
    
    const response = await axios.get(`/prescriptions/${props.prescriptionId}/view-letterhead`)
    prescriptionHtml.value = response.data
  } catch (err) {
    console.error('Error loading prescription:', err)
    error.value = {
      title: 'Failed to Load Prescription',
      message: err.response?.data?.message || 'Unable to load prescription details. Please try again.'
    }
  } finally {
    loading.value = false
  }
}

const retryLoad = () => {
  loadPrescription()
}

const printPrescription = () => {
  window.print()
}

const downloadPDF = async () => {
  try {
    pdfLoading.value = true

    // Create step-based progress
    const progress = createStepProgress([
      'Preparing data',
      'Generating PDF',
      'Finalizing document'
    ], {
      title: 'Generating PDF',
      message: 'Please wait while we generate your prescription PDF...',
      canCancel: true,
      onCancel: () => {
        pdfLoading.value = false
        showInfo('PDF generation cancelled')
      }
    })

    // Step 1: Preparing
    progress.nextStep('Loading prescription data...')
    await new Promise(resolve => setTimeout(resolve, 500))

    // Step 2: Generating
    progress.nextStep('Applying letterhead and formatting...')
    await new Promise(resolve => setTimeout(resolve, 800))

    const response = await axios.post(`/prescriptions/${props.prescriptionId}/generate-pdf`)

    // Step 3: Finalizing
    progress.nextStep('Preparing download...')
    await new Promise(resolve => setTimeout(resolve, 300))

    if (response.data.success) {
      progress.complete('PDF generated successfully!')

      // Create and trigger download
      const link = document.createElement('a')
      link.href = response.data.data.download_url
      link.download = response.data.data.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Show success modal
      successData.value = {
        title: 'PDF Generated Successfully!',
        message: 'Your prescription PDF has been generated and downloaded.',
        details: [
          { label: 'File name', value: response.data.data.filename },
          { label: 'File size', value: response.data.data.file_size || 'Unknown' },
          { label: 'Generated at', value: new Date().toLocaleString() }
        ],
        downloads: [
          {
            url: response.data.data.download_url,
            filename: response.data.data.filename,
            label: 'Download Again'
          }
        ]
      }
      showSuccessModal.value = true

      showSuccess('PDF downloaded successfully!')
    }
  } catch (err) {
    handleError(err, 'PDF Generation', {
      showModal: true,
      report: true
    })
  } finally {
    pdfLoading.value = false
  }
}

const emailPrescription = () => {
  emailAddress.value = props.prescription?.patient?.email || ''
  showEmailModal.value = true
}

const sendEmail = async () => {
  try {
    emailSending.value = true
    
    const response = await axios.post(`/prescriptions/${props.prescriptionId}/email`, {
      email: emailAddress.value,
      message: emailMessage.value
    })
    
    if (response.data.success) {
      toast.success('Prescription emailed successfully')
      showEmailModal.value = false
      emailAddress.value = ''
      emailMessage.value = ''
    }
  } catch (err) {
    console.error('Error sending email:', err)
    toast.error('Failed to send email')
  } finally {
    emailSending.value = false
  }
}

const sharePrescription = async () => {
  try {
    const response = await axios.post(`/prescriptions/${props.prescriptionId}/share`)
    
    if (response.data.success) {
      shareLink.value = response.data.data.share_url
      showShareModal.value = true
    }
  } catch (err) {
    console.error('Error creating share link:', err)
    toast.error('Failed to create share link')
  }
}

const copyShareLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    linkCopied.value = true
    toast.success('Link copied to clipboard')
    setTimeout(() => {
      linkCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Error copying link:', err)
    toast.error('Failed to copy link')
  }
}

// Enhanced error handling methods
let lastAction = null

const retryLastAction = () => {
  showErrorModal.value = false
  if (lastAction) {
    lastAction()
  }
}

const reportError = (errorData) => {
  // TODO: Implement error reporting to backend
  console.log('Reporting error:', errorData)
  toast.info('Error report sent to support team')
  showErrorModal.value = false
}

const cancelOperation = () => {
  showProgressModal.value = false
  pdfLoading.value = false
  emailLoading.value = false
  toast.info('Operation cancelled')
}

// Enhanced error wrapper for actions
const withErrorHandling = (action, actionName) => {
  return async (...args) => {
    lastAction = () => action(...args)
    try {
      await action(...args)
    } catch (err) {
      console.error(`Error in ${actionName}:`, err)
      error.value = {
        title: `${actionName} Failed`,
        message: `We encountered an issue while ${actionName.toLowerCase()}.`,
        details: err.response?.data?.message || err.message,
        trace: err.stack,
        timestamp: new Date().toISOString()
      }
      showErrorModal.value = true
    }
  }
}

// Handle escape key
const handleEscape = (event) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}

// Lifecycle
onMounted(() => {
  if (props.show && props.prescriptionId) {
    loadPrescription()
  }
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})

// Watch for prop changes
watch(() => props.show, (newVal) => {
  if (newVal && props.prescriptionId) {
    loadPrescription()
  }
})
</script>

<style scoped>
.prescription-content {
  max-width: none;
  color: #1f2937;
  line-height: 1.625;
}

.prescription-content h1,
.prescription-content h2,
.prescription-content h3,
.prescription-content h4 {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.prescription-content h1 {
  font-size: 1.5rem;
}

.prescription-content h2 {
  font-size: 1.25rem;
}

.prescription-content h3 {
  font-size: 1.125rem;
}

.prescription-content p {
  margin-bottom: 0.75rem;
}

.prescription-content ul,
.prescription-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prescription-content li {
  margin-bottom: 0.25rem;
}

.prescription-content table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #d1d5db;
  margin-bottom: 1rem;
}

.prescription-content th,
.prescription-content td {
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  text-align: left;
}

.prescription-content th {
  background-color: #f9fafb;
  font-weight: 600;
}

@media print {
  .prescription-content {
    color: #000000;
  }

  .prescription-content h1,
  .prescription-content h2,
  .prescription-content h3,
  .prescription-content h4 {
    color: #000000;
  }
}
</style>
