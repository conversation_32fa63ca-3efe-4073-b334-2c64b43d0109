<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium">Generated Letter</h3>
      </div>

      <div class="p-4 overflow-y-auto max-h-[70vh]">
        <!-- Editor -->
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">Letter Content</label>
          <textarea
            v-model="editedSummary"
            class="w-full h-96 p-3 border rounded-lg resize-none focus:outline-none focus:border-blue-500"
            placeholder="Letter content will appear here..."
          ></textarea>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end gap-2 p-4 border-t bg-gray-50">
        <button
          type="button"
          class="px-4 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
          @click="$emit('close')"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="button"
          class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black disabled:opacity-50"
          @click="handleSave"
          :disabled="isLoading"
        >
          <template v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </template>
          <template v-else>
            Save Document
          </template>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Template {
  id: number
  name: string
  category: string
}

interface Props {
  show: boolean
  summary: string
  template: Template | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'close': []
  'save': [content: string]
}>()

// Methods
const convertAsterisksToBold = (text: string): string => {
  if (!text) return ''
  // Convert **text** to bold formatting (simple text replacement)
  return text.replace(/\*\*(.*?)\*\*/g, '$1')
}

// State
const editedSummary = ref('')
const isLoading = ref(false)

// Watch for summary changes
watch(() => props.summary, (newSummary) => {
  editedSummary.value = convertAsterisksToBold(newSummary)
}, { immediate: true })

const handleSave = (): void => {
  isLoading.value = true
  emit('save', editedSummary.value)
  // Note: isLoading will be reset when parent closes modal
}
</script>

<style scoped>
/* Custom scrollbar for the content area */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
