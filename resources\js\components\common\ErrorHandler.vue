<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6">
        <!-- Error Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
          <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>

        <!-- Error Content -->
        <div class="text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            {{ error.title || 'Something went wrong' }}
          </h3>
          <p class="text-gray-600 mb-6">
            {{ error.message || 'An unexpected error occurred. Please try again.' }}
          </p>

          <!-- Error <PERSON>ails (collapsible) -->
          <div v-if="error.details || error.trace" class="mb-6">
            <button 
              @click="showDetails = !showDetails"
              class="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              {{ showDetails ? 'Hide' : 'Show' }} technical details
            </button>
            
            <div v-if="showDetails" class="mt-3 p-3 bg-gray-50 rounded-md text-left">
              <div v-if="error.details" class="mb-2">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Details:</h4>
                <pre class="text-xs text-gray-600 whitespace-pre-wrap">{{ error.details }}</pre>
              </div>
              <div v-if="error.trace" class="mb-2">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Stack Trace:</h4>
                <pre class="text-xs text-gray-600 whitespace-pre-wrap max-h-32 overflow-auto">{{ error.trace }}</pre>
              </div>
              <div v-if="error.timestamp" class="text-xs text-gray-500">
                Occurred at: {{ new Date(error.timestamp).toLocaleString() }}
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <button 
              v-if="canRetry"
              @click="retry"
              class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              Try Again
            </button>
            
            <button 
              v-if="canReport"
              @click="reportError"
              class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
            >
              Report Issue
            </button>
            
            <button 
              @click="dismiss"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              {{ canRetry || canReport ? 'Cancel' : 'Close' }}
            </button>
          </div>

          <!-- Help Links -->
          <div v-if="helpLinks && helpLinks.length > 0" class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm text-gray-600 mb-2">Need help?</p>
            <div class="flex flex-wrap gap-2 justify-center">
              <a 
                v-for="link in helpLinks" 
                :key="link.url"
                :href="link.url"
                target="_blank"
                class="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                {{ link.text }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  show: Boolean,
  error: {
    type: Object,
    default: () => ({})
  },
  canRetry: {
    type: Boolean,
    default: true
  },
  canReport: {
    type: Boolean,
    default: true
  },
  helpLinks: {
    type: Array,
    default: () => [
      { text: 'Documentation', url: '/docs' },
      { text: 'Contact Support', url: '/support' }
    ]
  }
})

const emit = defineEmits(['retry', 'report', 'dismiss'])

const showDetails = ref(false)

const retry = () => {
  emit('retry')
}

const reportError = () => {
  emit('report', props.error)
}

const dismiss = () => {
  emit('dismiss')
}
</script>
