<?php

namespace App\Repositories\Interfaces;

use App\Models\LabTestResult;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface LabTestResultRepositoryInterface
{
    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator;
    public function create(array $data): LabTestResult;
    public function update(LabTestResult $result, array $data): LabTestResult;
    public function delete(LabTestResult $result): bool;
    public function findById(int $id): ?LabTestResult;
    public function findByIdWithRelations(int $id, array $relations = []): ?LabTestResult;
    public function findByOrderNumber(string $orderNumber): ?LabTestResult;
    public function findByLabReference(string $labReferenceId): ?LabTestResult;
    public function getByPatient(int $patientId): Collection;
    public function getByRequest(int $requestId): Collection;
    public function getByStatus(string $status): Collection;
    public function getUnreviewedResults(): Collection;
    public function getRecentResults(int $limit = 10): Collection;
}
