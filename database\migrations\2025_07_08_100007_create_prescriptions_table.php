<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number')->unique(); // Auto-generated unique number
            $table->foreignId('consultation_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('prescriber_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('clinic_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('status', ['draft', 'active', 'dispensed', 'completed', 'cancelled', 'expired'])->default('draft');
            $table->enum('type', ['new', 'repeat', 'acute', 'chronic'])->default('new');
            $table->date('prescribed_date');
            $table->date('valid_until')->nullable(); // Prescription expiry
            $table->text('clinical_indication')->nullable(); // Why prescribed
            $table->text('additional_instructions')->nullable(); // General instructions
            $table->text('warnings')->nullable(); // Special warnings
            $table->boolean('is_private')->default(false); // Private prescription
            $table->boolean('is_electronic')->default(true); // Electronic vs paper
            $table->string('pharmacy_name')->nullable(); // Nominated pharmacy
            $table->text('pharmacy_address')->nullable();
            $table->integer('total_items')->default(0); // Number of medication items
            $table->decimal('total_cost', 10, 2)->nullable(); // Total prescription cost
            $table->datetime('dispensed_at')->nullable();
            $table->string('dispensed_by')->nullable(); // Pharmacist name
            $table->text('dispensing_notes')->nullable();
            $table->json('attachments')->nullable(); // Prescription images, etc.
            $table->timestamps();

            // Indexes
            $table->index(['patient_id', 'prescribed_date']);
            $table->index(['prescriber_id', 'prescribed_date']);
            $table->index('status');
            $table->index('type');
            $table->index('valid_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
