<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use App\Models\Consultation;
use App\Models\Appointment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ConsultationModuleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $provider;
    protected $patient;
    protected $clinic;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->clinic = Clinic::factory()->create();
        $this->user = User::factory()->create(['role' => 'provider']);
        $this->provider = Provider::factory()->create([
            'user_id' => $this->user->id,
            'clinic_id' => $this->clinic->id
        ]);
        $this->patient = Patient::factory()->create();
    }

    /** @test */
    public function it_can_list_consultations_for_authenticated_provider()
    {
        // Create consultations for this provider
        Consultation::factory()->count(3)->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id
        ]);

        // Create consultations for another provider (should not be visible)
        $otherProvider = Provider::factory()->create();
        Consultation::factory()->count(2)->create([
            'provider_id' => $otherProvider->id,
            'patient_id' => $this->patient->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/consultations');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'patient_id',
                            'provider_id',
                            'consultation_date',
                            'status',
                            'consultation_mode'
                        ]
                    ]
                ]
            ]);

        // Should only see consultations for this provider
        $this->assertEquals(3, count($response->json('data.data')));
    }

    /** @test */
    public function it_can_create_a_new_consultation()
    {
        $consultationData = [
            'patient_id' => $this->patient->id,
            'consultation_type' => 'general',
            'consultation_date' => now()->format('Y-m-d'),
            'consultation_mode' => 'in_person',
            'is_telemedicine' => false,
            'vital_signs' => [
                'blood_pressure' => ['value' => '120/80', 'unit' => 'mmHg'],
                'heart_rate' => ['value' => 72, 'unit' => 'bpm']
            ],
            'main_tabs' => [
                'concerns' => ['content' => 'Patient complains of headache'],
                'history' => ['content' => 'No significant medical history']
            ]
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/consultations', $consultationData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'patient_id',
                    'provider_id',
                    'clinic_id',
                    'consultation_type',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('consultations', [
            'patient_id' => $this->patient->id,
            'provider_id' => $this->provider->id,
            'clinic_id' => $this->clinic->id,
            'consultation_type' => 'general',
            'status' => 'draft'
        ]);
    }

    /** @test */
    public function it_validates_consultation_creation_data()
    {
        $invalidData = [
            'patient_id' => 999, // Non-existent patient
            'consultation_type' => 'invalid_type',
            'consultation_date' => 'invalid_date',
            'consultation_mode' => 'invalid_mode',
            'duration_minutes' => -5 // Invalid duration
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/consultations', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'patient_id',
                'consultation_type',
                'consultation_date',
                'consultation_mode',
                'duration_minutes'
            ]);
    }

    /** @test */
    public function it_can_show_a_specific_consultation()
    {
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/consultations/{$consultation->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'patient',
                    'provider',
                    'clinic',
                    'consultation_date',
                    'status'
                ]
            ]);
    }

    /** @test */
    public function it_prevents_viewing_other_providers_consultations()
    {
        $otherProvider = Provider::factory()->create();
        $consultation = Consultation::factory()->create([
            'provider_id' => $otherProvider->id,
            'patient_id' => $this->patient->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/consultations/{$consultation->id}");

        $response->assertStatus(403);
    }

    /** @test */
    public function it_can_update_a_consultation()
    {
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'draft'
        ]);

        $updateData = [
            'status' => 'in_progress',
            'consultation_type' => 'follow_up',
            'duration_minutes' => 30
        ];

        $response = $this->actingAs($this->user)
            ->putJson("/consultations/{$consultation->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('consultations', [
            'id' => $consultation->id,
            'status' => 'in_progress',
            'consultation_type' => 'follow_up',
            'duration_minutes' => 30
        ]);
    }

    /** @test */
    public function it_can_delete_a_non_completed_consultation()
    {
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'status' => 'draft'
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/consultations/{$consultation->id}");

        $response->assertStatus(200);
        $this->assertDatabaseMissing('consultations', ['id' => $consultation->id]);
    }

    /** @test */
    public function it_prevents_deleting_completed_consultations()
    {
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'status' => 'completed'
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/consultations/{$consultation->id}");

        $response->assertStatus(400);
        $this->assertDatabaseHas('consultations', ['id' => $consultation->id]);
    }

    /** @test */
    public function it_requires_authentication_for_consultation_access()
    {
        $response = $this->getJson('/consultations');
        $response->assertStatus(401);
    }

    /** @test */
    public function it_requires_clinician_role_for_consultation_access()
    {
        $nonClinicianUser = User::factory()->create(['role' => 'patient']);

        $response = $this->actingAs($nonClinicianUser)
            ->getJson('/consultations');

        $response->assertStatus(403);
    }
}
