<?php

namespace App\Repositories\Interfaces;

use App\Models\LabTestRequest;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface LabTestRequestRepositoryInterface
{
    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator;
    public function create(array $data): LabTestRequest;
    public function update(LabTestRequest $request, array $data): LabTestRequest;
    public function delete(LabTestRequest $request): bool;
    public function findById(int $id): ?LabTestRequest;
    public function findByIdWithRelations(int $id, array $relations = []): ?LabTestRequest;
    public function findByOrderNumber(string $orderNumber): ?LabTestRequest;
    public function getByPatient(int $patientId): Collection;
    public function getByProvider(int $providerId): Collection;
    public function getByStatus(string $status): Collection;
    public function getPendingRequests(): Collection;
    public function getRequestsForProcessing(): Collection;
}
