<template>
  <div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Lab Test Catalog</h2>
      <div class="flex space-x-3">
        <button
          @click="refreshCatalog"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Search Tests</label>
          <input
            v-model="searchQuery"
            @input="debouncedSearch"
            type="text"
            placeholder="Search by name or code..."
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
          <select
            v-model="selectedCategory"
            @change="filterByCategory"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
        </div>

        <!-- Selected Tests Counter -->
        <div class="flex items-end">
          <div class="bg-blue-50 px-4 py-2 rounded-lg">
            <span class="text-sm font-medium text-blue-700">
              Selected: {{ selectedTests.length }} tests
            </span>
            <span v-if="totalCost > 0" class="text-sm text-blue-600 ml-2">
              (£{{ totalCost.toFixed(2) }})
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Test Catalog Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="test in tests"
        :key="test.id"
        class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
        :class="{ 'ring-2 ring-blue-500 bg-blue-50': isSelected(test.test_code) }"
        @click="toggleTestSelection(test)"
      >
        <div class="p-4">
          <!-- Test Header -->
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="font-semibold text-gray-900">{{ test.test_name }}</h3>
              <p class="text-sm text-gray-500">{{ test.test_code }}</p>
            </div>
            <div class="text-right">
              <span class="text-lg font-bold text-green-600">{{ test.formatted_price }}</span>
              <div v-if="isSelected(test.test_code)" class="text-blue-600 mt-1">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>

          <!-- Category Badge -->
          <div class="mb-3">
            <span class="inline-block px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full">
              {{ test.category }}
            </span>
          </div>

          <!-- Test Requirements -->
          <div class="space-y-2 text-sm text-gray-600">
            <div v-if="test.requires_fasting" class="flex items-center">
              <i class="fas fa-clock text-orange-500 mr-2"></i>
              Fasting required ({{ test.fasting_hours }}h)
            </div>
            <div v-if="test.sample_type" class="flex items-center">
              <i class="fas fa-vial text-red-500 mr-2"></i>
              {{ test.sample_type }} sample
            </div>
            <div v-if="test.turnaround_time" class="flex items-center">
              <i class="fas fa-stopwatch text-blue-500 mr-2"></i>
              {{ test.turnaround_time }}
            </div>
          </div>

          <!-- Special Instructions -->
          <div v-if="test.special_instructions" class="mt-3 p-2 bg-yellow-50 rounded text-xs text-yellow-800">
            <i class="fas fa-exclamation-triangle mr-1"></i>
            {{ test.special_instructions }}
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && tests.length === 0" class="text-center py-12">
      <i class="fas fa-flask text-gray-400 text-4xl mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No tests found</h3>
      <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && pagination.last_page > 1" class="mt-6 flex justify-center">
      <nav class="flex space-x-2">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          class="px-3 py-2 text-sm rounded-lg transition-colors"
          :class="page === pagination.current_page 
            ? 'bg-blue-600 text-white' 
            : 'bg-white text-gray-700 hover:bg-gray-50 border'"
        >
          {{ page }}
        </button>
      </nav>
    </div>

    <!-- Selected Tests Summary -->
    <div v-if="selectedTests.length > 0" class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border p-4 max-w-sm">
      <h4 class="font-semibold text-gray-900 mb-2">Selected Tests ({{ selectedTests.length }})</h4>
      <div class="max-h-32 overflow-y-auto space-y-1 mb-3">
        <div v-for="test in selectedTests" :key="test.test_code" class="flex justify-between text-sm">
          <span>{{ test.test_name }}</span>
          <span class="font-medium">£{{ test.price.toFixed(2) }}</span>
        </div>
      </div>
      <div class="border-t pt-2 flex justify-between items-center">
        <span class="font-semibold">Total: £{{ totalCost.toFixed(2) }}</span>
        <button
          @click="$emit('tests-selected', selectedTests)"
          class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { debounce } from 'lodash'
import type { LabTest, PaginationMeta } from '@/types/tdl'

// Props
interface Props {
    preselectedTests?: LabTest[]
}

const props = withDefaults(defineProps<Props>(), {
    preselectedTests: () => []
})

// Emits
interface Emits {
    (e: 'tests-selected', tests: LabTest[]): void
}

const emit = defineEmits<Emits>()

// Reactive data
const tests = ref<LabTest[]>([])
const categories = ref<string[]>([])
const selectedTests = ref<LabTest[]>([])
const searchQuery = ref<string>('')
const selectedCategory = ref<string>('')
const loading = ref<boolean>(false)
const pagination = ref<PaginationMeta | null>(null)

// Computed
const totalCost = computed(() => {
    return selectedTests.value.reduce((sum, test) => sum + test.price, 0)
})

const visiblePages = computed(() => {
  if (!pagination.value) return []
  
  const current = pagination.value.current_page
  const last = pagination.value.last_page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const fetchTests = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: '12'
    })

    // Only add search parameter if it has a value
    if (searchQuery.value && searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim())
    }

    // Only add category filter if it has a value
    if (selectedCategory.value && selectedCategory.value !== '') {
      params.append('category', selectedCategory.value)
    }

    const response = await fetch(`/lab/catalog?${params}`)
    const data = await response.json()
    
    if (data.success) {
      tests.value = data.data.tests.data
      pagination.value = {
        current_page: data.data.tests.current_page,
        last_page: data.data.tests.last_page,
        total: data.data.tests.total
      }
      categories.value = data.data.categories
    }
  } catch (error) {
    // Handle error silently
  } finally {
    loading.value = false
  }
}

const debouncedSearch = debounce(() => {
  fetchTests(1)
}, 300)

const filterByCategory = () => {
  fetchTests(1)
}

const refreshCatalog = () => {
  fetchTests(1)
}

const goToPage = (page) => {
  fetchTests(page)
}

const isSelected = (testCode) => {
  return selectedTests.value.some(test => test.test_code === testCode)
}

const toggleTestSelection = (test) => {
  const index = selectedTests.value.findIndex(t => t.test_code === test.test_code)
  
  if (index > -1) {
    selectedTests.value.splice(index, 1)
  } else {
    selectedTests.value.push(test)
  }
}

// Watchers
watch(() => props.preselectedTests, (newTests) => {
  selectedTests.value = [...newTests]
}, { immediate: true })

// Lifecycle
onMounted(() => {
  fetchTests()
})
</script>


