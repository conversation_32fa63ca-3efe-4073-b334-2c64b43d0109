<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-lg w-full mx-4">
      <div class="p-4 border-b flex justify-between items-center">
        <h3 class="text-lg font-medium">Upload Document</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
 
      <div class="p-4">
        <form @submit.prevent="handleSubmit">
          <!-- Document Name -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Document Name</label>
            <input
              v-model="form.name"
              type="text"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              placeholder="Enter document name"
              required
            />
          </div>
 
          <!-- Document Type -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Document Type</label>
            <select
              v-model="form.type"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            >
              <option value="">Select type</option>
              <option
                v-for="type in documentTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.text }}
              </option>
            </select>
          </div>
 
          <!-- File Upload -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">File</label>
            <div
              class="border-2 border-dashed rounded p-4 text-center hover:bg-gray-50 cursor-pointer transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': isDragOver }"
              @click="triggerFileInput"
              @drop="handleDrop"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
            >
              <input
                ref="fileInput"
                type="file"
                class="hidden"
                @change="handleFileSelect"
                accept="*"
              />
              
              <div v-if="!selectedFile">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="17 8 12 3 7 8" />
                  <line x1="12" y1="3" x2="12" y2="15" />
                </svg>
                <p class="mt-2 text-sm text-gray-600">Click to browse or drag and drop</p>
                <p class="text-xs text-gray-500 mt-1">Any file type (Max 10MB)</p>
              </div>
              <div v-else class="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ selectedFile.name }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
                <button type="button" @click.stop="removeFile" class="ml-2 text-red-500 hover:text-red-700">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
 
          <!-- Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Description</label>
            <textarea
              v-model="form.description"
              rows="3"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              placeholder="Enter description..."
            ></textarea>
          </div>

          <!-- Upload Progress -->
          <div v-if="uploading && uploadProgress > 0" class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>Uploading...</span>
              <span>{{ uploadProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: uploadProgress + '%' }"></div>
            </div>
          </div>
 
          <!-- Buttons -->
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="px-4 py-2 text-sm border rounded hover:bg-gray-50"
              @click="$emit('close')"
              :disabled="uploading"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!isFormValid || uploading"
            >
              <template v-if="uploading">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Uploading...
              </template>
              <template v-else>Upload</template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useNotifications } from '../../composables/useNotifications'
import axios from 'axios'

interface Props {
  show: boolean
  encounterId: string | number
  patientDetails?: {
    patient_id: number
    name: string
    email?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  patientDetails: () => ({ patient_id: 0, name: '' })
})

const emit = defineEmits<{
  'close': []
  'upload-success': []
}>()

// Notifications
const { showSuccess, showError } = useNotifications()

// Form data
const form = ref({
  name: '',
  type: '',
  description: ''
})

const selectedFile = ref<File | null>(null)
const uploading = ref(false)
const uploadProgress = ref(0)
const isDragOver = ref(false)

const documentTypes = [
  { value: 'medical_record', text: 'Medical Record' },
  { value: 'lab_report', text: 'Lab Report' },
  { value: 'prescription', text: 'Prescription' },
  { value: 'scan', text: 'Scan/X-Ray' },
  { value: 'other', text: 'Other' }
]

// Computed
const isFormValid = computed(() => {
  return form.value.name && form.value.type && selectedFile.value
})

// File input ref
const fileInput = ref<HTMLInputElement>()

// Methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    validateAndSetFile(files[0])
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const validateAndSetFile = (file: File) => {
  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024 // 10MB in bytes
  if (file.size > maxSize) {
    showError('File size must be less than 10MB')
    return
  }

  selectedFile.value = file

  // Auto-fill document name if empty
  if (!form.value.name) {
    form.value.name = file.name.replace(/\.[^/.]+$/, '') // Remove file extension
  }
}

const removeFile = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const resetForm = () => {
  form.value = {
    name: '',
    type: '',
    description: ''
  }
  selectedFile.value = null
  uploadProgress.value = 0
  isDragOver.value = false
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return
 
  uploading.value = true
  uploadProgress.value = 0
  
  try {
    const formData = new FormData()
    formData.append('files[]', selectedFile.value!)
    formData.append('document_type', form.value.type)
    formData.append('description', form.value.description || '')
    
    const response = await axios.post(`/consultations/${props.encounterId}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })

    if (response.data.success) {
      showSuccess('Document uploaded successfully')
      emit('upload-success')
      emit('close')
      resetForm()
    } else {
      throw new Error(response.data.message || 'Upload failed')
    }
  } catch (error: any) {
    console.error('Upload error:', error)
    
    let errorMessage = 'An unexpected error occurred'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }
    
    showError(errorMessage)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}
</script>
