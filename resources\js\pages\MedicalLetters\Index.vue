<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import { useNotifications } from '@/composables/useNotifications';
import Icon from '@/components/Icon.vue';
import axios from 'axios';

const { showSuccess, showError } = useNotifications();

// Data
const loading = ref(false);
const letters = ref([]);
const templates = ref([]);
const searchQuery = ref('');
const selectedTemplate = ref('');

// Methods
const loadLetters = async () => {
    loading.value = true;
    try {
        // This would load medical letters from the API
        // For now, we'll show an empty state
        letters.value = [];
    } catch (error) {
        console.error('Error loading medical letters:', error);
        showError('Failed to load medical letters');
    } finally {
        loading.value = false;
    }
};

const loadTemplates = async () => {
    try {
        const response = await axios.get('/letter-templates/available');
        if (response.data && response.data.data) {
            templates.value = response.data.data;
        }
    } catch (error) {
        console.error('Error loading templates:', error);
    }
};

const createLetter = () => {
    if (!selectedTemplate.value) {
        showError('Please select a template first');
        return;
    }
    
    // Navigate to letter creation with selected template
    // This would be implemented based on your letter creation flow
    showSuccess('Letter creation feature coming soon');
};

const viewLetter = (letterId) => {
    // Navigate to letter view
    console.log('Viewing letter:', letterId);
};

const editLetter = (letterId) => {
    // Navigate to letter edit
    console.log('Editing letter:', letterId);
};

const deleteLetter = async (letterId) => {
    if (!confirm('Are you sure you want to delete this letter?')) {
        return;
    }
    
    try {
        // Delete letter API call would go here
        showSuccess('Letter deleted successfully');
        loadLetters();
    } catch (error) {
        console.error('Error deleting letter:', error);
        showError('Failed to delete letter');
    }
};

// Lifecycle
onMounted(() => {
    loadLetters();
    loadTemplates();
});
</script>

<template>
    <Head title="Medical Letters" />

    <AppLayout>
        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Medical Letters</h1>
                            <p class="mt-1 text-sm text-gray-600">
                                Create and manage medical letters and reports
                            </p>
                        </div>
                        <div class="flex space-x-3">
                            <select
                                v-model="selectedTemplate"
                                class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">Select Template</option>
                                <option v-for="template in templates" :key="template.id" :value="template.id">
                                    {{ template.name }}
                                </option>
                            </select>
                            <button
                                @click="createLetter"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
                            >
                                <Icon name="plus" class="w-4 h-4 mr-2" />
                                Create Letter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Search Letters</label>
                                <input
                                    v-model="searchQuery"
                                    type="text"
                                    placeholder="Search by patient name, letter type, or content..."
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Letters List -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            Medical Letters
                        </h3>
                    </div>

                    <div v-if="loading" class="p-6 text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p class="mt-2 text-gray-600">Loading letters...</p>
                    </div>

                    <div v-else-if="letters.length === 0" class="p-6 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No medical letters yet</h3>
                        <p class="text-gray-600 mb-4">
                            Start by creating your first medical letter using one of the available templates.
                        </p>
                        <button
                            @click="createLetter"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
                        >
                            <Icon name="plus" class="w-4 h-4 mr-2" />
                            Create Your First Letter
                        </button>
                    </div>

                    <div v-else class="divide-y divide-gray-200">
                        <div v-for="letter in letters" :key="letter.id" class="p-6 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h4 class="text-lg font-medium text-gray-900">{{ letter.title }}</h4>
                                    <div class="mt-1 flex items-center space-x-4 text-sm text-gray-600">
                                        <span>Patient: {{ letter.patient_name }}</span>
                                        <span>•</span>
                                        <span>Type: {{ letter.type }}</span>
                                        <span>•</span>
                                        <span>Created: {{ letter.created_at }}</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button
                                        @click="viewLetter(letter.id)"
                                        class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                                    >
                                        View
                                    </button>
                                    <button
                                        @click="editLetter(letter.id)"
                                        class="text-gray-600 hover:text-gray-900 text-sm font-medium"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        @click="deleteLetter(letter.id)"
                                        class="text-red-600 hover:text-red-900 text-sm font-medium"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Templates Info -->
                <div v-if="templates.length > 0" class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <Icon name="info" class="h-5 w-5 text-blue-400" />
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Available Templates</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>You have {{ templates.length }} letter template(s) available:</p>
                                <ul class="mt-1 list-disc list-inside">
                                    <li v-for="template in templates" :key="template.id">
                                        {{ template.name }}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
