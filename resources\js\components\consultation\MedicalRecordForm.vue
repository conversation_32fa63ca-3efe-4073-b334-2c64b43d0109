<template>
  <!-- Clean Medical Record Form -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="bg-gray-50 px-6 py-4 border-b border-gray-100">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-3">
          <h2 class="text-lg font-semibold text-gray-900">{{ form?.title }}</h2>
          <!-- AI populated badge -->
          <div v-if="isAIGenerated"
            class="px-2 py-1 rounded-full bg-blue-100 text-blue-700 text-xs font-medium flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path
                d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
              <path d="M5 3v4" />
              <path d="M19 17v4" />
              <path d="M3 5h4" />
              <path d="M17 19h4" />
            </svg>
            AI populated
          </div>
        </div>

        <div class="flex gap-2">
          <!-- Clone button -->
          <button v-if="displayPlusBtn" @click="handleClone"
            class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5v14"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="p-6">
      <textarea v-model="localContent"
        class="w-full h-32 text-sm resize-none border border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none"
        :placeholder="'Enter ' + form?.title.toLowerCase() + '...'"
        @input="handleContentChange">
      </textarea>

      <div class="flex justify-between items-center mt-4">
        <!-- Prefill Button -->
        <button @click="handlePrefill"
          class="text-sm text-teal-600 hover:text-teal-700 font-medium px-3 py-1.5 hover:bg-teal-50 rounded-lg transition-colors"
          :disabled="!hasPrefillTemplate">
          Prefill
        </button>

        <!-- Right side actions -->
        <div class="flex items-center gap-2">
          <!-- Reset Button -->
          <button v-if="isAIGenerated" @click="handleResetContent"
            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
            Reset
          </button>

          <!-- Approve AI button -->
          <button v-if="isAIGenerated" @click="handleApproveAI"
            class="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center gap-1 text-sm transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12L10 17L19 8" />
            </svg>
            Approve
          </button>
        </div>
      </div>

      <!-- Remove Quick Templates for Present Concerns, Present History, and Assessment & Plan -->
      <div v-if="showTemplates && shouldShowQuickTemplates" class="mt-4 pt-4 border-t border-gray-100">
        <h4 class="text-sm font-medium text-gray-700 mb-3">Quick Templates</h4>
        <div class="flex flex-wrap gap-2">
          <button v-for="template in form.templates" :key="template" @click="applyTemplate(template)"
            class="px-3 py-1.5 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
            {{ template }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MedicalRecordForm",

  props: {
    form: {
      type: [Object, null],
      validator: (value) => {
        return (
          value &&
          typeof value.title === "string" &&
          typeof value.type === "string" &&
          Array.isArray(value.templates) &&
          Array.isArray(value.entries)
        );
      },
    },
    displayPlusBtn: {
      type: Boolean,
      default: false,
    },
    aiData: {
      type: [Object, String],
      default: null,
    },
    approvedInstances: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      localContent: "",
      isAIGenerated: false,
      localApproved: false,
      isManuallyEdited: false,
      originalContent: "",
      pendingAiContent: null,
      saveTimeout: null,
      showTemplates: false,
    };
  },

  computed: {
    hasPrefillTemplate() {
      return this.form?.templates && this.form.templates.length > 0;
    },

    // Get the main content from the first entry
    currentContent() {
      if (this.form?.entries && this.form.entries.length > 0) {
        return this.form.entries[0].content || '';
      }
      return '';
    },

    // Determine if Quick Templates should be shown (hide for specific forms)
    shouldShowQuickTemplates() {
      const formsToHideTemplates = ['concerns', 'history', 'plan'];
      return !formsToHideTemplates.includes(this.form?.type);
    },
  },

  watch: {
    aiData: {
      handler(newData) {
        if (newData) {
          this.populateWithAI(newData);
        }
      },
      immediate: true,
      deep: true,
    },

    currentContent: {
      handler(newContent) {
        if (newContent !== undefined && newContent !== this.localContent) {
          this.localContent = newContent;
        }
      },
      immediate: true,
    },

    approvedInstances: {
      handler(newApproved) {
        const instanceId = this.form?.instanceId;
        if (instanceId && newApproved[instanceId]) {
          this.localApproved = true;
          this.isAIGenerated = false;
        }
      },
      immediate: true,
      deep: true,
    },
  },

  mounted() {
    // Check if this instance was previously approved
    const instanceId = this.form?.instanceId;
    if (instanceId) {
      const wasApproved = localStorage.getItem(`approved_${instanceId}`);
      if (wasApproved === 'true') {
        this.localApproved = true;
        this.isAIGenerated = false;
      }
    }

    // Set initial content from entries
    this.localContent = this.currentContent;
  },

  methods: {
    handleContentChange() {
      // Mark as manually edited if user types
      if (!this.isAIGenerated) {
        this.isManuallyEdited = true;
      }

      this.$emit("update:content", {
        type: this.form.type,
        content: this.localContent,
        instanceId: this.form.instanceId,
        isAIGenerated: this.isAIGenerated
      });

      // Auto-save with debounce
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }

      this.saveTimeout = setTimeout(() => {
        this.handleSave();
      }, 1500);
    },

    populateWithAI(aiData) {
      // If this content was previously approved, manually edited, or aiData is null, exit early
      if (this.localApproved || this.isManuallyEdited || !aiData) return;

      let contentToUse = "";
      let isAIGenerated = true;

      if (typeof aiData === 'string') {
        contentToUse = aiData;
      } else if (typeof aiData === 'object') {
        if (aiData.content) {
          contentToUse = aiData.content;
          if (aiData.isAIGenerated !== undefined) {
            isAIGenerated = aiData.isAIGenerated;
          }
        } else {
          contentToUse = aiData[this.form.type] || "";
        }
      }

      if (contentToUse.trim()) {
        // Ensure these flags are set correctly
        this.isAIGenerated = true; // Force this to true regardless of the input value
        this.originalContent = this.localContent || "";
        this.pendingAiContent = contentToUse;
        this.localContent = contentToUse;

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: true
        });
      }
    },

    // Handle AI approval
    handleApproveAI() {
      if (this.isAIGenerated) {
        this.isAIGenerated = false;
        this.pendingAiContent = null;
        this.localApproved = true;  // Set local approval state

        // Store in localStorage
        const instanceId = this.form?.instanceId;
        if (instanceId) {
          localStorage.setItem(`approved_${instanceId}`, 'true');
        }

        // Emit events
        this.$emit("ai-approved", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId
        });

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger a save immediately
        this.handleSave();
      }
    },

    // Reset to pre-AI content
    handleResetContent() {
      if (this.isAIGenerated) {
        this.localContent = this.originalContent || "";
        this.isAIGenerated = false;
        this.pendingAiContent = null;

        // Mark as manually edited to prevent re-population
        this.isManuallyEdited = true;

        // Emit the update
        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger save
        this.handleSave();
      }
    },

    handleSave() {
      this.$emit("save", {
        type: this.form.type,
        content: this.localContent,
        instanceId: this.form.instanceId,
        isAIGenerated: this.isAIGenerated
      });
    },

    handleClone() {
      this.$emit("clone", this.form.type);
    },

    handlePrefill() {
      // For specific forms, directly apply prefill content instead of showing templates
      if (['concerns', 'history', 'plan'].includes(this.form?.type)) {
        this.applyPrefillContent();
      } else {
        this.showTemplates = !this.showTemplates;
      }
    },

    applyPrefillContent() {
      let prefillContent = '';

      switch (this.form?.type) {
        case 'concerns':
          prefillContent = `* Main reason for consultation:
* Duration of symptoms:
* Severity:
* Associated symptoms:
* Aggravating factors:
* Relieving factors:`;
          break;
        case 'history':
          prefillContent = `* Past consultations:
* Previous treatments:
* Response to treatments:
* Recent changes:
* Impact on daily activities:`;
          break;
        case 'plan':
          prefillContent = `* Diagnosis:
* Treatment plan:
* Medications prescribed:
* Follow-up arrangements:
* Patient education:
* Referrals:`;
          break;
        default:
          return;
      }

      this.localContent = prefillContent;
      this.handleContentChange();
    },

    applyTemplate(template) {
      this.localContent = template;
      this.showTemplates = false;
      this.handleContentChange();
    },
  },
};
</script>
