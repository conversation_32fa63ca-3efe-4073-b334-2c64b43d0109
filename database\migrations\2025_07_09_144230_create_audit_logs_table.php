<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();

            // User and session information
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('user_type')->nullable(); // 'admin', 'provider', 'patient', 'clinic_admin'
            $table->string('session_id')->nullable();

            // Action details
            $table->string('action'); // 'create', 'read', 'update', 'delete', 'access', 'assign', etc.
            $table->string('resource_type'); // 'patient', 'appointment', 'consultation', etc.
            $table->string('resource_id')->nullable();
            $table->string('description');

            // Patient data access (HIPAA requirement)
            $table->foreignId('patient_id')->nullable()->constrained()->onDelete('cascade');
            $table->json('accessed_fields')->nullable(); // Which patient fields were accessed

            // Technical details
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('request_method')->nullable();
            $table->text('request_url')->nullable();

            // Data changes (for updates)
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();

            // Compliance and security
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->boolean('is_sensitive')->default(false); // PHI access
            $table->string('compliance_reason')->nullable(); // Business justification

            // Timestamps
            $table->timestamp('occurred_at')->useCurrent();
            $table->timestamps();

            // Indexes for performance and compliance queries
            $table->index(['user_id', 'occurred_at']);
            $table->index(['patient_id', 'occurred_at']);
            $table->index(['resource_type', 'resource_id']);
            $table->index(['action', 'occurred_at']);
            $table->index(['is_sensitive', 'occurred_at']);
            $table->index('ip_address');
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
