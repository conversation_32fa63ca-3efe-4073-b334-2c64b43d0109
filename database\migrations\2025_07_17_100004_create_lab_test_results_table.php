<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_test_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->nullable()->constrained('lab_test_requests')->onDelete('set null');
            $table->foreignId('clinic_id')->constrained('clinics')->onDelete('cascade');
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->string('order_number')->nullable();
            $table->string('lab_reference_id')->nullable(); // TDL's reference ID
            $table->enum('status', ['received', 'processing', 'completed', 'reviewed', 'archived'])->default('received');
            $table->json('result_data'); // Complete result data with biomarkers
            $table->string('azure_file_path')->nullable(); // Path to result file in Azure
            $table->text('physician_notes')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('received_at');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();

            $table->index(['clinic_id', 'status']);
            $table->index(['patient_id', 'status']);
            $table->index(['request_id']);
            $table->index(['order_number']);
            $table->index(['lab_reference_id']);
            $table->index(['status', 'received_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_test_results');
    }
};
