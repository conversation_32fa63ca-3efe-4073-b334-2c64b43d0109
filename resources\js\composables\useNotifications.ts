import { useToast } from 'vue-toastification'
import Swal from 'sweetalert2'

/**
 * Smart Notification Strategy Guide
 *
 * 🍞 TOAST NOTIFICATIONS (Non-blocking, auto-dismiss)
 * Use for:
 * - ✅ Success confirmations (saved, updated, deleted)
 * - ❌ Error messages (validation, network errors)
 * - ⚠️ Status updates (loading, progress)
 * - ℹ️ Background operations feedback
 * - 📧 Email sent confirmations
 * - 🔄 Auto-refresh notifications
 *
 * 🚨 SWEETALERT2 DIALOGS (Blocking, requires user action)
 * Use for:
 * - ❓ Confirmations requiring decision (delete, cancel)
 * - 📝 Input prompts (reason, password, notes)
 * - 🚨 Critical alerts requiring acknowledgment
 * - 📋 Complex forms or multi-step processes
 * - 🔐 Security-related confirmations
 *
 * 🧠 SMART METHODS (Auto-choose based on context)
 * - notifySuccess/Error/Warning/Info() - with modal option
 * - notifyOperation() - handles loading states automatically
 */

// Toast notification types
export type ToastType = 'success' | 'error' | 'warning' | 'info'

// SweetAlert2 configuration with professional styling
const swalConfig = {
    customClass: {
        popup: 'rounded-lg shadow-xl',
        title: 'text-lg font-semibold text-gray-900',
        content: 'text-gray-700',
        confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200',
        cancelButton: 'bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 mr-3',
        denyButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200'
    },
    buttonsStyling: false,
    showClass: {
        popup: 'animate__animated animate__fadeInDown animate__faster'
    },
    hideClass: {
        popup: 'animate__animated animate__fadeOutUp animate__faster'
    }
}

export function useNotifications() {
    const toast = useToast()

    // Toast notifications (positioned top-right) - 5 seconds duration with close button
    const showToast = (message: string, type: ToastType = 'info') => {
        const toastOptions = {
            timeout: 5000, // 5 seconds
            closeButton: "button", // Always show close button
            hideProgressBar: false,
        }

        switch (type) {
            case 'success':
                toast.success(message, toastOptions)
                break
            case 'error':
                toast.error(message, toastOptions)
                break
            case 'warning':
                toast.warning(message, toastOptions)
                break
            case 'info':
            default:
                toast.info(message, toastOptions)
                break
        }
    }

    // Success toast - 5 seconds with close button
    const showSuccess = (message: string) => {
        showToast(message, 'success')
    }

    // Error toast - 5 seconds with close button
    const showError = (message: string) => {
        showToast(message, 'error')
    }

    // Warning toast - 5 seconds with close button
    const showWarning = (message: string) => {
        showToast(message, 'warning')
    }

    // Info toast - 5 seconds with close button
    const showInfo = (message: string) => {
        showToast(message, 'info')
    }

    // SweetAlert2 confirmation dialog
    const showConfirm = async (
        title: string,
        text?: string,
        confirmButtonText: string = 'Yes',
        cancelButtonText: string = 'Cancel'
    ): Promise<boolean> => {
        const result = await Swal.fire({
            ...swalConfig,
            title,
            text,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText,
            cancelButtonText,
            reverseButtons: true
        })
        return result.isConfirmed
    }

    // SweetAlert2 alert dialog
    const showAlert = async (
        title: string,
        text?: string,
        icon: 'success' | 'error' | 'warning' | 'info' = 'info',
        confirmButtonText: string = 'OK'
    ): Promise<void> => {
        await Swal.fire({
            ...swalConfig,
            title,
            text,
            icon,
            confirmButtonText
        })
    }

    // SweetAlert2 input prompt
    const showPrompt = async (
        title: string,
        text?: string,
        inputPlaceholder?: string,
        inputValue?: string
    ): Promise<string | null> => {
        const result = await Swal.fire({
            ...swalConfig,
            title,
            text,
            input: 'text',
            inputPlaceholder,
            inputValue,
            showCancelButton: true,
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please enter a value'
                }
                return null
            }
        })
        return result.isConfirmed ? result.value : null
    }

    // SweetAlert2 textarea prompt
    const showTextareaPrompt = async (
        title: string,
        text?: string,
        inputPlaceholder?: string,
        inputValue?: string
    ): Promise<string | null> => {
        const result = await Swal.fire({
            ...swalConfig,
            title,
            text,
            input: 'textarea',
            inputPlaceholder,
            inputValue,
            showCancelButton: true,
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please enter a value'
                }
                return null
            }
        })
        return result.isConfirmed ? result.value : null
    }

    // SweetAlert2 delete confirmation
    const showDeleteConfirm = async (
        itemName: string,
        itemType: string = 'item'
    ): Promise<boolean> => {
        const result = await Swal.fire({
            ...swalConfig,
            title: `Delete ${itemType}?`,
            text: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            reverseButtons: true,
            customClass: {
                ...swalConfig.customClass,
                confirmButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200'
            }
        })
        return result.isConfirmed
    }

    // Smart notification methods
    const notifySuccess = (message: string, options: { modal?: boolean } = {}) => {
        if (options.modal) {
            return showAlert('Success', message, 'success')
        } else {
            return showSuccess(message)
        }
    }

    const notifyError = (message: string, options: { modal?: boolean } = {}) => {
        if (options.modal) {
            return showAlert('Error', message, 'error')
        } else {
            return showError(message)
        }
    }

    const notifyWarning = (message: string, options: { modal?: boolean } = {}) => {
        if (options.modal) {
            return showAlert('Warning', message, 'warning')
        } else {
            return showWarning(message)
        }
    }

    const notifyInfo = (message: string, options: { modal?: boolean } = {}) => {
        if (options.modal) {
            return showAlert('Information', message, 'info')
        } else {
            return showInfo(message)
        }
    }

    // Smart operation feedback
    const notifyOperation = async (
        operation: () => Promise<any>,
        messages: {
            loading?: string
            success?: string
            error?: string
        } = {}
    ) => {
        const loadingToast = showInfo(messages.loading || 'Processing...', { timeout: 0 })

        try {
            const result = await operation()
            // Hide loading toast
            if (loadingToast) {
                // Note: vue-toastification doesn't have hideToast method, so we'll use timeout
            }
            showSuccess(messages.success || 'Operation completed successfully')
            return result
        } catch (error: any) {
            // Hide loading toast
            if (loadingToast) {
                // Note: vue-toastification doesn't have hideToast method, so we'll use timeout
            }
            showError(messages.error || error.message || 'Operation failed')
            throw error
        }
    }

    return {
        // Toast notifications (non-blocking)
        showToast,
        showSuccess,
        showError,
        showWarning,
        showInfo,

        // SweetAlert2 dialogs (blocking)
        showConfirm,
        showAlert,
        showPrompt,
        showTextareaPrompt,
        showDeleteConfirm,

        // Smart notification methods
        notifySuccess,
        notifyError,
        notifyWarning,
        notifyInfo,
        notifyOperation
    }
}
