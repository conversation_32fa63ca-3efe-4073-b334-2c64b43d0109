<template>
  <div class="relative">
    <div class="relative">
      <Input
        v-model="searchQuery"
        :placeholder="placeholder"
        @input="onSearch"
        @focus="showDropdown = true"
        @blur="onBlur"
        :disabled="disabled"
        class="pr-10"
      />
      <Search class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
    </div>

    <!-- Dropdown -->
    <div
      v-if="showDropdown && !disabled"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
    >
      <div v-if="loading" class="p-3 text-center text-gray-500">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600 mx-auto"></div>
      </div>

      <div v-else-if="error" class="p-3 text-center text-red-500 text-sm">
        {{ error }}
      </div>

      <div v-else-if="medications.length === 0 && searchQuery" class="p-3 text-center text-gray-500 text-sm">
        No medications found
      </div>

      <div v-else-if="medications.length === 0" class="p-3 text-center text-gray-500 text-sm">
        Start typing to search medications
      </div>

      <div v-else>
        <button
          v-for="medication in medications"
          :key="medication.id"
          @mousedown.prevent="selectMedication(medication)"
          class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
        >
          <div class="font-medium">{{ medication.name }}</div>
          <div class="text-sm text-gray-500">
            <span v-if="medication.brand_name" class="font-medium">{{ medication.brand_name }}</span>
            <span v-if="medication.strength">{{ medication.strength }}</span>
            <span v-if="medication.form">{{ medication.form }}</span>
            <span v-if="medication.drug_class" class="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">
              {{ medication.drug_class }}
            </span>
          </div>
          <div v-if="medication.controlled_substance_schedule" class="text-xs text-red-600 mt-1">
            Controlled Substance - Schedule {{ medication.controlled_substance_schedule }}
          </div>
        </button>
      </div>
    </div>

    <!-- Selected medication display -->
    <div v-if="selectedMedication && !showDropdown" class="mt-2 p-2 bg-gray-50 rounded border">
      <div class="flex items-center justify-between">
        <div>
          <div class="font-medium">{{ selectedMedication.name }}</div>
          <div class="text-sm text-gray-500">
            <span v-if="selectedMedication.brand_name">{{ selectedMedication.brand_name }} • </span>
            <span v-if="selectedMedication.strength">{{ selectedMedication.strength }} • </span>
            <span v-if="selectedMedication.form">{{ selectedMedication.form }}</span>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          @click="clearSelection"
          class="text-gray-400 hover:text-gray-600"
        >
          <X class="w-4 h-4" />
        </Button>
      </div>
      
      <!-- Warnings -->
      <div v-if="selectedMedication.controlled_substance_schedule" class="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
        ⚠️ Controlled Substance - Schedule {{ selectedMedication.controlled_substance_schedule }}
      </div>
      
      <!-- Common interactions warning -->
      <div v-if="selectedMedication.interactions && selectedMedication.interactions.length > 0" class="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded">
        ⚠️ This medication has known drug interactions. Please review carefully.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, X } from 'lucide-vue-next'
import { usePrescriptionApi, type Medication } from '@/composables/usePrescriptionApi'

interface Props {
  modelValue?: number | null
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: number | null): void
  (e: 'medication-selected', medication: Medication | null): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search for a medication...',
  disabled: false,
  required: false
})

const emit = defineEmits<Emits>()

const { searchMedications, getMedication, loading, error } = usePrescriptionApi()

const searchQuery = ref('')
const showDropdown = ref(false)
const medications = ref<Medication[]>([])
const selectedMedication = ref<Medication | null>(null)

let searchTimeout: NodeJS.Timeout

const onSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(async () => {
    if (searchQuery.value.length >= 2) {
      await searchMeds()
    } else {
      medications.value = []
    }
  }, 300)
}

const searchMeds = async () => {
  try {
    const response = await searchMedications(searchQuery.value, 20)
    if (response?.data) {
      medications.value = response.data
    }
  } catch (err) {
    console.error('Error searching medications:', err)
  }
}

const selectMedication = (medication: Medication) => {
  selectedMedication.value = medication
  searchQuery.value = medication.name
  showDropdown.value = false
  emit('update:modelValue', medication.id)
  emit('medication-selected', medication)
}

const clearSelection = () => {
  selectedMedication.value = null
  searchQuery.value = ''
  emit('update:modelValue', null)
  emit('medication-selected', null)
}

const onBlur = () => {
  // Delay hiding dropdown to allow for click events
  setTimeout(() => {
    showDropdown.value = false
  }, 200)
}

// Load medication details if modelValue is provided
const loadMedicationById = async (medicationId: number) => {
  try {
    const response = await getMedication(medicationId)
    if (response?.data) {
      selectedMedication.value = response.data
      searchQuery.value = response.data.name
    }
  } catch (err) {
    console.error('Error loading medication:', err)
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== selectedMedication.value?.id) {
    loadMedicationById(newValue)
  } else if (!newValue) {
    selectedMedication.value = null
    searchQuery.value = ''
  }
})

onMounted(() => {
  if (props.modelValue) {
    loadMedicationById(props.modelValue)
  }
})
</script>
