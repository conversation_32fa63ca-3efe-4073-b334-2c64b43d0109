<?php

namespace App\Services;

use App\Models\ProductCategory;
use App\Repositories\ProductCategoryRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;

class ProductCategoryService
{
    protected ProductCategoryRepository $categoryRepository;

    public function __construct(ProductCategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Create a new product category with business logic
     */
    public function createCategory(array $data): ProductCategory
    {
        // Business logic: Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }
        
        // Business logic: Set sort order if not provided
        if (empty($data['sort_order'])) {
            $data['sort_order'] = $this->categoryRepository->getNextSortOrder($data['parent_id'] ?? null);
        }
        
        // Business logic: Set defaults
        $data['is_active'] = $data['is_active'] ?? true;

        return $this->categoryRepository->create($data);
    }

    /**
     * Update category with business logic
     */
    public function updateCategory(int $categoryId, array $data): ?ProductCategory
    {
        // Business logic: Update slug if name changed
        if (isset($data['name']) && !isset($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $categoryId);
        }

        return $this->categoryRepository->update($categoryId, $data);
    }

    /**
     * Get category details
     */
    public function getCategoryDetails(int $categoryId): ?ProductCategory
    {
        $category = $this->categoryRepository->find($categoryId);
        
        if (!$category) {
            return null;
        }

        // Load relationships
        $category->load(['parent', 'children', 'products']);

        return $category;
    }

    /**
     * Get active categories
     */
    public function getActiveCategories(): Collection
    {
        return $this->categoryRepository->findActive();
    }

    /**
     * Get root categories
     */
    public function getRootCategories(): Collection
    {
        return $this->categoryRepository->findRootCategories();
    }

    /**
     * Get categories by parent
     */
    public function getCategoriesByParent(int $parentId): Collection
    {
        return $this->categoryRepository->findByParent($parentId);
    }

    /**
     * Get category hierarchy
     */
    public function getCategoryHierarchy(): Collection
    {
        return $this->categoryRepository->getCategoryHierarchy();
    }

    /**
     * Get categories with product count
     */
    public function getCategoriesWithProductCount(): Collection
    {
        return $this->categoryRepository->findWithProductCount();
    }

    /**
     * Search categories
     */
    public function searchCategories(string $search): Collection
    {
        return $this->categoryRepository->search($search);
    }

    /**
     * Get categories with filters and pagination
     */
    public function getCategoriesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->categoryRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Get category by slug
     */
    public function getCategoryBySlug(string $slug): ?ProductCategory
    {
        return $this->categoryRepository->findBySlug($slug);
    }

    /**
     * Activate category
     */
    public function activateCategory(int $categoryId): bool
    {
        $category = $this->categoryRepository->update($categoryId, ['is_active' => true]);
        return $category !== null;
    }

    /**
     * Deactivate category
     */
    public function deactivateCategory(int $categoryId): bool
    {
        $category = $this->categoryRepository->update($categoryId, ['is_active' => false]);
        return $category !== null;
    }

    /**
     * Update category sort orders
     */
    public function updateSortOrders(array $sortData): bool
    {
        return $this->categoryRepository->updateSortOrders($sortData);
    }

    /**
     * Delete category with business logic
     */
    public function deleteCategory(int $categoryId): bool
    {
        $category = $this->categoryRepository->find($categoryId);
        
        if (!$category) {
            return false;
        }

        // Business logic: Check if category has products
        if ($category->products()->count() > 0) {
            throw new \Exception('Cannot delete category with existing products. Please move or delete products first.');
        }

        // Business logic: Check if category has children
        if ($category->children()->count() > 0) {
            throw new \Exception('Cannot delete category with subcategories. Please delete subcategories first.');
        }

        return $this->categoryRepository->delete($categoryId);
    }

    /**
     * Move category to different parent
     */
    public function moveCategory(int $categoryId, ?int $newParentId): bool
    {
        $category = $this->categoryRepository->find($categoryId);
        
        if (!$category) {
            return false;
        }

        // Business logic: Prevent circular references
        if ($newParentId && $this->wouldCreateCircularReference($categoryId, $newParentId)) {
            throw new \Exception('Cannot move category: would create circular reference.');
        }

        $updated = $this->categoryRepository->update($categoryId, [
            'parent_id' => $newParentId,
            'sort_order' => $this->categoryRepository->getNextSortOrder($newParentId)
        ]);

        return $updated !== null;
    }

    /**
     * Get category breadcrumb
     */
    public function getCategoryBreadcrumb(int $categoryId): array
    {
        $category = $this->categoryRepository->find($categoryId);
        
        if (!$category) {
            return [];
        }

        $breadcrumb = [];
        $current = $category;

        while ($current) {
            array_unshift($breadcrumb, [
                'id' => $current->id,
                'name' => $current->name,
                'slug' => $current->slug
            ]);
            
            $current = $current->parent;
        }

        return $breadcrumb;
    }

    /**
     * Generate unique slug
     */
    private function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = $this->categoryRepository->model->newQuery();
        $query->where('slug', $slug);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * Check if moving category would create circular reference
     */
    private function wouldCreateCircularReference(int $categoryId, int $newParentId): bool
    {
        $category = $this->categoryRepository->find($newParentId);
        
        while ($category) {
            if ($category->id === $categoryId) {
                return true;
            }
            $category = $category->parent;
        }
        
        return false;
    }
}
