<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">Schedule Follow-up</h3>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <form @submit.prevent="scheduleAppointment" class="space-y-4">
        <!-- Appointment Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Appointment Type</label>
          <select v-model="form.appointment_type" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="follow_up">Follow-up</option>
            <option value="review">Review</option>
            <option value="check_up">Check-up</option>
            <option value="consultation">Consultation</option>
          </select>
        </div>

        <!-- Date -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
          <input 
            v-model="form.date" 
            type="date" 
            :min="minDate"
            class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        <!-- Time -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Time</label>
          <select v-model="form.time" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
            <option value="">Select time</option>
            <option v-for="time in availableTimes" :key="time" :value="time">{{ time }}</option>
          </select>
        </div>

        <!-- Duration -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Duration (minutes)</label>
          <select v-model="form.duration" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="45">45 minutes</option>
            <option value="60">60 minutes</option>
          </select>
        </div>

        <!-- Mode -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Mode</label>
          <div class="flex gap-4">
            <label class="flex items-center">
              <input v-model="form.mode" type="radio" value="in_person" class="mr-2" />
              In-person
            </label>
            <label class="flex items-center">
              <input v-model="form.mode" type="radio" value="video" class="mr-2" />
              Video Call
            </label>
            <label class="flex items-center">
              <input v-model="form.mode" type="radio" value="phone" class="mr-2" />
              Phone Call
            </label>
          </div>
        </div>

        <!-- Priority -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
          <select v-model="form.priority" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="routine">Routine</option>
            <option value="urgent">Urgent</option>
            <option value="emergency">Emergency</option>
          </select>
        </div>

        <!-- Notes -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea 
            v-model="form.notes" 
            rows="3"
            class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Additional notes for the follow-up appointment..."
          ></textarea>
        </div>

        <!-- Quick Reasons -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Quick Reasons</label>
          <div class="flex flex-wrap gap-2">
            <button 
              v-for="reason in quickReasons" 
              :key="reason" 
              type="button"
              @click="addReason(reason)"
              class="px-3 py-1 text-sm bg-gray-100 rounded hover:bg-gray-200"
            >
              {{ reason }}
            </button>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 pt-4">
          <button 
            type="button" 
            @click="closeModal"
            class="px-4 py-2 border rounded hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit"
            :disabled="loading"
            class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center gap-2">
              <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Scheduling...
            </span>
            <span v-else>Schedule Appointment</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import axios from 'axios'

interface Props {
  show: boolean
  patientId?: string | number | null
  consultationId?: string | number | null
  providerId?: string | number | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'close': []
  'scheduled': [appointment: any]
}>()

// Form data
const form = ref({
  appointment_type: 'follow_up',
  date: '',
  time: '',
  duration: '30',
  mode: 'in_person',
  priority: 'routine',
  notes: ''
})

const loading = ref(false)

// Quick reasons for follow-up
const quickReasons = ref([
  'Review test results',
  'Check medication response',
  'Monitor progress',
  'Routine check-up',
  'Discuss treatment plan',
  'Address concerns'
])

// Available time slots
const availableTimes = ref([
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
])

// Computed
const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

// Methods
const closeModal = (): void => {
  emit('close')
  resetForm()
}

const resetForm = (): void => {
  form.value = {
    appointment_type: 'follow_up',
    date: '',
    time: '',
    duration: '30',
    mode: 'in_person',
    priority: 'routine',
    notes: ''
  }
}

const addReason = (reason: string): void => {
  if (form.value.notes) {
    form.value.notes += ', ' + reason
  } else {
    form.value.notes = reason
  }
}

const scheduleAppointment = async (): Promise<void> => {
  try {
    loading.value = true
    
    const payload = {
      patient_id: props.patientId,
      provider_id: props.providerId,
      consultation_id: props.consultationId,
      appointment_date: form.value.date,
      appointment_time: form.value.time,
      duration: parseInt(form.value.duration),
      appointment_type: form.value.appointment_type,
      mode: form.value.mode,
      priority: form.value.priority,
      notes: form.value.notes,
      status: 'scheduled'
    }
    
    const response = await axios.post('/appointments', payload)

    if (response.data.success || response.data.data) {
      emit('scheduled', response.data.data || response.data)
      closeModal()
    } else {
      throw new Error(response.data.message || 'Failed to schedule appointment')
    }
  } catch (error) {
    console.error('Error scheduling appointment:', error)
    // Error will be handled by the parent component
    throw error
  } finally {
    loading.value = false
  }
}

// Watch for date changes to update available times
watch(() => form.value.date, async (newDate) => {
  if (newDate && props.providerId) {
    try {
      // Fetch available time slots for the selected date and provider
      const response = await axios.get(`/providers/${props.providerId}/available-slots`, {
        params: { date: newDate }
      })
      
      if (response.data.success) {
        availableTimes.value = response.data.data || []
      }
    } catch (error) {
      console.error('Error fetching available slots:', error)
      // Fallback to default times
      availableTimes.value = [
        '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
        '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
        '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
      ]
    }
  }
})

// Set default date to tomorrow
const setDefaultDate = (): void => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  form.value.date = tomorrow.toISOString().split('T')[0]
}

// Initialize default date when modal opens
watch(() => props.show, (newShow) => {
  if (newShow) {
    setDefaultDate()
  }
})
</script>
