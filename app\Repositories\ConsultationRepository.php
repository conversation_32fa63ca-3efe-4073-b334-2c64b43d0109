<?php

namespace App\Repositories;

use App\Models\Consultation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class ConsultationRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Consultation $model
     * @return void
     */
    public function __construct(Consultation $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find consultations by patient.
     *
     * @param int $patientId
     * @return Collection
     */
    public function findByPatient(int $patientId): Collection
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Find consultations by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->findBy(['provider_id' => $providerId]);
    }
    
    /**
     * Find consultations by clinic.
     *
     * @param int $clinicId
     * @return Collection
     */
    public function findByClinic(int $clinicId): Collection
    {
        return $this->findBy(['clinic_id' => $clinicId]);
    }
    
    /**
     * Find consultations by status.
     *
     * @param string $status
     * @return Collection
     */
    public function findByStatus(string $status): Collection
    {
        return $this->findBy(['status' => $status]);
    }
    
    /**
     * Find consultations by appointment.
     *
     * @param int $appointmentId
     * @return Consultation|null
     */
    public function findByAppointment(int $appointmentId): ?Consultation
    {
        return $this->findOneBy(['appointment_id' => $appointmentId]);
    }
    
    /**
     * Find consultations by date range.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByDateRange(Carbon $startDate, Carbon $endDate, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->whereBetween('consultation_date', [$startDate, $endDate]);
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Find recent consultations for a patient.
     *
     * @param int $patientId
     * @param int $limit
     * @return Collection
     */
    public function findRecentByPatient(int $patientId, int $limit = 10): Collection
    {
        $query = $this->model->newQuery();
        $query->where('patient_id', $patientId)
              ->orderBy('consultation_date', 'desc')
              ->limit($limit);
        
        return $query->get();
    }
    
    /**
     * Find today's consultations for a provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findTodayByProvider(int $providerId): Collection
    {
        $query = $this->model->newQuery();
        $query->where('provider_id', $providerId)
              ->whereDate('consultation_date', today());
        
        return $query->get();
    }
    
    /**
     * Find in-progress consultations.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findInProgress(?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', 'in_progress');
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->get();
    }
    
    /**
     * Find telemedicine consultations.
     *
     * @param int|null $clinicId
     * @return Collection
     */
    public function findTelemedicine(?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('is_telemedicine', true);
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        return $query->get();
    }
    
    /**
     * Get consultations with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['patient.user', 'provider.user', 'appointment']);
        
        // Apply filters
        if (!empty($filters['patient_id'])) {
            $query->where('patient_id', $filters['patient_id']);
        }
        
        if (!empty($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }
        
        if (!empty($filters['clinic_id'])) {
            $query->where('clinic_id', $filters['clinic_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['consultation_type'])) {
            $query->where('consultation_type', $filters['consultation_type']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->whereDate('consultation_date', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('consultation_date', '<=', $filters['date_to']);
        }
        
        if (isset($filters['is_telemedicine'])) {
            $query->where('is_telemedicine', $filters['is_telemedicine']);
        }
        
        return $query->orderBy('consultation_date', 'desc')->paginate($perPage);
    }
    
    /**
     * Get consultation statistics.
     *
     * @param int|null $clinicId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getStatistics(?int $clinicId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = $this->model->newQuery();
        
        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('consultation_date', [$startDate, $endDate]);
        }
        
        $total = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $inProgress = $query->where('status', 'in_progress')->count();
        $telemedicine = $query->where('is_telemedicine', true)->count();
        
        return [
            'total' => $total,
            'completed' => $completed,
            'in_progress' => $inProgress,
            'draft' => $query->where('status', 'draft')->count(),
            'cancelled' => $query->where('status', 'cancelled')->count(),
            'telemedicine' => $telemedicine,
            'in_person' => $total - $telemedicine,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }
    
    /**
     * Update consultation status.
     *
     * @param int $consultationId
     * @param string $status
     * @return bool
     */
    public function updateStatus(int $consultationId, string $status): bool
    {
        $data = ['status' => $status];
        
        if ($status === 'completed') {
            $data['completed_at'] = now();
        } elseif ($status === 'in_progress') {
            $data['started_at'] = now();
        }
        
        $consultation = $this->update($consultationId, $data);
        return $consultation !== null;
    }
}
