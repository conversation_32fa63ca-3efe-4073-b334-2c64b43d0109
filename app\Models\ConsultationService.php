<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'consultation_id',
        'service_id',
        'service_name',
        'description',
        'unit_price',
        'quantity',
        'total_price',
        'is_billable',
        'is_billed',
        'bill_id',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'quantity' => 'integer',
        'total_price' => 'decimal:2',
        'is_billable' => 'boolean',
        'is_billed' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($consultationService) {
            $consultationService->total_price = $consultationService->unit_price * $consultationService->quantity;
        });
    }

    /**
     * Get the consultation that owns the service.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the service associated with the consultation service.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the bill associated with the consultation service.
     */
    public function bill()
    {
        return $this->belongsTo(Bill::class);
    }

    /**
     * Scope to get billable services.
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * Scope to get unbilled services.
     */
    public function scopeUnbilled($query)
    {
        return $query->where('is_billable', true)->where('is_billed', false);
    }

    /**
     * Mark service as billed.
     */
    public function markAsBilled(int $billId): void
    {
        $this->update([
            'is_billed' => true,
            'bill_id' => $billId,
        ]);
    }

    /**
     * Get formatted unit price.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '£' . number_format($this->unit_price, 2);
    }

    /**
     * Get formatted total price.
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return '£' . number_format($this->total_price, 2);
    }
}
