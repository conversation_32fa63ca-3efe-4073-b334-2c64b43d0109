<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-lg w-full mx-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium">Select Template</h3>
      </div>

      <div class="p-4">
        <!-- Template Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Template Format</label>
          <div v-if="isLoading" class="flex items-center space-x-2 p-2">
            <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            <span>Loading templates...</span>
          </div>
          <select v-else v-model="selectedTemplate"
            class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500" :disabled="isGenerating">
            <option value="">Choose a template</option>
            <option v-for="template in templates" :key="template.key" :value="template.key">
              {{ template.name }} ({{ template.category_display }})
            </option>
          </select>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end gap-2">
          <button type="button" class="px-4 py-2 text-sm border rounded hover:bg-gray-50" @click="$emit('close')">
            Cancel
          </button>
          <button type="button"
            class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50"
            :disabled="!selectedTemplate || isGenerating" @click="handleGenerate">
            <template v-if="isGenerating">
              <span class="inline-block animate-spin mr-2">↻</span>
              Generating...
            </template>
            <template v-else>Generate</template>
          </button>
        </div>
      </div>
    </div>

    <!-- Summary Modal -->
    <LetterSummaryModal 
      v-if="showSummaryModal" 
      :show="showSummaryModal" 
      :summary="generatedSummary"
      :template="selectedTemplateData" 
      @close="showSummaryModal = false" 
      @save="handleSave" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import axios from 'axios'
import LetterSummaryModal from './LetterSummaryModal.vue'

interface Template {
  id: number
  name: string
  category: string
  category_display: string
  content: string
  type: string
}

interface Props {
  show: boolean
  encounterId: string | number
  consultationData: Record<string, any>
  patientDetails: {
    patient_id: number
    name: string
    email?: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'close': []
  'select': [template: any]
  'reload-files': []
}>()

const toast = useToast()

// State
const selectedTemplate = ref('')
const isGenerating = ref(false)
const showSummaryModal = ref(false)
const generatedSummary = ref('')
const templates = ref<Template[]>([])
const selectedTemplateData = ref<Template | null>(null)
const isLoading = ref(true)

// Methods
const loadTemplates = async (): Promise<void> => {
  isLoading.value = true
  try {
    const response = await axios.get('/letter-templates/available')
    if (response.data?.success) {
      templates.value = response.data.data || []
    } else {
      throw new Error(response.data?.message || 'Failed to load templates')
    }
  } catch (error: any) {
    console.error('Failed to load templates:', error)
    toast.error('Failed to load templates')
  } finally {
    isLoading.value = false
  }
}

const handleGenerate = async (): Promise<void> => {
  if (!selectedTemplate.value) return

  isGenerating.value = true
  try {
    // Find selected template data
    selectedTemplateData.value = templates.value.find(t => t.key === selectedTemplate.value) || null
    
    if (!selectedTemplateData.value) {
      throw new Error('Template not found')
    }

    // Prepare payload for template processing
    const payload = {
      template_content: selectedTemplateData.value.content,
      consultation_id: props.encounterId,
      patient_id: props.patientDetails.patient_id,
      consultation_data: props.consultationData,
    }

    const response = await axios.post('/medical-letters/process-template', payload)

    if (response.data?.success) {
      generatedSummary.value = response.data.data.summary || response.data.data.processed_content
      showSummaryModal.value = true
    } else {
      throw new Error(response.data?.message || 'Failed to generate letter')
    }
  } catch (error: any) {
    console.error('Failed to generate summary:', error)
    toast.error('Failed to generate letter from template')
  } finally {
    isGenerating.value = false
  }
}

const handleSave = async (editedContent: string): Promise<void> => {
  try {
    if (!selectedTemplateData.value) {
      throw new Error('No template selected')
    }

    // Generate letter from template
    const payload = {
      template_key: selectedTemplateData.value.key,
      consultation_id: props.encounterId,
      patient_id: props.patientDetails.patient_id,
      consultation_data: props.consultationData,
    }

    const response = await axios.post('/medical-letters/generate-from-template', payload)

    if (response.data?.success) {
      // Update the generated letter content with edited content
      const letterId = response.data.data.letter.id
      await axios.put(`/medical-letters/${letterId}`, {
        content: editedContent,
        status: 'final'
      })

      toast.success('Letter saved successfully')
      showSummaryModal.value = false
      emit('close')
      emit('select', response.data.data.letter)
      emit('reload-files')
    } else {
      throw new Error(response.data?.message || 'Failed to save letter')
    }
  } catch (error: any) {
    console.error('Failed to save letter:', error)
    toast.error('Failed to save letter')
  }
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})
</script>
