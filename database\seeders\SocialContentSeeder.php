<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SocialContent;
use App\Models\User;
use Illuminate\Support\Str;

class SocialContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create a user for the posts
        $user = User::first();
        if (!$user) {
            $user = User::factory()->create([
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'role' => 'provider'
            ]);
        }

        $posts = [
            [
                'caption' => 'Understanding the importance of regular exercise for cardiovascular health. Just 30 minutes of moderate activity daily can significantly reduce your risk of heart disease.',
                'health_topics' => ['cardiovascular', 'exercise', 'prevention'],
                'content_type' => 'text',
                'media_url' => null
            ],
            [
                'caption' => 'Mental health tip: Practice mindfulness meditation for just 10 minutes daily. It can help reduce stress, improve focus, and enhance overall well-being.',
                'health_topics' => ['mental health', 'meditation', 'stress management'],
                'content_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center'
            ],
            [
                'caption' => 'Nutrition fact: Eating a variety of colorful fruits and vegetables ensures you get a wide range of vitamins, minerals, and antioxidants essential for optimal health.',
                'health_topics' => ['nutrition', 'diet', 'vitamins'],
                'content_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=600&h=400&fit=crop&crop=center'
            ],
            [
                'caption' => 'Sleep hygiene matters! Aim for 7-9 hours of quality sleep each night. Create a consistent bedtime routine and keep your bedroom cool, dark, and quiet.',
                'health_topics' => ['sleep', 'wellness', 'health tips'],
                'content_type' => 'text',
                'media_url' => null
            ],
            [
                'caption' => 'Hydration is key to good health. Drink at least 8 glasses of water daily. Your body needs water for digestion, circulation, and temperature regulation.',
                'health_topics' => ['hydration', 'wellness', 'health tips'],
                'content_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=600&h=400&fit=crop&crop=center'
            ],
            [
                'caption' => 'The benefits of regular health checkups cannot be overstated. Early detection of health issues can lead to better treatment outcomes and improved quality of life.',
                'health_topics' => ['preventive care', 'health checkups', 'early detection'],
                'content_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop&crop=center'
            ],
            [
                'caption' => 'Managing stress is crucial for overall health. Try deep breathing exercises, yoga, or spending time in nature to help reduce stress levels.',
                'health_topics' => ['stress management', 'mental health', 'wellness'],
                'content_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop&crop=center'
            ],
            [
                'caption' => 'Did you know that laughter can boost your immune system? It releases endorphins and reduces stress hormones, contributing to better overall health.',
                'health_topics' => ['mental health', 'immune system', 'wellness'],
                'content_type' => 'text',
                'media_url' => null
            ]
        ];

        foreach ($posts as $index => $postData) {
            SocialContent::create([
                'source' => 'internal',
                'source_id' => (string) Str::uuid(),
                'content_type' => $postData['content_type'],
                'media_url' => $postData['media_url'],
                'caption' => $postData['caption'],
                'health_topics' => $postData['health_topics'],
                'relevance_score' => rand(80, 100) / 100,
                'engagement_metrics' => [
                    'likes' => rand(5, 50),
                    'shares' => rand(1, 10),
                    'saves' => rand(2, 20),
                    'comments' => rand(0, 15),
                ],
                'filtered_status' => 'approved',
                'published_at' => now()->subMinutes(rand(1, 1440)), // Random time in last 24 hours
                'user_id' => $user->id,
            ]);
        }

        $this->command->info('Created ' . count($posts) . ' sample social content posts');

        // Create some sample stories
        $stories = [
            [
                'caption' => 'Morning workout complete! 💪',
                'media_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=600&fit=crop'
            ],
            [
                'caption' => 'Healthy breakfast to start the day right! 🥗',
                'media_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=600&fit=crop'
            ],
            [
                'caption' => 'Meditation session in the park 🧘‍♀️',
                'media_type' => 'image',
                'media_url' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop'
            ]
        ];

        foreach ($stories as $storyData) {
            \App\Models\Story::create([
                'user_id' => $user->id,
                'media_url' => $storyData['media_url'],
                'media_type' => $storyData['media_type'],
                'caption' => $storyData['caption'],
                'expires_at' => now()->addHours(24),
                'is_active' => true,
                'viewers' => [],
            ]);
        }

        $this->command->info('Created ' . count($stories) . ' sample stories');
    }
}
