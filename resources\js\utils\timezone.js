/**
 * Timezone and Daylight Savings Time Utilities
 * 
 * This utility handles timezone conversion and daylight savings time automatically
 * to ensure appointment times are consistent across different time periods.
 */

/**
 * Get the user's current timezone
 * @returns {string} The timezone identifier (e.g., 'America/New_York')
 */
export function getUserTimezone() {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Convert appointment time to user's local timezone
 * @param {string} appointmentDate - The appointment date (YYYY-MM-DD)
 * @param {string} appointmentTime - The appointment time (HH:mm)
 * @param {string} fromTimezone - The timezone the appointment is stored in (optional, defaults to user's timezone)
 * @returns {Object} Object with local date and time
 */
export function convertAppointmentToLocal(appointmentDate, appointmentTime, fromTimezone = null) {
    if (!appointmentDate || !appointmentTime) {
        return { date: appointmentDate, time: appointmentTime };
    }

    try {
        // Create a date object in the source timezone
        const appointmentDateTime = new Date(`${appointmentDate}T${appointmentTime}:00`);
        
        // If no source timezone specified, assume it's already in user's timezone
        if (!fromTimezone) {
            return {
                date: appointmentDate,
                time: appointmentTime,
                datetime: appointmentDateTime
            };
        }

        // Convert to user's local timezone
        const userTimezone = getUserTimezone();
        const localDateTime = new Date(appointmentDateTime.toLocaleString('en-US', { timeZone: userTimezone }));
        
        return {
            date: localDateTime.toISOString().split('T')[0],
            time: localDateTime.toTimeString().slice(0, 5),
            datetime: localDateTime
        };
    } catch (error) {
        console.warn('Error converting appointment time:', error);
        return { date: appointmentDate, time: appointmentTime };
    }
}

/**
 * Convert local time to appointment storage timezone
 * @param {string} localDate - The local date (YYYY-MM-DD)
 * @param {string} localTime - The local time (HH:mm)
 * @param {string} toTimezone - The timezone to convert to (optional, defaults to user's timezone)
 * @returns {Object} Object with converted date and time
 */
export function convertLocalToAppointment(localDate, localTime, toTimezone = null) {
    if (!localDate || !localTime) {
        return { date: localDate, time: localTime };
    }

    try {
        // Create a date object in user's local timezone
        const localDateTime = new Date(`${localDate}T${localTime}:00`);
        
        // If no target timezone specified, return as-is
        if (!toTimezone) {
            return {
                date: localDate,
                time: localTime,
                datetime: localDateTime
            };
        }

        // Convert to target timezone
        const targetDateTime = new Date(localDateTime.toLocaleString('en-US', { timeZone: toTimezone }));
        
        return {
            date: targetDateTime.toISOString().split('T')[0],
            time: targetDateTime.toTimeString().slice(0, 5),
            datetime: targetDateTime
        };
    } catch (error) {
        console.warn('Error converting local time:', error);
        return { date: localDate, time: localTime };
    }
}

/**
 * Format appointment time for display, handling timezone conversion
 * @param {string} appointmentDate - The appointment date
 * @param {string} appointmentTime - The appointment time
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date/time string
 */
export function formatAppointmentTime(appointmentDate, appointmentTime, options = {}) {
    const {
        includeTimezone = true,
        format = 'long' // 'long', 'short', 'time-only'
    } = options;

    if (!appointmentDate || !appointmentTime) {
        return 'N/A';
    }

    try {
        const dateTime = new Date(`${appointmentDate}T${appointmentTime}:00`);
        const userTimezone = getUserTimezone();
        
        let formatOptions = {};
        
        if (format === 'time-only') {
            formatOptions = {
                hour: '2-digit',
                minute: '2-digit',
                timeZone: userTimezone
            };
        } else if (format === 'short') {
            formatOptions = {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: userTimezone
            };
        } else {
            formatOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: userTimezone
            };
        }

        if (includeTimezone && format !== 'time-only') {
            formatOptions.timeZoneName = 'short';
        }

        return dateTime.toLocaleString('en-US', formatOptions);
    } catch (error) {
        console.warn('Error formatting appointment time:', error);
        return `${appointmentDate} ${appointmentTime}`;
    }
}

/**
 * Check if daylight savings time is currently active
 * @param {string} timezone - The timezone to check (optional, defaults to user's timezone)
 * @returns {boolean} True if DST is active
 */
export function isDaylightSavingsActive(timezone = null) {
    const targetTimezone = timezone || getUserTimezone();
    const now = new Date();
    
    try {
        // Create dates for January (definitely standard time) and July (definitely DST if applicable)
        const january = new Date(now.getFullYear(), 0, 1);
        const july = new Date(now.getFullYear(), 6, 1);
        
        const januaryOffset = january.toLocaleString('en', { timeZone: targetTimezone, timeZoneName: 'longOffset' });
        const julyOffset = july.toLocaleString('en', { timeZone: targetTimezone, timeZoneName: 'longOffset' });
        const currentOffset = now.toLocaleString('en', { timeZone: targetTimezone, timeZoneName: 'longOffset' });
        
        // If current offset matches July (summer) and is different from January, DST is active
        return currentOffset === julyOffset && januaryOffset !== julyOffset;
    } catch (error) {
        console.warn('Error checking daylight savings:', error);
        return false;
    }
}

/**
 * Get timezone offset in hours
 * @param {string} timezone - The timezone (optional, defaults to user's timezone)
 * @returns {number} Offset in hours
 */
export function getTimezoneOffset(timezone = null) {
    const targetTimezone = timezone || getUserTimezone();
    const now = new Date();
    
    try {
        const utc = new Date(now.toUTCString());
        const local = new Date(now.toLocaleString('en-US', { timeZone: targetTimezone }));
        return (local.getTime() - utc.getTime()) / (1000 * 60 * 60);
    } catch (error) {
        console.warn('Error getting timezone offset:', error);
        return 0;
    }
}

/**
 * Auto-adjust appointment times for daylight savings changes
 * This function can be called when the app loads to adjust any stored times
 * @param {Array} appointments - Array of appointment objects
 * @param {boolean} forceConversion - Whether to force timezone conversion (default: false)
 * @returns {Array} Array of appointments with adjusted times
 */
export function adjustAppointmentsForDST(appointments, forceConversion = false) {
    // If not forcing conversion, return appointments as-is
    // This assumes appointments are already stored in the correct local timezone
    if (!forceConversion) {
        return appointments;
    }

    return appointments.map(appointment => {
        if (!appointment.date || !appointment.time_slot?.start_time) {
            return appointment;
        }

        try {
            // Convert appointment time to ensure it's in the correct timezone
            const adjusted = convertAppointmentToLocal(
                appointment.date,
                appointment.time_slot.start_time
            );

            return {
                ...appointment,
                time_slot: {
                    ...appointment.time_slot,
                    start_time: adjusted.time,
                    end_time: appointment.time_slot.end_time ? 
                        convertAppointmentToLocal(appointment.date, appointment.time_slot.end_time).time :
                        appointment.time_slot.end_time
                }
            };
        } catch (error) {
            console.warn('Error adjusting appointment for DST:', error);
            return appointment;
        }
    });
}