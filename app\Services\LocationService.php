<?php

namespace App\Services;

use App\Models\Country;
use App\Models\Product;
use App\Models\Service;
use App\Models\Provider;
use Illuminate\Database\Eloquent\Builder;

class LocationService
{
    protected $countryFilterService;

    public function __construct()
    {
        $this->countryFilterService = new CountryFilterService();
    }

    /**
     * Filter products by country availability
     */
    public function filterProductsByCountry(Builder $query, string $countryCode): Builder
    {
        return $this->countryFilterService->filterProductsByCountry($query, $countryCode);
    }

    /**
     * Filter services by country availability
     */
    public function filterServicesByCountry(Builder $query, string $countryCode): Builder
    {
        return $this->countryFilterService->filterServicesByCountry($query, $countryCode);
    }

    /**
     * Filter providers by country
     */
    public function filterProvidersByCountry(Builder $query, string $countryCode): Builder
    {
        return $query->where('country_code', $countryCode);
    }

    /**
     * Get products available in a specific country with country-specific pricing
     */
    public function getProductsForCountry(string $countryCode, array $filters = [])
    {
        $query = Product::with(['countries' => function ($q) use ($countryCode) {
            $q->where('country_code', $countryCode);
        }]);

        // Apply country filter
        $query = $this->filterProductsByCountry($query, $countryCode);

        // Apply additional filters
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_featured'])) {
            $query->where('is_featured', $filters['is_featured']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->active()->approved()->get()->map(function ($product) use ($countryCode) {
            return $this->formatProductWithCountryPricing($product, $countryCode);
        });
    }

    /**
     * Get services available in a specific country with country-specific pricing
     */
    public function getServicesForCountry(string $countryCode, array $filters = [])
    {
        $query = Service::with([
            'countries' => function ($q) use ($countryCode) {
                $q->where('country_code', $countryCode);
            },
            'provider.user',
            'provider.clinic'
        ]);

        // Apply country filter
        $query = $this->filterServicesByCountry($query, $countryCode);

        // Apply additional filters
        if (isset($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_telemedicine'])) {
            $query->where('is_telemedicine', $filters['is_telemedicine']);
        }

        return $query->where('active', true)->approved()->get()->map(function ($service) use ($countryCode) {
            return $this->formatServiceWithCountryPricing($service, $countryCode);
        });
    }

    /**
     * Get providers available in a specific country
     */
    public function getProvidersForCountry(string $countryCode, array $filters = [])
    {
        $query = Provider::with(['user', 'clinic', 'country']);

        // Apply country filter
        $query = $this->filterProvidersByCountry($query, $countryCode);

        // Apply additional filters
        if (isset($filters['specialization'])) {
            $query->where('specialization', $filters['specialization']);
        }

        if (isset($filters['verification_status'])) {
            $query->where('verification_status', $filters['verification_status']);
        }

        return $query->get();
    }

    /**
     * Format product with country-specific pricing
     */
    private function formatProductWithCountryPricing(Product $product, string $countryCode): array
    {
        $country = Country::where('code', $countryCode)->first();
        $countryProduct = $product->countries->first();

        $price = $countryProduct && $countryProduct->pivot->price_override 
            ? $countryProduct->pivot->price_override 
            : $product->price;

        $salePrice = $countryProduct && $countryProduct->pivot->sale_price_override 
            ? $countryProduct->pivot->sale_price_override 
            : $product->sale_price;

        return [
            'id' => $product->id,
            'name' => $product->name,
            'slug' => $product->slug,
            'description' => $product->description,
            'short_description' => $product->short_description,
            'type' => $product->type,
            'price' => $price,
            'sale_price' => $salePrice,
            'effective_price' => $salePrice ?? $price,
            'formatted_price' => $country ? $country->currency_symbol . number_format($salePrice ?? $price, 2) : '$' . number_format($salePrice ?? $price, 2),
            'currency' => $country ? [
                'code' => $country->currency_code,
                'symbol' => $country->currency_symbol,
                'name' => $country->currency_name,
            ] : null,
            'featured_image' => $product->featured_image,
            'is_featured' => $product->is_featured,
            'is_active' => $product->is_active,
            'created_at' => $product->created_at,
            'updated_at' => $product->updated_at,
        ];
    }

    /**
     * Format service with country-specific pricing
     */
    private function formatServiceWithCountryPricing(Service $service, string $countryCode): array
    {
        $country = Country::where('code', $countryCode)->first();
        $countryService = $service->countries->first();

        $price = $countryService && $countryService->pivot->price_override 
            ? $countryService->pivot->price_override 
            : $service->price;

        return [
            'id' => $service->id,
            'name' => $service->name,
            'description' => $service->description,
            'duration' => $service->duration,
            'price' => $price,
            'formatted_price' => $country ? $country->currency_symbol . number_format($price, 2) : '$' . number_format($price, 2),
            'currency' => $country ? [
                'code' => $country->currency_code,
                'symbol' => $country->currency_symbol,
                'name' => $country->currency_name,
            ] : null,
            'category' => $service->category,
            'is_telemedicine' => $service->is_telemedicine,
            'is_home_visit' => $service->is_home_visit ?? false,
            'active' => $service->active,
            'provider_id' => $service->provider_id,
            'provider' => $service->provider ? [
                'id' => $service->provider->id,
                'user' => $service->provider->user ? [
                    'id' => $service->provider->user->id,
                    'name' => $service->provider->user->name,
                    'email' => $service->provider->user->email,
                ] : null,
                'specialization' => $service->provider->specialization,
                'bio' => $service->provider->bio,
                'gender' => $service->provider->gender,
                'languages' => $service->provider->languages,
                'verification_status' => $service->provider->verification_status,
                'practice_locations' => $service->provider->practice_locations,
                'rating' => $service->provider->rating,
            ] : null,
            'created_at' => $service->created_at,
            'updated_at' => $service->updated_at,
        ];
    }

    /**
     * Get country by user's location or default
     */
    public function getCountryForUser($user = null): ?Country
    {
        if ($user && $user->country_code) {
            return Country::where('code', $user->country_code)->activeAndSupported()->first();
        }

        // Default to US if no user country
        return Country::where('code', 'US')->activeAndSupported()->first();
    }

    /**
     * Get supported payment gateways for a country
     */
    public function getPaymentGatewaysForCountry(string $countryCode): array
    {
        $country = Country::where('code', $countryCode)->activeAndSupported()->first();
        
        return $country ? $country->supported_payment_gateways : ['stripe'];
    }

    /**
     * Get currency information for a country
     */
    public function getCurrencyForCountry(string $countryCode): ?array
    {
        $country = Country::where('code', $countryCode)->activeAndSupported()->first();
        
        if (!$country) {
            return null;
        }

        return [
            'code' => $country->currency_code,
            'symbol' => $country->currency_symbol,
            'name' => $country->currency_name,
        ];
    }
}
