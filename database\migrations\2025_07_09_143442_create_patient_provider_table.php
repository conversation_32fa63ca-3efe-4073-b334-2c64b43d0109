<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_provider', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->enum('relationship_type', ['created', 'appointment', 'assigned'])->default('assigned');
            $table->foreignId('assigned_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('assignment_reason')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('assigned_at')->useCurrent();
            $table->timestamp('deactivated_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['patient_id', 'provider_id']);
            $table->index(['provider_id', 'is_active']);
            $table->index('relationship_type');
            $table->index('assigned_at');

            // Unique constraint to prevent duplicate active relationships
            $table->unique(['patient_id', 'provider_id', 'relationship_type'], 'unique_active_patient_provider');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_provider');
    }
};
