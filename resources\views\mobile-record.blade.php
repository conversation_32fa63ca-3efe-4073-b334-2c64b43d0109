<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Mobile Recording - Medroid EHR</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .recording-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .wave-animation {
            animation: wave 1s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { height: 10px; }
            50% { height: 30px; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-blue-600 text-white rounded-lg p-4 mb-4">
                <h1 class="text-xl font-bold">Medroid EHR</h1>
                <p class="text-blue-100">Mobile Recording</p>
            </div>
            <p class="text-gray-600 text-sm">Record audio for your consultation session</p>
        </div>

        <!-- Session Status -->
        <div id="session-status" class="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Session Status:</span>
                <span id="status-indicator" class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Checking...
                </span>
            </div>
            <div id="session-timer" class="text-xs text-gray-500 mt-2"></div>
        </div>

        <!-- Recording Interface -->
        <div id="recording-interface" class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <!-- Recording Status Display -->
            <div id="recording-status" class="text-center mb-6">
                <div id="idle-state" class="block">
                    <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600">Ready to record</p>
                </div>

                <div id="recording-state" class="hidden">
                    <div class="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 recording-animation">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-red-600 font-medium">Recording...</p>
                    <div class="flex justify-center space-x-1 mt-2">
                        <div class="w-1 bg-red-500 rounded-full wave-animation" style="animation-delay: 0s;"></div>
                        <div class="w-1 bg-red-500 rounded-full wave-animation" style="animation-delay: 0.2s;"></div>
                        <div class="w-1 bg-red-500 rounded-full wave-animation" style="animation-delay: 0.4s;"></div>
                        <div class="w-1 bg-red-500 rounded-full wave-animation" style="animation-delay: 0.6s;"></div>
                    </div>
                    <div id="recording-timer" class="text-sm text-gray-600 mt-2">00:00</div>
                </div>

                <div id="completed-state" class="hidden">
                    <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <p class="text-green-600 font-medium">Recording completed!</p>
                </div>
            </div>

            <!-- Recording Controls -->
            <div class="flex justify-center space-x-4">
                <button id="record-btn" class="bg-red-500 hover:bg-red-600 text-white rounded-full w-16 h-16 flex items-center justify-center transition-colors">
                    <svg id="record-icon" class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                    <svg id="stop-icon" class="w-8 h-8 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- File Upload Alternative -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h3 class="font-medium text-gray-900 mb-4">Or Upload Audio File</h3>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input type="file" id="audio-upload" accept="audio/*" class="hidden">
                <label for="audio-upload" class="cursor-pointer">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="text-sm text-gray-600">Click to select an audio file</p>
                    <p class="text-xs text-gray-500 mt-1">MP3, WAV, M4A, etc.</p>
                </label>
            </div>
            <div id="upload-progress" class="hidden mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2 text-center">Uploading...</p>
            </div>
        </div>

        <!-- Messages -->
        <div id="messages" class="space-y-2"></div>

        <!-- Footer -->
        <div class="text-center text-xs text-gray-500 mt-8">
            <p>Secure recording session</p>
            <p>Session ID: {{ $sessionId }}</p>
        </div>
    </div>

    <script>
        // Session and recording state
        let sessionId = '{{ $sessionId }}';
        let mediaRecorder = null;
        let audioChunks = [];
        let recordingStartTime = null;
        let recordingTimer = null;
        let sessionCheckTimer = null;

        // DOM elements
        const recordBtn = document.getElementById('record-btn');
        const recordIcon = document.getElementById('record-icon');
        const stopIcon = document.getElementById('stop-icon');
        const idleState = document.getElementById('idle-state');
        const recordingState = document.getElementById('recording-state');
        const completedState = document.getElementById('completed-state');
        const recordingTimerEl = document.getElementById('recording-timer');
        const statusIndicator = document.getElementById('status-indicator');
        const sessionTimer = document.getElementById('session-timer');
        const audioUpload = document.getElementById('audio-upload');
        const uploadProgress = document.getElementById('upload-progress');
        const progressBar = document.getElementById('progress-bar');
        const messagesContainer = document.getElementById('messages');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkSessionStatus();
            startSessionMonitoring();
            setupEventListeners();
        });

        function setupEventListeners() {
            recordBtn.addEventListener('click', toggleRecording);
            audioUpload.addEventListener('change', handleFileUpload);
        }

        function checkSessionStatus() {
            fetch('/consultations/mobile-recording/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ session_id: sessionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateSessionStatus(data.data);
                } else {
                    showMessage('Session expired or invalid', 'error');
                    statusIndicator.textContent = 'Expired';
                    statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
                }
            })
            .catch(error => {
                console.error('Error checking session:', error);
                showMessage('Failed to check session status', 'error');
            });
        }

        function updateSessionStatus(sessionData) {
            if (sessionData.time_remaining > 0) {
                statusIndicator.textContent = 'Active';
                statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
                
                const minutes = Math.floor(sessionData.time_remaining / 60);
                const seconds = sessionData.time_remaining % 60;
                sessionTimer.textContent = `Expires in ${minutes}:${seconds.toString().padStart(2, '0')}`;
            } else {
                statusIndicator.textContent = 'Expired';
                statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
                sessionTimer.textContent = 'Session has expired';
                recordBtn.disabled = true;
            }
        }

        function startSessionMonitoring() {
            sessionCheckTimer = setInterval(checkSessionStatus, 30000); // Check every 30 seconds
        }

        async function toggleRecording() {
            if (!mediaRecorder || mediaRecorder.state === 'inactive') {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    uploadRecording(audioBlob);
                };

                mediaRecorder.start();
                recordingStartTime = Date.now();
                
                // Update UI
                idleState.classList.add('hidden');
                recordingState.classList.remove('hidden');
                recordIcon.classList.add('hidden');
                stopIcon.classList.remove('hidden');
                recordBtn.className = 'bg-gray-500 hover:bg-gray-600 text-white rounded-full w-16 h-16 flex items-center justify-center transition-colors';

                // Start recording timer
                recordingTimer = setInterval(updateRecordingTimer, 1000);
                
                showMessage('Recording started', 'success');
            } catch (error) {
                console.error('Error starting recording:', error);
                showMessage('Failed to start recording. Please check microphone permissions.', 'error');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                clearInterval(recordingTimer);
                
                // Update UI
                recordingState.classList.add('hidden');
                completedState.classList.remove('hidden');
                
                showMessage('Recording completed. Uploading...', 'info');
            }
        }

        function updateRecordingTimer() {
            if (recordingStartTime) {
                const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                recordingTimerEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                uploadProgress.classList.remove('hidden');
                uploadRecording(file);
            }
        }

        function uploadRecording(audioBlob) {
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'recording.wav');
            formData.append('session_id', sessionId);

            fetch('/consultations/mobile-recording/upload', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Recording uploaded successfully!', 'success');
                    // Update session to mark as processed
                    markSessionProcessed();
                } else {
                    showMessage('Failed to upload recording: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showMessage('Failed to upload recording', 'error');
            })
            .finally(() => {
                uploadProgress.classList.add('hidden');
                progressBar.style.width = '0%';
            });
        }

        function markSessionProcessed() {
            // This would update the session status to indicate recording is ready for processing
            statusIndicator.textContent = 'Completed';
            statusIndicator.className = 'px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
            sessionTimer.textContent = 'Recording ready for processing';
        }

        function showMessage(message, type) {
            const messageEl = document.createElement('div');
            const bgColor = type === 'error' ? 'bg-red-100 text-red-800' : 
                           type === 'success' ? 'bg-green-100 text-green-800' : 
                           'bg-blue-100 text-blue-800';
            
            messageEl.className = `p-3 rounded-lg ${bgColor} text-sm`;
            messageEl.textContent = message;
            
            messagesContainer.appendChild(messageEl);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                messageEl.remove();
            }, 5000);
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (sessionCheckTimer) clearInterval(sessionCheckTimer);
            if (recordingTimer) clearInterval(recordingTimer);
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
            }
        });
    </script>
</body>
</html>
