<?php

namespace App\Services;

use App\Models\Country;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Log;

class PaymentGatewayService
{
    protected $stripeService;
    protected $locationService;

    public function __construct(StripeService $stripeService, LocationService $locationService)
    {
        $this->stripeService = $stripeService;
        $this->locationService = $locationService;
    }

    /**
     * Create a payment intent using the appropriate gateway for the user's country
     */
    public function createPaymentIntent(User $user, SubscriptionPlan $plan, ?string $countryCode = null): array
    {
        try {
            // Determine country
            $country = $this->getCountryForPayment($user, $countryCode);
            
            if (!$country) {
                return [
                    'success' => false,
                    'message' => 'Country not supported for payments'
                ];
            }

            // Get supported payment gateways for the country
            $supportedGateways = $country->supported_payment_gateways;
            
            // For now, we'll use <PERSON><PERSON> as the primary gateway
            // In the future, you can add logic to choose the best gateway
            if (in_array('stripe', $supportedGateways)) {
                return $this->createStripePaymentIntent($user, $plan, $country);
            }

            // Add other payment gateways here
            if (in_array('razorpay', $supportedGateways)) {
                return $this->createRazorpayPaymentIntent($user, $plan, $country);
            }

            if (in_array('paystack', $supportedGateways)) {
                return $this->createPaystackPaymentIntent($user, $plan, $country);
            }

            return [
                'success' => false,
                'message' => 'No supported payment gateway available for this country'
            ];

        } catch (\Exception $e) {
            Log::error('Payment gateway error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Payment processing failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get the appropriate country for payment processing
     */
    private function getCountryForPayment(User $user, ?string $countryCode = null): ?Country
    {
        if ($countryCode) {
            return Country::where('code', $countryCode)->activeAndSupported()->first();
        }

        return $this->locationService->getCountryForUser($user);
    }

    /**
     * Create Stripe payment intent with country-specific currency
     */
    private function createStripePaymentIntent(User $user, SubscriptionPlan $plan, Country $country): array
    {
        try {
            // Convert price to country currency if needed
            $amount = $this->convertPriceToCountryCurrency($plan->price, $plan->currency, $country->currency_code);
            
            // Use the existing StripeService but with country-specific parameters
            $stripeCustomer = $this->stripeService->getOrCreateCustomer($user);

            $paymentIntent = $this->stripeService->stripe->paymentIntents->create([
                'amount' => (int)($amount * 100), // Convert to cents
                'currency' => strtolower($country->currency_code),
                'customer' => $stripeCustomer->id,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'plan_slug' => $plan->slug,
                    'country_code' => $country->code,
                ],
                'description' => "Subscription to {$plan->name} plan",
            ]);

            return [
                'success' => true,
                'gateway' => 'stripe',
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'currency' => $country->currency_code,
                'amount' => $amount,
                'formatted_amount' => $country->currency_symbol . number_format($amount, 2),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment intent creation failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Stripe payment processing failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create Razorpay payment intent (placeholder for future implementation)
     */
    private function createRazorpayPaymentIntent(User $user, SubscriptionPlan $plan, Country $country): array
    {
        // TODO: Implement Razorpay integration
        return [
            'success' => false,
            'message' => 'Razorpay integration not yet implemented'
        ];
    }

    /**
     * Create Paystack payment intent (placeholder for future implementation)
     */
    private function createPaystackPaymentIntent(User $user, SubscriptionPlan $plan, Country $country): array
    {
        // TODO: Implement Paystack integration
        return [
            'success' => false,
            'message' => 'Paystack integration not yet implemented'
        ];
    }

    /**
     * Convert price from one currency to another
     * For now, this is a simple placeholder. In production, you'd use a currency conversion API
     */
    private function convertPriceToCountryCurrency(float $amount, string $fromCurrency, string $toCurrency): float
    {
        // If currencies are the same, no conversion needed
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        // Simple conversion rates (in production, use a real currency API)
        $conversionRates = [
            'USD' => [
                'GBP' => 0.79,
                'EUR' => 0.85,
                'INR' => 83.0,
                'CAD' => 1.35,
                'AUD' => 1.50,
                'NGN' => 1500.0,
                'ZAR' => 18.5,
            ],
            'GBP' => [
                'USD' => 1.27,
                'EUR' => 1.08,
                'INR' => 105.0,
                'CAD' => 1.71,
                'AUD' => 1.90,
                'NGN' => 1900.0,
                'ZAR' => 23.4,
            ],
            // Add more conversion rates as needed
        ];

        if (isset($conversionRates[$fromCurrency][$toCurrency])) {
            return $amount * $conversionRates[$fromCurrency][$toCurrency];
        }

        // If no conversion rate found, return original amount
        return $amount;
    }

    /**
     * Get supported payment gateways for a country
     */
    public function getSupportedGateways(string $countryCode): array
    {
        return $this->locationService->getPaymentGatewaysForCountry($countryCode);
    }

    /**
     * Get the primary payment gateway for a country
     */
    public function getPrimaryGateway(string $countryCode): string
    {
        $gateways = $this->getSupportedGateways($countryCode);

        if (empty($gateways)) {
            return 'stripe'; // Default fallback
        }

        // For India, prioritize Razorpay
        if ($countryCode === 'IN' && in_array('razorpay', $gateways)) {
            return 'razorpay';
        }

        // Return the first supported gateway
        return $gateways[0];
    }

    /**
     * Check if a specific gateway is supported for a country
     */
    public function isGatewaySupported(string $countryCode, string $gateway): bool
    {
        $availableGateways = $this->getSupportedGateways($countryCode);
        return in_array($gateway, $availableGateways);
    }

    /**
     * Get currency information for a country
     */
    public function getCurrencyInfo(string $countryCode): ?array
    {
        return $this->locationService->getCurrencyForCountry($countryCode);
    }

    /**
     * Format price for display in country's currency
     */
    public function formatPrice(float $amount, string $countryCode): string
    {
        return CurrencyService::formatPrice($amount, $countryCode);
    }
}
