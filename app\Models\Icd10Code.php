<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Icd10Code extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'category',
        'short_description',
        'long_description',
        'chapter',
        'section',
        'is_billable',
        'is_valid_for_submission',
        'gender_restriction',
        'age_low',
        'age_high',
        'synonyms',
        'includes',
        'excludes',
        'version',
    ];

    protected $casts = [
        'is_billable' => 'boolean',
        'is_valid_for_submission' => 'boolean',
        'synonyms' => 'array',
        'includes' => 'array',
        'excludes' => 'array',
        'age_low' => 'integer',
        'age_high' => 'integer',
    ];

    /**
     * Search ICD-10 codes by term
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('code', 'LIKE', "%{$term}%")
              ->orWhere('short_description', 'LIKE', "%{$term}%")
              ->orWhere('long_description', 'LIKE', "%{$term}%");
        });
    }

    /**
     * Filter by chapter
     */
    public function scopeByChapter($query, $chapter)
    {
        return $query->where('chapter', $chapter);
    }

    /**
     * Filter billable codes only
     */
    public function scopeBillable($query)
    {
        return $query->where('is_billable', true);
    }

    /**
     * Filter valid for submission codes only
     */
    public function scopeValidForSubmission($query)
    {
        return $query->where('is_valid_for_submission', true);
    }

    /**
     * Get formatted code with description
     */
    public function getFormattedAttribute()
    {
        return "{$this->code} - {$this->short_description}";
    }
}
