#!/bin/bash

# Medroid App Deployment Script
# This script handles the deployment of the Medroid application

set -e  # Exit on any error

echo "🚀 Starting Medroid App Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "artisan file not found. Please run this script from the Laravel project root."
    exit 1
fi

print_status "Checking environment..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create one before deployment."
    exit 1
fi

# Install/Update Composer dependencies
print_status "Installing Composer dependencies..."
composer install --optimize-autoloader --no-dev

# Install/Update NPM dependencies
print_status "Installing NPM dependencies..."
npm install

# Build frontend assets
print_status "Building frontend assets..."
npm run build

# Run database migrations
print_status "Running database migrations..."
php artisan migrate --force

# Seed subscription plans
print_status "Seeding subscription plans..."
php artisan db:seed --class=SubscriptionPlanSeeder

# Clear application caches
print_status "Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Optimize application
print_status "Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set proper permissions (adjust paths as needed for your server)
print_status "Setting file permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

print_status "🎉 Deployment completed successfully!"

echo ""
echo "📋 Post-deployment checklist:"
echo "1. Verify environment variables are set correctly"
echo "2. Test the membership page: /membership"
echo "3. Test subscription flow with Stripe test cards"
echo "4. Check application logs for any errors"
echo "5. Verify database connections"
echo ""

print_warning "Don't forget to:"
echo "- Update Stripe webhook endpoints if needed"
echo "- Test payment processing in staging first"
echo "- Monitor application logs after deployment"
