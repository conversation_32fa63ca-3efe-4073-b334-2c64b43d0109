import type {
    LabTest,
    LabTestRequest,
    LabTestResult,
    TdlSettings,
    TdlConnectionStatus,
    AzureFileStats,
    ApiResponse,
    PaginatedResponse,
    TestSelectionRequirements
} from '@/types/tdl'

class TdlLabService {
    private baseUrl = ''

    // Test Catalog Methods
    async getTestCatalog(params: {
        page?: number
        per_page?: number
        search?: string
        category?: string
    } = {}): Promise<ApiResponse<{
        tests: PaginatedResponse<LabTest>
        categories: string[]
        stats: any
    }>> {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
            if (value) searchParams.append(key, value.toString())
        })

        const response = await fetch(`${this.baseUrl}/lab/catalog?${searchParams}`)
        return response.json()
    }

    async getActiveTests(): Promise<ApiResponse<{ tests: LabTest[] }>> {
        const response = await fetch(`${this.baseUrl}/lab/catalog/active`)
        return response.json()
    }

    async getTestCategories(): Promise<ApiResponse<{ categories: string[] }>> {
        const response = await fetch(`${this.baseUrl}/lab/catalog/categories`)
        return response.json()
    }

    async validateTestSelection(testCodes: string[]): Promise<ApiResponse<{
        tests: LabTest[]
        requirements: TestSelectionRequirements
        total_cost: number
        formatted_cost: string
    }>> {
        const response = await fetch(`${this.baseUrl}/lab/catalog/validate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify({ test_codes: testCodes })
        })
        return response.json()
    }

    // Lab Request Methods
    async getLabRequests(params: {
        page?: number
        per_page?: number
        status?: string
        patient_id?: number
    } = {}): Promise<ApiResponse<{ requests: PaginatedResponse<LabTestRequest> }>> {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
            if (value) searchParams.append(key, value.toString())
        })

        const response = await fetch(`${this.baseUrl}/lab/requests?${searchParams}`)
        return response.json()
    }

    async createLabRequest(data: {
        patient_id: number
        test_codes: string[]
        consultation_id?: number
        clinical_notes?: string
        urgent?: boolean
        fasting_status?: boolean
        collection_date?: string
    }): Promise<ApiResponse<{ request: LabTestRequest }>> {
        const response = await fetch(`${this.baseUrl}/lab/requests`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify(data)
        })
        return response.json()
    }

    async getLabRequest(id: number): Promise<ApiResponse<{ request: LabTestRequest }>> {
        const response = await fetch(`${this.baseUrl}/lab/requests/${id}`)
        return response.json()
    }

    async sendLabRequest(id: number): Promise<ApiResponse<any>> {
        const response = await fetch(`${this.baseUrl}/lab/requests/${id}/send`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.getCsrfToken()
            }
        })
        return response.json()
    }

    // Lab Results Methods
    async getLabResults(params: {
        page?: number
        per_page?: number
        status?: string
        patient_id?: number
        abnormal_only?: boolean
        date_from?: string
    } = {}): Promise<ApiResponse<{ results: PaginatedResponse<LabTestResult> }>> {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
            if (value) searchParams.append(key, value.toString())
        })

        const response = await fetch(`${this.baseUrl}/lab/results?${searchParams}`)
        return response.json()
    }

    async getLabResult(id: number): Promise<ApiResponse<{ result: LabTestResult }>> {
        const response = await fetch(`${this.baseUrl}/lab/results/${id}`)
        return response.json()
    }

    async reviewLabResult(id: number, notes?: string): Promise<ApiResponse<{ result: LabTestResult }>> {
        const response = await fetch(`${this.baseUrl}/lab/results/${id}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify({ notes })
        })
        return response.json()
    }

    async processResults(): Promise<ApiResponse<{ processed_count: number; results: LabTestResult[] }>> {
        const response = await fetch(`${this.baseUrl}/lab/results/process`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.getCsrfToken()
            }
        })
        return response.json()
    }

    async downloadResult(id: number): Promise<ApiResponse<any>> {
        const response = await fetch(`${this.baseUrl}/lab/results/${id}/download`)
        return response.json()
    }

    // TDL Settings Methods
    async getTdlSettings(): Promise<ApiResponse<{ settings: TdlSettings | null; is_configured: boolean }>> {
        const response = await fetch(`${this.baseUrl}/lab/settings`)
        return response.json()
    }

    async updateTdlSettings(data: Partial<TdlSettings>): Promise<ApiResponse<{ settings: TdlSettings; is_configured: boolean }>> {
        const response = await fetch(`${this.baseUrl}/lab/settings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify(data)
        })
        return response.json()
    }

    async testConnection(): Promise<ApiResponse<any>> {
        const response = await fetch(`${this.baseUrl}/lab/settings/test-connection`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.getCsrfToken()
            }
        })
        return response.json()
    }

    async testAzureConnection(connectionString: string): Promise<ApiResponse<any>> {
        const response = await fetch(`${this.baseUrl}/lab/settings/test-azure`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify({ azure_connection_string: connectionString })
        })
        return response.json()
    }

    async getConnectionStatus(): Promise<ApiResponse<{ status: TdlConnectionStatus }>> {
        const response = await fetch(`${this.baseUrl}/lab/settings/status`)
        return response.json()
    }

    async getAzureStats(): Promise<ApiResponse<{ stats: AzureFileStats }>> {
        const response = await fetch(`${this.baseUrl}/lab/settings/azure-stats`)
        return response.json()
    }

    async disableIntegration(): Promise<ApiResponse<{ settings: Partial<TdlSettings> }>> {
        const response = await fetch(`${this.baseUrl}/lab/settings/disable`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.getCsrfToken()
            }
        })
        return response.json()
    }

    // Utility Methods
    private getCsrfToken(): string {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (!token) {
            throw new Error('CSRF token not found')
        }
        return token
    }

    async getPatients(): Promise<ApiResponse<{ patients: any[] }>> {
        const response = await fetch('/get-patients')
        return response.json()
    }
}

export const tdlLabService = new TdlLabService()
export default tdlLabService
