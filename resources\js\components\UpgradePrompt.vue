<script setup lang="ts">
import { computed } from 'vue';
import { Link, usePage } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Props {
  variant?: 'banner' | 'card' | 'inline';
  title?: string;
  description?: string;
  features?: string[];
  showDismiss?: boolean;
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'card',
  title: 'Upgrade to Premium',
  description: 'Unlock advanced features and get more from your Medroid experience',
  features: () => [
    'Unlimited chat messages',
    'Priority appointment booking',
    'Advanced health insights',
    'Premium support'
  ],
  showDismiss: false,
});

const emit = defineEmits<{
  dismiss: [];
}>();

const page = usePage();
const user = computed(() => (page.props as any).auth?.user);

// Check if user is on free plan
const isFreePlan = computed(() => {
  const subscription = user.value?.subscription;
  return !subscription || subscription.plan?.is_free || subscription.plan?.slug === 'free';
});

// Don't show upgrade prompt if user is already on a paid plan
const shouldShow = computed(() => {
  return user.value && isFreePlan.value;
});
</script>

<template>
  <div v-if="shouldShow">
    <!-- Banner Variant -->
    <div 
      v-if="variant === 'banner'" 
      :class="[
        'bg-gradient-to-r from-orange-500 to-pink-500 text-white p-4 rounded-lg shadow-lg',
        className
      ]"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">{{ title }}</h3>
            <p class="text-sm opacity-90">{{ description }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <Link
            href="/membership"
            class="bg-white text-orange-600 hover:bg-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
          >
            Upgrade Now
          </Link>
          <button 
            v-if="showDismiss"
            @click="emit('dismiss')"
            class="text-white hover:text-gray-200 p-1"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Card Variant -->
    <Card v-else-if="variant === 'card'" :class="className">
      <CardHeader>
        <div class="flex items-center justify-between">
          <div>
            <CardTitle class="flex items-center space-x-2">
              <span>{{ title }}</span>
              <Badge variant="secondary" class="bg-orange-100 text-orange-800">Free Plan</Badge>
            </CardTitle>
            <CardDescription>{{ description }}</CardDescription>
          </div>
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <ul class="space-y-2">
            <li v-for="feature in features" :key="feature" class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-sm">{{ feature }}</span>
            </li>
          </ul>
          <div class="flex space-x-2">
            <Link href="/membership" class="flex-1">
              <Button class="w-full bg-orange-500 hover:bg-orange-600">
                View Plans
              </Button>
            </Link>
            <button 
              v-if="showDismiss"
              @click="emit('dismiss')"
              class="text-gray-400 hover:text-gray-600 p-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Inline Variant -->
    <div 
      v-else-if="variant === 'inline'"
      :class="[
        'bg-orange-50 border border-orange-200 rounded-lg p-3',
        className
      ]"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          <div>
            <p class="text-sm font-medium text-orange-800">{{ title }}</p>
            <p class="text-xs text-orange-600">{{ description }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <Link
            href="/membership"
            class="text-xs bg-orange-500 hover:bg-orange-600 text-white font-medium py-1 px-3 rounded transition-colors duration-200"
          >
            Upgrade
          </Link>
          <button 
            v-if="showDismiss"
            @click="emit('dismiss')"
            class="text-orange-400 hover:text-orange-600 p-1"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
