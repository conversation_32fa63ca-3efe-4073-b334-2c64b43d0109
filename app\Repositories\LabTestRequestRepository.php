<?php

namespace App\Repositories;

use App\Models\LabTestRequest;
use App\Repositories\Interfaces\LabTestRequestRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class LabTestRequestRepository extends BaseRepository implements LabTestRequestRepositoryInterface
{
    public function __construct(LabTestRequest $model)
    {
        parent::__construct($model);
    }

    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator
    {
        return $this->model->where('clinic_id', $clinicId)
            ->with(['patient', 'provider', 'consultation'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function create(array $data): LabTestRequest
    {
        if (!isset($data['order_number'])) {
            $data['order_number'] = LabTestRequest::generateOrderNumber();
        }
        
        return $this->model->create($data);
    }

    public function update(LabTestRequest $request, array $data): LabTestRequest
    {
        $request->update($data);
        return $request->fresh();
    }

    public function delete(LabTestRequest $request): bool
    {
        return $request->delete();
    }

    public function findById(int $id): ?LabTestRequest
    {
        return $this->model->find($id);
    }

    public function findByIdWithRelations(int $id, array $relations = []): ?LabTestRequest
    {
        $defaultRelations = ['patient', 'provider', 'clinic', 'consultation', 'results'];
        $relations = empty($relations) ? $defaultRelations : $relations;
        
        return $this->model->with($relations)->find($id);
    }

    public function findByOrderNumber(string $orderNumber): ?LabTestRequest
    {
        return $this->model->where('order_number', $orderNumber)->first();
    }

    public function getByPatient(int $patientId): Collection
    {
        return $this->model->forPatient($patientId)
            ->with(['provider', 'results'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getByProvider(int $providerId): Collection
    {
        return $this->model->forProvider($providerId)
            ->with(['patient', 'results'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getByStatus(string $status): Collection
    {
        return $this->model->byStatus($status)
            ->with(['patient', 'provider', 'clinic'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getPendingRequests(): Collection
    {
        return $this->model->byStatus('pending')
            ->with(['clinic', 'patient', 'provider'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    public function getRequestsForProcessing(): Collection
    {
        return $this->model->whereIn('status', ['sent', 'processing'])
            ->with(['clinic', 'patient', 'provider'])
            ->orderBy('sent_at', 'asc')
            ->get();
    }
}
