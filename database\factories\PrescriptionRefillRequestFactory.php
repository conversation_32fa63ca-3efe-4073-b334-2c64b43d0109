<?php

namespace Database\Factories;

use App\Models\PrescriptionRefillRequest;
use App\Models\Prescription;
use App\Models\Patient;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PrescriptionRefillRequestFactory extends Factory
{
    protected $model = PrescriptionRefillRequest::class;

    public function definition(): array
    {
        $requestedAt = $this->faker->dateTimeBetween('-1 month', 'now');
        
        return [
            'original_prescription_id' => Prescription::factory(),
            'patient_id' => Patient::factory(),
            'requested_by' => User::factory(),
            'reviewed_by' => null,
            'new_prescription_id' => null,
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected', 'expired']),
            'request_reason' => $this->faker->sentence(),
            'patient_notes' => $this->faker->paragraph(),
            'requested_items' => [
                [
                    'prescription_item_id' => $this->faker->numberBetween(1, 100),
                    'quantity' => $this->faker->numberBetween(10, 90),
                    'notes' => $this->faker->sentence(),
                ]
            ],
            'requested_at' => $requestedAt,
            'reviewed_at' => function (array $attributes) {
                return in_array($attributes['status'], ['approved', 'rejected']) ? 
                    $this->faker->dateTimeBetween($attributes['requested_at'], 'now') : null;
            },
            'review_notes' => function (array $attributes) {
                return $attributes['reviewed_at'] ? $this->faker->sentence() : null;
            },
            'rejection_reason' => function (array $attributes) {
                return $attributes['status'] === 'rejected' ? $this->faker->sentence() : null;
            },
            'requires_consultation' => $this->faker->boolean(20),
            'expires_at' => $this->faker->dateTimeBetween($requestedAt, '+30 days'),
        ];
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'reviewed_by' => null,
            'reviewed_at' => null,
            'review_notes' => null,
            'rejection_reason' => null,
            'new_prescription_id' => null,
        ]);
    }

    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'reviewed_by' => User::factory(),
            'reviewed_at' => $this->faker->dateTimeBetween($attributes['requested_at'], 'now'),
            'review_notes' => $this->faker->sentence(),
            'rejection_reason' => null,
            'new_prescription_id' => Prescription::factory(),
        ]);
    }

    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'reviewed_by' => User::factory(),
            'reviewed_at' => $this->faker->dateTimeBetween($attributes['requested_at'], 'now'),
            'review_notes' => $this->faker->sentence(),
            'rejection_reason' => $this->faker->sentence(),
            'new_prescription_id' => null,
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'expires_at' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
        ]);
    }

    public function requiresConsultation(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_consultation' => true,
        ]);
    }
}
