/**
 * Utility functions for building clean API parameters
 */

/**
 * Build URLSearchParams with only non-empty values
 * @param {Object} params - Object with parameter key-value pairs
 * @returns {URLSearchParams} - Clean URLSearchParams object
 */
export function buildCleanParams(params) {
    const cleanParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
        // Only add parameter if it has a meaningful value
        if (value !== null && 
            value !== undefined && 
            value !== '' && 
            value !== 'all' && 
            !(Array.isArray(value) && value.length === 0)) {
            
            // Trim string values
            const cleanValue = typeof value === 'string' ? value.trim() : value
            
            // Only add if still has value after trimming
            if (cleanValue !== '') {
                cleanParams.append(key, cleanValue.toString())
            }
        }
    })
    
    return cleanParams
}

/**
 * Build clean axios params object (for use with axios params option)
 * @param {Object} params - Object with parameter key-value pairs
 * @returns {Object} - Clean params object
 */
export function buildCleanAxiosParams(params) {
    const cleanParams = {}
    
    Object.entries(params).forEach(([key, value]) => {
        // Only add parameter if it has a meaningful value
        if (value !== null && 
            value !== undefined && 
            value !== '' && 
            value !== 'all' && 
            !(Array.isArray(value) && value.length === 0)) {
            
            // Trim string values
            const cleanValue = typeof value === 'string' ? value.trim() : value
            
            // Only add if still has value after trimming
            if (cleanValue !== '') {
                cleanParams[key] = cleanValue
            }
        }
    })
    
    return cleanParams
}

/**
 * Build query string with only non-empty values
 * @param {Object} params - Object with parameter key-value pairs
 * @returns {string} - Clean query string (without leading ?)
 */
export function buildCleanQueryString(params) {
    const cleanParams = buildCleanParams(params)
    return cleanParams.toString()
}

/**
 * Build full URL with clean query string
 * @param {string} baseUrl - Base URL
 * @param {Object} params - Object with parameter key-value pairs
 * @returns {string} - Complete URL with clean query string
 */
export function buildCleanUrl(baseUrl, params) {
    const queryString = buildCleanQueryString(params)
    return queryString ? `${baseUrl}?${queryString}` : baseUrl
}
