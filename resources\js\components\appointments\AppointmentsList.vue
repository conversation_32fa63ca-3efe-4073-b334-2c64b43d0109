<template>
  <div class="appointments-management">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 mb-4">
      <div class="p-6 border-b border-gray-100 rounded-t-2xl">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-1">Appointments</h2>
            <p class="text-gray-600">Manage and track your appointments efficiently</p>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="showStats = !showStats"
              class="inline-flex items-center px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 transition-all duration-200"
            >
              <Icon name="bar-chart-3" class="w-4 h-4 mr-2" />
              {{ showStats ? 'Hide Stats' : 'Show Stats' }}
            </button>
            <button
              @click="exportAppointments"
              :disabled="loading"
              class="inline-flex items-center px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 disabled:opacity-50 transition-all duration-200"
            >
              <Icon name="download" class="w-4 h-4 mr-2" />
              Export
            </button>
            <button
              @click="showAddAppointment = true"
              class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 transition-all duration-200"
            >
              <Icon name="plus" class="w-4 h-4 mr-2" />
              Add Appointment
            </button>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="p-6 bg-gradient-to-r from-gray-50 to-white border-b border-gray-100">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          <!-- Clinic Filter -->
          <div v-if="userRole === 'admin' || userRole === 'super_admin'">
            <label class="block text-sm font-semibold text-gray-800 mb-2">Clinic</label>
            <select
              v-model="filters.clinic_id"
              @change="applyFilters"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
            >
              <option value="">All Clinics</option>
              <option v-for="clinic in clinics" :key="clinic?.id" :value="clinic?.id">
                {{ clinic?.name }}
              </option>
            </select>
          </div>

          <!-- Provider Filter -->
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">Provider</label>
            <select
              v-model="filters.provider_id"
              @change="applyFilters"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
              style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
            >
              <option value="">All Providers</option>
              <option v-for="provider in providers" :key="provider?.id" :value="provider?.id">
                {{ provider?.name }}
              </option>
            </select>
          </div>

          <!-- Patient Filter -->
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">Patient</label>
            <select
              v-model="filters.patient_id"
              @change="applyFilters"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
              style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
            >
              <option value="">All Patients</option>
              <option v-for="patient in patients" :key="patient?.id || patient?.user?.id" :value="patient?.id">
                {{ patient?.user?.name || patient?.name }}
              </option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">Status</label>
            <select
              v-model="filters.status"
              @change="applyFilters"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
              style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
            >
              <option value="">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="rescheduled">Rescheduled</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no_show">No Show</option>
            </select>
          </div>

          <!-- Consultation Status Filter -->
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">Consultation</label>
            <select
              v-model="filters.consultation_status"
              @change="applyFilters"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors appearance-none bg-no-repeat bg-right pr-10"
              style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
            >
              <option value="">All Appointments</option>
              <option value="with_consultation">With Consultation</option>
              <option value="without_consultation">Ready for Consultation</option>
              <option value="consultation_needed">Consultation Needed</option>
            </select>
          </div>

          <!-- Date Range -->
          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">From Date</label>
            <input
              type="date"
              v-model="filters.start_date"
              @change="onManualDateChange"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
            />
          </div>

          <div>
            <label class="block text-sm font-semibold text-gray-800 mb-2">To Date</label>
            <input
              type="date"
              v-model="filters.end_date"
              @change="onManualDateChange"
              class="w-full px-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
            />
          </div>
        </div>

        <!-- Search and Quick Filters -->
        <div class="mt-4 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                type="text"
                v-model="filters.search"
                @input="debounceSearch"
                placeholder="Search appointments..."
                class="w-64 pl-10 pr-4 py-2.5 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white hover:border-gray-300 transition-colors"
              />
              <Icon name="search" class="absolute left-3 top-3 w-4 h-4 text-gray-400" />
            </div>

            <!-- Quick Date Filters -->
            <div class="flex items-center space-x-2">
              <button
                v-for="preset in datePresets"
                :key="preset.value"
                @click="applyDatePreset(preset.value)"
                :class="[
                  'px-3 py-1.5 text-xs font-medium rounded-md transition-colors',
                  filters.date_preset === preset.value
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
                ]"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>

          <!-- Clear Filters -->
          <button
            @click="clearFilters"
            class="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div v-if="showStats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4 transition-all duration-300">
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-md border border-blue-200 p-4 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
          <Icon name="calendar" class="w-6 h-6 text-blue-600 mr-3" />
          <div>
            <p class="text-xs font-semibold text-blue-700">Total Appointments</p>
            <p class="text-xl font-bold text-blue-900">{{ metadata.total_appointments || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-xl shadow-md border border-amber-200 p-4 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
          <Icon name="clock" class="w-6 h-6 text-amber-600 mr-3" />
          <div>
            <p class="text-xs font-semibold text-amber-700">Upcoming</p>
            <p class="text-xl font-bold text-amber-900">{{ metadata.upcoming_appointments || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl shadow-md border border-emerald-200 p-4 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
          <Icon name="circle-check" class="w-6 h-6 text-emerald-600 mr-3" />
          <div>
            <p class="text-xs font-semibold text-emerald-700">Completed</p>
            <p class="text-xl font-bold text-emerald-900">{{ metadata.completed_appointments || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-teal-50 to-teal-100 rounded-xl shadow-md border border-teal-200 p-4 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
          <Icon name="dollar-sign" class="w-6 h-6 text-teal-600 mr-3" />
          <div>
            <p class="text-xs font-semibold text-teal-700">Revenue</p>
            <p class="text-xl font-bold text-teal-900">£{{ formatCurrency(metadata.total_revenue || 0) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Appointments Table -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
      <!-- Table Header with Bulk Actions -->
      <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white rounded-t-2xl">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h3 class="text-xl font-bold text-gray-900">
              Appointments List
              <span class="text-sm font-normal text-gray-500 ml-2 bg-gray-100 px-2 py-1 rounded-full">
                {{ pagination.total || 0 }} total
              </span>
            </h3>

            <!-- Bulk Actions -->
            <div v-if="selectedAppointments.length > 0" class="flex items-center space-x-3">
              <span class="text-sm font-medium text-gray-700 bg-blue-50 px-3 py-1 rounded-full">
                {{ selectedAppointments.length }} selected
              </span>
              <button
                @click="showBulkStatusModal = true"
                class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-xl hover:bg-blue-200 transition-colors"
              >
                Update Status
              </button>
              <button
                @click="confirmBulkDelete"
                class="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 rounded-xl hover:bg-red-200 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>

          <!-- Per Page Selector -->
          <div class="flex items-center space-x-3">
            <label class="text-sm font-medium text-gray-700">Show:</label>
            <select
              v-model="pagination.per_page"
              @change="loadAppointments"
              class="px-3 py-2 border border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white"
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <div>
        <!-- Loading State -->
      <div v-if="loading" class="p-12 text-center">
        <div class="inline-flex flex-col items-center">
          <div class="animate-spin rounded-full h-12 w-12 border-4 border-teal-200 border-t-teal-600 mb-4"></div>
          <span class="text-lg font-medium text-gray-700">Loading appointments...</span>
          <span class="text-sm text-gray-500 mt-1">Please wait while we fetch your data</span>
        </div>
      </div>

      <!-- Card Content -->
      <div v-else class="space-y-4 p-6">
        <!-- Bulk Actions Header -->
        <div v-if="selectedAppointments.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm font-medium text-blue-900">
                {{ selectedAppointments.length }} appointment(s) selected
              </span>
              <button
                @click="showBulkStatusModal = true"
                class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
              >
                Update Status
              </button>
              <button
                @click="confirmBulkDelete"
                class="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
              >
                Delete Selected
              </button>
            </div>
            <button
              @click="selectedAppointments = []"
              class="text-blue-600 hover:text-blue-800"
            >
              Clear Selection
            </button>
          </div>
        </div>

        <!-- Select All Option -->
        <div class="flex items-center justify-between mb-6">
          <label class="flex items-center space-x-3 text-sm font-medium text-gray-700">
            <input
              type="checkbox"
              :checked="allSelected"
              @change="toggleSelectAll"
              class="rounded-lg border-gray-300 text-teal-600 focus:ring-teal-500 w-4 h-4"
            />
            <span>Select all appointments</span>
          </label>

          <!-- Sort Options -->
          <div class="flex items-center space-x-3">
            <span class="text-sm font-medium text-gray-700">Sort by:</span>
            <select
              v-model="sortColumn"
              @change="loadAppointments"
              class="text-sm border border-gray-200 rounded-xl px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white"
            >
              <option value="scheduled_at">Date</option>
              <option value="patient_name">Patient</option>
              <option value="provider_name">Provider</option>
              <option value="status">Status</option>
            </select>
            <button
              @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'; loadAppointments()"
              class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Icon :name="sortDirection === 'asc' ? 'chevron-up' : 'chevron-down'" class="w-4 h-4" />
            </button>
          </div>
        </div>
        <!-- Appointment Cards -->
        <div class="space-y-2">
          <div
            v-for="appointment in appointments"
            :key="appointment.id"
            class="bg-white border border-gray-100 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:border-gray-200 relative"
          >
            <div :class="['p-4', { 'rounded-b-xl': !shouldShowQuickActions(appointment) }]">
              <!-- Card Header -->
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3 flex-1">
                  <input
                    type="checkbox"
                    :value="appointment.id"
                    v-model="selectedAppointments"
                    class="rounded border-gray-300 text-teal-600 focus:ring-teal-500 w-4 h-4"
                  />
                  <div class="min-w-0 flex-1">
                    <div class="flex items-center space-x-2 flex-wrap">
                      <h3 class="text-sm font-semibold text-gray-900 leading-tight">
                        {{ appointment.service?.name || appointment.reason || 'General Consultation' }}
                      </h3>
                      <span :class="getTypeBadgeClass(appointment.is_telemedicine)" class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full">
                        {{ appointment.is_telemedicine ? 'Telemedicine' : 'In-Person' }}
                      </span>
                      <span :class="getStatusBadgeClass(appointment.status)" class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full">
                        {{ formatStatus(appointment.status) }}
                      </span>
                      <span :class="getCheckInBadgeClass(appointment)" class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full">
                        <Icon :name="getCheckInIcon(appointment)" class="w-3 h-3 mr-1" />
                        {{ getCheckInStatus(appointment) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Actions Dropdown -->
                <div class="relative">
                  <button
                    @click="toggleDropdown(appointment.id)"
                    class="inline-flex items-center justify-center w-8 h-8 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                  >
                    <Icon name="more-vertical" class="w-4 h-4 text-gray-600" />
                  </button>

                  <!-- Dropdown Menu -->
                  <div
                    v-if="dropdownOpen === appointment.id"
                    class="absolute right-0 top-full mt-1 w-48 rounded-lg shadow-xl bg-white ring-1 ring-black ring-opacity-5 z-[9999]"
                  >
                    <div class="py-1">
                      <button
                        @click="viewAppointment(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="eye" class="w-4 h-4 mr-3 text-blue-500" />
                        View
                      </button>

                      <!-- Check-in/Check-out Actions -->
                      <div class="border-t border-gray-100 my-1"></div>
                      <button
                        v-if="!appointment.checked_in_at && !appointment.checked_out_at && canCheckIn(appointment)"
                        @click="checkInPatient(appointment)"
                        :disabled="checkingIn === appointment.id"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <Icon name="check-circle" class="w-4 h-4 mr-3 text-teal-500" />
                        {{ checkingIn === appointment.id ? 'Checking In...' : 'Check In Patient' }}
                      </button>
                      <button
                        v-else-if="appointment.checked_in_at && !appointment.checked_out_at"
                        @click="checkOutPatient(appointment)"
                        :disabled="checkingOut === appointment.id"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <Icon name="log-out" class="w-4 h-4 mr-3 text-orange-500" />
                        {{ checkingOut === appointment.id ? 'Checking Out...' : 'Check Out Patient' }}
                      </button>

                      <!-- Consultation Actions -->
                      <div v-if="appointment.consultation_id || canCreateConsultation(appointment)" class="border-t border-gray-100 my-1"></div>
                      <button
                        v-if="appointment.consultation_id"
                        @click="viewConsultation(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="file-text" class="w-4 h-4 mr-3 text-emerald-500" />
                        View Consultation
                      </button>
                      <button
                        v-else-if="canCreateConsultation(appointment)"
                        @click="createConsultation(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="plus-circle" class="w-4 h-4 mr-3 text-emerald-500" />
                        Start Consultation
                      </button>

                      <div class="border-t border-gray-100 my-1"></div>

                      <button
                        v-if="appointment.is_telemedicine"
                        @click="recordVideoConference(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="video" class="w-4 h-4 mr-3 text-purple-500" />
                        Record Video Conference link
                      </button>
                      <button
                        v-if="canRescheduleAppointment(appointment)"
                        @click="rescheduleAppointment(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="calendar" class="w-4 h-4 mr-3 text-green-500" />
                        Reschedule
                      </button>
                      <button
                        @click="viewPatientDashboard(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <Icon name="user" class="w-4 h-4 mr-3 text-indigo-500" />
                        Patient dashboard
                      </button>

                      <div class="border-t border-gray-100 my-1"></div>

                      <button
                        @click="deleteAppointment(appointment)"
                        class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                      >
                        <Icon name="trash-2" class="w-4 h-4 mr-3 text-red-500" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Card Body -->
              <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
                <!-- Patient Info -->
                <div class="flex items-center space-x-2">
                  <Icon name="user" class="w-4 h-4 text-blue-600" />
                  <div class="min-w-0 flex-1">
                    <button
                      @click="viewPatientDetails(appointment)"
                      class="text-xs font-medium text-blue-600 hover:text-blue-800 truncate text-left transition-colors block"
                    >
                      {{ appointment.patient?.user?.name || 'N/A' }}
                    </button>
                    <p class="text-xs text-gray-500">Patient</p>
                  </div>
                </div>

                <!-- Provider Info -->
                <div class="flex items-center space-x-2">
                  <Icon name="user-check" class="w-4 h-4 text-teal-600" />
                  <div class="min-w-0 flex-1">
                    <p class="text-xs font-medium text-gray-900 truncate">
                      {{ appointment.provider?.user?.name || 'N/A' }}
                    </p>
                    <p class="text-xs text-gray-500">Provider</p>
                  </div>
                </div>

                <!-- Date & Time -->
                <div class="flex items-center space-x-2">
                  <Icon name="calendar" class="w-4 h-4 text-purple-600" />
                  <div class="min-w-0 flex-1">
                    <p class="text-xs font-medium text-gray-900">
                      {{ formatDate(appointment.scheduled_at) }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ formatTime(appointment.scheduled_at) }}
                    </p>
                  </div>
                </div>

                <!-- Payment & Amount -->
                <div class="flex items-center space-x-2">
                  <Icon name="credit-card" class="w-4 h-4 text-amber-600" />
                  <div class="min-w-0 flex-1">
                    <div class="flex items-center space-x-2">
                      <p class="text-xs font-medium text-gray-900">
                        £{{ formatCurrency(appointment.amount || appointment.service?.price || 0) }}
                      </p>
                      <span :class="getPaymentBadgeClass(appointment.payment_status)" class="inline-flex px-1.5 py-0.5 text-xs font-medium rounded-full">
                        {{ formatPaymentStatus(appointment.payment_status) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quick Actions Bar -->
              <div v-if="shouldShowQuickActions(appointment)" class="border-t border-gray-100 px-4 py-2.5 bg-gray-50 rounded-b-xl">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <!-- Check-in/Check-out Actions -->
                    <button
                      v-if="!appointment.checked_in_at && !appointment.checked_out_at && canCheckIn(appointment)"
                      @click="checkInPatient(appointment)"
                      :disabled="checkingIn === appointment.id"
                      class="inline-flex items-center px-2.5 py-1 text-xs font-medium text-teal-700 bg-teal-50 border border-teal-200 rounded-md hover:bg-teal-100 transition-colors disabled:opacity-50"
                    >
                      <Icon name="check-circle" class="w-3 h-3 mr-1" />
                      {{ checkingIn === appointment.id ? 'Checking In...' : 'Check In' }}
                    </button>

                    <button
                      v-else-if="appointment.checked_in_at && !appointment.checked_out_at"
                      @click="checkOutPatient(appointment)"
                      :disabled="checkingOut === appointment.id"
                      class="inline-flex items-center px-2.5 py-1 text-xs font-medium text-orange-700 bg-orange-50 border border-orange-200 rounded-md hover:bg-orange-100 transition-colors disabled:opacity-50"
                    >
                      <Icon name="log-out" class="w-3 h-3 mr-1" />
                      {{ checkingOut === appointment.id ? 'Checking Out...' : 'Check Out' }}
                    </button>

                    <!-- Consultation Quick Action -->
                    <button
                      v-if="appointment.consultation_id"
                      @click="viewConsultation(appointment)"
                      class="inline-flex items-center px-2.5 py-1 text-xs font-medium text-emerald-700 bg-emerald-50 border border-emerald-200 rounded-md hover:bg-emerald-100 transition-colors"
                    >
                      <Icon name="file-text" class="w-3 h-3 mr-1" />
                      View Consultation
                    </button>
                    <button
                      v-else-if="canCreateConsultation(appointment)"
                      @click="createConsultation(appointment)"
                      class="inline-flex items-center px-2.5 py-1 text-xs font-medium text-emerald-700 bg-white border border-emerald-200 rounded-md hover:bg-emerald-50 transition-colors"
                    >
                      <Icon name="plus-circle" class="w-3 h-3 mr-1" />
                      Start Consultation
                    </button>
                  </div>

                  <div class="text-xs text-gray-500">
                    {{ formatTime(appointment.scheduled_at) }}
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && appointments.length === 0" class="text-center py-16">
          <Icon name="calendar" class="w-16 h-16 text-gray-400 mx-auto mb-6" />
          <h3 class="text-2xl font-bold text-gray-900 mb-3">No appointments found</h3>
          <p class="text-gray-600 mb-8 max-w-md mx-auto">Try adjusting your filters or create a new appointment to get started.</p>
          <button
            @click="showAddAppointment = true"
            class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
          >
            <Icon name="plus" class="w-5 h-5 mr-2" />
            Add First Appointment
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="!loading && appointments.length > 0" class="bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 rounded-b-2xl">
        <Pagination
          :currentPage="pagination.current_page"
          :lastPage="pagination.last_page"
          :total="pagination.total"
          :perPage="pagination.per_page"
          :from="pagination.from"
          :to="pagination.to"
          @page-changed="changePage"
        />
      </div>
      </div>
    </div>

    <!-- Add Appointment Modal -->
    <div v-if="showAddAppointment" class="fixed inset-0 z-[9999] overflow-y-auto">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="!savingAppointment && (showAddAppointment = false)"></div>

      <!-- Modal container -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative w-full max-w-5xl bg-white rounded-lg shadow-xl transform transition-all">
          <!-- Modal Header -->
          <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Add appointment</h3>
            <button
              @click="!savingAppointment && (showAddAppointment = false)"
              :disabled="savingAppointment"
              class="text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon name="x" class="w-5 h-5" />
            </button>
          </div>

          <!-- Modal Body -->
          <div class="flex h-[600px] modal-content">
            <!-- Left Side - Form -->
            <div class="flex-1 px-6 py-6 overflow-y-auto border-r border-gray-200">
              <div class="space-y-6">
                <!-- Select Clinic (only for admin users) -->
                <div v-if="props.userRole === 'admin' || props.userRole === 'super_admin'" class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <label class="block text-sm font-semibold text-blue-900 mb-2">Select Clinic *</label>
                  <select v-model="appointmentForm.clinic_id" @change="onClinicChange" class="w-full px-3 py-2.5 text-sm border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                    <option value="">Choose a clinic</option>
                    <option v-for="clinic in clinics" :key="clinic?.id" :value="clinic?.id">{{ clinic?.name }}</option>
                  </select>
                </div>

                <!-- Step 1: Provider Selection -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <div class="flex items-center mb-3">
                    <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">1</div>
                    <label class="text-sm font-semibold text-gray-900">Provider *</label>
                  </div>
                  <select v-model="appointmentForm.provider_id" @change="onProviderChange" class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                    <option value="">Select a provider</option>
                    <option v-for="provider in filteredProviders" :key="provider?.id" :value="provider?.id">
                      {{ provider?.name }}
                    </option>
                  </select>
                  <div v-if="filteredProviders.length === 0" class="text-xs text-red-500 mt-1">
                    No providers available. Please check your clinic selection.
                  </div>
                </div>

                <!-- Step 2: Service Selection -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200" :class="{ 'opacity-50': !appointmentForm.provider_id }">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">2</div>
                      <label class="text-sm font-semibold text-gray-900">Service *</label>
                    </div>
                    <button class="px-3 py-1.5 text-xs text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50">
                      + Add Service
                    </button>
                  </div>
                  <select v-model="appointmentForm.service_id" @change="onServiceChange" :disabled="!appointmentForm.provider_id" class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100">
                    <option value="">Select a service</option>
                    <option v-for="service in filteredServices" :key="service?.id" :value="service?.id">
                      {{ service?.name }}
                    </option>
                  </select>
                  <div v-if="!appointmentForm.service_id && appointmentForm.provider_id" class="text-xs text-orange-600 mt-1">
                    Service selection is required
                  </div>
                </div>

                <!-- Step 3: Date & Time Selection -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200" :class="{ 'opacity-50': !appointmentForm.service_id }">
                  <div class="flex items-center mb-3">
                    <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">3</div>
                    <label class="text-sm font-semibold text-gray-900">Appointment Date & Time *</label>
                  </div>

                  <div class="space-y-3">
                    <div>
                      <label class="block text-xs font-medium text-gray-600 mb-1">Available Slot</label>
                      <div class="text-xs text-blue-600 mb-2">Session 1</div>
                      <input
                        type="date"
                        v-model="appointmentForm.date"
                        @change="onDateChange"
                        :min="getCurrentDate()"
                        :disabled="!appointmentForm.service_id"
                        class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
                      />
                    </div>

                    <!-- Time Slots Grid -->
                    <div v-if="availableTimeSlots.length > 0 && appointmentForm.date" class="grid grid-cols-3 gap-2">
                      <button
                        v-for="slot in availableTimeSlots"
                        :key="slot.time"
                        @click="selectTimeSlot(slot)"
                        :class="[
                          'px-3 py-2 text-xs border rounded-md transition-colors text-center font-medium',
                          selectedTimeSlot?.time === slot.time
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-300'
                        ]"
                      >
                        {{ slot.time }}
                      </button>
                    </div>
                    <div v-else-if="appointmentForm.date && appointmentForm.service_id" class="text-xs text-gray-500 py-3 text-center bg-white rounded border border-gray-200">
                      No time slots available for selected date
                    </div>
                  </div>
                </div>

                <!-- Step 4: Patient Selection -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200" :class="{ 'opacity-50': !selectedTimeSlot }">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">4</div>
                      <label class="text-sm font-semibold text-gray-900">Patient *</label>
                    </div>
                    <button class="px-3 py-1.5 text-xs text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50">
                      + Add patient
                    </button>
                  </div>
                  <select v-model="appointmentForm.patient_id" :disabled="!selectedTimeSlot" class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100">
                    <option value="">Select a patient</option>
                    <option v-for="patient in patients" :key="patient?.id || patient?.user?.id" :value="patient?.id">
                      {{ patient?.user?.name || patient?.name }}
                    </option>
                  </select>
                </div>

                <!-- Notes -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <label class="block text-sm font-semibold text-gray-900 mb-2">Notes</label>
                  <textarea
                    v-model="appointmentForm.description"
                    rows="3"
                    placeholder="Add any special notes or instructions..."
                    class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  ></textarea>
                </div>

                <!-- Payment Method -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <label class="block text-sm font-semibold text-gray-900 mb-3">Payment Method</label>
                  <div class="grid grid-cols-3 gap-3">
                    <label class="flex flex-col items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-white transition-colors" :class="{ 'bg-blue-50 border-blue-500': appointmentForm.payment_method === 'stripe' }">
                      <input type="radio" v-model="appointmentForm.payment_method" value="stripe" class="sr-only" />
                      <Icon name="credit-card" class="w-6 h-6 text-blue-600 mb-2" />
                      <div class="text-xs font-medium text-center">Stripe</div>
                      <div class="text-xs text-gray-500 text-center mt-1">Pay securely online with cards</div>
                    </label>

                    <label class="flex flex-col items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-white transition-colors" :class="{ 'bg-green-50 border-green-500': appointmentForm.payment_method === 'offline' }">
                      <input type="radio" v-model="appointmentForm.payment_method" value="offline" class="sr-only" />
                      <Icon name="banknote" class="w-6 h-6 text-green-600 mb-2" />
                      <div class="text-xs font-medium text-center">Pay Offline</div>
                      <div class="text-xs text-gray-500 text-center mt-1">Pay using cash or cheque</div>
                    </label>

                    <label class="flex flex-col items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-white transition-colors" :class="{ 'bg-orange-50 border-orange-500': appointmentForm.payment_method === 'later' }">
                      <input type="radio" v-model="appointmentForm.payment_method" value="later" class="sr-only" />
                      <Icon name="clock" class="w-6 h-6 text-orange-600 mb-2" />
                      <div class="text-xs font-medium text-center">Pay Later</div>
                      <div class="text-xs text-gray-500 text-center mt-1">Pay at a later date</div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Side - Service Details -->
            <div class="w-80 px-6 py-6 bg-gradient-to-b from-blue-50 to-gray-50">
              <div v-if="selectedService" class="space-y-4">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Service Detail</h3>
                  <div class="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
                    <div class="text-center mb-4">
                      <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Icon name="stethoscope" class="w-6 h-6 text-blue-600" />
                      </div>
                      <div class="text-lg font-bold text-gray-900">{{ selectedService.name }}</div>
                      <div class="text-sm text-gray-600 mt-1">{{ selectedService.description || 'Professional medical service' }}</div>
                    </div>

                    <div class="space-y-3 mb-4">
                      <div v-if="selectedService.duration" class="flex items-center justify-between py-2 border-b border-gray-100">
                        <div class="flex items-center">
                          <Icon name="clock" class="w-4 h-4 text-gray-500 mr-2" />
                          <span class="text-sm text-gray-600">Duration</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ selectedService.duration }} min</span>
                      </div>

                      <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <div class="flex items-center">
                          <Icon name="user-check" class="w-4 h-4 text-gray-500 mr-2" />
                          <span class="text-sm text-gray-600">Provider</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ getSelectedProviderName() }}</span>
                      </div>

                      <div class="flex items-center justify-between py-2">
                        <div class="flex items-center">
                          <Icon name="dollar-sign" class="w-4 h-4 text-gray-500 mr-2" />
                          <span class="text-sm text-gray-600">Price</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600">{{ formatPrice(selectedService.price) }}</span>
                      </div>
                    </div>

                    <!-- Total Section -->
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <div class="flex items-center justify-between">
                        <div class="text-base font-semibold text-blue-900">Total Amount</div>
                        <div class="text-xl font-bold text-blue-900">
                          {{ formatPrice(selectedService.price) }}
                        </div>
                      </div>
                      <div class="text-xs text-blue-700 mt-1">Inclusive of all charges</div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="flex items-center justify-center h-full">
                <div class="text-center text-gray-500">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon name="clipboard-list" class="w-8 h-8 text-gray-400" />
                  </div>
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Service Details</h4>
                  <p class="text-xs text-gray-500 max-w-48">Select a service from the form to view pricing and details here</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end px-6 py-4 space-x-3 border-t border-gray-200 bg-gray-50">
            <button
              @click="!savingAppointment && (showAddAppointment = false)"
              :disabled="savingAppointment"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              @click="saveAppointment"
              :disabled="!isFormValid || savingAppointment"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="savingAppointment">Saving...</span>
              <span v-else>Save</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Patient View Modal -->
    <PatientViewModal
      :is-open="showPatientModal"
      :patient="selectedPatient"
      :show-edit-button="false"
      @close="closePatientModal"
    />

    <!-- Appointment View Modal -->
    <AppointmentViewModal
      :is-open="showAppointmentModal"
      :appointment="selectedAppointment"
      :show-edit-button="false"
      @close="closeAppointmentModal"
      @view-patient="viewPatientDetails"
    />

    <!-- Reschedule Modal -->
    <RescheduleModal
      :is-open="showRescheduleModal"
      :appointment="appointmentToReschedule"
      @close="closeRescheduleModal"
      @reschedule="handleReschedule"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useApi } from '@/composables/useApi'
import { useNotifications } from '@/composables/useNotifications'
import { useConsultationApi } from '@/composables/useConsultationApi'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import PatientViewModal from '@/components/PatientViewModal.vue'
import AppointmentViewModal from '@/components/AppointmentViewModal.vue'
import RescheduleModal from '@/components/RescheduleModal.vue'
import { formatAppointmentDateTime, formatTime as displayTime, formatTimeRange } from '@/utils/timeDisplay'

// Props
const props = defineProps({
  userRole: {
    type: String,
    default: 'patient'
  },
  userId: {
    type: Number,
    default: null
  },
  currentUser: {
    type: Object,
    default: null
  }
})

// Composables
const { get, post, put, delete: del } = useApi()
const { showSuccess, showError, showConfirm, showAlert } = useNotifications()
const { createFromAppointment } = useConsultationApi()

// Reactive data
const loading = ref(false)
const appointments = ref([])
const metadata = ref({})
const pagination = reactive({
  current_page: 1,
  last_page: 1,
  per_page: 25,
  total: 0,
  from: 0,
  to: 0
})

// Filters
const filters = reactive({
  clinic_id: '',
  provider_id: '',
  patient_id: '',
  status: '',
  payment_status: '',
  consultation_status: '',
  start_date: '',
  end_date: '',
  search: '',
  date_preset: ''
})

// Dropdown data
const clinics = ref([])
const providers = ref([])
const patients = ref([])
const services = ref([])

// UI state
const selectedAppointments = ref([])
const dropdownOpen = ref(null)
const showAddAppointment = ref(false)
const showPatientModal = ref(false)
const selectedPatient = ref(null)
const showAppointmentModal = ref(false)
const selectedAppointment = ref(null)
const showRescheduleModal = ref(false)
const appointmentToReschedule = ref(null)
const showBulkStatusModal = ref(false)
const checkingIn = ref(null)
const checkingOut = ref(null)
const showStats = ref(false)

// Appointment form data
const appointmentForm = reactive({
  clinic_id: '',
  provider_id: '',
  service_id: '',
  patient_id: '',
  date: '',
  appointment_date: '',
  status: 'scheduled',
  description: '',
  payment_method: 'stripe'
})

// Time slots and form state
const availableTimeSlots = ref([])
const selectedTimeSlot = ref(null)
const savingAppointment = ref(false)

// Sorting
const sortColumn = ref('scheduled_at')
const sortDirection = ref('desc')

// Date presets
const datePresets = [
  { label: 'Today', value: 'today' },
  { label: 'Tomorrow', value: 'tomorrow' },
  { label: 'This Week', value: 'this_week' },
  { label: 'Next Week', value: 'next_week' },
  { label: 'This Month', value: 'this_month' },
  { label: 'Upcoming', value: 'upcoming' },
  { label: 'Past', value: 'past' }
]

// Computed properties
const allSelected = computed(() => {
  return appointments.value.length > 0 && selectedAppointments.value.length === appointments.value.length
})

// Search debounce
let searchTimeout = null
const debounceSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    applyFilters()
  }, 500)
}

// Appointment form computed properties
const filteredProviders = computed(() => {
  console.log('Filtering providers:', {
    userRole: props.userRole,
    clinicId: appointmentForm.clinic_id,
    totalProviders: providers.value.length,
    providers: providers.value
  })

  // The API already handles clinic filtering on the backend based on user role
  // For admin users: returns all providers if no clinic filter, or filtered by clinic
  // For non-admin users: returns only providers from their clinic

  // For admin users, we need to reload providers when clinic changes
  if (props.userRole === 'admin' || props.userRole === 'super_admin') {
    // If no clinic is selected, show empty list to force clinic selection
    if (!appointmentForm.clinic_id) {
      console.log('Admin: No clinic selected, showing empty list')
      return []
    }
  }

  // Return all providers from API (already filtered by backend)
  console.log('Showing providers from API:', providers.value)
  return providers.value
})

const filteredServices = computed(() => {
  if (!Array.isArray(services.value)) {
    console.log('Services is not an array:', services.value)
    return []
  }

  console.log('Filtering services:', {
    provider_id: appointmentForm.provider_id,
    total_services: services.value.length,
    services_sample: services.value.slice(0, 3).map(s => ({ id: s?.id, name: s?.name, provider_id: s?.provider_id }))
  })

  if (!appointmentForm.provider_id) return []

  const filtered = services.value.filter(service => service?.provider_id == appointmentForm.provider_id)
  console.log('Filtered services result:', filtered.length, filtered.map(s => ({ id: s?.id, name: s?.name })))

  return filtered
})

const selectedService = computed(() => {
  if (!appointmentForm.service_id || !Array.isArray(services.value)) return null
  return services.value.find(service => service?.id == appointmentForm.service_id)
})

const getSelectedProviderName = () => {
  if (!appointmentForm.provider_id) return 'Not selected'
  const provider = providers.value.find(p => p?.id == appointmentForm.provider_id)
  return provider?.name || 'Unknown Provider'
}

const isFormValid = computed(() => {
  // For non-admin users, clinic_id might be auto-set, so only require it for admin users
  const clinicRequired = (props.userRole === 'admin' || props.userRole === 'super_admin') ? appointmentForm.clinic_id : true
  
  return clinicRequired &&
         appointmentForm.provider_id &&
         appointmentForm.service_id &&
         appointmentForm.patient_id &&
         appointmentForm.date &&
         selectedTimeSlot.value &&
         (appointmentForm.description || true) // Description becomes reason, which is required
})

// Methods
const loadAppointments = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.current_page,
      per_page: pagination.per_page,
      sort_by: sortColumn.value,
      sort_dir: sortDirection.value
    }

    // Remove empty filters
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    // Build URL with query parameters
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `/appointments-list?${queryString}` : '/appointments-list'

    const response = await get(url)
    console.log('Appointments API response:', response)
    console.log('Total appointments returned:', response?.data?.length || 0)

    // Handle paginated response structure
    if (response && response.data) {
      appointments.value = response.data || []
      metadata.value = response.metadata || {}

      // Update pagination
      if (response.pagination) {
        Object.assign(pagination, response.pagination)
      }
    } else {
      // Fallback for direct array response
      appointments.value = Array.isArray(response) ? response : []
    }

  } catch (error) {
    console.error('Error loading appointments:', error)
    showError('Failed to load appointments')
  } finally {
    loading.value = false
  }
}

const loadDropdownData = async () => {
  try {
    // Load clinics (for admin users)
    if (props.userRole === 'admin' || props.userRole === 'super_admin') {
      const clinicsResponse = await get('/clinics-list-dropdown')
      clinics.value = clinicsResponse.data || []
    }

    // Load providers
    const providersResponse = await get('/providers-list-dropdown')
    providers.value = providersResponse.data || []
    console.log('Loaded providers:', providers.value)

    // Load patients
    const patientsResponse = await get('/patients-list-dropdown')
    patients.value = patientsResponse.data || []
    console.log('Loaded patients:', patients.value)

    // Load services
    const servicesResponse = await get('/services-list-dropdown')
    console.log('Services response:', servicesResponse)
    services.value = servicesResponse?.data || []

    console.log('Loaded services:', services.value)
    // Initialize clinic for non-admin users
    await initializeClinicForUser()

  } catch (error) {
    console.error('Error loading dropdown data:', error)
  }
}

const applyFilters = () => {
  pagination.current_page = 1
  selectedAppointments.value = []
  loadAppointments()
}

const onManualDateChange = () => {
  // Clear date preset when manual dates are selected
  filters.date_preset = ''
  applyFilters()
}

const applyDatePreset = (preset) => {
  // Clear manual date filters when using presets
  filters.start_date = ''
  filters.end_date = ''
  filters.date_preset = preset

  applyFilters()
}

const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  selectedAppointments.value = []
  applyFilters()
}



const changePage = (page) => {
  pagination.current_page = page
  loadAppointments()
}

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedAppointments.value = []
  } else {
    selectedAppointments.value = appointments.value.map(a => a.id)
  }
}

const toggleDropdown = (appointmentId) => {
  dropdownOpen.value = dropdownOpen.value === appointmentId ? null : appointmentId
}

// Appointment actions
const viewAppointment = (appointment) => {
  dropdownOpen.value = null
  selectedAppointment.value = appointment
  showAppointmentModal.value = true
}

const recordVideoConference = (appointment) => {
  dropdownOpen.value = null
  // Navigate to video consultation initialization
  window.location.href = `/video/initialize/${appointment.id}`
}

const rescheduleAppointment = (appointment) => {
  dropdownOpen.value = null
  appointmentToReschedule.value = appointment
  showRescheduleModal.value = true
}

const viewPatientDashboard = (appointment) => {
  dropdownOpen.value = null
  // Navigate to patients management page with patient context
  window.location.href = `/patients?search=${appointment.patient?.name || ''}`
}

const viewPatientDetails = (appointment) => {
  selectedPatient.value = appointment.patient
  showPatientModal.value = true
}

const closePatientModal = () => {
  showPatientModal.value = false
  selectedPatient.value = null
}

const closeAppointmentModal = () => {
  showAppointmentModal.value = false
  selectedAppointment.value = null
}

const closeRescheduleModal = () => {
  showRescheduleModal.value = false
  appointmentToReschedule.value = null
}

const handleReschedule = async (rescheduleData) => {
  try {
    // Convert time to time_slot format expected by the API
    const timeSlot = {
      start_time: rescheduleData.newTime,
      end_time: addMinutesToTime(rescheduleData.newTime, 30) // Assuming 30-minute slots
    }

    const response = await post(`/appointments/${rescheduleData.appointmentId}/reschedule`, {
      date: rescheduleData.newDate,
      time_slot: timeSlot,
      notes: rescheduleData.reason,
      notify_patient: rescheduleData.notifyPatient,
      notify_provider: rescheduleData.notifyProvider,
      status: 'rescheduled'
    })

    if (!response) {
      showError('Failed to reschedule appointment. Please try again.')
      return
    }

    if (response?.data?.appointment) {
      // Update the appointment in the list
      const index = appointments.value.findIndex(a => a.id === rescheduleData.appointmentId)
      if (index !== -1) {
        appointments.value[index] = response.data.appointment
      }
    }

    showSuccess('Appointment rescheduled successfully')
    closeRescheduleModal()

    // Refresh the appointments list to get updated data
    await loadAppointments()
  } catch (error) {
    console.error('Reschedule failed:', error)
    showError(error.response?.data?.message || error.response?.data?.error || 'Failed to reschedule appointment')
  }
}

// Helper function to add minutes to time string
const addMinutesToTime = (timeString, minutes) => {
  const [hours, mins] = timeString.split(':').map(Number)
  const totalMinutes = hours * 60 + mins + minutes
  const newHours = Math.floor(totalMinutes / 60)
  const newMins = totalMinutes % 60
  return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`
}

// Helper function to format appointment time for display
const getAppointmentTimeDisplay = (appointment) => {
  if (!appointment?.time_slot?.start_time) {
    return 'N/A'
  }
  return displayTime(appointment.time_slot.start_time)
}

// Helper function to get time range display
const getTimeRangeDisplay = (appointment) => {
  if (!appointment?.time_slot) {
    return 'N/A'
  }
  return formatTimeRange(appointment.time_slot)
}

// Check-in/Check-out helper functions
const getCheckInStatus = (appointment) => {
  if (appointment.checked_out_at) {
    return 'Checked Out'
  } else if (appointment.checked_in_at) {
    return 'Ready for Consultation'
  } else {
    return 'Pending Check-in'
  }
}

const getCheckInIcon = (appointment) => {
  if (appointment.checked_out_at) {
    return 'log-out'
  } else if (appointment.checked_in_at) {
    return 'check-circle'
  } else {
    return 'clock'
  }
}

const getCheckInBadgeClass = (appointment) => {
  if (appointment.checked_out_at) {
    return 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border border-gray-300 shadow-sm'
  } else if (appointment.checked_in_at) {
    return 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300 shadow-sm'
  } else {
    return 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700 border border-orange-300 shadow-sm'
  }
}

const canCheckIn = (appointment) => {
  return appointment.status === 'rescheduled' ||
         appointment.status === 'scheduled' ||
         appointment.status === 'no_show'
}

const canRescheduleAppointment = (appointment) => {
  // Allow rescheduling for scheduled, rescheduled, and no_show appointments
  // Don't allow rescheduling for completed, cancelled, or in_progress appointments
  const allowedStatuses = ['scheduled', 'rescheduled', 'no_show']
  return allowedStatuses.includes(appointment.status)
}

const shouldShowQuickActions = (appointment) => {
  // Hide quick actions bar for checked-out appointments
  if (appointment.checked_out_at) {
    return false
  }

  // Show if there are any available actions
  return (
    // Check-in action available (including for no-show appointments)
    (!appointment.checked_in_at && (canCheckIn(appointment) || appointment.status === 'no_show')) ||
    // Check-out action available
    (appointment.checked_in_at && !appointment.checked_out_at) ||
    // Consultation actions available
    appointment.consultation_id ||
    canCreateConsultation(appointment)
  )
}

// Check-in/Check-out functions
const checkInPatient = async (appointment) => {
  if (checkingIn.value === appointment.id) return

  checkingIn.value = appointment.id
  try {
    const response = await post(`/appointments/${appointment.id}/check-in`)

    if (response?.data?.appointment) {
      // Update the appointment in the list
      const index = appointments.value.findIndex(a => a.id === appointment.id)
      if (index !== -1) {
        appointments.value[index] = response.data.appointment

        // Update the selected appointment in modal if it's the same appointment
        if (selectedAppointment.value && selectedAppointment.value.id === appointment.id) {
          selectedAppointment.value = response.data.appointment
        }
      }
    } else {
      // Fallback: update local state if response doesn't contain appointment data
      const index = appointments.value.findIndex(a => a.id === appointment.id)
      if (index !== -1) {
        const updatedAppointment = {
          ...appointments.value[index],
          checked_in_at: new Date().toISOString(),
          check_in_status: 'checked_in',
          status: 'in_progress'
        }
        appointments.value[index] = updatedAppointment

        // Update the selected appointment in modal if it's the same appointment
        if (selectedAppointment.value && selectedAppointment.value.id === appointment.id) {
          selectedAppointment.value = updatedAppointment
        }
      }
    }

    showSuccess('Patient checked in successfully')
  } catch (error) {
    console.error('Check-in failed:', error)
    showError(error.response?.data?.error || 'Failed to check in patient')
  } finally {
    checkingIn.value = null
  }
}

const checkOutPatient = async (appointment) => {
  if (checkingOut.value === appointment.id) return

  checkingOut.value = appointment.id
  try {
    const response = await post(`/appointments/${appointment.id}/check-out`)

    if (response?.data?.appointment) {
      // Update the appointment in the list
      const index = appointments.value.findIndex(a => a.id === appointment.id)
      if (index !== -1) {
        appointments.value[index] = response.data.appointment

        // Update the selected appointment in modal if it's the same appointment
        if (selectedAppointment.value && selectedAppointment.value.id === appointment.id) {
          selectedAppointment.value = response.data.appointment
        }
      }
    } else {
      // Fallback: update local state if response doesn't contain appointment data
      const index = appointments.value.findIndex(a => a.id === appointment.id)
      if (index !== -1) {
        const updatedAppointment = {
          ...appointments.value[index],
          checked_out_at: new Date().toISOString(),
          check_in_status: 'checked_out',
          status: 'completed',
          ended_at: new Date().toISOString()
        }
        appointments.value[index] = updatedAppointment

        // Update the selected appointment in modal if it's the same appointment
        if (selectedAppointment.value && selectedAppointment.value.id === appointment.id) {
          selectedAppointment.value = updatedAppointment
        }
      }
    }

    showSuccess('Patient checked out successfully')
  } catch (error) {
    console.error('Check-out failed:', error)
    showError(error.response?.data?.error || 'Failed to check out patient')
  } finally {
    checkingOut.value = null
  }
}

// Helper function to check if current user is a clinician
const isCurrentUserClinician = computed(() => {
  if (!props.currentUser) return false

  // Check if user has is_clinician flag set
  if (props.currentUser.is_clinician) return true

  // Check if user has provider relationship
  if (props.currentUser.provider) return true

  // Check if user has provider role
  if (props.userRole === 'provider') return true

  return false
})

// Consultation-related functions
const canCreateConsultation = (appointment) => {
  // Only allow consultation creation for rescheduled appointments that are checked in
  // AND only if the current user is a clinician
  return ['rescheduled', 'in_progress', 'scheduled'].includes(appointment.status) &&
         !appointment.consultation_id &&
         appointment.checked_in_at &&
         !appointment.checked_out_at &&
         isCurrentUserClinician.value
}

const viewConsultation = (appointment) => {
  dropdownOpen.value = null
  // Navigate to consultation view
  window.location.href = `/consultations/${appointment.consultation_id}`
}

const createConsultation = async (appointment) => {
  dropdownOpen.value = null

  try {
    // Create consultation from appointment using the consultation API
    const response = await createFromAppointment(appointment.id)

    // Check if the response indicates user is not a clinician
    if (response?.data?.user_type === 'non_clinician') {
      showAlert(
        'Clinician Access Required',
        response.data.message || 'Only clinicians can create consultations. Please contact your administrator to get clinician access.',
        'info'
      )
      return
    }

    // Check if the response indicates insufficient access
    if (response?.data?.user_type === 'insufficient_access') {
      showAlert(
        'Access Denied',
        response.data.message || 'You do not have access to create consultations for this appointment.',
        'warning'
      )
      return
    }

    if (response?.data?.id) {
      showSuccess('Consultation created successfully')
      // Navigate to the consultation edit page to start the consultation
      window.location.href = `/consultations/${response.data.id}/edit`
    } else if (response?.data?.success === false) {
      showAlert(
        'Unable to Create Consultation',
        response.data.message || 'Failed to create consultation.',
        'warning'
      )
    } else {
      showError('Failed to create consultation. Invalid response.')
    }
  } catch (error) {
    console.error('Error creating consultation:', error)

    // Check if it's a permission error
    if (error.response?.status === 403) {
      showAlert(
        'Access Denied',
        'You do not have permission to create consultations. Please contact your administrator.',
        'warning'
      )
    } else {
      showError('Failed to create consultation. Please try again.')
    }
  }
}

const deleteAppointment = async (appointment) => {
  dropdownOpen.value = null

  const confirmed = await showConfirm(
    'Delete Appointment',
    `Are you sure you want to delete the appointment with ${appointment.patient?.user?.name}?`,
    'Delete',
    'Cancel'
  )

  if (!confirmed) return

  try {
    console.log('Deleting appointment:', appointment.id)
    const response = await del(`/delete-appointment/${appointment.id}`)
    console.log('Delete response:', response)
    showSuccess('Appointment deleted successfully')
    loadAppointments()
  } catch (error) {
    console.error('Delete appointment error:', error)
    const errorMessage = error.response?.data?.message || error.message || 'Failed to delete appointment'
    showError(errorMessage)
  }
}

// Bulk actions
const confirmBulkDelete = async () => {
  const confirmed = await showConfirm(
    'Delete Appointments',
    `Are you sure you want to delete ${selectedAppointments.value.length} appointments?`,
    'Delete All',
    'Cancel'
  )

  if (!confirmed) return

  try {
    const response = await post('/appointments/bulk-delete', {
      appointment_ids: selectedAppointments.value
    })

    if (response && response.deleted_count !== undefined) {
      let message = `Successfully deleted ${response.deleted_count} appointments`

      if (response.skipped_appointments && response.skipped_appointments.length > 0) {
        message += `\n\n${response.skipped_appointments.length} appointments could not be deleted:`
        response.skipped_appointments.forEach(skipped => {
          message += `\n• Appointment #${skipped.id}: ${skipped.reason}`
        })

        showAlert(
          'Partial Success',
          message,
          'warning',
          'OK'
        )
      } else {
        showSuccess(message)
      }
    } else {
      showSuccess(`Successfully deleted ${selectedAppointments.value.length} appointments`)
    }

    selectedAppointments.value = []
    loadAppointments()
  } catch (error) {
    console.error('Bulk delete error:', error)
    showError(error.response?.data?.message || 'Failed to delete appointments')
  }
}

const exportAppointments = async () => {
  try {
    const params = { ...filters }

    // Remove empty filters
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('/appointments/export', {
      params,
      responseType: 'blob'
    })

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `appointments_export_${new Date().toISOString().split('T')[0]}.csv`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    showSuccess('Appointments exported successfully')
  } catch (error) {
    showError('Failed to export appointments')
  }
}

// Appointment form methods
const onClinicChange = async () => {
  appointmentForm.provider_id = ''
  appointmentForm.service_id = ''
  availableTimeSlots.value = []
  selectedTimeSlot.value = null

  if (appointmentForm.clinic_id) {
    try {
      // Load providers for the selected clinic (admin users)
      if (props.userRole === 'admin' || props.userRole === 'super_admin') {
        const providersResponse = await get(`/providers-list?clinic_id=${appointmentForm.clinic_id}`)
        providers.value = providersResponse.data || []
        console.log('Reloaded providers for clinic:', appointmentForm.clinic_id, providers.value)
      }

      // Load services for the selected clinic
      const servicesResponse = await get(`/services-list-dropdown?clinic_id=${appointmentForm.clinic_id}`)
      services.value = servicesResponse?.data || []
    } catch (error) {
      console.error('Error loading clinic data:', error)
    }
  }
}

const initializeClinicForUser = async () => {
  // For non-admin users, automatically set their clinic
  if (props.userRole !== 'admin' && props.userRole !== 'super_admin') {
    try {
      // Get user's clinic information
      const response = await get('/user-clinic-info')
      if (response.data && response.data.clinic_id) {
        appointmentForm.clinic_id = response.data.clinic_id
        onClinicChange()
      } else {
        // Fallback: Find the user's clinic from the providers list
        const userProvider = providers.value.find(provider => provider.user_id === props.userId)
        if (userProvider && userProvider.clinic_id) {
          appointmentForm.clinic_id = userProvider.clinic_id
          onClinicChange()
        }
      }
    } catch (error) {
      console.error('Error getting user clinic info:', error)
      // Fallback: Find the user's clinic from the providers list
      const userProvider = providers.value.find(provider => provider.user_id === props.userId)
      if (userProvider && userProvider.clinic_id) {
        appointmentForm.clinic_id = userProvider.clinic_id
        onClinicChange()
      }
    }
  }
}

const onProviderChange = async () => {
  appointmentForm.service_id = ''
  availableTimeSlots.value = []
  selectedTimeSlot.value = null

  if (appointmentForm.provider_id && appointmentForm.date) {
    loadAvailableSlots()
  }
}

const onServiceChange = () => {
  // Clear time slots when service changes as it might affect availability
  availableTimeSlots.value = []
  selectedTimeSlot.value = null

  if (appointmentForm.provider_id && appointmentForm.date && appointmentForm.service_id) {
    loadAvailableSlots()
  }
}

const onDateChange = () => {
  availableTimeSlots.value = []
  selectedTimeSlot.value = null

  if (appointmentForm.provider_id && appointmentForm.date) {
    loadAvailableSlots()
  }
}

const loadAvailableSlots = async () => {
  if (!appointmentForm.provider_id || !appointmentForm.date) return

  try {
    console.log('Fetching available slots for provider:', appointmentForm.provider_id, 'date:', appointmentForm.date)
    
    const response = await get(`/providers/${appointmentForm.provider_id}/available-slots?date=${appointmentForm.date}&service_id=${appointmentForm.service_id || ''}`)
    
    console.log('Available slots response:', response)
    
    if (response && response.slots && Array.isArray(response.slots)) {
      // Filter out past time slots for today
      const now = new Date()
      const selectedDate = new Date(appointmentForm.date + 'T00:00:00') // Use local timezone
      const isToday = selectedDate.toDateString() === now.toDateString()
      
      const filteredSlots = response.slots.filter(slot => {
        if (!isToday) return true // Show all slots for future dates
        
        // For today, only show future time slots (add 5 minute buffer)
        const slotTime = new Date(`${appointmentForm.date}T${slot.start_time}:00`)
        const bufferTime = new Date(now.getTime() + 5 * 60000) // Add 5 minutes buffer
        return slotTime > bufferTime
      })
      
      // Transform backend response to match frontend format
      availableTimeSlots.value = filteredSlots.map(slot => ({
        time: slot.start_time,
        available: slot.available,
        start_time: slot.start_time,
        end_time: slot.end_time
      }))
      
      console.log('Processed available slots:', availableTimeSlots.value)
    } else {
      console.warn('No valid slots received from API')
      availableTimeSlots.value = []
    }
  } catch (error) {
    console.error('Error loading available slots:', error)
    availableTimeSlots.value = []
  }
}

const selectTimeSlot = (slot) => {
  selectedTimeSlot.value = slot
}

// Helper function to get current date in YYYY-MM-DD format (browser timezone)
const getCurrentDate = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const resetAppointmentForm = () => {
  // Reset all form fields to initial state
  Object.assign(appointmentForm, {
    clinic_id: '',
    provider_id: '',
    service_id: '',
    patient_id: '',
    date: '',
    appointment_date: '',
    status: 'scheduled',
    description: '',
    payment_method: 'stripe'
  })

  // Clear time slots and selections
  availableTimeSlots.value = []
  selectedTimeSlot.value = null

  // Clear any loading states
  savingAppointment.value = false

  console.log('Appointment form reset successfully')
}

const saveAppointment = async () => {
  console.log('Save appointment called')
  console.log('Form valid:', isFormValid.value)
  console.log('Form data:', appointmentForm)
  console.log('Selected time slot:', selectedTimeSlot.value)

  if (!isFormValid.value) {
    console.log('Form validation failed')
    return
  }

  savingAppointment.value = true
  console.log('Setting saving state to true')

  try {
    // Get the selected time slot details
    const startTime = selectedTimeSlot.value.start_time || selectedTimeSlot.value.time
    const endTime = selectedTimeSlot.value.end_time
    const serviceDuration = selectedService.value?.duration || 30 // Default to 30 minutes

    // Create proper datetime for scheduled_at field (ISO format with timezone)
    const localDateTime = new Date(`${appointmentForm.date}T${startTime}:00`)
    const scheduledAt = localDateTime.toISOString()
    
    // Calculate end time if not provided
    let calculatedEndTime = endTime
    if (!calculatedEndTime) {
      const [startHour, startMinute] = startTime.split(':').map(Number)
      const startDate = new Date()
      startDate.setHours(startHour, startMinute, 0, 0)
      const endDate = new Date(startDate.getTime() + serviceDuration * 60000)
      calculatedEndTime = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`
    }

    const appointmentData = {
      provider_id: appointmentForm.provider_id,
      service_id: appointmentForm.service_id,
      patient_id: appointmentForm.patient_id,
      date: appointmentForm.date,
      scheduled_at: scheduledAt,
      time_slot: {
        start_time: startTime,
        end_time: calculatedEndTime
      },
      duration: serviceDuration,
      reason: appointmentForm.description || 'General consultation',
      notes: appointmentForm.description || null,
      status: appointmentForm.status || 'scheduled',
      is_telemedicine: false,
      payment_method: appointmentForm.payment_method || 'stripe'
    }

    console.log('Sending appointment data:', appointmentData)
    
    // Use axios directly for better error handling
    const axiosInstance = (await import('axios')).default
    
    try {
      const response = await axiosInstance.post('/save-appointment', appointmentData)
      console.log('Axios response:', response)
      const data = response.data

      if (data && (data.success || data.data)) {
        console.log('Appointment saved successfully')
        
        // Close modal first to prevent z-index issues
        showAddAppointment.value = false
        resetAppointmentForm()

        // Clear additional form state
        availableTimeSlots.value = []
        selectedTimeSlot.value = null

        // Reload appointments list
        loadAppointments()

        // Show success message after modal is closed
        await showAlert(
          'Success!',
          data.message || 'Appointment has been created successfully.',
          'success',
          'OK'
        )
      } else {
        console.log('Unexpected response format:', data)
        showError('Unexpected response from server')
      }
    } catch (axiosError) {
      console.error('Axios error:', axiosError)
      console.error('Response data:', axiosError.response?.data)
      console.error('Response status:', axiosError.response?.status)
      console.error('Response headers:', axiosError.response?.headers)
      
      // Re-throw to be caught by outer catch
      throw axiosError
    }
  } catch (error) {
    console.error('Error saving appointment:', error)
    console.error('Error details:', error.response?.data)
    console.error('Full error response:', error.response)
    
    // Show detailed validation errors if available
    if (error.response?.data?.errors) {
      const errorMessages = Object.values(error.response.data.errors).flat().join('\n')
      showError(`Validation Error:\n${errorMessages}`)
    } else {
      showError(error.response?.data?.message || 'Failed to create appointment')
    }
  } finally {
    console.log('Setting saving state to false')
    savingAppointment.value = false
  }
}

// Formatting methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    }

    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow'
    }

    return date.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
    })
  } catch (error) {
    return dateString
  }
}

const formatTime = (dateString) => {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  } catch (error) {
    return 'N/A'
  }
}

const formatPrice = (price) => {
  if (!price && price !== 0) return 'Free'

  // Convert to number if it's a string
  const numPrice = typeof price === 'string' ? parseFloat(price) : price

  if (isNaN(numPrice)) return 'Free'

  // Format as currency (assuming GBP based on your screenshot)
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(numPrice)
}

const formatCurrency = (amount) => {
  return parseFloat(amount || 0).toFixed(2)
}

const formatStatus = (status) => {
  const statusMap = {
    'scheduled': 'Scheduled',
    'rescheduled': 'Rescheduled',
    'in_progress': 'In Progress',
    'completed': 'Completed',
    'cancelled': 'Cancelled',
    'no_show': 'No Show'
  }
  return statusMap[status] || status
}

const formatPaymentStatus = (status) => {
  const statusMap = {
    'paid': 'Paid',
    'pending': 'Pending',
    'unpaid': 'Unpaid',
    'failed': 'Failed',
    'refunded': 'Refunded',
    'pending_payment': 'Payment Required'
  }
  return statusMap[status] || status
}

// Badge classes with modern gradient styling
const getStatusBadgeClass = (status) => {
  const classes = {
    'scheduled': 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 shadow-sm',
    'rescheduled': 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-300 shadow-sm',
    'in_progress': 'bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800 border border-amber-300 shadow-sm',
    'completed': 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 shadow-sm',
    'cancelled': 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 shadow-sm',
    'no_show': 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 shadow-sm'
  }
  return classes[status] || 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 shadow-sm'
}

const getPaymentBadgeClass = (status) => {
  const classes = {
    'paid': 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 shadow-sm',
    'pending': 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300 shadow-sm',
    'unpaid': 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 shadow-sm',
    'failed': 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 shadow-sm',
    'refunded': 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 shadow-sm',
    'pending_payment': 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 border border-orange-300 shadow-sm'
  }
  return classes[status] || 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 shadow-sm'
}

const getTypeBadgeClass = (isTelemedicine) => {
  return isTelemedicine
    ? 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-300 shadow-sm'
    : 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 border border-orange-300 shadow-sm'
}

// Lifecycle
onMounted(() => {
  loadDropdownData()
  loadAppointments()
})

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    dropdownOpen.value = null
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.appointments-management {
  min-height: 100vh;
}

.appointments-management > * + * {
  margin-top: 1.5rem;
}

/* Custom scrollbar for table */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.25rem;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles */
.focus\:ring-blue-500:focus {
  --tw-ring-color: rgb(59 130 246 / 0.5);
}

/* Hover effects */
.hover\:bg-gray-50:hover {
  background-color: rgb(249 250 251);
}

.hover\:bg-blue-700:hover {
  background-color: rgb(29 78 216);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Modal fixes */
.fixed {
  position: fixed !important;
}

.z-\[9999\] {
  z-index: 9999 !important;
}

/* Ensure modal content is properly positioned */
.modal-content {
  max-height: 90vh;
  overflow-y: auto;
}
</style>
