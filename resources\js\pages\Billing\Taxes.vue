<template>
    <AppLayout title="Tax Management">
        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">Tax Management</h2>
                                <p class="text-gray-600 mt-1">Configure taxes for your clinic</p>
                            </div>
                            <button @click="showCreateModal = true" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Add New Tax
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Taxes List -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="tax in taxes" :key="tax.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ tax.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ tax.type === 'percentage' ? 'Percentage' : 'Fixed Amount' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ tax.type === 'percentage' ? tax.rate + '%' : '£' + tax.rate }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="tax.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ tax.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button @click="editTax(tax)" 
                                                        class="text-blue-600 hover:text-blue-900">
                                                    Edit
                                                </button>
                                                <button @click="deleteTax(tax.id)" 
                                                        class="text-red-600 hover:text-red-900">
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Create/Edit Tax Modal -->
                <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                {{ showCreateModal ? 'Add New Tax' : 'Edit Tax' }}
                            </h3>
                            <form @submit.prevent="saveTax">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                                    <input v-model="taxForm.name" 
                                           type="text" 
                                           required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                                    <select v-model="taxForm.type" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="percentage">Percentage</option>
                                        <option value="fixed">Fixed Amount</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Rate {{ taxForm.type === 'percentage' ? '(%)' : '(£)' }}
                                    </label>
                                    <input v-model="taxForm.rate" 
                                           type="number" 
                                           step="0.01"
                                           min="0"
                                           required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea v-model="taxForm.description" 
                                              rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                </div>

                                <div class="mb-6">
                                    <label class="flex items-center">
                                        <input v-model="taxForm.is_active" 
                                               type="checkbox" 
                                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700">Active</span>
                                    </label>
                                </div>

                                <div class="flex justify-end space-x-3">
                                    <button type="button" 
                                            @click="closeModal"
                                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" 
                                            :disabled="loading"
                                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50">
                                        {{ loading ? 'Saving...' : 'Save' }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import axios from 'axios'

const taxes = ref([])
const loading = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingTax = ref(null)

const taxForm = reactive({
    name: '',
    type: 'percentage',
    rate: '',
    description: '',
    is_active: true
})

const loadTaxes = async () => {
    try {
        const response = await axios.get('/taxes')
        taxes.value = response.data.data
    } catch (error) {
        console.error('Error loading taxes:', error)
    }
}

const saveTax = async () => {
    loading.value = true
    try {
        if (showEditModal.value && editingTax.value) {
            await axios.put(`/taxes/${editingTax.value.id}`, taxForm)
        } else {
            await axios.post('/taxes', taxForm)
        }
        closeModal()
        await loadTaxes()
    } catch (error) {
        console.error('Error saving tax:', error)
    } finally {
        loading.value = false
    }
}

const editTax = (tax) => {
    editingTax.value = tax
    Object.assign(taxForm, {
        name: tax.name,
        type: tax.type,
        rate: tax.rate,
        description: tax.description || '',
        is_active: tax.is_active
    })
    showEditModal.value = true
}

const deleteTax = async (taxId) => {
    if (confirm('Are you sure you want to delete this tax?')) {
        try {
            await axios.delete(`/taxes/${taxId}`)
            await loadTaxes()
        } catch (error) {
            console.error('Error deleting tax:', error)
        }
    }
}

const closeModal = () => {
    showCreateModal.value = false
    showEditModal.value = false
    editingTax.value = null
    Object.assign(taxForm, {
        name: '',
        type: 'percentage',
        rate: '',
        description: '',
        is_active: true
    })
}

onMounted(() => {
    loadTaxes()
})
</script>
