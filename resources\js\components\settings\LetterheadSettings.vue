<template>
  <div class="letterhead-settings">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Letterhead Settings</h3>
        <p class="mt-1 text-sm text-gray-500">
          Customize your clinic's letterhead for prescriptions and medical documents
        </p>
      </div>

      <div class="p-6 space-y-8">
        <!-- Header Settings -->
        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900">Header Settings</h4>
          
          <!-- Header Type Selection -->
          <div class="space-y-3">
            <label class="text-sm font-medium text-gray-700">Header Type</label>
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="settings.header_type"
                  value="html"
                  class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">HTML Content</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="settings.header_type"
                  value="image"
                  class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Image</span>
              </label>
            </div>
          </div>

          <!-- Header HTML Content -->
          <div v-if="settings.header_type === 'html'" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Header HTML Content</label>
            <textarea
              v-model="settings.header_html"
              rows="6"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter custom HTML for header (leave empty to use default clinic information)"
            ></textarea>
            <p class="text-xs text-gray-500">
              You can use HTML tags for formatting. Leave empty to use default clinic header.
            </p>
          </div>

          <!-- Header Image Upload -->
          <div v-if="settings.header_type === 'image'" class="space-y-4">
            <div v-if="settings.header_content" class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Current Header Image</label>
              <div class="flex items-center space-x-4">
                <img
                  :src="settings.header_content"
                  alt="Header Image"
                  class="h-20 w-auto border border-gray-300 rounded"
                />
                <button
                  @click="removeHeaderImage"
                  type="button"
                  class="px-3 py-1 text-sm text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ settings.header_content ? 'Replace Header Image' : 'Upload Header Image' }}
              </label>
              <input
                ref="headerImageInput"
                type="file"
                accept="image/*"
                @change="uploadHeaderImage"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p class="text-xs text-gray-500">
                Recommended size: 800x200px. Max file size: 2MB. Formats: JPG, PNG, GIF
              </p>
            </div>
          </div>
        </div>

        <!-- Footer Settings -->
        <div class="space-y-4 border-t border-gray-200 pt-8">
          <h4 class="text-md font-medium text-gray-900">Footer Settings</h4>
          
          <!-- Footer Type Selection -->
          <div class="space-y-3">
            <label class="text-sm font-medium text-gray-700">Footer Type</label>
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="settings.footer_type"
                  value="html"
                  class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">HTML Content</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="settings.footer_type"
                  value="image"
                  class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Image</span>
              </label>
            </div>
          </div>

          <!-- Footer HTML Content -->
          <div v-if="settings.footer_type === 'html'" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Footer HTML Content</label>
            <textarea
              v-model="settings.footer_html"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter custom HTML for footer (leave empty to use default footer)"
            ></textarea>
            <p class="text-xs text-gray-500">
              You can use HTML tags for formatting. Leave empty to use default footer.
            </p>
          </div>

          <!-- Footer Image Upload -->
          <div v-if="settings.footer_type === 'image'" class="space-y-4">
            <div v-if="settings.footer_content" class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Current Footer Image</label>
              <div class="flex items-center space-x-4">
                <img
                  :src="settings.footer_content"
                  alt="Footer Image"
                  class="h-16 w-auto border border-gray-300 rounded"
                />
                <button
                  @click="removeFooterImage"
                  type="button"
                  class="px-3 py-1 text-sm text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ settings.footer_content ? 'Replace Footer Image' : 'Upload Footer Image' }}
              </label>
              <input
                ref="footerImageInput"
                type="file"
                accept="image/*"
                @change="uploadFooterImage"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p class="text-xs text-gray-500">
                Recommended size: 800x100px. Max file size: 2MB. Formats: JPG, PNG, GIF
              </p>
            </div>
          </div>
        </div>

        <!-- Preview Section -->
        <div class="border-t border-gray-200 pt-8">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-md font-medium text-gray-900">Preview</h4>
            <button
              @click="generatePreview"
              type="button"
              class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
            >
              Generate Preview
            </button>
          </div>
          
          <div v-if="previewHtml" class="border border-gray-300 rounded-lg p-4 bg-gray-50">
            <iframe
              :srcdoc="previewHtml"
              class="w-full h-96 border-0 rounded"
              sandbox="allow-same-origin"
            ></iframe>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3 border-t border-gray-200 pt-6">
          <button
            @click="resetSettings"
            type="button"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Reset
          </button>
          <button
            @click="saveSettings"
            :disabled="saving"
            type="button"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {{ saving ? 'Saving...' : 'Save Settings' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import axios from 'axios'

const toast = useToast()

// Reactive data
const loading = ref(false)
const saving = ref(false)
const previewHtml = ref('')

const settings = ref({
  clinic_id: null,
  header_html: '',
  footer_html: '',
  header_image_url: '',
  footer_image_url: '',
  header_file_id: null,
  footer_file_id: null,
  header_type: 'html',
  footer_type: 'html',
  header_content: null,
  footer_content: null,
  is_active: true,
})

// Template refs
const headerImageInput = ref(null)
const footerImageInput = ref(null)

// Methods
const loadSettings = async () => {
  try {
    loading.value = true
    const response = await axios.get('/letterhead/settings')
    
    if (response.data.success) {
      settings.value = { ...settings.value, ...response.data.data }
    }
  } catch (error) {
    console.error('Error loading letterhead settings:', error)
    toast.error('Failed to load letterhead settings')
  } finally {
    loading.value = false
  }
}

const saveSettings = async () => {
  try {
    saving.value = true
    
    const payload = {
      header_html: settings.value.header_html,
      footer_html: settings.value.footer_html,
      header_image_url: settings.value.header_image_url,
      footer_image_url: settings.value.footer_image_url,
      header_file_id: settings.value.header_file_id,
      footer_file_id: settings.value.footer_file_id,
      header_type: settings.value.header_type,
      footer_type: settings.value.footer_type,
      is_active: settings.value.is_active,
    }
    
    const response = await axios.post('/letterhead/settings', payload)
    
    if (response.data.success) {
      settings.value = { ...settings.value, ...response.data.data }
      toast.success('Letterhead settings saved successfully')
    }
  } catch (error) {
    console.error('Error saving letterhead settings:', error)
    toast.error('Failed to save letterhead settings')
  } finally {
    saving.value = false
  }
}

const uploadHeaderImage = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    const formData = new FormData()
    formData.append('header_image', file)
    
    const response = await axios.post('/letterhead/upload-header-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (response.data.success) {
      settings.value.header_image_url = response.data.data.header_image_url
      settings.value.header_file_id = response.data.data.header_file_id
      settings.value.header_content = response.data.data.header_content
      toast.success('Header image uploaded successfully')
    }
  } catch (error) {
    console.error('Error uploading header image:', error)
    toast.error('Failed to upload header image')
  }
  
  // Reset file input
  if (headerImageInput.value) {
    headerImageInput.value.value = ''
  }
}

const uploadFooterImage = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    const formData = new FormData()
    formData.append('footer_image', file)
    
    const response = await axios.post('/letterhead/upload-footer-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (response.data.success) {
      settings.value.footer_image_url = response.data.data.footer_image_url
      settings.value.footer_file_id = response.data.data.footer_file_id
      settings.value.footer_content = response.data.data.footer_content
      toast.success('Footer image uploaded successfully')
    }
  } catch (error) {
    console.error('Error uploading footer image:', error)
    toast.error('Failed to upload footer image')
  }
  
  // Reset file input
  if (footerImageInput.value) {
    footerImageInput.value.value = ''
  }
}

const removeHeaderImage = async () => {
  try {
    const response = await axios.delete('/letterhead/remove-header-image')
    
    if (response.data.success) {
      settings.value.header_image_url = ''
      settings.value.header_file_id = null
      settings.value.header_content = null
      settings.value.header_type = 'html'
      toast.success('Header image removed successfully')
    }
  } catch (error) {
    console.error('Error removing header image:', error)
    toast.error('Failed to remove header image')
  }
}

const removeFooterImage = async () => {
  try {
    const response = await axios.delete('/letterhead/remove-footer-image')
    
    if (response.data.success) {
      settings.value.footer_image_url = ''
      settings.value.footer_file_id = null
      settings.value.footer_content = null
      settings.value.footer_type = 'html'
      toast.success('Footer image removed successfully')
    }
  } catch (error) {
    console.error('Error removing footer image:', error)
    toast.error('Failed to remove footer image')
  }
}

const generatePreview = () => {
  // Generate a simple preview HTML
  previewHtml.value = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; text-align: center; }
        .content { min-height: 200px; padding: 20px; }
        .footer { border-top: 1px solid #ccc; padding-top: 20px; margin-top: 30px; text-align: center; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="header">
        ${settings.value.header_type === 'image' && settings.value.header_content 
          ? `<img src="${settings.value.header_content}" style="max-height: 100px;">` 
          : settings.value.header_html || '<h1>Your Clinic Name</h1><p>Default clinic information will appear here</p>'
        }
      </div>
      <div class="content">
        <h2>Sample Document Content</h2>
        <p>This is how your letterhead will appear on prescriptions and medical documents.</p>
      </div>
      <div class="footer">
        ${settings.value.footer_type === 'image' && settings.value.footer_content 
          ? `<img src="${settings.value.footer_content}" style="max-height: 50px;">` 
          : settings.value.footer_html || '<p>Generated on ' + new Date().toLocaleDateString() + '</p>'
        }
      </div>
    </body>
    </html>
  `
}

const resetSettings = () => {
  settings.value = {
    clinic_id: settings.value.clinic_id,
    header_html: '',
    footer_html: '',
    header_image_url: '',
    footer_image_url: '',
    header_file_id: null,
    footer_file_id: null,
    header_type: 'html',
    footer_type: 'html',
    header_content: null,
    footer_content: null,
    is_active: true,
  }
  previewHtml.value = ''
}

// Lifecycle
onMounted(() => {
  loadSettings()
})
</script>
