<?php

namespace App\Services;

use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class StripeService
{
    public $stripe; // Made public for PaymentGatewayService access
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Create a payment intent for a one-time payment
     */
    public function createPaymentIntent(User $user, SubscriptionPlan $plan): array
    {
        try {
            // Create or get customer
            $stripeCustomer = $this->getOrCreateCustomer($user);

            // Create payment intent
            $paymentIntent = $this->stripe->paymentIntents->create([
                'amount' => (int)($plan->price * 100), // Convert to cents
                'currency' => $plan->currency,
                'customer' => $stripeCustomer->id,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'plan_slug' => $plan->slug,
                ],
                'description' => "Subscription to {$plan->name} plan",
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment intent creation failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'plan_id' => $plan->id,
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Subscribe a user to a plan using Cashier
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, string $paymentMethodId): array
    {
        try {
            // Handle free plans differently - don't create Stripe subscription
            if ($plan->isFree()) {
                // Just assign the plan in our database
                $this->subscriptionService->assignPlan($user, $plan);

                return [
                    'success' => true,
                    'subscription_id' => null,
                    'invoice_url' => null,
                    'message' => "Successfully subscribed to {$plan->name} plan",
                ];
            }

            // Log payment method details for debugging
            Log::info('Creating Stripe subscription', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'payment_method_id' => $paymentMethodId,
                'stripe_key' => substr(config('services.stripe.key'), 0, 20) . '...',
                'stripe_secret' => substr(config('services.stripe.secret'), 0, 20) . '...',
            ]);

            // For paid plans, create Stripe subscription
            $user->createOrGetStripeCustomer();
            
            // Try to retrieve the payment method first to validate it exists
            try {
                $paymentMethod = $this->stripe->paymentMethods->retrieve($paymentMethodId);
                Log::info('Payment method retrieved successfully', [
                    'payment_method_id' => $paymentMethodId,
                    'type' => $paymentMethod->type,
                    'card_brand' => $paymentMethod->card->brand ?? 'N/A',
                    'card_last4' => $paymentMethod->card->last4 ?? 'N/A',
                ]);
            } catch (\Stripe\Exception\InvalidRequestException $e) {
                Log::error('Payment method not found', [
                    'payment_method_id' => $paymentMethodId,
                    'error' => $e->getMessage(),
                ]);
                throw new \Exception('Payment method not found. Please try again.');
            }

            $user->addPaymentMethod($paymentMethodId);
            $user->updateDefaultPaymentMethod($paymentMethodId);

            // Create the subscription
            $subscription = $user->newSubscription('default', $plan->stripe_price_id)
                ->create($paymentMethodId);

            // Update user's subscription plan in our database
            $this->subscriptionService->assignPlan($user, $plan);

            // Create and send invoice for the subscription
            $invoiceResult = $this->createAndSendInvoice($user, $plan);

            $message = "Successfully subscribed to {$plan->name} plan";
            if ($invoiceResult['success']) {
                $message .= ". Invoice has been sent to your email.";
            }

            return [
                'success' => true,
                'subscription_id' => $subscription->stripe_id,
                'invoice_url' => $invoiceResult['invoice_url'] ?? null,
                'message' => $message,
            ];
        } catch (IncompletePayment $e) {
            // Handle payment that requires additional confirmation
            return [
                'success' => false,
                'requires_action' => true,
                'payment_intent_client_secret' => $e->payment->clientSecret(),
                'message' => 'Additional payment confirmation required',
            ];
        } catch (\Exception $e) {
            Log::error('Stripe subscription creation failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'payment_method_id' => $paymentMethodId,
                'stripe_key' => substr(config('services.stripe.key'), 0, 20) . '...',
                'stripe_secret' => substr(config('services.stripe.secret'), 0, 20) . '...',
            ]);

            return [
                'success' => false,
                'message' => 'Subscription failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Cancel a user's subscription
     */
    public function cancelSubscription(User $user): array
    {
        try {
            // Get the active subscription
            $subscription = $user->subscription('default');

            if (!$subscription) {
                return [
                    'success' => false,
                    'message' => 'No active subscription found',
                ];
            }

            // Cancel the subscription
            $subscription->cancel();

            // Assign free plan to the user
            $freePlan = SubscriptionPlan::where('price', 0)->first();
            if ($freePlan) {
                $this->subscriptionService->assignPlan($user, $freePlan);
            }

            return [
                'success' => true,
                'message' => 'Subscription cancelled successfully',
            ];
        } catch (\Exception $e) {
            Log::error('Stripe subscription cancellation failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel subscription: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get or create a Stripe customer for the user
     */
    public function getOrCreateCustomer(User $user)
    {
        if ($user->stripe_id) {
            return $this->stripe->customers->retrieve($user->stripe_id);
        }

        $customer = $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        $user->stripe_id = $customer->id;
        $user->save();

        return $customer;
    }

    /**
     * Handle a successful payment
     */
    public function handleSuccessfulPayment(string $paymentIntentId): array
    {
        try {
            $paymentIntent = $this->stripe->paymentIntents->retrieve($paymentIntentId);
            
            if ($paymentIntent->status !== 'succeeded') {
                return [
                    'success' => false,
                    'message' => 'Payment has not been completed',
                ];
            }

            $userId = $paymentIntent->metadata->user_id ?? null;
            $planId = $paymentIntent->metadata->plan_id ?? null;

            if (!$userId || !$planId) {
                return [
                    'success' => false,
                    'message' => 'Invalid payment metadata',
                ];
            }

            $user = User::find($userId);
            $plan = SubscriptionPlan::find($planId);

            if (!$user || !$plan) {
                return [
                    'success' => false,
                    'message' => 'User or plan not found',
                ];
            }

            // Assign the plan to the user
            $result = $this->subscriptionService->assignPlan($user, $plan);

            return [
                'success' => $result,
                'message' => $result ? 'Payment processed successfully' : 'Failed to update subscription',
            ];
        } catch (\Exception $e) {
            Log::error('Failed to handle successful payment', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process payment: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment methods for a user
     */
    public function getPaymentMethods(User $user): array
    {
        try {
            $user->createOrGetStripeCustomer();
            $methods = $user->paymentMethods();

            return [
                'success' => true,
                'payment_methods' => $methods,
                'default_payment_method' => $user->defaultPaymentMethod(),
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get payment methods', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get payment methods: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get invoices for a user
     */
    public function getUserInvoices(User $user, int $limit = 20): array
    {
        try {
            if (!$user->stripe_id) {
                return [
                    'success' => true,
                    'invoices' => [],
                ];
            }

            $invoices = $this->stripe->invoices->all([
                'customer' => $user->stripe_id,
                'limit' => $limit,
                'expand' => ['data.subscription', 'data.payment_intent'],
            ]);

            $formattedInvoices = [];
            foreach ($invoices->data as $invoice) {
                $formattedInvoices[] = [
                    'id' => $invoice->id,
                    'number' => $invoice->number,
                    'amount_paid' => $invoice->amount_paid / 100, // Convert from cents
                    'amount_due' => $invoice->amount_due / 100,
                    'currency' => strtoupper($invoice->currency),
                    'status' => $invoice->status,
                    'created' => date('Y-m-d H:i:s', $invoice->created),
                    'due_date' => $invoice->due_date ? date('Y-m-d', $invoice->due_date) : null,
                    'paid' => $invoice->paid,
                    'hosted_invoice_url' => $invoice->hosted_invoice_url,
                    'invoice_pdf' => $invoice->invoice_pdf,
                    'description' => $invoice->description,
                    'subscription_id' => $invoice->subscription,
                ];
            }

            return [
                'success' => true,
                'invoices' => $formattedInvoices,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get user invoices', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get invoices: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Send invoice to user via email
     */
    public function sendInvoiceToUser(string $invoiceId): array
    {
        try {
            $invoice = $this->stripe->invoices->sendInvoice($invoiceId);

            return [
                'success' => true,
                'message' => 'Invoice sent successfully',
                'invoice_id' => $invoice->id,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to send invoice', [
                'error' => $e->getMessage(),
                'invoice_id' => $invoiceId,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send invoice: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create and send invoice for a subscription
     */
    public function createAndSendInvoice(User $user, SubscriptionPlan $plan): array
    {
        try {
            // Get or create customer
            $stripeCustomer = $this->getOrCreateCustomer($user);

            // Create invoice item
            $this->stripe->invoiceItems->create([
                'customer' => $stripeCustomer->id,
                'amount' => (int)($plan->price * 100), // Convert to cents
                'currency' => $plan->currency,
                'description' => "Subscription to {$plan->name} plan",
            ]);

            // Create invoice
            $invoice = $this->stripe->invoices->create([
                'customer' => $stripeCustomer->id,
                'collection_method' => 'send_invoice',
                'description' => "Invoice for {$plan->name} subscription",
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                ],
            ]);

            // Finalize and send the invoice
            $this->stripe->invoices->finalizeInvoice($invoice->id);
            $this->stripe->invoices->sendInvoice($invoice->id);

            Log::info('Invoice created and sent', [
                'user_id' => $user->id,
                'invoice_id' => $invoice->id,
                'plan_id' => $plan->id,
            ]);

            return [
                'success' => true,
                'invoice_id' => $invoice->id,
                'invoice_url' => $invoice->hosted_invoice_url,
                'message' => 'Invoice created and sent successfully',
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create and send invoice', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'plan_id' => $plan->id,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create invoice: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Download invoice PDF
     */
    public function downloadInvoice(User $user, string $invoiceId): array
    {
        try {
            $invoice = $this->stripe->invoices->retrieve($invoiceId);

            // Verify the invoice belongs to the user
            if ($invoice->customer !== $user->stripe_id) {
                return [
                    'success' => false,
                    'message' => 'Invoice not found or access denied',
                ];
            }

            return [
                'success' => true,
                'pdf_url' => $invoice->invoice_pdf,
                'hosted_url' => $invoice->hosted_invoice_url,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to download invoice', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'invoice_id' => $invoiceId,
            ]);

            return [
                'success' => false,
                'message' => 'Failed to download invoice: ' . $e->getMessage(),
            ];
        }
    }
}
