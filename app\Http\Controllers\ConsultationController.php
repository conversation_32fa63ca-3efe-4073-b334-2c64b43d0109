<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Appointment;
use App\Models\ConsultationFile;
use App\Models\Prescription;
use App\Models\ConsultationDocument;
use App\Services\ConsultationService;
use App\Services\ConsultationEmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Services\DeepgramService;
use App\Services\MistralService;
use App\Http\Requests\Consultation\StoreConsultationRequest;
use App\Http\Requests\Consultation\UpdateConsultationRequest;

class ConsultationController extends Controller
{
    protected ConsultationService $consultationService;
    protected ConsultationEmailService $consultationEmailService;

    public function __construct(
        ConsultationService $consultationService,
        ConsultationEmailService $consultationEmailService
    ) {
        $this->consultationService = $consultationService;
        $this->consultationEmailService = $consultationEmailService;
    }
    /**
     * Get consultations for the authenticated clinician.
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();

            // Check if user is a clinician or admin
            if (!$user->isClinician() && !$user->isAdmin()) {
                return $this->errorResponse('Access denied. Only clinicians and admins can access consultations.', 403);
            }

            // For non-admin users, check if user has provider profile
            if (!$user->isAdmin() && !$user->provider) {
                Log::warning('User attempting to access consultations without provider profile', ['user_id' => $user->id]);
                return $this->errorResponse('Provider profile not found. Please contact administrator.', 400);
            }

            // Use service with business logic
            $filters = [
                'status' => $request->get('status'),
                'consultation_type' => $request->get('consultation_type'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'patient_id' => $request->get('patient_id'),
            ];

            // For non-admin users, filter by clinic
            if (!$user->isAdmin()) {
                $filters['clinic_id'] = $user->clinic_id;
            }

            $consultations = $this->consultationService->getConsultationsWithFilters(
                array_filter($filters),
                $request->get('per_page', 15)
            );

            return $this->successResponse($consultations, 'Consultations retrieved successfully');
        } catch (\InvalidArgumentException $e) {
            Log::warning('Invalid consultation filter parameters', ['error' => $e->getMessage(), 'user_id' => Auth::id()]);
            return $this->errorResponse('Invalid filter parameters provided.', 400);
        } catch (\Exception $e) {
            Log::error('Error retrieving consultations', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to retrieve consultations. Please try again.', 500);
        }
    }

    /**
     * Get a specific consultation.
     */
    public function show($id)
    {
        try {
            $user = Auth::user();

            // Validate consultation ID - only validate if it's not a special route like 'template'
            if (!is_numeric($id) || $id <= 0) {
                return $this->errorResponse('Invalid consultation ID provided.', 400);
            }

            // Check if user is a clinician or admin
            if (!$user->isClinician() && !$user->isAdmin()) {
                return $this->errorResponse('Access denied. Only clinicians and admins can view consultations.', 403);
            }

            // For non-admin users, check if user has provider profile
            if (!$user->isAdmin() && !$user->provider) {
                Log::warning('User attempting to view consultation without provider profile', ['user_id' => $user->id]);
                return $this->errorResponse('Provider profile not found. Please contact administrator.', 400);
            }

            // Use service layer to get consultation details
            $consultation = $this->consultationService->getConsultationDetails($id);

            if (!$consultation) {
                Log::info('Consultation not found', ['consultation_id' => $id, 'user_id' => $user->id]);
                return $this->errorResponse('Consultation not found', 404);
            }

            // Check if user has access to this consultation (admins can view all consultations)
            if (!$user->isAdmin() && $consultation->clinic_id !== $user->clinic_id) {
                Log::warning('Unauthorized consultation access attempt', [
                    'consultation_id' => $id,
                    'user_id' => $user->id,
                    'user_clinic_id' => $user->clinic_id,
                    'consultation_clinic_id' => $consultation->clinic_id
                ]);
                return $this->errorResponse('Access denied. You can only view consultations from your clinic.', 403);
            }

            return $this->successResponse($consultation, 'Consultation retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Error retrieving consultation', [
                'consultation_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to retrieve consultation. Please try again.', 500);
        }
    }

    /**
     * Create a new consultation.
     */
    public function store(StoreConsultationRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $user = Auth::user();

            // Get provider ID
            $provider = $user->provider;
            if (!$provider) {
                return $this->errorResponse('Provider profile not found. Please contact administrator.', 400);
            }

            // Prepare consultation data
            $consultationData = $validatedData;
            $consultationData['provider_id'] = $provider->id;
            $consultationData['clinic_id'] = $provider->clinic_id;

            // Use service layer to create consultation
            $consultation = $this->consultationService->createConsultation($consultationData);

            return $this->successResponse($consultation->load(['patient.user', 'provider.user']), 'Consultation created successfully', 201);
        } catch (\Exception $e) {
            Log::error('Error creating consultation: ' . $e->getMessage());
            return $this->errorResponse('Failed to create consultation. Please try again.', 500);
        }
    }

    /**
     * Update a consultation.
     */
    public function update(UpdateConsultationRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();

            // Use service layer to update consultation
            $consultation = $this->consultationService->updateConsultation($id, $validatedData);

            if (!$consultation) {
                return $this->errorResponse('Consultation not found', 404);
            }

            return $this->successResponse($consultation->fresh()->load(['patient.user', 'provider.user']), 'Consultation updated successfully');
        } catch (\Exception $e) {
            Log::error('Error updating consultation: ' . $e->getMessage());
            return $this->errorResponse('Failed to update consultation. Please try again.', 500);
        }
    }

    /**
     * Delete a consultation.
     */
    public function destroy($id)
    {
        try {
            $user = Auth::user();

            // Check if user is a clinician or admin
            if (!$user->isClinician() && !$user->isAdmin()) {
                return $this->errorResponse('Access denied. Only clinicians and admins can delete consultations.', 403);
            }

            // Use service layer to delete consultation
            // For admins, pass null as provider ID to allow deletion of any consultation
            $providerId = $user->isAdmin() ? null : ($user->provider->id ?? 0);
            $deleted = $this->consultationService->deleteConsultation($id, $providerId);

            if (!$deleted) {
                return $this->errorResponse('Consultation not found or cannot be deleted.', 404);
            }

            return $this->successResponse(null, 'Consultation deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting consultation: ' . $e->getMessage());
            return $this->errorResponse('Failed to delete consultation. Please try again.', 500);
        }
    }

    /**
     * Create consultation from appointment.
     */
    public function createFromAppointment($appointmentId)
    {
        $user = Auth::user();

        // Check if user is a clinician - only clinicians can create consultations
        if (!$user->isClinician()) {
            return response()->json([
                'success' => false,
                'message' => 'Only clinicians can create consultations. Please contact your administrator to get clinician access.',
                'user_type' => 'non_clinician'
            ], 200); // Return 200 instead of 403 to handle gracefully in frontend
        }

        $appointment = Appointment::with(['patient', 'provider', 'service'])->find($appointmentId);

        if (!$appointment) {
            return response()->json([
                'message' => 'Appointment not found'
            ], 404);
        }

        // Check if user has access to this appointment
        $hasAccess = false;
        $userClinicId = $user->getClinicIdAttribute();
        $appointmentClinicId = $appointment->provider->clinic_id;

        // Debug logging
        \Log::info('Consultation access check', [
            'user_id' => $user->id,
            'user_role' => $user->role,
            'is_clinician' => $user->isClinician(),
            'user_clinic_id' => $userClinicId,
            'appointment_clinic_id' => $appointmentClinicId,
            'appointment_provider_id' => $appointment->provider_id,
            'user_provider_id' => $user->provider->id ?? null,
        ]);

        // Super admins and admins can access all appointments
        if ($user->isAdmin() || $user->hasRole(['super_admin'])) {
            $hasAccess = true;
        }
        // Clinic admins can access appointments in their clinic
        elseif ($user->hasRole('clinic_admin') && $userClinicId === $appointmentClinicId) {
            $hasAccess = true;
        }
        // Providers can access their own appointments
        elseif ($user->provider && $appointment->provider_id === $user->provider->id) {
            $hasAccess = true;
        }
        // Clinicians can access appointments if they are assigned to the same clinic
        elseif ($user->isClinician() && $userClinicId && $userClinicId === $appointmentClinicId) {
            $hasAccess = true;
        }

        if (!$hasAccess) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. You can only create consultations for appointments you have access to.',
                'user_type' => 'insufficient_access'
            ], 200);
        }

        // Check if consultation already exists for this appointment
        $existingConsultation = Consultation::where('appointment_id', $appointmentId)->first();
        if ($existingConsultation) {
            return response()->json([
                'message' => 'Consultation already exists for this appointment',
                'data' => $existingConsultation
            ], 400);
        }

        $consultation = Consultation::create([
            'appointment_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'provider_id' => $appointment->provider_id,
            'clinic_id' => $appointment->provider->clinic_id,
            'consultation_type' => 'general',
            'status' => 'in_progress',
            'consultation_date' => $appointment->date,
            'consultation_mode' => $appointment->is_telemedicine ? 'video' : 'in_person',
            'is_telemedicine' => $appointment->is_telemedicine,
            'chief_complaint' => $appointment->reason,
            'started_at' => now(),
        ]);

        return response()->json([
            'message' => 'Consultation created from appointment successfully',
            'data' => $consultation->load(['patient.user', 'provider.user', 'appointment'])
        ], 201);
    }

    /**
     * Get consultation dashboard data.
     */
    public function getDashboardData(Request $request)
    {
        try {
            $user = $request->user();
            $patientId = (int) $request->get('patient_id');
            $consultationId = $request->get('consultation_id') ? (int) $request->get('consultation_id') : null;

            // Validate required parameters
            if (!$user) {
                return $this->errorResponse('Authentication required', 401);
            }

            if (!$patientId) {
                return $this->errorResponse('Patient ID is required', 400);
            }

            // Validate patient exists and user has access
            $patient = Patient::with('user')->find($patientId);
            if (!$patient) {
                return $this->errorResponse('Patient not found', 404);
            }

            // Check clinic access for non-admin users
            if (!$user->isAdmin() && $patient->clinic_id !== $user->clinic_id) {
                return $this->errorResponse('Access denied. You can only view patients from your clinic.', 403);
            }



            // Get consultation details
            $consultation = null;
            if ($consultationId) {
                $consultation = $this->consultationService->getConsultationDetails($consultationId);
                if (!$consultation || $consultation->patient_id !== $patientId) {
                    return $this->errorResponse('Consultation not found or does not belong to this patient', 404);
                }
            } else {
                // Get the latest consultation for this patient
                $latestConsultation = \App\Models\Consultation::where('patient_id', $patientId)
                    ->orderBy('created_at', 'desc')
                    ->first();
                if ($latestConsultation) {
                    $consultation = $this->consultationService->getConsultationDetails($latestConsultation->id);
                }
            }

            // Get patient's consultation history with related data (last 10)
            $recentConsultations = \App\Models\Consultation::with([
                'provider.user',
                'appointment',
                'mainPrescriptions.items',
                'prescriptions', // ConsultationPrescription
                'documents'
            ])
                ->where('patient_id', $patientId)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get patient's upcoming appointments
            $upcomingAppointments = \App\Models\Appointment::with(['provider.user', 'service'])
                ->where('patient_id', $patientId)
                ->where('date', '>=', now()->format('Y-m-d'))
                ->orderBy('date')
                ->orderBy('time_slot->start_time')
                ->limit(3)
                ->get();

            return $this->successResponse([
                'patient' => $patient,
                'consultation' => $consultation,
                'recent_consultations' => $recentConsultations,
                'upcoming_appointments' => $upcomingAppointments,
            ], 'Consultation dashboard data retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving consultation dashboard data', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to retrieve dashboard data. Please try again.', 500);
        }
    }

    /**
     * Get the consultation template structure.
     */
    public function getTemplate()
    {
        try {
            Log::info('Template endpoint called', [
                'user_id' => Auth::id(),
                'method' => request()->method(),
                'url' => request()->url()
            ]);

            $templatePath = resource_path('json/consultation_template.json');

            if (!file_exists($templatePath)) {
                Log::error('Template file not found', ['path' => $templatePath]);
                return $this->errorResponse('Template not found', 404);
            }

            $template = json_decode(file_get_contents($templatePath), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Template JSON error', ['error' => json_last_error_msg()]);
                return $this->errorResponse('Invalid template format', 500);
            }

            Log::info('Template loaded successfully');
            return $this->successResponse($template, 'Template loaded successfully');

        } catch (\Exception $e) {
            Log::error('Template endpoint error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to load template', 500);
        }
    }

    /**
     * Transcribe audio file using AI service
     */
    public function transcribeAudio(Request $request)
    {
        // Handle both file uploads and base64 audio data
        $isFileUpload = $request->hasFile('audio_file');

        if ($isFileUpload) {
            $validator = Validator::make($request->all(), [
                'audio_file' => 'required|file|mimes:mp3,wav,ogg,webm,m4a,mp4,aac,flac|max:204800', // 200MB max
                'encounter_id' => 'required|string'
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'audio_file' => 'required|string', // Base64 encoded audio
                'encounter_id' => 'required|string',
                'audio_mime_type' => 'sometimes|string',
                'file_size_mb' => 'sometimes|numeric'
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $encounterId = $request->encounter_id;

            // Initialize Deepgram service
            $deepgramService = new DeepgramService();

            // Check if Deepgram is configured
            if (!$deepgramService->isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deepgram API is not configured. Please contact your administrator.'
                ], 500);
            }

            // Handle file upload or base64 data
            if ($isFileUpload) {
                $audioFile = $request->file('audio_file');

                // Check if file upload was successful
                if (!$audioFile || !$audioFile->isValid()) {
                    Log::error('Invalid audio file upload', [
                        'file' => $audioFile ? 'exists' : 'null',
                        'valid' => $audioFile ? $audioFile->isValid() : false,
                        'error' => $audioFile ? $audioFile->getError() : 'no file'
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid audio file upload'
                    ], 422);
                }

                // Store the file
                try {
                    $filePath = $audioFile->store('temp/audio', 'local');
                    $fullPath = Storage::disk('local')->path($filePath);

                    Log::info('Processing uploaded audio file', [
                        'file_name' => $audioFile->getClientOriginalName(),
                        'file_size' => $audioFile->getSize(),
                        'mime_type' => $audioFile->getMimeType(),
                        'stored_path' => $filePath,
                        'full_path' => $fullPath,
                        'file_exists' => file_exists($fullPath),
                        'storage_disk_path' => Storage::disk('local')->path(''),
                        'directory_exists' => is_dir(dirname($fullPath))
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to store audio file', [
                        'error' => $e->getMessage(),
                        'file_name' => $audioFile->getClientOriginalName()
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to store audio file: ' . $e->getMessage()
                    ], 500);
                }

                // Check file size (200MB limit like plugin)
                if ($audioFile->getSize() > 200 * 1024 * 1024) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File size exceeds 200MB limit'
                    ], 422);
                }

                // Verify file exists before transcription
                if (!file_exists($fullPath)) {
                    Log::error('Audio file not found after storage', [
                        'expected_path' => $fullPath,
                        'storage_path' => $filePath,
                        'alternative_path' => storage_path('app/' . $filePath),
                        'alternative_exists' => file_exists(storage_path('app/' . $filePath))
                    ]);

                    // Try alternative path
                    $alternativePath = storage_path('app/' . $filePath);
                    if (file_exists($alternativePath)) {
                        Log::info('Using alternative path for audio file', ['path' => $alternativePath]);
                        $fullPath = $alternativePath;
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to save audio file for processing'
                        ], 500);
                    }
                }

                // Transcribe from file
                $result = $deepgramService->transcribeFromFile($fullPath);

                // Clean up temporary file
                Storage::disk('local')->delete($filePath);
            } else {
                // Handle base64 audio data
                $base64Audio = $request->audio_file;
                $mimeType = $request->audio_mime_type ?? 'audio/wav';

                Log::info('Processing base64 audio data', [
                    'data_size' => strlen($base64Audio),
                    'mime_type' => $mimeType
                ]);

                // Transcribe from base64
                $result = $deepgramService->transcribeFromBase64($base64Audio, $mimeType);
            }

            if (!$result['success']) {
                throw new \Exception($result['error']);
            }

            $transcriptionData = $result['data'];

            return response()->json([
                'success' => true,
                'data' => [
                    'transcript' => $transcriptionData['full_transcript'],
                    'diarized_transcript' => $transcriptionData['diarized_transcript'],
                    'duration' => $this->calculateDuration($transcriptionData['diarized_transcript']),
                    'confidence' => $this->calculateAverageConfidence($transcriptionData['diarized_transcript']),
                    'file_info' => $isFileUpload ? [
                        'name' => $audioFile->getClientOriginalName(),
                        'size' => $audioFile->getSize(),
                        'type' => $audioFile->getMimeType()
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to transcribe audio: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Analyze transcript and extract medical information
     */
    public function analyzeTranscript(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transcript' => 'required|string',
            'encounter_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transcript = $request->transcript;
            $encounterId = $request->encounter_id;

            // Initialize Mistral service
            $mistralService = new MistralService();

            // Check if Mistral is configured
            if (!$mistralService->isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mistral AI is not configured. Please contact your administrator.'
                ], 500);
            }

            // Analyze transcript using Mistral AI
            $startTime = microtime(true);
            $result = $mistralService->analyzeTranscript($transcript);
            $processingTime = round(microtime(true) - $startTime, 2);

            if (!$result['success']) {
                throw new \Exception($result['error']);
            }

            $extractedData = $result['data'];

            return response()->json([
                'success' => true,
                'data' => [
                    'extracted_data' => $extractedData,
                    'confidence' => 0.85, // Mistral doesn't provide confidence scores
                    'processing_time' => $processingTime
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to analyze transcript: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Populate consultation records with AI-extracted data
     */
    public function aiPopulateRecords(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'encounter_id' => 'required|string',
                'extracted_data' => 'required|array',
                'raw_data' => 'sometimes|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $encounterId = $request->encounter_id;
            $extractedData = $request->extracted_data;
            $rawData = $request->raw_data ?? [];

            // Find the consultation
            $consultation = Consultation::find($encounterId);
            if (!$consultation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Consultation not found'
                ], 404);
            }

            // Update consultation with AI-extracted data
            $aiData = [
                'ai_extracted_data' => $extractedData,
                'ai_raw_data' => $rawData,
                'ai_populated_at' => now(),
                'ai_populated_by' => Auth::id()
            ];

            // Update consultation additional_details with AI data
            $additionalDetails = $consultation->additional_details ?? [];
            $additionalDetails['ai_data'] = $aiData;
            $consultation->additional_details = $additionalDetails;

            // Update specific consultation fields if they exist in extracted data
            if (isset($extractedData['concerns']) && !empty($extractedData['concerns'])) {
                $consultation->chief_complaint = $extractedData['concerns'];
            }

            if (isset($extractedData['history']) && !empty($extractedData['history'])) {
                $consultation->history_of_present_illness = $extractedData['history'];
            }

            if (isset($extractedData['examination']) && !empty($extractedData['examination'])) {
                $consultation->physical_examination = $extractedData['examination'];
            }

            if (isset($extractedData['plan']) && !empty($extractedData['plan'])) {
                $consultation->treatment_plan = $extractedData['plan'];
            }

            $consultation->save();

            // Create AI population log entry
            $logEntry = [
                'action' => 'ai_populate',
                'data' => $extractedData,
                'timestamp' => now(),
                'user_id' => Auth::id()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Consultation records populated with AI data successfully',
                'data' => [
                    'consultation_id' => $consultation->id,
                    'populated_fields' => array_keys($extractedData),
                    'ai_data' => $aiData
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to populate records with AI data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get files for a consultation
     */
    public function getFiles($consultationId)
    {
        try {
            $files = ConsultationFile::where('consultation_id', $consultationId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($file) {
                    return [
                        'id' => $file->id,
                        'name' => $file->original_name,
                        'type' => $file->mime_type,
                        'size' => $file->file_size,
                        'url' => Storage::url($file->file_path),
                        'download_url' => route('consultation.file.download', $file->id),
                        'created_at' => $file->created_at->toISOString(),
                        'uploaded_by' => $file->uploadedBy->name ?? 'Unknown'
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $files
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload files for a consultation
     */
    public function uploadFiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'encounter_id' => 'required|string',
            'files' => 'required|array',
            'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,txt|max:10000' // 10MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $encounterId = $request->encounter_id;
            $uploadedFiles = [];

            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $mimeType = $file->getMimeType();
                $fileSize = $file->getSize();

                // Store file
                $filePath = $file->store('consultations/' . $encounterId, 'public');

                // Create database record
                $consultationFile = ConsultationFile::create([
                    'consultation_id' => $encounterId,
                    'original_name' => $originalName,
                    'file_path' => $filePath,
                    'mime_type' => $mimeType,
                    'file_size' => $fileSize,
                    'uploaded_by' => Auth::id()
                ]);

                $uploadedFiles[] = [
                    'id' => $consultationFile->id,
                    'name' => $originalName,
                    'type' => $mimeType,
                    'size' => $fileSize,
                    'url' => Storage::url($filePath),
                    'download_url' => route('consultation.file.download', $consultationFile->id),
                    'created_at' => $consultationFile->created_at->toISOString()
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $uploadedFiles,
                'message' => count($uploadedFiles) . ' file(s) uploaded successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download a consultation file
     */
    public function downloadFile($fileId)
    {
        try {
            $file = ConsultationFile::findOrFail($fileId);

            // Check if file exists
            if (!Storage::disk('public')->exists($file->file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            return Storage::disk('public')->download($file->file_path, $file->original_name);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a consultation file
     */
    public function deleteFile($fileId)
    {
        try {
            $file = ConsultationFile::findOrFail($fileId);

            // Check if user has permission to delete this file
            if ($file->uploaded_by !== Auth::id() && !Auth::user()->isClinician() && !Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to delete this file'
                ], 403);
            }

            // Delete file from storage
            Storage::disk('public')->delete($file->file_path);

            // Delete database record
            $file->delete();

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a mobile recording session
     */
    public function createMobileRecordingSession(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'encounter_id' => 'required|string',
                'expire_minutes' => 'integer|min:5|max:60'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $encounterId = $request->encounter_id;
            $expireMinutes = $request->expire_minutes ?? 30;

            // Generate unique session ID
            $sessionId = 'mobile_' . uniqid() . '_' . time();

            // Create QR code URL (simplified - in production you'd use a proper QR code service)
            $recordUrl = url("/mobile-record/{$sessionId}");
            $qrCodeUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($recordUrl);

            // Store session data in cache
            $sessionData = [
                'session_id' => $sessionId,
                'encounter_id' => $encounterId,
                'record_url' => $recordUrl,
                'created_at' => now(),
                'expires_at' => now()->addMinutes($expireMinutes),
                'status' => 'waiting',
                'processed' => false,
                'transcription_ready' => false
            ];

            Cache::put("mobile_recording_session_{$sessionId}", $sessionData, now()->addMinutes($expireMinutes + 5));

            return response()->json([
                'success' => true,
                'message' => 'Mobile recording session created successfully',
                'data' => [
                    'session_id' => $sessionId,
                    'qr_code_url' => $qrCodeUrl,
                    'record_url' => $recordUrl,
                    'expires_at' => $sessionData['expires_at']->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create mobile recording session: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check mobile recording session status
     */
    public function checkMobileRecordingStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'session_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $sessionId = $request->session_id;
            $sessionData = Cache::get("mobile_recording_session_{$sessionId}");

            if (!$sessionData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session not found or expired'
                ], 404);
            }

            $timeRemaining = max(0, now()->diffInSeconds($sessionData['expires_at'], false));

            return response()->json([
                'success' => true,
                'message' => 'Session status retrieved',
                'data' => [
                    'status' => $sessionData['status'],
                    'processed' => $sessionData['processed'],
                    'transcription_ready' => $sessionData['transcription_ready'],
                    'time_remaining' => $timeRemaining,
                    'document_id' => $sessionData['document_id'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check session status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process mobile recording and return transcript
     */
    public function processMobileRecording(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'session_id' => 'required|string',
                'encounter_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $sessionId = $request->session_id;
            $sessionData = Cache::get("mobile_recording_session_{$sessionId}");

            if (!$sessionData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session not found or expired'
                ], 404);
            }

            if (!$sessionData['processed']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Recording not yet processed'
                ], 400);
            }

            // Check if transcription data is available
            if (!isset($sessionData['transcription_data']) || !$sessionData['transcription_ready']) {
                // Try to transcribe if audio file exists but transcription is missing
                if (isset($sessionData['audio_file_path'])) {
                    $audioPath = storage_path('app/public/' . $sessionData['audio_file_path']);

                    if (file_exists($audioPath)) {
                        $deepgramService = new DeepgramService();

                        if ($deepgramService->isConfigured()) {
                            $transcriptionResult = $deepgramService->transcribeFromFile($audioPath);

                            if ($transcriptionResult['success']) {
                                $sessionData['transcription_data'] = $transcriptionResult['data'];
                                $sessionData['transcription_ready'] = true;

                                // Update cache with transcription data
                                Cache::put("mobile_recording_session_{$sessionId}", $sessionData, now()->addMinutes(120));
                            } else {
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Failed to transcribe audio: ' . $transcriptionResult['error']
                                ], 500);
                            }
                        } else {
                            return response()->json([
                                'success' => false,
                                'message' => 'Transcription service not configured'
                            ], 500);
                        }
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'Audio file not found'
                        ], 404);
                    }
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'No audio file available for transcription'
                    ], 400);
                }
            }

            // Get transcription data
            $transcriptionData = $sessionData['transcription_data'];
            $transcript = $transcriptionData['full_transcript'] ?? '';
            $diarizedTranscript = $transcriptionData['diarized_transcript'] ?? [];

            return response()->json([
                'success' => true,
                'message' => 'Mobile recording processed successfully',
                'data' => [
                    'transcript' => $transcript,
                    'diarized_transcript' => $diarizedTranscript,
                    'session_id' => $sessionId,
                    'duration' => $this->calculateDuration($diarizedTranscript),
                    'confidence' => $this->calculateAverageConfidence($diarizedTranscript)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process mobile recording: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload mobile recording audio file (matching plugin implementation)
     */
    public function uploadMobileRecording(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'session_id' => 'required|string',
                'audio_file' => 'required|file|mimes:wav,mp3,m4a,ogg,webm,aac,flac|max:204800' // 200MB max like plugin
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $sessionId = $request->session_id;
            $sessionData = Cache::get("mobile_recording_session_{$sessionId}");

            if (!$sessionData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session not found or expired'
                ], 404);
            }

            $audioFile = $request->file('audio_file');
            $fileSize = $audioFile->getSize();
            $fileName = $audioFile->getClientOriginalName();
            $mimeType = $audioFile->getMimeType();

            Log::info('Mobile recording upload received', [
                'session_id' => $sessionId,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'mime_type' => $mimeType
            ]);

            // Check file size (200MB limit like plugin)
            if ($fileSize > 200 * 1024 * 1024) {
                return response()->json([
                    'success' => false,
                    'message' => 'File size exceeds 200MB limit'
                ], 422);
            }

            // Store the uploaded audio file
            $storedFileName = 'mobile_recording_' . $sessionId . '_' . time() . '.' . $audioFile->getClientOriginalExtension();
            $audioPath = $audioFile->storeAs('mobile-recordings', $storedFileName, 'public');
            $fullPath = storage_path('app/public/' . $audioPath);

            // Immediately transcribe the audio using Deepgram
            $deepgramService = new DeepgramService();

            if ($deepgramService->isConfigured()) {
                try {
                    $transcriptionResult = $deepgramService->transcribeFromFile($fullPath);

                    if ($transcriptionResult['success']) {
                        $sessionData['transcription_data'] = $transcriptionResult['data'];
                        $sessionData['transcription_ready'] = true;
                        Log::info('Mobile recording transcribed successfully', ['session_id' => $sessionId]);
                    } else {
                        Log::error('Mobile recording transcription failed', [
                            'session_id' => $sessionId,
                            'error' => $transcriptionResult['error']
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Mobile recording transcription exception', [
                        'session_id' => $sessionId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Update session data to mark as processed
            $sessionData['status'] = 'uploaded';
            $sessionData['processed'] = true;
            $sessionData['audio_file_path'] = $audioPath;
            $sessionData['file_info'] = [
                'original_name' => $fileName,
                'stored_name' => $storedFileName,
                'size' => $fileSize,
                'mime_type' => $mimeType
            ];
            $sessionData['uploaded_at'] = now();

            // Update cache with new session data (extend expiration)
            Cache::put("mobile_recording_session_{$sessionId}", $sessionData, now()->addMinutes(120));

            return response()->json([
                'success' => true,
                'message' => 'Audio file uploaded and processed successfully',
                'data' => [
                    'session_id' => $sessionId,
                    'file_path' => $audioPath,
                    'status' => 'uploaded',
                    'transcription_ready' => $sessionData['transcription_ready'] ?? false,
                    'file_info' => $sessionData['file_info']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload audio file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate duration from diarized transcript
     */
    private function calculateDuration(array $diarizedTranscript): float
    {
        if (empty($diarizedTranscript)) {
            return 0.0;
        }

        $maxEnd = 0;
        foreach ($diarizedTranscript as $utterance) {
            if (isset($utterance['end']) && $utterance['end'] > $maxEnd) {
                $maxEnd = $utterance['end'];
            }
        }

        return round($maxEnd, 2);
    }

    /**
     * Calculate average confidence from diarized transcript
     */
    private function calculateAverageConfidence(array $diarizedTranscript): float
    {
        if (empty($diarizedTranscript)) {
            return 0.0;
        }

        $totalConfidence = 0;
        $count = 0;

        foreach ($diarizedTranscript as $utterance) {
            if (isset($utterance['confidence'])) {
                $totalConfidence += $utterance['confidence'];
                $count++;
            }
        }

        return $count > 0 ? round($totalConfidence / $count, 2) : 0.85; // Default confidence
    }

    /**
     * Send consultation summary to patient via email.
     */
    public function sendSummary(Request $request, $id)
    {
        try {
            $user = Auth::user();

            // Check if user is a clinician or admin
            if (!$user->isClinician() && !$user->isAdmin()) {
                return response()->json([
                    'message' => 'Access denied. Only clinicians and admins can send consultation summaries.'
                ], 403);
            }

            $consultation = Consultation::with([
                'patient.user',
                'provider.user',
                'clinic'
            ])->find($id);

            if (!$consultation) {
                return response()->json([
                    'message' => 'Consultation not found'
                ], 404);
            }

            // Check if user has access to this consultation
            if ($consultation->provider_id !== ($user->provider->id ?? 0)) {
                return response()->json([
                    'message' => 'Access denied. You can only send summaries for your own consultations.'
                ], 403);
            }

            // Validate request
            $validated = $request->validate([
                'patient_email' => 'required|email',
            ]);

            // Send consultation summary
            $success = $this->consultationEmailService->shareConsultationSummary(
                $consultation,
                [$validated['patient_email']],
                ['prescriptions', 'notes', 'diagnoses'] // Default items to include
            );

            if ($success) {
                return response()->json([
                    'message' => 'Consultation summary sent successfully',
                    'email_sent_to' => $validated['patient_email']
                ]);
            } else {
                return response()->json([
                    'message' => 'Failed to send consultation summary'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error sending consultation summary: ' . $e->getMessage(), [
                'consultation_id' => $id,
                'user_id' => $user->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to send consultation summary. Please try again.'
            ], 500);
        }
    }
}
