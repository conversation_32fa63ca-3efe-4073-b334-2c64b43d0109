<template>
    <!-- Reschedule Appointment Modal -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm transition-opacity duration-300" @click="$emit('close')"></div>
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative w-full max-w-2xl bg-white rounded-lg shadow-xl transform transition-all duration-300 scale-100">
                <!-- Header -->
                <div class="bg-white border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <Icon name="calendar" class="text-blue-600 w-6 h-6 mr-3" />
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Reschedule Appointment</h3>
                                <p class="text-sm text-gray-500">{{ appointment?.service?.name || 'General Consultation' }}</p>
                            </div>
                        </div>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 hover:bg-gray-100 rounded-lg">
                            <Icon name="x" class="w-5 h-5" />
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <div v-if="appointment" class="space-y-6">
                        <!-- Current Appointment Info -->
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 shadow-sm">
                            <div class="flex items-start">
                                <Icon name="info" class="text-blue-600 w-5 h-5 mr-3 mt-0.5" />
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-blue-900 mb-2">Current Appointment</h4>
                                    <div class="grid grid-cols-2 gap-3 text-sm">
                                        <div>
                                            <span class="font-medium text-blue-800">Date:</span>
                                            <span class="text-blue-700 ml-1">{{ formatDate(appointment.scheduled_at) }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-blue-800">Time:</span>
                                            <span class="text-blue-700 ml-1">{{ formatTime(appointment.scheduled_at) }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-blue-800">Patient:</span>
                                            <span class="text-blue-700 ml-1">{{ appointment.patient?.user?.name || appointment.patient_name }}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-blue-800">Provider:</span>
                                            <span class="text-blue-700 ml-1">{{ appointment.provider?.user?.name || appointment.provider_name }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reschedule Form -->
                        <div class="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                            <div class="flex items-center mb-4">
                                <Icon name="calendar-plus" class="text-emerald-600 w-4 h-4 mr-2" />
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">New Schedule</h4>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Date Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">New Date</label>
                                    <input
                                        type="date"
                                        v-model="rescheduleForm.date"
                                        :min="minDate"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 text-sm"
                                        required
                                    />
                                </div>

                                <!-- Time Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">New Time</label>
                                    <select
                                        v-model="rescheduleForm.time"
                                        :disabled="isLoadingSlots || !rescheduleForm.date"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                                        required
                                    >
                                        <option value="">
                                            {{ isLoadingSlots ? 'Loading time slots...' : !rescheduleForm.date ? 'Select date first' : 'Select time' }}
                                        </option>
                                        <option v-for="slot in availableTimeSlots" :key="slot.value" :value="slot.value">
                                            {{ slot.label }}
                                        </option>
                                    </select>
                                    <p v-if="availableTimeSlots.length === 0 && rescheduleForm.date && !isLoadingSlots" class="mt-1 text-sm text-amber-600">
                                        No available time slots for this date. Please select a different date.
                                    </p>
                                </div>
                            </div>

                            <!-- Reason for Reschedule -->
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Reschedule <span class="text-red-500">*</span></label>
                                <textarea
                                    v-model="rescheduleForm.reason"
                                    required
                                    rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 text-sm"
                                    placeholder="Please provide a reason for rescheduling (required)..."
                                ></textarea>
                            </div>
                        </div>

                        <!-- Notification Settings -->
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200 rounded-lg p-4">
                            <div class="flex items-center mb-3">
                                <Icon name="bell" class="text-amber-600 w-4 h-4 mr-2" />
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Notifications</h4>
                            </div>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input
                                        type="checkbox"
                                        v-model="rescheduleForm.notifyPatient"
                                        class="rounded border-gray-300 text-teal-600 focus:ring-teal-500 mr-2"
                                    />
                                    <span class="text-sm text-gray-700">Notify patient about the reschedule</span>
                                </label>
                                <label class="flex items-center">
                                    <input
                                        type="checkbox"
                                        v-model="rescheduleForm.notifyProvider"
                                        class="rounded border-gray-300 text-teal-600 focus:ring-teal-500 mr-2"
                                    />
                                    <span class="text-sm text-gray-700">Notify provider about the reschedule</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div v-else class="text-center py-8">
                        <Icon name="calendar-x" class="text-gray-400 w-16 h-16 mx-auto mb-4" />
                        <p class="text-gray-500">No appointment information available</p>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-white px-6 py-4 border-t border-gray-200 flex justify-between items-center rounded-b-lg">
                    <div class="text-xs text-gray-500">
                        All times are in your local timezone
                    </div>
                    <div class="flex space-x-3">
                        <button @click="$emit('close')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Cancel
                        </button>
                        <button @click="handleReschedule" :disabled="!isFormValid || isSubmitting" class="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ isSubmitting ? 'Rescheduling...' : 'Reschedule Appointment' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import Icon from '@/components/Icon.vue'
import { useApi } from '@/composables/useApi'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    appointment: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['close', 'reschedule'])

// Composables
const { get } = useApi()

// Form data
const rescheduleForm = ref({
    date: '',
    time: '',
    reason: '',
    notifyPatient: true,
    notifyProvider: true
})

const isSubmitting = ref(false)
const isLoadingSlots = ref(false)
const availableTimeSlots = ref([])

// Computed properties
const minDate = computed(() => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return tomorrow.toISOString().split('T')[0]
})

const isFormValid = computed(() => {
    return rescheduleForm.value.date && rescheduleForm.value.time && rescheduleForm.value.reason
})

// Watch for date changes to load available time slots
watch(() => rescheduleForm.value.date, (newDate) => {
    if (newDate) {
        loadAvailableTimeSlots()
    }
})

// Methods
const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}



const loadAvailableTimeSlots = async () => {
    if (!rescheduleForm.value.date || !props.appointment?.service_id || !props.appointment?.provider_id) {
        availableTimeSlots.value = []
        return
    }

    try {
        isLoadingSlots.value = true

        console.log('Loading time slots for reschedule:', {
            date: rescheduleForm.value.date,
            provider_id: props.appointment.provider_id,
            service_id: props.appointment.service_id,
            clinic_slug: props.appointment.clinic?.slug
        })

        // Try multiple API endpoints for fetching available slots
        let response = null

        // First try the appointment booking endpoint if clinic slug is available
        if (props.appointment.clinic?.slug) {
            try {
                console.log('Trying booking endpoint...')
                const url = `/book-appointment/${props.appointment.clinic.slug}/services/${props.appointment.service_id}/slots?date=${rescheduleForm.value.date}&provider_id=${props.appointment.provider_id}`
                response = await get(url)
                console.log('Booking endpoint response:', response)
            } catch (bookingError) {
                console.warn('Booking endpoint failed, trying alternative:', bookingError)
            }
        }

        // If booking endpoint failed, try the provider-specific endpoint
        if (!response) {
            try {
                console.log('Trying provider endpoint...')
                const url = `/providers/${props.appointment.provider_id}/available-slots?date=${rescheduleForm.value.date}&service_id=${props.appointment.service_id || ''}`
                response = await get(url)
                console.log('Provider endpoint response:', response)
            } catch (providerError) {
                console.warn('Provider endpoint failed:', providerError)
            }
        }

        // Process the response based on the endpoint used
        if (response?.slots) {
            // Provider endpoint format (direct slots array) - this is the most common format
            availableTimeSlots.value = response.slots.map(slot => ({
                value: slot.start_time,
                label: formatTimeSlot(slot),
                available: true
            }))
            console.log('Processed slots from provider endpoint:', availableTimeSlots.value)
        } else if (response?.data?.slots) {
            // Booking endpoint format
            availableTimeSlots.value = response.data.slots.map(slot => ({
                value: slot.time || slot.start_time,
                label: slot.formatted_time || slot.label || formatTimeSlot(slot),
                available: slot.available !== false
            })).filter(slot => slot.available)
            console.log('Processed slots from booking endpoint:', availableTimeSlots.value)
        } else if (response?.data) {
            // Handle other response formats
            const slots = response.data.available_slots || response.data.time_slots || response.data.slots || []
            availableTimeSlots.value = slots.map(slot => ({
                value: typeof slot === 'string' ? slot : (slot.time || slot.start_time),
                label: typeof slot === 'string' ? formatTime(slot) : (slot.formatted_time || slot.label || formatTimeSlot(slot)),
                available: true
            }))
            console.log('Processed slots from alternative format:', availableTimeSlots.value)
        } else {
            availableTimeSlots.value = []
            console.log('No slots found in response:', response)
        }
    } catch (error) {
        console.error('Failed to load time slots:', error)
        availableTimeSlots.value = []

        // Only show fallback slots if we have a valid date and provider
        if (rescheduleForm.value.date && props.appointment.provider_id) {
            console.log('Using fallback time slots')
            // Fallback to basic time slots if all API calls fail
            const slots = []
            for (let hour = 9; hour < 18; hour++) {
                for (let minute = 0; minute < 60; minute += 30) {
                    const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
                    const label = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                    })
                    slots.push({ value: time, label, available: true })
                }
            }
            availableTimeSlots.value = slots
        }
    } finally {
        isLoadingSlots.value = false
    }
}

// Helper function to format time slots
const formatTimeSlot = (slot) => {
    if (slot.start_time && slot.end_time) {
        return `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`
    } else if (slot.time) {
        return formatTime(slot.time)
    }
    return 'Unknown time'
}

const formatTime = (timeString) => {
    if (!timeString) return 'N/A'
    try {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        })
    } catch (e) {
        return timeString
    }
}

const handleReschedule = async () => {
    if (!isFormValid.value || isSubmitting.value) return
    
    isSubmitting.value = true
    try {
        const rescheduleData = {
            appointmentId: props.appointment.id,
            newDate: rescheduleForm.value.date,
            newTime: rescheduleForm.value.time,
            reason: rescheduleForm.value.reason,
            notifyPatient: rescheduleForm.value.notifyPatient,
            notifyProvider: rescheduleForm.value.notifyProvider
        }
        
        emit('reschedule', rescheduleData)
    } catch (error) {
        console.error('Reschedule failed:', error)
    } finally {
        isSubmitting.value = false
    }
}

// Reset form when modal opens
watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        rescheduleForm.value = {
            date: '',
            time: '',
            reason: '',
            notifyPatient: true,
            notifyProvider: true
        }
        availableTimeSlots.value = []
    }
})
</script>
