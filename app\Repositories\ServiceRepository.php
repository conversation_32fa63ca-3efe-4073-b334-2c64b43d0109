<?php

namespace App\Repositories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ServiceRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Service $model
     * @return void
     */
    public function __construct(Service $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find services by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->model->where('provider_id', $providerId)
                          ->with('countries')
                          ->get();
    }
    
    /**
     * Find active services.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findActive(?int $providerId = null): Collection
    {
        $criteria = ['active' => true];
        
        if ($providerId) {
            $criteria['provider_id'] = $providerId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find services by category.
     *
     * @param string $category
     * @param int|null $providerId
     * @return Collection
     */
    public function findByCategory(string $category, ?int $providerId = null): Collection
    {
        $criteria = ['category' => $category];
        
        if ($providerId) {
            $criteria['provider_id'] = $providerId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find services by approval status.
     *
     * @param string $status
     * @param int|null $providerId
     * @return Collection
     */
    public function findByApprovalStatus(string $status, ?int $providerId = null): Collection
    {
        $criteria = ['approval_status' => $status];
        
        if ($providerId) {
            $criteria['provider_id'] = $providerId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find telemedicine services.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findTelemedicine(?int $providerId = null): Collection
    {
        $criteria = ['is_telemedicine' => true];
        
        if ($providerId) {
            $criteria['provider_id'] = $providerId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find internal services.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findInternal(?int $providerId = null): Collection
    {
        $criteria = ['internal' => true];
        
        if ($providerId) {
            $criteria['provider_id'] = $providerId;
        }
        
        return $this->findBy($criteria);
    }
    
    /**
     * Find services by price range.
     *
     * @param float $minPrice
     * @param float $maxPrice
     * @param int|null $providerId
     * @return Collection
     */
    public function findByPriceRange(float $minPrice, float $maxPrice, ?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->whereBetween('price', [$minPrice, $maxPrice]);
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->get();
    }
    
    /**
     * Find services with discounts.
     *
     * @param int|null $providerId
     * @return Collection
     */
    public function findWithDiscounts(?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('discount_percentage', '>', 0)
              ->where(function ($q) {
                  $q->whereNull('discount_valid_until')
                    ->orWhere('discount_valid_until', '>', now());
              });
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->get();
    }
    
    /**
     * Search services by name or description.
     *
     * @param string $search
     * @param int|null $providerId
     * @return Collection
     */
    public function search(string $search, ?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('category', 'like', "%{$search}%");
        });
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->get();
    }
    
    /**
     * Get services with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['provider.user', 'category']);
        
        // Apply filters
        if (!empty($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }
        
        if (!empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }
        
        if (!empty($filters['approval_status'])) {
            $query->where('approval_status', $filters['approval_status']);
        }
        
        if (isset($filters['active'])) {
            $query->where('active', $filters['active']);
        }
        
        if (isset($filters['is_telemedicine'])) {
            $query->where('is_telemedicine', $filters['is_telemedicine']);
        }
        
        if (isset($filters['internal'])) {
            $query->where('internal', $filters['internal']);
        }
        
        if (!empty($filters['min_price'])) {
            $query->where('price', '>=', $filters['min_price']);
        }
        
        if (!empty($filters['max_price'])) {
            $query->where('price', '<=', $filters['max_price']);
        }
        
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Update service approval status.
     *
     * @param int $serviceId
     * @param string $status
     * @param int|null $approvedBy
     * @param string|null $reason
     * @return bool
     */
    public function updateApprovalStatus(int $serviceId, string $status, ?int $approvedBy = null, ?string $reason = null): bool
    {
        $data = [
            'approval_status' => $status,
            'approved_by' => $approvedBy,
            'rejection_reason' => $reason
        ];
        
        if ($status === 'approved') {
            $data['approved_at'] = now();
        }
        
        $service = $this->update($serviceId, $data);
        return $service !== null;
    }
}
