<script setup lang="ts">
import { Head, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import AppLayout from '../../layouts/AppLayout.vue';
import SettingsLayout from '../../layouts/settings/Layout.vue';
import { type BreadcrumbItem, type SharedData, type User } from '../../types';
import { useNotifications } from '../../composables/useNotifications';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Payment Settings',
        href: '/settings/payment-settings',
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;
const { showSuccess, showError } = useNotifications();

// State
const loading = ref(false);
const saving = ref(false);
const testing = ref(false);

// Data
const paymentSettings = ref<any[]>([]);

// Stripe Settings
const stripeSettings = ref({
    enabled: false,
    publishable_key: '',
    secret_key: '',
    webhook_secret: '',
    currency: 'usd'
});

// Computed properties
const isStripeConfigured = computed(() => {
    return stripeSettings.value.publishable_key && stripeSettings.value.secret_key;
});

const stripeStatusText = computed(() => {
    if (!isStripeConfigured.value) return 'Not Configured';
    return stripeSettings.value.enabled ? 'Active' : 'Inactive';
});

const stripeStatusColor = computed(() => {
    if (!isStripeConfigured.value) return 'bg-gray-100 text-gray-800';
    return stripeSettings.value.enabled ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
});



// Load data on mount
onMounted(async () => {
    await loadPaymentSettings();
    await loadStripeSettings();
});

const loadPaymentSettings = async () => {
    try {
        loading.value = true;
        const response = await axios.get('/settings/payment-settings/data');
        paymentSettings.value = response.data.payment_settings || [];
    } catch (err: any) {
        const errorMessage = err.response?.data?.message || err.message || 'Failed to load payment settings';
        showError(errorMessage);
        paymentSettings.value = [];
    } finally {
        loading.value = false;
    }
};



// Load existing Stripe settings
const loadStripeSettings = async () => {
    try {
        const existingStripe = paymentSettings.value.find(setting => setting.payment_provider === 'stripe');

        if (existingStripe) {
            stripeSettings.value.enabled = existingStripe.is_active;
            // Load actual values from backend for editing
            const response = await axios.get(`/settings/payment-settings/${existingStripe.id}/edit`);

            if (response.data.payment_settings && response.data.payment_settings.details) {
                const details = response.data.payment_settings.details;
                stripeSettings.value.publishable_key = details.publishable_key || '';
                stripeSettings.value.secret_key = details.secret_key || '';
                stripeSettings.value.webhook_secret = details.webhook_secret || '';
                stripeSettings.value.currency = details.currency || 'usd';
            }
        }
    } catch (error) {
        console.error('Error loading Stripe settings:', error);
    }
};

// Save Stripe settings (handles both create and update)
const saveStripeSettings = async () => {
    try {
        saving.value = true;

        const data = {
            clinic_id: user.clinic_id,
            payment_provider: 'stripe',
            details: {
                publishable_key: stripeSettings.value.publishable_key,
                secret_key: stripeSettings.value.secret_key,
                webhook_secret: stripeSettings.value.webhook_secret,
                currency: stripeSettings.value.currency
            },
            is_active: stripeSettings.value.enabled
        };

        // Always use POST to store endpoint - it handles both create and update
        const response = await axios.post('/settings/payment-settings', data);

        if (response.data.success) {
            showSuccess(response.data.message || 'Stripe settings saved successfully!');
            await loadPaymentSettings(); // Reload the list
            await loadStripeSettings(); // Reload Stripe settings
        }
    } catch (error: any) {
        console.error('Error saving Stripe settings:', error);
        if (error.response?.data?.errors) {
            // Handle validation errors
            const errors = error.response.data.errors;
            const errorMessages = Object.values(errors).flat().join(', ');
            showError(errorMessages);
        } else if (error.response?.data?.message) {
            showError(error.response.data.message);
        } else {
            showError('Failed to save Stripe settings. Please try again.');
        }
    } finally {
        saving.value = false;
    }
};

// Toggle Stripe status
const toggleStripeStatus = async () => {
    // Only allow toggle if Stripe is configured
    if (!isStripeConfigured.value) {
        stripeSettings.value.enabled = false; // Revert
        showError('Please configure Stripe settings first before enabling.');
        return;
    }

    try {
        const data = {
            clinic_id: user.clinic_id,
            payment_provider: 'stripe',
            details: {
                publishable_key: stripeSettings.value.publishable_key,
                secret_key: stripeSettings.value.secret_key,
                webhook_secret: stripeSettings.value.webhook_secret,
                currency: stripeSettings.value.currency
            },
            is_active: stripeSettings.value.enabled
        };

        const response = await axios.post('/settings/payment-settings', data);

        if (response.data.success) {
            await loadPaymentSettings(); // Reload the list
            showSuccess(`Stripe ${stripeSettings.value.enabled ? 'enabled' : 'disabled'} successfully!`);
        }
    } catch (error) {
        console.error('Error toggling Stripe status:', error);
        stripeSettings.value.enabled = !stripeSettings.value.enabled; // Revert on error
        showError('Failed to update Stripe status. Please try again.');
    }
};

// Test Stripe integration
const testIntegration = async () => {
    try {
        testing.value = true;

        const response = await axios.get(`/stripe/test-integration?clinic_id=${user.clinic_id}`);

        if (response.data.success) {
            showSuccess('Stripe integration test successful! Your configuration is working correctly.');
        } else {
            showError(`Integration test failed: ${response.data.error}`);
        }
    } catch (error: any) {
        const errorMessage = error.response?.data?.error || error.response?.data?.message || 'Failed to test integration';
        showError(`Integration test failed: ${errorMessage}`);
    } finally {
        testing.value = false;
    }
};
</script>

<template>
    <Head title="Payment Settings" />

    <AppLayout>
        <SettingsLayout :breadcrumbs="breadcrumbs">
            <div class="space-y-6">
                <!-- Header -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Payment Settings</h1>
                        <p class="text-sm text-gray-600 mt-1">
                            Configure payment providers for your clinic
                        </p>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="text-gray-600 mt-2">Loading payment settings...</p>
                </div>

                <!-- Payment Provider Cards -->
                <div v-else class="max-w-2xl">
                    <!-- Stripe Card -->
                    <Card class="relative">
                        <CardHeader class="pb-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="flex items-center space-x-3">
                                            <CardTitle class="text-xl">Stripe Payment Settings</CardTitle>
                                            <Badge :class="stripeStatusColor">{{ stripeStatusText }}</Badge>
                                        </div>
                                        <CardDescription class="mt-1">Configure your Stripe payment gateway to accept online payments</CardDescription>
                                    </div>
                                </div>

                                <!-- Status Toggle -->
                                <div class="flex items-center space-x-3">
                                    <label class="relative inline-flex items-center cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed': !isStripeConfigured }">
                                        <input
                                            type="checkbox"
                                            v-model="stripeSettings.enabled"
                                            @change="toggleStripeStatus"
                                            :disabled="!isStripeConfigured"
                                            class="sr-only peer"
                                        >
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:bg-gray-100"></div>
                                    </label>
                                    <span class="text-sm font-medium text-gray-700">
                                        {{ stripeSettings.enabled ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                        </CardHeader>

                        <CardContent class="space-y-6">
                            <!-- Configuration Notice -->
                            <div v-if="!isStripeConfigured" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-start space-x-3">
                                    <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <h4 class="text-sm font-medium text-blue-800">Setup Required</h4>
                                        <p class="text-sm text-blue-700 mt-1">Configure your Stripe API keys to start accepting payments. You can find these in your Stripe Dashboard.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Publishable Key -->
                                <div>
                                    <Label class="block text-sm font-medium text-gray-700 mb-2">
                                        <svg class="w-4 h-4 inline mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Publishable Key
                                        <span class="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        type="text"
                                        v-model="stripeSettings.publishable_key"
                                        placeholder="pk_test_..."
                                        class="w-full font-mono text-sm"
                                    />
                                    <p class="text-xs text-gray-500 mt-1">Safe to use in client-side code</p>
                                </div>

                                <!-- Secret Key -->
                                <div>
                                    <Label class="block text-sm font-medium text-gray-700 mb-2">
                                        <svg class="w-4 h-4 inline mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Secret Key
                                        <span class="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        type="password"
                                        v-model="stripeSettings.secret_key"
                                        placeholder="sk_test_..."
                                        class="w-full font-mono text-sm"
                                    />
                                    <p class="text-xs text-gray-500 mt-1">Keep this secure and private</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Webhook Secret -->
                                <div>
                                    <Label class="block text-sm font-medium text-gray-700 mb-2">
                                        <svg class="w-4 h-4 inline mr-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                        </svg>
                                        Webhook Secret
                                        <span class="text-gray-500 text-xs">(Optional)</span>
                                    </Label>
                                    <Input
                                        type="password"
                                        v-model="stripeSettings.webhook_secret"
                                        placeholder="whsec_..."
                                        class="w-full font-mono text-sm"
                                    />
                                    <p class="text-xs text-gray-500 mt-1">For webhook verification</p>
                                </div>

                                <!-- Default Currency -->
                                <div>
                                    <Label class="block text-sm font-medium text-gray-700 mb-2">
                                        <svg class="w-4 h-4 inline mr-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                        </svg>
                                        Default Currency
                                    </Label>
                                    <Select v-model="stripeSettings.currency">
                                        <SelectTrigger class="w-full">
                                            <SelectValue placeholder="Select currency" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="usd">USD - US Dollar</SelectItem>
                                            <SelectItem value="eur">EUR - Euro</SelectItem>
                                            <SelectItem value="gbp">GBP - British Pound</SelectItem>
                                            <SelectItem value="cad">CAD - Canadian Dollar</SelectItem>
                                            <SelectItem value="aud">AUD - Australian Dollar</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <p class="text-xs text-gray-500 mt-1">Default currency for payments</p>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="pt-6 border-t border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600">
                                        <span v-if="isStripeConfigured" class="flex items-center text-green-600">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                            Configuration complete
                                        </span>
                                        <span v-else class="flex items-center text-amber-600">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            Required fields missing
                                        </span>
                                    </div>
                                    <Button
                                        @click="saveStripeSettings"
                                        :disabled="saving || (!stripeSettings.publishable_key || !stripeSettings.secret_key)"
                                        class="px-8"
                                    >
                                        <div v-if="saving" class="flex items-center">
                                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Saving...
                                        </div>
                                        <div v-else class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Save Configuration
                                        </div>
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Help Section -->
                    <div class="mt-8 bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Need Help Setting Up Stripe?</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Getting Your API Keys</h4>
                                <ol class="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                                    <li>Log in to your Stripe Dashboard</li>
                                    <li>Navigate to Developers → API keys</li>
                                    <li>Copy your Publishable key and Secret key</li>
                                    <li>For webhooks, go to Developers → Webhooks</li>
                                </ol>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Test vs Live Mode</h4>
                                <p class="text-sm text-gray-600 mb-2">
                                    Use test keys (pk_test_... and sk_test_...) for development and testing.
                                    Switch to live keys when you're ready to accept real payments.
                                </p>
                                <a href="https://stripe.com/docs/keys" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    Learn more about API keys →
                                </a>
                            </div>
                        </div>

                        <!-- Test Integration Button -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">Test Integration</h4>
                                    <p class="text-sm text-gray-600">Verify your Stripe configuration is working correctly</p>
                                </div>
                                <Button
                                    @click="testIntegration"
                                    :disabled="!isStripeConfigured || testing"
                                    variant="outline"
                                    class="px-4"
                                >
                                    <div v-if="testing" class="flex items-center">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                        Testing...
                                    </div>
                                    <div v-else>Test Connection</div>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
