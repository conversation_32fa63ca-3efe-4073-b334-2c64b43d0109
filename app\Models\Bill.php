<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Bill extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'bill_number',
        'clinic_id',
        'patient_id',
        'provider_id',
        'consultation_id',
        'appointment_id',
        'title',
        'subtotal',
        'tax_data',
        'tax_amount',
        'discount',
        'total_amount',
        'payment_status',
        'status',
        'bill_date',
        'due_date',
        'paid_at',
        'notes',
        'wp_bill_id',
        'wp_encounter_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_data' => 'array',
        'tax_amount' => 'decimal:2',
        'discount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'bill_date' => 'datetime',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($bill) {
            if (empty($bill->bill_number)) {
                $bill->bill_number = static::generateBillNumber();
            }
        });
    }

    /**
     * Generate a unique bill number.
     */
    public static function generateBillNumber(): string
    {
        $prefix = 'BILL-';
        $date = now()->format('Ymd');

        do {
            $number = $prefix . $date . '-' . strtoupper(Str::random(6));
        } while (static::where('bill_number', $number)->exists());

        return $number;
    }

    /**
     * Get the clinic that owns the bill.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the patient associated with the bill.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the provider associated with the bill.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the consultation associated with the bill.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the appointment associated with the bill.
     */
    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the bill items for the bill.
     */
    public function billItems()
    {
        return $this->hasMany(BillItem::class);
    }

    /**
     * Get the payments for the bill.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'bill_id');
    }

    /**
     * Get the consultation services associated with this bill.
     */
    public function consultationServices()
    {
        return $this->hasMany(ConsultationService::class);
    }

    /**
     * Calculate and update bill totals.
     */
    public function calculateTotals()
    {
        $this->subtotal = $this->billItems->sum('total_price');

        // Apply taxes if tax_data exists
        $taxAmount = 0;
        if ($this->tax_data && is_array($this->tax_data)) {
            foreach ($this->tax_data as $taxInfo) {
                $taxAmount += $taxInfo['amount'] ?? 0;
            }
        }

        $this->tax_amount = $taxAmount;
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount;

        return $this;
    }

    /**
     * Mark bill as paid.
     */
    public function markAsPaid()
    {
        $this->update([
            'payment_status' => 'paid',
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    /**
     * Check if bill is paid.
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAttribute(): string
    {
        return '£' . number_format($this->total_amount, 2);
    }

    /**
     * Get the total paid amount from payments.
     */
    public function getPaidAmountAttribute(): float
    {
        return $this->payments()->sum('amount') ?? 0.0;
    }

    /**
     * Get the remaining amount due.
     */
    public function getDueAmountAttribute(): float
    {
        return $this->total_amount - $this->paid_amount;
    }
}
