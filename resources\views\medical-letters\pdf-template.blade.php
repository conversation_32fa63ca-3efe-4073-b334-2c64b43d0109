<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Medical Letter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 20px;
            color: #333;
        }
        
        .letterhead {
            text-align: center;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .letterhead h1 {
            color: #0066cc;
            margin: 0;
            font-size: 24px;
        }
        
        .letterhead .practice-info {
            margin-top: 10px;
            font-size: 11px;
            color: #666;
        }
        
        .doctor-info {
            text-align: right;
            margin-bottom: 30px;
        }
        
        .doctor-info .name {
            font-weight: bold;
            font-size: 14px;
        }
        
        .doctor-info .registration {
            font-size: 11px;
            color: #666;
        }
        
        .letter-date {
            text-align: right;
            margin-bottom: 30px;
            font-size: 11px;
        }
        
        .recipient {
            margin-bottom: 30px;
        }
        
        .recipient .name {
            font-weight: bold;
        }
        
        .recipient .address {
            white-space: pre-line;
            margin-top: 5px;
        }
        
        .subject {
            font-weight: bold;
            margin-bottom: 20px;
            text-decoration: underline;
        }
        
        .content {
            margin-bottom: 30px;
            text-align: justify;
            white-space: pre-line;
        }
        
        .patient-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #0066cc;
            margin: 20px 0;
        }
        
        .patient-info .label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        
        .signature-section {
            margin-top: 50px;
        }
        
        .signature {
            margin-top: 20px;
        }
        
        .signature img {
            max-height: 60px;
            max-width: 200px;
        }
        
        .signature .name {
            font-weight: bold;
            margin-top: 10px;
        }
        
        .signature .title {
            font-size: 11px;
            color: #666;
        }
        
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #999;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>
<body>
    @if($include_letterhead && $doctor_settings && $doctor_settings->practice_name)
    <div class="letterhead">
        <h1>{{ $doctor_settings->practice_name }}</h1>
        <div class="practice-info">
            @if($doctor_settings->practice_address)
                <div>{{ $doctor_settings->practice_address }}</div>
            @endif
            @if($doctor_settings->practice_phone || $doctor_settings->practice_email)
                <div>
                    @if($doctor_settings->practice_phone)
                        Tel: {{ $doctor_settings->practice_phone }}
                    @endif
                    @if($doctor_settings->practice_phone && $doctor_settings->practice_email)
                        | 
                    @endif
                    @if($doctor_settings->practice_email)
                        Email: {{ $doctor_settings->practice_email }}
                    @endif
                </div>
            @endif
        </div>
    </div>
    @endif

    <div class="doctor-info">
        <div class="name">{{ $variables['doctor_name'] ?? $letter->creator->name }}</div>
        @if($doctor_settings && $doctor_settings->registration_display)
            <div class="registration">{{ $doctor_settings->registration_display }}</div>
        @endif
    </div>

    <div class="letter-date">
        {{ \Carbon\Carbon::parse($letter->letter_date)->format('d F Y') }}
    </div>

    @if($letter->recipient_name)
    <div class="recipient">
        <div class="name">{{ $letter->full_recipient_name }}</div>
        @if($letter->recipient_address)
            <div class="address">{{ $letter->recipient_address }}</div>
        @endif
    </div>
    @endif

    <div class="subject">
        Re: {{ $letter->subject }}
    </div>

    @if($letter->patient)
    <div class="patient-info">
        <div><span class="label">Patient Name:</span> {{ $variables['patient_name'] ?? $letter->patient->user->name }}</div>
        @if($variables['patient_dob'])
            <div><span class="label">Date of Birth:</span> {{ $variables['patient_dob'] }}</div>
        @endif
        @if($letter->consultation && $variables['consultation_date'])
            <div><span class="label">Consultation Date:</span> {{ $variables['consultation_date'] }}</div>
        @endif
    </div>
    @endif

    <div class="content">
        {{ $letter->content }}
    </div>

    @if($letter->additional_notes)
    <div class="content">
        <strong>Additional Notes:</strong><br>
        {{ $letter->additional_notes }}
    </div>
    @endif

    <div class="signature-section">
        <div>Yours sincerely,</div>
        
        @if($include_signature && $doctor_settings && $doctor_settings->hasDigitalSignature())
        <div class="signature">
            <img src="{{ $doctor_settings->getSignatureDataUrl() }}" alt="Digital Signature">
        </div>
        @else
        <div style="height: 60px;"></div>
        @endif
        
        <div class="signature">
            <div class="name">
                @if($doctor_settings && $doctor_settings->professional_prefix)
                    {{ $doctor_settings->professional_prefix }} 
                @endif
                {{ $letter->creator->name }}
            </div>
            @if($doctor_settings && $doctor_settings->registration_display)
                <div class="title">{{ $doctor_settings->registration_display }}</div>
            @endif
        </div>
    </div>

    <div class="footer">
        This letter was generated electronically on {{ now()->format('d/m/Y H:i') }} | 
        Letter ID: {{ $letter->id }} | 
        @if($doctor_settings && $doctor_settings->practice_name)
            {{ $doctor_settings->practice_name }}
        @else
            Medroid Healthcare Platform
        @endif
    </div>
</body>
</html>
