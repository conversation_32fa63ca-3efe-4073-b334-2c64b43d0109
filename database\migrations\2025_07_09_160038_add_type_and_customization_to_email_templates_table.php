<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            // Add type column to categorize templates
            $table->string('type')->default('notification')->after('description');

            // Add allow_customization column for admin control
            $table->boolean('allow_customization')->default(true)->after('type');

            // Add indexes for better performance
            $table->index('type');
            $table->index(['is_active', 'allow_customization']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->dropIndex(['type']);
            $table->dropIndex(['is_active', 'allow_customization']);
            $table->dropColumn(['type', 'allow_customization']);
        });
    }
};
