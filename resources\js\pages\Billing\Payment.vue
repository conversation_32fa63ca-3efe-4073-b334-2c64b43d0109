<template>
    <AppLayout title="Pay Bill">
        <div class="py-6">
            <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
                <!-- Bill <PERSON> -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="text-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">Pay Bill</h2>
                            <p class="text-gray-600 mt-1">Bill #{{ bill.bill_number }}</p>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <dl class="space-y-4">
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Patient:</dt>
                                    <dd class="text-sm text-gray-900">{{ bill.patient?.user?.name }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Provider:</dt>
                                    <dd class="text-sm text-gray-900">{{ bill.provider?.user?.name }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Date:</dt>
                                    <dd class="text-sm text-gray-900">{{ formatDate(bill.bill_date) }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Due Date:</dt>
                                    <dd class="text-sm text-gray-900">{{ bill.due_date ? formatDate(bill.due_date) : 'No due date' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Bill Items -->
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Services</h3>
                            <div class="space-y-2">
                                <div v-for="item in bill.bill_items" :key="item.id" 
                                     class="flex justify-between items-center py-2 border-b border-gray-100">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ item.item_name }}</p>
                                        <p v-if="item.description" class="text-xs text-gray-500">{{ item.description }}</p>
                                        <p class="text-xs text-gray-500">{{ item.quantity }} × £{{ item.unit_price }}</p>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">£{{ item.total_price }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Bill Total -->
                        <div class="mt-6 border-t border-gray-200 pt-4">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Subtotal:</span>
                                    <span class="text-sm text-gray-900">£{{ bill.subtotal }}</span>
                                </div>
                                <div v-if="bill.tax_amount > 0" class="flex justify-between">
                                    <span class="text-sm text-gray-600">Tax:</span>
                                    <span class="text-sm text-gray-900">£{{ bill.tax_amount }}</span>
                                </div>
                                <div v-if="bill.discount > 0" class="flex justify-between">
                                    <span class="text-sm text-gray-600">Discount:</span>
                                    <span class="text-sm text-gray-900">-£{{ bill.discount }}</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t pt-2">
                                    <span>Total:</span>
                                    <span>£{{ bill.total_amount }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Status -->
                <div v-if="bill.payment_status === 'paid'" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span class="text-green-800 font-medium">This bill has been paid</span>
                    </div>
                </div>

                <!-- Payment Form -->
                <div v-else class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
                        
                        <!-- Credit Balance -->
                        <div v-if="userCredit > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-blue-800">Available Credit: £{{ userCredit }}</p>
                                    <p class="text-xs text-blue-600">You can use your credit balance to pay for this bill</p>
                                </div>
                                <label class="flex items-center">
                                    <input v-model="useCredits" 
                                           type="checkbox" 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-blue-800">Use Credits</span>
                                </label>
                            </div>
                            
                            <div v-if="useCredits" class="mt-3">
                                <label class="block text-sm font-medium text-blue-800 mb-1">Credit Amount</label>
                                <input v-model="creditAmount" 
                                       type="number" 
                                       step="0.01"
                                       :max="Math.min(userCredit, bill.total_amount)"
                                       min="0"
                                       class="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- Payment Amount -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                                <span class="text-lg font-medium text-gray-900">Amount to Pay:</span>
                                <span class="text-2xl font-bold text-gray-900">£{{ paymentAmount.toFixed(2) }}</span>
                            </div>
                        </div>

                        <!-- Payment Button -->
                        <div class="text-center">
                            <button v-if="paymentAmount > 0" 
                                    @click="initiatePayment"
                                    :disabled="processing"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors disabled:opacity-50">
                                {{ processing ? 'Processing...' : `Pay £${paymentAmount.toFixed(2)}` }}
                            </button>
                            <button v-else 
                                    @click="payWithCreditsOnly"
                                    :disabled="processing"
                                    class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors disabled:opacity-50">
                                {{ processing ? 'Processing...' : 'Pay with Credits' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import axios from 'axios'

const props = defineProps({
    bill: Object,
    token: String,
    userCredit: {
        type: Number,
        default: 0
    }
})

const processing = ref(false)
const useCredits = ref(false)
const creditAmount = ref(0)

const paymentAmount = computed(() => {
    if (!useCredits.value) return props.bill.total_amount
    const creditToUse = Math.min(creditAmount.value || 0, props.userCredit, props.bill.total_amount)
    return Math.max(0, props.bill.total_amount - creditToUse)
})

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const initiatePayment = async () => {
    processing.value = true
    try {
        const response = await axios.post(`/bills/${props.bill.id}/payment/intent`, {
            use_credits: useCredits.value,
            credit_amount: useCredits.value ? creditAmount.value : 0,
            currency: 'gbp'
        })

        // Here you would integrate with Stripe Elements
        // For now, we'll just show a success message
        alert('Payment initiated successfully! (Stripe integration would happen here)')
        
        // Redirect to success page or reload
        router.reload()
    } catch (error) {
        console.error('Payment error:', error)
        alert('Payment failed. Please try again.')
    } finally {
        processing.value = false
    }
}

const payWithCreditsOnly = async () => {
    processing.value = true
    try {
        const response = await axios.post(`/bills/${props.bill.id}/payment/intent`, {
            use_credits: true,
            credit_amount: props.bill.total_amount,
            currency: 'gbp'
        })

        alert('Payment completed successfully with credits!')
        router.reload()
    } catch (error) {
        console.error('Payment error:', error)
        alert('Payment failed. Please try again.')
    } finally {
        processing.value = false
    }
}

onMounted(() => {
    // Set default credit amount to bill total if user has enough credits
    if (props.userCredit >= props.bill.total_amount) {
        creditAmount.value = props.bill.total_amount
    } else {
        creditAmount.value = props.userCredit
    }
})
</script>
