<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Consolidated WordPress extended fields and data migration
     */
    public function up(): void
    {
        // Extended WordPress fields for users table
        Schema::table('users', function (Blueprint $table) {
            // Additional common fields for WordPress migration data
            $table->string('city')->nullable()->after('address');
            $table->string('state')->nullable()->after('city');
            $table->string('country')->nullable()->after('state');
            $table->string('postal_code')->nullable()->after('country');

            // Store original basic_data for reference during migration
            $table->json('wp_basic_data')->nullable()->after('postal_code');
        });

        // Extended WordPress fields for clinics table
        Schema::table('clinics', function (Blueprint $table) {
            // WordPress specific fields
            $table->string('country_code')->nullable()->after('country');
            $table->string('country_calling_code')->nullable()->after('country_code');
            $table->json('specialties')->nullable()->after('services_offered');
            $table->string('profile_image')->nullable()->after('logo');
            $table->unsignedBigInteger('clinic_admin_id')->nullable()->after('wp_clinic_id');
            $table->string('clinic_admin_email')->nullable()->after('clinic_admin_id');
            $table->string('allow_no_of_doc')->nullable()->after('clinic_admin_email');
            $table->json('settings')->nullable()->after('allow_no_of_doc');
        });

        // Extended WordPress fields for consultations table
        Schema::table('consultations', function (Blueprint $table) {
            $table->text('description')->nullable()->after('consultation_mode');
            $table->integer('added_by_wp_id')->nullable()->after('description');
        });

        // Extended WordPress fields for appointments table
        Schema::table('appointments', function (Blueprint $table) {
            $table->foreignId('clinic_id')->nullable()->after('patient_id')->constrained()->onDelete('set null');
            $table->foreignId('consultation_id')->nullable()->after('clinic_id')->constrained()->onDelete('set null');
            $table->index('clinic_id');
            $table->index('consultation_id');
        });

        // Extended WordPress fields for services table
        Schema::table('services', function (Blueprint $table) {
            $table->foreignId('clinic_id')->nullable()->after('category_id')->constrained()->onDelete('set null');
            $table->string('type')->nullable()->after('category');
            $table->index('clinic_id');
        });

        // Make certain fields nullable for WordPress migration compatibility
        Schema::table('categories', function (Blueprint $table) {
            $table->foreignId('clinic_id')->nullable()->change();
        });

        Schema::table('services', function (Blueprint $table) {
            $table->foreignId('provider_id')->nullable()->change();
        });

        // Extended WordPress fields for patients table
        Schema::table('patients', function (Blueprint $table) {
            // Add medical_history JSON column for storing medical history data
            if (!Schema::hasColumn('patients', 'medical_history')) {
                $table->json('medical_history')->nullable();
            }
        });

        // Extended WordPress fields for bills table
        if (Schema::hasTable('bills')) {
            Schema::table('bills', function (Blueprint $table) {
                if (!Schema::hasColumn('bills', 'wp_encounter_id')) {
                    $table->integer('wp_encounter_id')->nullable()->after('wp_bill_id');
                }
                $table->foreignId('provider_id')->nullable()->change();
            });
        }

        // Extended WordPress fields for provider_availabilities table
        if (Schema::hasTable('provider_availabilities')) {
            Schema::table('provider_availabilities', function (Blueprint $table) {
                $table->foreignId('clinic_id')->nullable()->after('provider_id')->constrained()->onDelete('cascade');
                $table->integer('wp_clinic_session_id')->nullable()->after('clinic_id');
                $table->index('clinic_id');
                $table->index('wp_clinic_session_id');
            });
        }

        // Extended WordPress fields for provider_absences table
        if (Schema::hasTable('provider_absences')) {
            Schema::table('provider_absences', function (Blueprint $table) {
                $table->foreignId('clinic_id')->nullable()->after('provider_id')->constrained()->onDelete('cascade');
                $table->integer('wp_clinic_schedule_id')->nullable()->after('clinic_id');
                $table->index('clinic_id');
                $table->index('wp_clinic_schedule_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse users table changes
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['city', 'state', 'country', 'postal_code', 'wp_basic_data']);
        });

        // Reverse clinics table changes
        Schema::table('clinics', function (Blueprint $table) {
            $table->dropColumn([
                'country_code', 'country_calling_code', 'specialties', 'profile_image',
                'clinic_admin_id', 'clinic_admin_email', 'allow_no_of_doc', 'settings'
            ]);
        });

        // Reverse consultations table changes
        Schema::table('consultations', function (Blueprint $table) {
            $table->dropColumn(['description', 'added_by_wp_id']);
        });

        // Reverse appointments table changes
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropForeign(['clinic_id']);
            $table->dropForeign(['consultation_id']);
            $table->dropIndex(['clinic_id']);
            $table->dropIndex(['consultation_id']);
            $table->dropColumn(['clinic_id', 'consultation_id']);
        });

        // Reverse services table changes
        Schema::table('services', function (Blueprint $table) {
            $table->dropForeign(['clinic_id']);
            $table->dropIndex(['clinic_id']);
            $table->dropColumn(['clinic_id', 'type']);
        });

        // Reverse patients table changes
        if (Schema::hasColumn('patients', 'medical_history')) {
            Schema::table('patients', function (Blueprint $table) {
                $table->dropColumn('medical_history');
            });
        }

        // Reverse bills table changes
        if (Schema::hasTable('bills') && Schema::hasColumn('bills', 'wp_encounter_id')) {
            Schema::table('bills', function (Blueprint $table) {
                $table->dropColumn('wp_encounter_id');
            });
        }

        // Reverse provider_availabilities table changes
        if (Schema::hasTable('provider_availabilities')) {
            Schema::table('provider_availabilities', function (Blueprint $table) {
                $table->dropForeign(['clinic_id']);
                $table->dropIndex(['clinic_id']);
                $table->dropIndex(['wp_clinic_session_id']);
                $table->dropColumn(['clinic_id', 'wp_clinic_session_id']);
            });
        }

        // Reverse provider_absences table changes
        if (Schema::hasTable('provider_absences')) {
            Schema::table('provider_absences', function (Blueprint $table) {
                $table->dropForeign(['clinic_id']);
                $table->dropIndex(['clinic_id']);
                $table->dropIndex(['wp_clinic_schedule_id']);
                $table->dropColumn(['clinic_id', 'wp_clinic_schedule_id']);
            });
        }
    }
};
