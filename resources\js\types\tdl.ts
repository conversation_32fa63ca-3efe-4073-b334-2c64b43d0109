export interface LabTest {
    id: number
    test_code: string
    test_name: string
    category: string
    price: number
    formatted_price: string
    description?: string
    requirements?: LabTestRequirements
    is_active: boolean
    requires_fasting: boolean
    fasting_hours?: number
    sample_type?: string
    turnaround_time?: string
    special_instructions?: string
}

export interface LabTestRequirements {
    fasting_required?: boolean
    fasting_hours?: number
    special_instructions?: string
    sample_type?: string
    turnaround_time?: string
}

export interface LabTestRequest {
    id: number
    clinic_id: number
    patient_id: number
    provider_id: number
    consultation_id?: number
    order_number: string
    status: 'pending' | 'sent' | 'processing' | 'completed' | 'failed' | 'cancelled'
    tests: LabTestRequestItem[]
    request_data: LabTestRequestData
    hl7_message?: string
    azure_file_path?: string
    sent_at?: string
    completed_at?: string
    created_at: string
    updated_at: string
    patient?: User
    provider?: User
    consultation?: Consultation
    results?: LabTestResult[]
}

export interface LabTestRequestItem {
    test_code: string
    test_name: string
    category: string
    price: number
    requirements?: LabTestRequirements
}

export interface LabTestRequestData {
    clinical_notes?: string
    urgent: boolean
    fasting_status: boolean
    collection_date: string
    total_cost: number
    requirements: TestSelectionRequirements
}

export interface TestSelectionRequirements {
    fasting_required: boolean
    max_fasting_hours: number
    special_instructions: string[]
    sample_types: string[]
    max_turnaround_time: string
}

export interface LabTestResult {
    id: number
    request_id?: number
    clinic_id: number
    patient_id: number
    order_number?: string
    lab_reference_id?: string
    status: 'received' | 'processing' | 'completed' | 'reviewed' | 'archived'
    result_data: LabTestResultData
    azure_file_path?: string
    physician_notes?: string
    reviewed_by?: number
    received_at: string
    processed_at?: string
    reviewed_at?: string
    created_at: string
    updated_at: string
    patient?: User
    request?: LabTestRequest
    reviewer?: User
    has_abnormal_results: boolean
    abnormal_count: number
    test_count: number
}

export interface LabTestResultData {
    patient_info: PatientInfo
    tests: TestResult[]
    report_date: string
    lab_reference: string
}

export interface PatientInfo {
    patient_id?: string
    name: string
    dob: string
    gender: string
}

export interface TestResult {
    test_code: string
    test_name: string
    biomarkers: Biomarker[]
}

export interface Biomarker {
    name: string
    value: string
    units?: string
    reference_range?: string
    abnormal_flag: 'N' | 'H' | 'L' | 'HH' | 'LL' | 'A'
    observation_datetime?: string
}

export interface TdlSettings {
    id?: number
    clinic_id: number
    azure_connection_string?: string
    tdl_account_id?: string
    is_active: boolean
    settings: TdlSettingsOptions
    has_azure_connection?: boolean
}

export interface TdlSettingsOptions {
    auto_send_requests: boolean
    auto_process_results: boolean
    polling_frequency: number
    notification_email?: string
    require_physician_review: boolean
    auto_cleanup_files: boolean
}

export interface TdlConnectionStatus {
    is_configured: boolean
    is_active: boolean
    has_azure_connection: boolean
    has_tdl_account: boolean
    connection_test: boolean
    last_activity?: string
}

export interface AzureFileStats {
    requests_count: number
    results_count: number
    total_size: number
    error?: string
}

export interface User {
    id: number
    first_name: string
    last_name: string
    email: string
    name?: string
    date_of_birth?: string
    gender?: string
    phone?: string
    address?: string
}

export interface Consultation {
    id: number
    patient_id: number
    provider_id: number
    clinic_id: number
    status: string
    created_at: string
    patient?: User
    provider?: User
}

export interface PaginationMeta {
    current_page: number
    last_page: number
    total: number
    per_page: number
}

export interface ApiResponse<T = any> {
    success: boolean
    message: string
    data: T
}

export interface PaginatedResponse<T> {
    data: T[]
    current_page: number
    last_page: number
    total: number
    per_page: number
}
