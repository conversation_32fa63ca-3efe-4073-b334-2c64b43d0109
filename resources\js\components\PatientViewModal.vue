<template>
    <!-- Patient Details Modal -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm" @click="$emit('close')"></div>
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl">
                <!-- Header -->
                <div class="bg-white border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-8 w-8 bg-teal-50 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user text-teal-600 text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Patient Details</h3>
                        </div>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 hover:bg-gray-100 rounded-lg">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6 max-h-[70vh] overflow-y-auto">
                    <div v-if="patient" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Basic Information</h4>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Patient ID:</span>
                                    <span class="text-gray-900">{{ patient.patient_unique_id || `P${patient.id}` }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Name:</span>
                                    <span class="text-gray-900">{{ patient.user?.name || `${patient.first_name || ''} ${patient.last_name || ''}`.trim() || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Email:</span>
                                    <span class="text-gray-900">{{ patient.user?.email || patient.email || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Phone:</span>
                                    <span class="text-gray-900">{{ patient.user?.phone_number || patient.phone_number || patient.phone || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Date of Birth:</span>
                                    <span class="text-gray-900">{{ formatDate(patient.date_of_birth) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Age:</span>
                                    <span class="text-gray-900">{{ calculateAge(patient.date_of_birth) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Gender:</span>
                                    <span class="text-gray-900 capitalize">{{ patient.gender || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">NHS Number:</span>
                                    <span class="text-gray-900">{{ patient.nhs_number || 'N/A' }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Address Information</h4>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Address:</span>
                                    <span class="text-gray-900">{{ patient.user?.address || patient.address || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">City:</span>
                                    <span class="text-gray-900">{{ patient.user?.city || patient.city || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Postal Code:</span>
                                    <span class="text-gray-900">{{ patient.user?.postal_code || patient.postal_code || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Country:</span>
                                    <span class="text-gray-900">{{ patient.user?.country || patient.country || 'N/A' }}</span>
                                </div>
                                <hr class="my-2 border-gray-300">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Registered GP:</span>
                                    <span class="text-gray-900">{{ patient.registered_gp_name || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">GP Address:</span>
                                    <span class="text-gray-900">{{ patient.registered_gp_address || 'N/A' }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Insurance Information -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Insurance Information</h4>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Provider:</span>
                                    <span class="text-gray-900">{{ patient.insurance_provider || 'None' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Policy Number:</span>
                                    <span class="text-gray-900">{{ patient.insurance_policy_number || 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Expiry Date:</span>
                                    <span class="text-gray-900">{{ formatDate(patient.insurance_expiry_date) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Emergency Contact</h4>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div v-if="patient.emergency_contact" class="text-gray-900">{{ patient.emergency_contact }}</div>
                                <div v-else class="text-gray-600 italic">No emergency contact information provided</div>
                            </div>
                        </div>

                        <!-- Medical Information -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50 lg:col-span-2">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Medical Information</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Allergies -->
                                <div>
                                    <h5 class="font-semibold text-red-700 mb-2 flex items-center">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>Allergies
                                    </h5>
                                    <div v-if="parseJsonField(patient.allergies).length > 0" class="space-y-1">
                                        <span v-for="allergy in parseJsonField(patient.allergies)" :key="allergy"
                                              class="inline-block bg-red-200 text-red-900 text-xs font-medium px-3 py-1 rounded-full mr-2 mb-1">
                                            {{ allergy }}
                                        </span>
                                    </div>
                                    <span v-else class="text-gray-600 italic">None known</span>
                                </div>

                                <!-- Current Medications -->
                                <div>
                                    <h5 class="font-semibold text-blue-700 mb-2 flex items-center">
                                        <i class="fas fa-pills mr-2"></i>Current Medications
                                    </h5>
                                    <div v-if="patient.current_medications" class="text-sm text-gray-900 bg-blue-50 p-3 rounded-md">
                                        {{ patient.current_medications }}
                                    </div>
                                    <span v-else class="text-gray-600 italic">None</span>
                                </div>

                                <!-- Medical History -->
                                <div>
                                    <h5 class="font-semibold text-orange-700 mb-2 flex items-center">
                                        <i class="fas fa-stethoscope mr-2"></i>Medical History
                                    </h5>
                                    <div v-if="patient.medical_history" class="text-sm text-gray-900 bg-orange-50 p-3 rounded-md">
                                        {{ patient.medical_history }}
                                    </div>
                                    <span v-else class="text-gray-600 italic">None</span>
                                </div>

                                <!-- Previous Surgeries -->
                                <div>
                                    <h5 class="font-semibold text-purple-700 mb-2 flex items-center">
                                        <i class="fas fa-cut mr-2"></i>Previous Surgeries
                                    </h5>
                                    <div v-if="parseJsonObject(patient.health_history).surgeries?.length > 0" class="space-y-1">
                                        <div v-for="surgery in parseJsonObject(patient.health_history).surgeries" :key="surgery"
                                             class="inline-block bg-purple-200 text-purple-900 text-xs font-medium px-3 py-1 rounded-full mr-2 mb-1">
                                            {{ surgery }}
                                        </div>
                                    </div>
                                    <span v-else class="text-gray-600 italic">None</span>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Preferences -->
                        <div class="border border-gray-200 rounded-lg p-5 bg-gray-50 lg:col-span-2">
                            <div class="flex items-center mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Appointment Preferences</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-semibold text-indigo-700 mb-2">Preferred Days:</h5>
                                    <div v-if="parseJsonObject(patient.appointment_preferences).preferred_days?.length > 0" class="space-y-1">
                                        <span v-for="day in parseJsonObject(patient.appointment_preferences).preferred_days" :key="day"
                                              class="inline-block bg-indigo-200 text-indigo-900 text-xs font-medium px-3 py-1 rounded-full mr-2 mb-1">
                                            {{ day }}
                                        </span>
                                    </div>
                                    <span v-else class="text-gray-600 italic">No preference</span>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-indigo-700 mb-2">Preferred Time:</h5>
                                    <span class="text-gray-900">{{ parseJsonObject(patient.appointment_preferences).preferred_time || 'No preference' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-else class="text-center py-8">
                        <Icon name="user" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p class="text-gray-500">No patient information available</p>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-white px-6 py-4 border-t border-gray-200 flex justify-end space-x-3 rounded-b-lg">
                    <button @click="$emit('close')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                    <button v-if="showEditButton && patient" @click="$emit('edit', patient)" class="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2">
                        Edit Patient
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import Icon from '@/components/Icon.vue'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    patient: {
        type: Object,
        default: null
    },
    showEditButton: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['close', 'edit'])

// Helper functions
const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

// Parse JSON fields safely
const parseJsonField = (field) => {
    if (!field) return [];
    try {
        return Array.isArray(field) ? field : JSON.parse(field);
    } catch (e) {
        return [];
    }
};

const parseJsonObject = (field) => {
    if (!field) return {};
    try {
        return typeof field === 'object' ? field : JSON.parse(field);
    } catch (e) {
        return {};
    }
};
</script>
