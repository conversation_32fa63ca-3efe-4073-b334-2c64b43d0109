<?php

namespace App\Repositories\Interfaces;

use App\Models\Tax;
use Illuminate\Database\Eloquent\Collection;

interface TaxRepositoryInterface
{
    public function getAllByClinic(int $clinicId): Collection;
    public function getActiveByClinic(int $clinicId): Collection;
    public function create(array $data): Tax;
    public function update(Tax $tax, array $data): Tax;
    public function delete(Tax $tax): bool;
    public function findById(int $id): ?Tax;
}
