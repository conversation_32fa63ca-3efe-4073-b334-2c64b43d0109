interface DrugSearchResult {
    rxcui: string
    name: string
    display_name: string
    score: number
}

interface DrugDetails {
    rxcui: string
    properties?: any
    drug_classes?: any[]
    related?: any
}

interface DrugInteraction {
    rxcui1: string
    rxcui2: string
    description: string
    severity: string
}

interface ApiResponse<T = any> {
    success: boolean
    message: string
    data: T
}

class DrugSearchService {
    private baseUrl = '/drugs'

    /**
     * Search for drugs using RxNorm API
     */
    async searchDrugs(query: string, limit: number = 20): Promise<ApiResponse<{
        drugs: DrugSearchResult[]
        suggestions: string[]
        query: string
        count: number
        has_suggestions: boolean
    }>> {
        const params = new URLSearchParams({
            query,
            limit: limit.toString()
        })

        const response = await fetch(`${this.baseUrl}/enhanced-search?${params}`)
        return response.json()
    }

    /**
     * Get detailed drug information by RxCUI
     */
    async getDrugDetails(rxcui: string): Promise<ApiResponse<{
        rxcui: string
        details: DrugDetails
        drug_classes: any[]
        ndc_codes: string[]
    }>> {
        const response = await fetch(`${this.baseUrl}/${rxcui}/details`)
        return response.json()
    }

    /**
     * Check drug interactions for multiple drugs
     */
    async checkDrugInteractions(rxcuis: string[]): Promise<ApiResponse<{
        rxcuis: string[]
        interactions: DrugInteraction[]
        has_interactions: boolean
    }>> {
        const response = await fetch(`${this.baseUrl}/interactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.getCsrfToken()
            },
            body: JSON.stringify({ rxcuis })
        })
        return response.json()
    }

    /**
     * Get spelling suggestions for drug names
     */
    async getSpellingSuggestions(query: string): Promise<ApiResponse<{
        query: string
        suggestions: string[]
    }>> {
        const params = new URLSearchParams({ query })
        const response = await fetch(`${this.baseUrl}/suggestions?${params}`)
        return response.json()
    }

    /**
     * Get drug classes for a specific drug
     */
    async getDrugClasses(rxcui: string): Promise<ApiResponse<{
        rxcui: string
        drug_classes: any[]
    }>> {
        const response = await fetch(`${this.baseUrl}/${rxcui}/classes`)
        return response.json()
    }

    /**
     * Simple drug search with basic results
     */
    async simpleSearch(query: string, limit: number = 10): Promise<ApiResponse<{
        drugs: DrugSearchResult[]
        query: string
        count: number
    }>> {
        const params = new URLSearchParams({
            query,
            limit: limit.toString()
        })

        const response = await fetch(`${this.baseUrl}/search?${params}`)
        return response.json()
    }

    /**
     * Get CSRF token from meta tag
     */
    private getCsrfToken(): string {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (!token) {
            throw new Error('CSRF token not found')
        }
        return token
    }








}

export const drugSearchService = new DrugSearchService()
export default drugSearchService
