<?php

return [
    
    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'default' => env('APP_TIMEZONE', 'UTC'),

    /*
    |--------------------------------------------------------------------------
    | Timezone Storage
    |--------------------------------------------------------------------------
    |
    | This determines how appointment times are stored in the database.
    | 'utc' - Store all times in UTC and convert for display
    | 'local' - Store times in the application's local timezone
    | 'user' - Store times in the user's timezone (requires user timezone tracking)
    |
    */

    'storage_mode' => env('TIMEZONE_STORAGE_MODE', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Auto DST Adjustment
    |--------------------------------------------------------------------------
    |
    | When enabled, the system will automatically detect and handle
    | daylight savings time transitions for appointment scheduling.
    |
    */

    'auto_dst_adjustment' => env('AUTO_DST_ADJUSTMENT', true),

    /*
    |--------------------------------------------------------------------------
    | Supported Timezones
    |--------------------------------------------------------------------------
    |
    | List of timezones supported by the application. This can help
    | limit timezone selection and improve performance.
    |
    */

    'supported_timezones' => [
        'UTC' => 'UTC',
        'America/New_York' => 'Eastern Time',
        'America/Chicago' => 'Central Time', 
        'America/Denver' => 'Mountain Time',
        'America/Los_Angeles' => 'Pacific Time',
        'Europe/London' => 'GMT/BST',
        'Europe/Berlin' => 'CET/CEST',
        'Asia/Tokyo' => 'JST',
        'Australia/Sydney' => 'AEDT/AEST',
    ],

];