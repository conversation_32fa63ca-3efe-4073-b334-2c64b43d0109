<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6">
        <!-- Progress Icon/Animation -->
        <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4">
          <div v-if="type === 'spinner'" class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          
          <div v-else-if="type === 'pulse'" class="flex space-x-1">
            <div class="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
            <div class="w-3 h-3 bg-primary rounded-full animate-pulse" style="animation-delay: 0.1s"></div>
            <div class="w-3 h-3 bg-primary rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
          </div>
          
          <div v-else-if="type === 'bounce'" class="flex space-x-1">
            <div class="w-3 h-3 bg-primary rounded-full animate-bounce"></div>
            <div class="w-3 h-3 bg-primary rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-3 h-3 bg-primary rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
          
          <div v-else class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>

        <!-- Progress Content -->
        <div class="text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            {{ title || 'Processing...' }}
          </h3>
          <p class="text-gray-600 mb-4">
            {{ message || 'Please wait while we process your request.' }}
          </p>

          <!-- Progress Bar -->
          <div v-if="showProgress && progress >= 0" class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>{{ progressLabel || 'Progress' }}</span>
              <span>{{ Math.round(progress) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                :style="{ width: progress + '%' }"
              ></div>
            </div>
          </div>

          <!-- Steps Indicator -->
          <div v-if="steps && steps.length > 0" class="mb-4">
            <div class="flex justify-between items-center">
              <div 
                v-for="(step, index) in steps" 
                :key="index"
                class="flex flex-col items-center flex-1"
              >
                <div 
                  class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mb-2"
                  :class="{
                    'bg-green-500 text-white': step.status === 'completed',
                    'bg-primary text-white': step.status === 'active',
                    'bg-gray-200 text-gray-600': step.status === 'pending'
                  }"
                >
                  <svg v-if="step.status === 'completed'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <div v-else-if="step.status === 'active'" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <span class="text-xs text-center text-gray-600">{{ step.label }}</span>
              </div>
            </div>
          </div>

          <!-- Current Operation -->
          <div v-if="currentOperation" class="mb-4 p-3 bg-blue-50 rounded-md">
            <p class="text-sm text-blue-800">
              <span class="font-medium">Current:</span> {{ currentOperation }}
            </p>
          </div>

          <!-- Estimated Time -->
          <div v-if="estimatedTime" class="mb-4 text-sm text-gray-600">
            Estimated time remaining: {{ formatTime(estimatedTime) }}
          </div>

          <!-- Cancel Button -->
          <div v-if="canCancel" class="flex justify-center">
            <button 
              @click="cancel"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  show: Boolean,
  type: {
    type: String,
    default: 'spinner', // spinner, pulse, bounce
    validator: (value) => ['spinner', 'pulse', 'bounce'].includes(value)
  },
  title: String,
  message: String,
  progress: {
    type: Number,
    default: -1 // -1 means indeterminate
  },
  progressLabel: String,
  showProgress: {
    type: Boolean,
    default: true
  },
  steps: Array, // [{ label: 'Step 1', status: 'completed|active|pending' }]
  currentOperation: String,
  estimatedTime: Number, // in seconds
  canCancel: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['cancel'])

const cancel = () => {
  emit('cancel')
}

const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${seconds} seconds`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''}`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours} hour${hours > 1 ? 's' : ''} ${minutes > 0 ? `${minutes} minute${minutes > 1 ? 's' : ''}` : ''}`
  }
}
</script>
