<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing email templates with appropriate types
        $templateTypes = [
            // Appointment templates
            'appointment-booked-patient' => 'appointment',
            'appointment-booked-provider' => 'appointment',
            'appointment-confirmed-patient' => 'appointment',
            'appointment-confirmed-provider' => 'appointment',
            'appointment-cancelled-patient' => 'appointment',
            'appointment-cancelled-provider' => 'appointment',
            'appointment-reminder-patient' => 'appointment',
            'appointment-reminder-provider' => 'appointment',
            'appointment-rescheduled-patient' => 'appointment',
            'appointment-rescheduled-provider' => 'appointment',

            // Welcome templates
            'user-registration' => 'welcome',
            'provider-registration' => 'welcome',

            // Security templates (should not allow customization)
            'password-reset' => 'security',
            'email-verification' => 'security',

            // Patient communication
            'patient-invoice' => 'patient',
            'payment-confirmation' => 'patient',
            'payment-reminder' => 'patient',

            // System notifications
            'referral-invitation' => 'notification',
        ];

        foreach ($templateTypes as $slug => $type) {
            DB::table('email_templates')
                ->where('slug', $slug)
                ->update(['type' => $type]);
        }

        // Set security templates to not allow customization
        DB::table('email_templates')
            ->where('type', 'security')
            ->update(['allow_customization' => false]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset all templates to default type and allow customization
        DB::table('email_templates')->update([
            'type' => 'notification',
            'allow_customization' => true,
        ]);
    }
};
