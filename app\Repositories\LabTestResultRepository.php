<?php

namespace App\Repositories;

use App\Models\LabTestResult;
use App\Repositories\Interfaces\LabTestResultRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class LabTestResultRepository extends BaseRepository implements LabTestResultRepositoryInterface
{
    public function __construct(LabTestResult $model)
    {
        parent::__construct($model);
    }

    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator
    {
        return $this->model->where('clinic_id', $clinicId)
            ->with(['patient', 'request', 'reviewer'])
            ->orderBy('received_at', 'desc')
            ->paginate($perPage);
    }

    public function create(array $data): LabTestResult
    {
        if (!isset($data['received_at'])) {
            $data['received_at'] = now();
        }
        
        return $this->model->create($data);
    }

    public function update(LabTestResult $result, array $data): LabTestResult
    {
        $result->update($data);
        return $result->fresh();
    }

    public function delete(LabTestResult $result): bool
    {
        return $result->delete();
    }

    public function findById(int $id): ?LabTestResult
    {
        return $this->model->find($id);
    }

    public function findByIdWithRelations(int $id, array $relations = []): ?LabTestResult
    {
        $defaultRelations = ['patient', 'request', 'clinic', 'reviewer'];
        $relations = empty($relations) ? $defaultRelations : $relations;
        
        return $this->model->with($relations)->find($id);
    }

    public function findByOrderNumber(string $orderNumber): ?LabTestResult
    {
        return $this->model->where('order_number', $orderNumber)->first();
    }

    public function findByLabReference(string $labReferenceId): ?LabTestResult
    {
        return $this->model->where('lab_reference_id', $labReferenceId)->first();
    }

    public function getByPatient(int $patientId): Collection
    {
        return $this->model->forPatient($patientId)
            ->with(['request'])
            ->orderBy('received_at', 'desc')
            ->get();
    }

    public function getByRequest(int $requestId): Collection
    {
        return $this->model->where('request_id', $requestId)
            ->orderBy('received_at', 'desc')
            ->get();
    }

    public function getByStatus(string $status): Collection
    {
        return $this->model->byStatus($status)
            ->with(['patient', 'request'])
            ->orderBy('received_at', 'desc')
            ->get();
    }

    public function getUnreviewedResults(): Collection
    {
        return $this->model->unreviewed()
            ->with(['patient', 'request', 'clinic'])
            ->orderBy('received_at', 'asc')
            ->get();
    }

    public function getRecentResults(int $limit = 10): Collection
    {
        return $this->model->with(['patient', 'request'])
            ->orderBy('received_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
