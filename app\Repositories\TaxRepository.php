<?php

namespace App\Repositories;

use App\Models\Tax;
use App\Repositories\Interfaces\TaxRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class TaxRepository implements TaxRepositoryInterface
{
    public function getAllByClinic(int $clinicId): Collection
    {
        return Tax::where('clinic_id', $clinicId)
                  ->orderBy('name')
                  ->get();
    }

    public function getActiveByClinic(int $clinicId): Collection
    {
        return Tax::activeForClinic($clinicId)->get();
    }

    public function create(array $data): Tax
    {
        return Tax::create($data);
    }

    public function update(Tax $tax, array $data): Tax
    {
        $tax->update($data);
        return $tax->fresh();
    }

    public function delete(Tax $tax): bool
    {
        return $tax->delete();
    }

    public function findById(int $id): ?Tax
    {
        return Tax::find($id);
    }
}
