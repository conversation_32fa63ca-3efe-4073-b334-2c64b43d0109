<?php

namespace App\Models;

use App\Traits\ClinicFilterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class PaymentSettings extends Model
{
    use HasFactory, ClinicFilterable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'payment_provider',
        'details',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'details' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the clinic that owns the payment settings.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get payment settings for a specific clinic and provider.
     */
    public static function getForClinic($clinicId, $provider = 'stripe')
    {
        return static::where('clinic_id', $clinicId)
            ->where('payment_provider', $provider)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get Stripe configuration for a clinic.
     * Always starts with system config and overwrites with clinic config if available.
     */
    public static function getStripeConfig($clinicId = null)
    {
        // Start with system configuration
        $config = [
            'key' => config('services.stripe.key'),
            'secret' => config('services.stripe.secret'),
            'webhook_secret' => config('services.stripe.webhook.secret'),
            'currency' => 'usd',
            'source' => 'system',
            'clinic_id' => $clinicId
        ];

        // If no clinic ID provided, return system configuration
        if (!$clinicId) {
            return $config;
        }

        try {
            $settings = static::getForClinic($clinicId, 'stripe');

            // If clinic has valid, active settings, overwrite system config
            if ($settings && $settings->is_active && $settings->isConfigured()) {
                $config['key'] = $settings->details['publishable_key'];
                $config['secret'] = $settings->details['secret_key'];
                $config['webhook_secret'] = $settings->details['webhook_secret'] ?? $config['webhook_secret'];
                $config['currency'] = $settings->details['currency'] ?? $config['currency'];
                $config['source'] = 'clinic_specific';
            } else {
                $config['source'] = 'system_fallback';
            }
        } catch (\Exception $e) {
            \Log::warning("Error loading payment settings for clinic {$clinicId}: " . $e->getMessage());
            $config['source'] = 'system_error_fallback';
        }

        return $config;
    }

    /**
     * Check if clinic has valid payment settings.
     */
    public static function clinicHasValidSettings($clinicId)
    {
        if (!$clinicId) {
            return false;
        }

        try {
            $settings = static::getForClinic($clinicId, 'stripe');
            return $settings && $settings->is_active && $settings->isConfigured();
        } catch (\Exception $e) {
            \Log::warning("Error checking payment settings for clinic {$clinicId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Mask sensitive information in details for API responses.
     */
    public function getMaskedDetailsAttribute()
    {
        $details = $this->details;

        if (!$details) {
            // Return empty structure based on payment provider
            return $this->getEmptyDetailsStructure();
        }

        $masked = $details;

        // Mask sensitive keys
        if (isset($details['secret_key'])) {
            $masked['secret_key'] = $this->maskKey($details['secret_key']);
        }

        if (isset($details['webhook_secret'])) {
            $masked['webhook_secret'] = $this->maskKey($details['webhook_secret']);
        }

        return $masked;
    }

    /**
     * Get empty details structure based on payment provider
     */
    private function getEmptyDetailsStructure()
    {
        switch ($this->payment_provider) {
            case 'stripe':
                return [
                    'publishable_key' => '',
                    'secret_key' => '',
                    'webhook_secret' => '',
                    'currency' => ''
                ];
            case 'paypal':
                return [
                    'client_id' => '',
                    'client_secret' => '',
                    'environment' => ''
                ];
            case 'square':
                return [
                    'application_id' => '',
                    'access_token' => '',
                    'environment' => ''
                ];
            default:
                return [];
        }
    }

    /**
     * Mask a key showing only the last 4 characters.
     */
    private function maskKey($key)
    {
        if (strlen($key) <= 4) {
            return str_repeat('*', strlen($key));
        }

        return str_repeat('*', strlen($key) - 4) . substr($key, -4);
    }

    /**
     * Scope to get active payment settings.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get settings by provider.
     */
    public function scopeByProvider($query, $provider)
    {
        return $query->where('payment_provider', $provider);
    }

    /**
     * Check if the payment settings are properly configured.
     */
    public function isConfigured()
    {
        if (!$this->details || !$this->is_active) {
            return false;
        }

        switch ($this->payment_provider) {
            case 'stripe':
                return isset($this->details['publishable_key']) &&
                       isset($this->details['secret_key']) &&
                       !empty($this->details['publishable_key']) &&
                       !empty($this->details['secret_key']);
            default:
                return !empty($this->details);
        }
    }

    /**
     * Get validation rules for payment provider details.
     */
    public static function getValidationRules($provider)
    {
        switch ($provider) {
            case 'stripe':
                return [
                    'details.publishable_key' => 'required|string|starts_with:pk_',
                    'details.secret_key' => 'required|string|starts_with:sk_',
                    'details.webhook_secret' => 'nullable|string|starts_with:whsec_',
                    'details.currency' => 'nullable|string|size:3',
                ];
            default:
                return [
                    'details' => 'required|array',
                ];
        }
    }
}
