<?php

namespace App\Services;

use App\Models\ConsultationDocument;
use App\Models\Prescription;
use App\Models\Consultation;
use App\Models\LetterheadSettings;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class DocumentGenerationService
{
    /**
     * Generate prescription PDF with letterhead.
     */
    public function generatePrescriptionPDF(Prescription $prescription): array
    {
        try {
            // Load prescription with relationships
            $prescription->load(['patient', 'provider', 'clinic', 'items.medication']);

            // Ensure clinic has professional defaults
            $clinic = $this->ensureClinicDefaults($prescription->clinic);

            $data = [
                'prescription' => $prescription,
                'clinic' => $clinic,
                'letterheadSettings' => $clinic->letterheadSettings,
                'document_title' => 'Prescription',
                'document_reference' => $this->generateDocumentReference('RX', $prescription->id)
            ];

            // Generate HTML from template
            $html = view('documents.prescription', $data)->render();

            // Generate PDF
            $pdf = Pdf::loadHTML($html)
                ->setPaper('A4')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                    'defaultFont' => 'Arial'
                ]);

            // Create filename
            $filename = "prescription_{$prescription->id}_" . now()->format('Y_m_d_His') . ".pdf";
            $path = "documents/prescriptions/{$filename}";

            // Save PDF to storage
            Storage::put($path, $pdf->output());

            // Save record in consultation_documents if consultation exists
            $documentRecord = null;
            if ($prescription->consultation_id) {
                $documentRecord = ConsultationDocument::create([
                    'consultation_id' => $prescription->consultation_id,
                    'document_type' => 'prescription',
                    'file_path' => $path,
                    'file_name' => $filename,
                    'original_name' => "Prescription for {$prescription->patient->name}.pdf",
                    'file_size' => strlen($pdf->output()),
                    'mime_type' => 'application/pdf',
                    'description' => "Generated prescription for {$prescription->patient->name}",
                    'uploaded_by' => Auth::id(),
                ]);
            }

            return [
                'success' => true,
                'file_path' => $path,
                'filename' => $filename,
                'download_url' => Storage::url($path),
                'document_record' => $documentRecord,
                'message' => 'Prescription PDF generated successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate prescription PDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate medical letter PDF with letterhead.
     */
    public function generateMedicalLetterPDF(array $letterData): array
    {
        try {
            $documentRef = $letterData['document_reference'] ?? $this->generateDocumentReference('ML');

            // Ensure clinic has professional defaults if provided
            if (isset($letterData['clinic'])) {
                $letterData['clinic'] = $this->ensureClinicDefaults($letterData['clinic']);
            }

            $data = array_merge([
                'document_title' => $letterData['letter_title'] ?? 'Medical Letter',
                'document_reference' => $documentRef,
                'confidentiality_notice' => true,
                'letterheadSettings' => $letterData['clinic']->letterheadSettings ?? null,
            ], $letterData);

            // Generate HTML from template
            $html = view('documents.medical-letter', $data)->render();

            // Generate PDF
            $pdf = Pdf::loadHTML($html)
                ->setPaper('A4')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                    'defaultFont' => 'Arial'
                ]);

            // Create filename
            $filename = "letter_" . now()->format('Y_m_d_His') . ".pdf";
            $path = "documents/letters/{$filename}";

            // Save PDF to storage
            Storage::put($path, $pdf->output());

            // Save record in consultation_documents if consultation_id provided
            $documentRecord = null;
            if (isset($letterData['consultation_id']) && $letterData['consultation_id']) {
                $documentRecord = ConsultationDocument::create([
                    'consultation_id' => $letterData['consultation_id'],
                    'document_type' => 'medical_letter',
                    'file_path' => $path,
                    'file_name' => $filename,
                    'original_name' => ($letterData['letter_title'] ?? 'Medical Letter') . ".pdf",
                    'file_size' => strlen($pdf->output()),
                    'mime_type' => 'application/pdf',
                    'description' => $letterData['letter_title'] ?? 'Medical Letter',
                    'uploaded_by' => Auth::id(),
                ]);
            }

            return [
                'success' => true,
                'file_path' => $path,
                'filename' => $filename,
                'download_url' => Storage::url($path),
                'document_record' => $documentRecord,
                'message' => 'Medical letter PDF generated successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate medical letter PDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate HTML preview for prescription.
     */
    public function generatePrescriptionHTML(Prescription $prescription): string
    {
        // Load prescription with relationships
        $prescription->load(['patient', 'provider', 'clinic', 'items.medication']);

        // Ensure clinic has professional defaults
        $clinic = $this->ensureClinicDefaults($prescription->clinic);

        $data = [
            'prescription' => $prescription,
            'clinic' => $clinic,
            'letterheadSettings' => $clinic->letterheadSettings,
            'document_title' => 'Prescription',
            'document_reference' => $this->generateDocumentReference('RX', $prescription->id)
        ];

        return view('documents.prescription', $data)->render();
    }

    /**
     * Generate HTML preview for medical letter.
     */
    public function generateMedicalLetterHTML(array $letterData): string
    {
        $documentRef = $letterData['document_reference'] ?? $this->generateDocumentReference('ML');

        // Ensure clinic has professional defaults if provided
        if (isset($letterData['clinic'])) {
            $letterData['clinic'] = $this->ensureClinicDefaults($letterData['clinic']);
        }

        $data = array_merge([
            'document_title' => $letterData['letter_title'] ?? 'Medical Letter',
            'document_reference' => $documentRef,
            'confidentiality_notice' => true,
            'letterheadSettings' => $letterData['clinic']->letterheadSettings ?? null,
        ], $letterData);

        return view('documents.medical-letter', $data)->render();
    }

    /**
     * Generate document reference number.
     */
    public function generateDocumentReference(string $prefix, int $id = null): string
    {
        $id = $id ?? rand(100000, 999999);
        return $prefix . '-' . str_pad($id, 6, '0', STR_PAD_LEFT) . '-' . now()->format('Ymd');
    }

    /**
     * Get document download response.
     */
    public function getDocumentDownload(string $path)
    {
        if (!Storage::exists($path)) {
            throw new \Exception('Document not found');
        }

        return Storage::download($path);
    }

    /**
     * Delete document and its record.
     */
    public function deleteDocument(ConsultationDocument $document): bool
    {
        try {
            // Delete file from storage
            if (Storage::exists($document->file_path)) {
                Storage::delete($document->file_path);
            }

            // Delete database record
            $document->delete();

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get document statistics for a clinic.
     */
    public function getDocumentStats(int $clinicId): array
    {
        $stats = ConsultationDocument::whereHas('consultation', function ($query) use ($clinicId) {
            $query->where('clinic_id', $clinicId);
        })
        ->selectRaw('document_type, COUNT(*) as count')
        ->groupBy('document_type')
        ->pluck('count', 'document_type')
        ->toArray();

        return [
            'total_documents' => array_sum($stats),
            'prescriptions' => $stats['prescription'] ?? 0,
            'medical_letters' => $stats['medical_letter'] ?? 0,
            'other_documents' => array_sum(array_filter($stats, function($key) {
                return !in_array($key, ['prescription', 'medical_letter']);
            }, ARRAY_FILTER_USE_KEY)),
        ];
    }

    /**
     * Ensure clinic has minimum required information for professional letterhead.
     */
    private function ensureClinicDefaults($clinic): object
    {
        // Create a copy of the clinic object to avoid modifying the original
        $clinicData = clone $clinic;

        // Load letterhead settings for the clinic
        $letterheadSettings = LetterheadSettings::where('clinic_id', $clinic->id)
            ->where('is_active', true)
            ->first();

        // Attach letterhead settings to clinic data
        $clinicData->letterheadSettings = $letterheadSettings;

        // Ensure clinic has a name
        if (empty($clinicData->name)) {
            $clinicData->name = 'Medical Clinic';
        }

        // Ensure clinic has basic contact info for professional appearance
        if (empty($clinicData->address) && empty($clinicData->city)) {
            $clinicData->address = 'Medical Center';
            $clinicData->city = 'Healthcare City';
        }

        return $clinicData;
    }

    /**
     * Generate a professional document with enhanced clinic data.
     */
    public function generatePrescriptionPDFWithDefaults(Prescription $prescription): array
    {
        try {
            // Load prescription with relationships
            $prescription->load(['patient', 'provider', 'clinic', 'items.medication']);

            // Ensure clinic has professional defaults
            $clinic = $this->ensureClinicDefaults($prescription->clinic);

            $data = [
                'prescription' => $prescription,
                'clinic' => $clinic,
                'document_title' => 'Prescription',
                'document_reference' => $this->generateDocumentReference('RX', $prescription->id)
            ];

            return $this->generatePrescriptionPDF($prescription);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate prescription PDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate consultation summary PDF with all details.
     */
    public function generateConsultationSummaryPDF($consultation, array $includeItems = []): array
    {
        try {
            // Load consultation with all relationships
            $consultation->load([
                'patient.user',
                'provider.user',
                'clinic',
                'prescriptions.items.medication',
                'diagnoses',
                'notes',
                'documents',
                'treatmentPlans',
                'vitals',
                'tabs'
            ]);

            // Ensure clinic has professional defaults
            $clinic = $this->ensureClinicDefaults($consultation->clinic);

            // Prepare consultation data
            $data = [
                'consultation' => $consultation,
                'clinic' => $clinic,
                'letterheadSettings' => $clinic->letterheadSettings,
                'includeItems' => $includeItems,
                'document_title' => 'Consultation Summary',
                'document_reference' => $this->generateDocumentReference('CS', $consultation->id),
                'generated_at' => now(),
                'patient' => $consultation->patient,
                'provider' => $consultation->provider,
                'prescriptions' => $consultation->prescriptions,
                'diagnoses' => $consultation->diagnoses,
                'notes' => $consultation->notes,
                'treatmentPlans' => $consultation->treatmentPlans,
                'vitals' => $consultation->vitals,
                'tabs' => $consultation->tabs
            ];

            // Generate HTML from template
            $html = view('documents.consultation-summary', $data)->render();

            // Generate PDF
            $pdf = Pdf::loadHTML($html)
                ->setPaper('A4')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                    'defaultFont' => 'Arial'
                ]);

            // Create filename
            $filename = "consultation_summary_{$consultation->id}_" . now()->format('Y_m_d_His') . ".pdf";
            $path = "documents/consultation-summaries/{$filename}";

            // Save PDF to storage
            Storage::put($path, $pdf->output());

            return [
                'success' => true,
                'file_path' => Storage::path($path),
                'public_url' => Storage::url($path),
                'filename' => $filename,
                'message' => 'Consultation summary PDF generated successfully'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate consultation summary PDF: ' . $e->getMessage()
            ];
        }
    }
}
