<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescription - {{ $prescription->id }}</title>
    <style>
        /* Modern CSS3 Styling for PDF */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Styling */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #0d9488;
        }

        .clinic-info {
            flex: 1;
        }

        .clinic-name {
            font-size: 24px;
            font-weight: 700;
            color: #0d9488;
            margin-bottom: 5px;
        }

        .clinic-details {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
        }

        .prescription-meta {
            text-align: right;
            font-size: 11px;
        }

        .prescription-id {
            font-size: 14px;
            font-weight: 600;
            color: #0d9488;
            margin-bottom: 5px;
        }

        /* Patient & Provider Info */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            gap: 30px;
        }

        .patient-info, .provider-info {
            flex: 1;
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #0d9488;
        }

        .info-title {
            font-size: 14px;
            font-weight: 600;
            color: #0d9488;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-item {
            margin-bottom: 5px;
            font-size: 11px;
        }

        .info-label {
            font-weight: 600;
            color: #374151;
            display: inline-block;
            width: 80px;
        }

        /* Prescription Items */
        .medications-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e5e7eb;
        }

        .medication-item {
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .medication-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .medication-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 11px;
        }

        .detail-item {
            display: flex;
            align-items: center;
        }

        .detail-label {
            font-weight: 600;
            color: #6b7280;
            margin-right: 8px;
            min-width: 70px;
        }

        .detail-value {
            color: #374151;
        }

        .instructions {
            margin-top: 10px;
            padding: 10px;
            background: #fef3c7;
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
        }

        .instructions-label {
            font-weight: 600;
            color: #92400e;
            font-size: 10px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .instructions-text {
            color: #92400e;
            font-size: 11px;
        }

        /* Notes Section */
        .notes-section {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .notes-title {
            font-size: 12px;
            font-weight: 600;
            color: #0369a1;
            margin-bottom: 8px;
        }

        .notes-content {
            font-size: 11px;
            color: #1e40af;
            line-height: 1.5;
        }

        /* Footer */
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }

        .signature-section {
            text-align: center;
        }

        .signature-line {
            width: 200px;
            border-bottom: 1px solid #6b7280;
            margin-bottom: 5px;
            height: 40px;
        }

        .signature-label {
            font-size: 10px;
            color: #6b7280;
            font-weight: 600;
        }

        .provider-credentials {
            font-size: 11px;
            color: #374151;
            margin-top: 5px;
        }

        .prescription-footer {
            font-size: 10px;
            color: #6b7280;
            text-align: right;
        }

        /* FHIR Compliance Badge */
        .fhir-badge {
            display: inline-block;
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Print Optimizations */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .container {
                padding: 0;
            }
            
            .medication-item {
                break-inside: avoid;
            }
        }

        /* Responsive Design */
        @media (max-width: 600px) {
            .info-section {
                flex-direction: column;
                gap: 15px;
            }
            
            .medication-details {
                grid-template-columns: 1fr;
            }
            
            .footer {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="clinic-info">
                <div class="clinic-name">{{ $clinic->name ?? 'Medical Clinic' }}</div>
                <div class="clinic-details">
                    @if($clinic)
                        {{ $clinic->address }}<br>
                        {{ $clinic->city }}, {{ $clinic->postal_code }}<br>
                        Phone: {{ $clinic->phone }} | Email: {{ $clinic->email }}
                    @endif
                </div>
            </div>
            <div class="prescription-meta">
                <div class="prescription-id">Prescription #{{ $prescription->id }}</div>
                <div>Date: {{ $prescription->created_at->format('d/m/Y') }}</div>
                <div>Time: {{ $prescription->created_at->format('H:i') }}</div>
                <div class="fhir-badge">FHIR R4 Compliant</div>
            </div>
        </div>

        <!-- Patient & Provider Information -->
        <div class="info-section">
            <div class="patient-info">
                <div class="info-title">Patient Information</div>
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    {{ $patient->user->name }}
                </div>
                <div class="info-item">
                    <span class="info-label">DOB:</span>
                    {{ $patient->date_of_birth ? \Carbon\Carbon::parse($patient->date_of_birth)->format('d/m/Y') : 'Not provided' }}
                </div>
                <div class="info-item">
                    <span class="info-label">Gender:</span>
                    {{ ucfirst($patient->gender ?? 'Not specified') }}
                </div>
                <div class="info-item">
                    <span class="info-label">Phone:</span>
                    {{ $patient->phone ?? 'Not provided' }}
                </div>
                <div class="info-item">
                    <span class="info-label">Patient ID:</span>
                    #{{ $patient->id }}
                </div>
            </div>

            <div class="provider-info">
                <div class="info-title">Prescriber Information</div>
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    {{ $provider->user->name ?? 'Unknown Provider' }}
                </div>
                <div class="info-item">
                    <span class="info-label">Title:</span>
                    {{ $provider->title ?? 'Medical Practitioner' }}
                </div>
                <div class="info-item">
                    <span class="info-label">License:</span>
                    {{ $provider->license_number ?? 'Not provided' }}
                </div>
                <div class="info-item">
                    <span class="info-label">Contact:</span>
                    {{ $provider->user->email ?? 'Not provided' }}
                </div>
                @if($consultation)
                <div class="info-item">
                    <span class="info-label">Consultation:</span>
                    {{ $consultation->consultation_date->format('d/m/Y') }}
                </div>
                @endif
            </div>
        </div>

        <!-- Medications -->
        <div class="medications-section">
            <div class="section-title">Prescribed Medications</div>
            
            @forelse($items as $item)
            <div class="medication-item">
                <div class="medication-name">
                    {{ $item->medication->name ?? $item->medication_name }}
                    @if($item->medication->strength)
                        - {{ $item->medication->strength }}
                    @endif
                </div>
                
                <div class="medication-details">
                    <div class="detail-item">
                        <span class="detail-label">Dosage:</span>
                        <span class="detail-value">{{ $item->dosage }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Frequency:</span>
                        <span class="detail-value">{{ $item->frequency }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value">{{ $item->duration }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Quantity:</span>
                        <span class="detail-value">{{ $item->quantity }}</span>
                    </div>
                </div>

                @if($item->instructions)
                <div class="instructions">
                    <div class="instructions-label">Special Instructions</div>
                    <div class="instructions-text">{{ $item->instructions }}</div>
                </div>
                @endif
            </div>
            @empty
            <div class="medication-item">
                <div class="medication-name">No medications prescribed</div>
            </div>
            @endforelse
        </div>

        <!-- Notes -->
        @if($prescription->notes)
        <div class="notes-section">
            <div class="notes-title">Additional Notes</div>
            <div class="notes-content">{{ $prescription->notes }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div class="signature-section">
                <div class="signature-line"></div>
                <div class="signature-label">Doctor's Signature</div>
                <div class="provider-credentials">
                    {{ $provider->user->name ?? 'Unknown Provider' }}<br>
                    {{ $provider->title ?? 'Medical Practitioner' }}
                </div>
            </div>
            
            <div class="prescription-footer">
                <div>Generated: {{ $generated_at->format('d/m/Y H:i') }}</div>
                <div>This prescription is FHIR R4 compliant</div>
                <div>Medroid Healthcare Platform</div>
            </div>
        </div>
    </div>
</body>
</html>
