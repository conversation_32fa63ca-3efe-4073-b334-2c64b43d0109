<template>
    <div class="stripe-checkout">
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Stripe Payment</h3>
                <img src="/images/stripe-logo.png" alt="Stripe" class="h-6" />
            </div>

            <!-- Amount Display -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Amount to pay:</span>
                    <span class="text-xl font-bold text-gray-900">{{ formatAmount(amount) }}</span>
                </div>
                <p class="text-sm text-gray-500 mt-1">{{ description }}</p>
            </div>

            <!-- Payment Methods -->
            <div class="space-y-4 mb-6">
                <h4 class="font-medium text-gray-900">Available Payment Methods</h4>
                <div class="grid grid-cols-1 gap-3">
                    <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">Credit/Debit Card</div>
                            <div class="text-sm text-gray-600">Visa, Mastercard, American Express</div>
                        </div>
                    </div>

                    <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">Bank Transfer</div>
                            <div class="text-sm text-gray-600">Direct bank transfers</div>
                        </div>
                    </div>

                    <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">Digital Wallets</div>
                            <div class="text-sm text-gray-600">Apple Pay, Google Pay</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h5 class="font-medium text-blue-900 mb-2">Stripe Features</h5>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Instant processing
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Secure encryption
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        International cards accepted
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Instant refunds available
                    </li>
                </ul>
            </div>

            <!-- Pay Button -->
            <button
                @click="initiatePayment"
                :disabled="isLoading"
                class="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center"
            >
                <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span v-if="isLoading">Processing...</span>
                <span v-else>Pay {{ formatAmount(amount) }} with Stripe</span>
            </button>

            <!-- Security Notice -->
            <div class="mt-4 text-center">
                <p class="text-xs text-gray-500">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Your payment information is secure and encrypted
                </p>
            </div>

            <!-- Error Display -->
            <div v-if="error" class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm text-red-800">{{ error }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useCountryManager } from '@/composables/useCountryManager'

// Props
const props = defineProps({
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'GBP'
    },
    description: {
        type: String,
        default: 'Payment'
    }
})

// Emits
const emit = defineEmits(['success', 'error', 'dismiss'])

// Country manager
const { formatAmount } = useCountryManager()

// State
const isLoading = ref(false)
const error = ref(null)

// Methods
const initiatePayment = async () => {
    isLoading.value = true
    error.value = null

    try {
        // Simulate payment processing
        await new Promise(resolve => setTimeout(resolve, 2000))

        // For demo purposes, simulate success
        const result = {
            payment_id: 'stripe_' + Date.now(),
            amount: props.amount,
            currency: props.currency,
            status: 'succeeded',
            gateway: 'stripe'
        }

        emit('success', result)
    } catch (err) {
        error.value = err.message || 'Payment failed. Please try again.'
        emit('error', err)
    } finally {
        isLoading.value = false
    }
}
</script>

<style scoped>
.stripe-checkout {
    max-width: 500px;
}
</style>
