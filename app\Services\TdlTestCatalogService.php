<?php

namespace App\Services;

use App\Repositories\Interfaces\LabTestCatalogRepositoryInterface;
use App\Models\LabTestCatalog;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class TdlTestCatalogService
{
    public function __construct(
        private LabTestCatalogRepositoryInterface $testCatalogRepository
    ) {}

    /**
     * Get paginated test catalog
     */
    public function getPaginatedTests(int $perPage = 20): LengthAwarePaginator
    {
        return $this->testCatalogRepository->getAll($perPage);
    }

    /**
     * Get all active tests
     */
    public function getAllActiveTests(): Collection
    {
        return $this->testCatalogRepository->getActive();
    }

    /**
     * Search tests by query
     */
    public function searchTests(string $query, int $perPage = 20): LengthAwarePaginator
    {
        return $this->testCatalogRepository->search($query, $perPage);
    }

    /**
     * Get tests by category
     */
    public function getTestsByCategory(string $category): Collection
    {
        return $this->testCatalogRepository->getByCategory($category);
    }

    /**
     * Get all available categories
     */
    public function getCategories(): array
    {
        return $this->testCatalogRepository->getCategories();
    }

    /**
     * Get test by ID
     */
    public function getTestById(int $id): ?LabTestCatalog
    {
        return $this->testCatalogRepository->findById($id);
    }

    /**
     * Get test by code
     */
    public function getTestByCode(string $testCode): ?LabTestCatalog
    {
        return $this->testCatalogRepository->findByCode($testCode);
    }

    /**
     * Get multiple tests by codes
     */
    public function getTestsByCodes(array $testCodes): Collection
    {
        return $this->testCatalogRepository->findByCodes($testCodes);
    }

    /**
     * Validate test selection
     */
    public function validateTestSelection(array $testCodes): array
    {
        $validTests = $this->getTestsByCodes($testCodes);
        $validCodes = $validTests->pluck('test_code')->toArray();
        $invalidCodes = array_diff($testCodes, $validCodes);

        return [
            'valid_tests' => $validTests,
            'invalid_codes' => $invalidCodes,
            'is_valid' => empty($invalidCodes)
        ];
    }

    /**
     * Calculate total cost for selected tests
     */
    public function calculateTotalCost(array $testCodes): float
    {
        $tests = $this->getTestsByCodes($testCodes);
        return $tests->sum('price');
    }

    /**
     * Get test requirements summary
     */
    public function getTestRequirements(array $testCodes): array
    {
        $tests = $this->getTestsByCodes($testCodes);
        $requirements = [
            'fasting_required' => false,
            'max_fasting_hours' => 0,
            'special_instructions' => [],
            'sample_types' => [],
            'max_turnaround_time' => '24 hours'
        ];

        foreach ($tests as $test) {
            if ($test->requiresFasting()) {
                $requirements['fasting_required'] = true;
                $requirements['max_fasting_hours'] = max(
                    $requirements['max_fasting_hours'], 
                    $test->getFastingHours() ?? 0
                );
            }

            if ($test->getSpecialInstructions()) {
                $requirements['special_instructions'][] = $test->getSpecialInstructions();
            }

            if ($test->getSampleType()) {
                $requirements['sample_types'][] = $test->getSampleType();
            }
        }

        $requirements['special_instructions'] = array_unique($requirements['special_instructions']);
        $requirements['sample_types'] = array_unique($requirements['sample_types']);

        return $requirements;
    }

    /**
     * Format tests for request
     */
    public function formatTestsForRequest(array $testCodes): array
    {
        $tests = $this->getTestsByCodes($testCodes);
        
        return $tests->map(function ($test) {
            return [
                'test_code' => $test->test_code,
                'test_name' => $test->test_name,
                'category' => $test->category,
                'price' => $test->price,
                'requirements' => $test->requirements
            ];
        })->toArray();
    }

    /**
     * Create new test in catalog
     */
    public function createTest(array $data): LabTestCatalog
    {
        return $this->testCatalogRepository->create($data);
    }

    /**
     * Update existing test
     */
    public function updateTest(int $id, array $data): LabTestCatalog
    {
        $test = $this->getTestById($id);
        
        if (!$test) {
            throw new \Exception('Test not found');
        }

        return $this->testCatalogRepository->update($test, $data);
    }

    /**
     * Delete test from catalog
     */
    public function deleteTest(int $id): bool
    {
        $test = $this->getTestById($id);
        
        if (!$test) {
            throw new \Exception('Test not found');
        }

        return $this->testCatalogRepository->delete($test);
    }

    /**
     * Get catalog statistics
     */
    public function getCatalogStats(): array
    {
        $allTests = $this->getAllActiveTests();
        
        return [
            'total_tests' => $allTests->count(),
            'categories' => $this->getCategories(),
            'category_counts' => $allTests->groupBy('category')->map->count(),
            'price_range' => [
                'min' => $allTests->min('price'),
                'max' => $allTests->max('price'),
                'average' => $allTests->avg('price')
            ]
        ];
    }
}
