<?php

namespace App\Services;

use App\Models\Bill;
use App\Models\BillItem;
use App\Repositories\Interfaces\BillItemRepositoryInterface;

class BillItemService
{
    public function __construct(
        private BillItemRepositoryInterface $billItemRepository,
        private BillService $billService
    ) {}

    public function createBillItem(Bill $bill, array $data, int $userClinicId): BillItem
    {
        if ($bill->clinic_id !== $userClinicId) {
            throw new \Exception('Unauthorized access to bill', 403);
        }

        $billItem = $this->billItemRepository->create([
            'bill_id' => $bill->id,
            'service_id' => $data['service_id'] ?? null,
            'item_name' => $data['item_name'],
            'description' => $data['description'] ?? null,
            'unit_price' => $data['unit_price'],
            'quantity' => $data['quantity'],
        ]);

        return $this->billItemRepository->findById($billItem->id);
    }

    public function updateBillItem(Bill $bill, BillItem $billItem, array $data, int $userClinicId): BillItem
    {
        if ($bill->clinic_id !== $userClinicId || $billItem->bill_id !== $bill->id) {
            throw new \Exception('Unauthorized access to bill item', 403);
        }

        $updatedBillItem = $this->billItemRepository->update($billItem, $data);

        return $this->billItemRepository->findById($updatedBillItem->id);
    }

    public function deleteBillItem(Bill $bill, BillItem $billItem, int $userClinicId): bool
    {
        if ($bill->clinic_id !== $userClinicId || $billItem->bill_id !== $bill->id) {
            throw new \Exception('Unauthorized access to bill item', 403);
        }

        return $this->billItemRepository->delete($billItem);
    }
}
