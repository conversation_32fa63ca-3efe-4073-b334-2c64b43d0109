<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultation_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->nullable()->constrained()->onDelete('set null');
            $table->string('service_name'); // Store service name in case service is deleted
            $table->text('description')->nullable();
            $table->decimal('unit_price', 10, 2); // Price at time of consultation
            $table->integer('quantity')->default(1);
            $table->decimal('total_price', 10, 2); // unit_price * quantity
            $table->boolean('is_billable')->default(true); // Whether this should be included in billing
            $table->boolean('is_billed')->default(false); // Whether this has been added to a bill
            $table->foreignId('bill_id')->nullable()->constrained()->onDelete('set null'); // Reference to bill if billed
            $table->json('metadata')->nullable(); // Additional service-specific data
            $table->timestamps();

            $table->index(['consultation_id', 'is_billable']);
            $table->index(['consultation_id', 'is_billed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultation_services');
    }
};
