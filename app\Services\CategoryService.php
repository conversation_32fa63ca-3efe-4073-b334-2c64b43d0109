<?php

namespace App\Services;

use App\Models\Category;
use App\Repositories\CategoryRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryService
{
    protected CategoryRepository $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Create a new category with business logic
     */
    public function createCategory(array $data): Category
    {
        // Business logic: Set defaults
        $data['is_active'] = $data['is_active'] ?? true;

        return $this->categoryRepository->create($data);
    }

    /**
     * Update category with business logic
     */
    public function updateCategory(int $categoryId, array $data, ?int $clinicId = null): ?Category
    {
        // Verify category belongs to clinic if clinic_id provided
        if ($clinicId) {
            $category = $this->categoryRepository->find($categoryId);
            if (!$category || $category->clinic_id !== $clinicId) {
                return null;
            }
        }

        return $this->categoryRepository->update($categoryId, $data);
    }

    /**
     * Get category details
     */
    public function getCategoryDetails(int $categoryId, ?int $clinicId = null): ?array
    {
        $category = $this->categoryRepository->find($categoryId);

        if (!$category) {
            return null;
        }

        // Verify category belongs to clinic if clinic_id provided
        if ($clinicId && $category->clinic_id !== $clinicId) {
            return null;
        }

        // Load relationships
        $category->load(['clinic', 'services']);

        return [
            'category' => $category,
            'clinic' => $category->clinic ? $category->clinic->only(['id', 'name']) : null
        ];
    }

    /**
     * Get categories for a specific clinic
     */
    public function getCategoriesForClinic(array $filters): array
    {
        $clinicId = $filters['clinic_id'];
        $activeOnly = $filters['active_only'] ?? false;

        // Get clinic
        $clinic = \App\Models\Clinic::findOrFail($clinicId);

        // Get categories
        $categories = $this->categoryRepository->findByClinic($clinicId, $activeOnly);

        return [
            'categories' => $categories,
            'clinic' => $clinic->only(['id', 'name'])
        ];
    }

    /**
     * Delete category with business logic
     */
    public function deleteCategory(int $categoryId, ?int $clinicId = null): array
    {
        $category = $this->categoryRepository->find($categoryId);

        if (!$category) {
            return ['success' => false, 'reason' => 'not_found'];
        }

        // Verify category belongs to clinic if clinic_id provided
        if ($clinicId && $category->clinic_id !== $clinicId) {
            return ['success' => false, 'reason' => 'not_found'];
        }

        // Check if category has services
        if ($category->services()->count() > 0) {
            return ['success' => false, 'reason' => 'has_services'];
        }

        $deleted = $this->categoryRepository->delete($categoryId);

        return ['success' => $deleted];
    }

    /**
     * Get active categories for dropdown
     */
    public function getActiveCategoriesForDropdown(int $clinicId): Collection
    {
        return $this->categoryRepository->findActiveForDropdown($clinicId);
    }

    /**
     * Get categories by clinic
     */
    public function getCategoriesByClinic(int $clinicId): Collection
    {
        return $this->categoryRepository->findByClinic($clinicId);
    }

    /**
     * Get active categories
     */
    public function getActiveCategories(?int $clinicId = null): Collection
    {
        return $this->categoryRepository->findActive($clinicId);
    }

    /**
     * Get categories with service count
     */
    public function getCategoriesWithServiceCount(?int $clinicId = null): Collection
    {
        return $this->categoryRepository->findWithServiceCount($clinicId);
    }

    /**
     * Search categories
     */
    public function searchCategories(string $search, ?int $clinicId = null): Collection
    {
        return $this->categoryRepository->search($search, $clinicId);
    }

    /**
     * Get categories with filters and pagination
     */
    public function getCategoriesWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->categoryRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Activate category
     */
    public function activateCategory(int $categoryId): bool
    {
        $category = $this->categoryRepository->update($categoryId, ['is_active' => true]);
        return $category !== null;
    }

    /**
     * Deactivate category
     */
    public function deactivateCategory(int $categoryId): bool
    {
        $category = $this->categoryRepository->update($categoryId, ['is_active' => false]);
        return $category !== null;
    }



    /**
     * Get category statistics
     */
    public function getCategoryStatistics(int $clinicId): array
    {
        return $this->categoryRepository->getStatistics($clinicId);
    }

    /**
     * Bulk update categories
     */
    public function bulkUpdateCategories(array $categoryIds, array $data): int
    {
        $updated = 0;
        
        foreach ($categoryIds as $categoryId) {
            if ($this->categoryRepository->update($categoryId, $data)) {
                $updated++;
            }
        }
        
        return $updated;
    }

    /**
     * Get categories for dropdown/select
     */
    public function getCategoriesForSelect(?int $clinicId = null): array
    {
        $categories = $this->getActiveCategories($clinicId);
        
        return $categories->pluck('name', 'id')->toArray();
    }
}
