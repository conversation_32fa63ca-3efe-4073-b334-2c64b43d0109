<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Payment Required</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .bill-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .amount { font-size: 24px; font-weight: bold; color: #3B82F6; text-align: center; margin: 20px 0; }
        .payment-button { 
            display: inline-block; 
            background: #10B981; 
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 5px; 
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .footer { text-align: center; padding: 20px; color: #666; }
        .due-date { background: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payment Required</h1>
            <p>{{ $clinicName }}</p>
        </div>
        
        <div class="content">
            <h2>Dear {{ $patientName }},</h2>
            
            <p>We hope this message finds you well. This is a friendly reminder that you have an outstanding bill that requires payment.</p>
            
            <div class="bill-details">
                <h3>Bill Details:</h3>
                <p><strong>Bill Number:</strong> {{ $bill->bill_number }}</p>
                <p><strong>Date:</strong> {{ $bill->created_at->format('F j, Y') }}</p>
                @if($dueDate)
                <p><strong>Due Date:</strong> {{ $dueDate->format('F j, Y') }}</p>
                @endif
            </div>
            
            <div class="amount">
                Total Amount Due: £{{ number_format($totalAmount, 2) }}
            </div>
            
            @if($dueDate && $dueDate->isPast())
            <div class="due-date">
                <strong>⚠️ This bill is overdue.</strong> Please make payment as soon as possible to avoid any late fees.
            </div>
            @elseif($dueDate)
            <div class="due-date">
                <strong>📅 Payment Due:</strong> {{ $dueDate->format('F j, Y') }}
            </div>
            @endif
            
            <div style="text-align: center;">
                <a href="{{ $paymentLink }}" class="payment-button">Pay Now</a>
            </div>
            
            <p>You can make your payment securely online by clicking the "Pay Now" button above. If you have any questions about this bill or need to discuss payment options, please don't hesitate to contact us.</p>
            
            <p>Thank you for choosing {{ $clinicName }} for your healthcare needs.</p>
        </div>
        
        <div class="footer">
            <p>{{ $clinicName }}</p>
            <p>If you have already made this payment, please disregard this notice.</p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
