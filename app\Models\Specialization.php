<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Specialization extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'label',
        'description',
        'specialty_id',
        'is_active',
        'wp_specialization_id',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'wp_specialization_id' => 'integer',
        'specialty_id' => 'integer',
    ];

    /**
     * Get the specialties for this specialization.
     */
    public function specialties()
    {
        return $this->hasMany(Specialty::class);
    }

    /**
     * Get the specialty that owns this specialization.
     */
    public function specialty()
    {
        return $this->belongsTo(Specialty::class);
    }

    /**
     * Get the providers for this specialization.
     */
    public function providers()
    {
        return $this->hasMany(Provider::class, 'specialization', 'name');
    }

    /**
     * Scope to get only active specializations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get specializations ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
