<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_tdl_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained('clinics')->onDelete('cascade');
            $table->text('azure_connection_string')->nullable();
            $table->string('tdl_account_id')->nullable();
            $table->boolean('is_active')->default(false);
            $table->json('settings')->nullable(); // Additional configuration options
            $table->timestamps();

            $table->unique('clinic_id');
            $table->index(['clinic_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_tdl_settings');
    }
};
