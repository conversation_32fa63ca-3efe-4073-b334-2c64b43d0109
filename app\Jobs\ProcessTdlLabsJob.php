<?php

namespace App\Jobs;

use App\Services\TdlLabService;
use App\Repositories\Interfaces\ClinicTdlSettingRepositoryInterface;
use App\Repositories\Interfaces\LabTestRequestRepositoryInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessTdlLabsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(
        TdlLabService $labService,
        ClinicTdlSettingRepositoryInterface $settingsRepository,
        LabTestRequestRepositoryInterface $requestRepository
    ): void {
        Log::info('TDL Labs processing job started');

        try {
            // Get all active TDL settings
            $activeSettings = $settingsRepository->getActiveSettings();
            
            if ($activeSettings->isEmpty()) {
                Log::info('No active TDL settings found, skipping processing');
                return;
            }

            $totalProcessed = 0;
            $totalErrors = 0;

            foreach ($activeSettings as $setting) {
                try {
                    Log::info("Processing TDL for clinic {$setting->clinic_id}");
                    
                    // Process pending requests
                    $sentRequests = $this->processPendingRequests($labService, $requestRepository, $setting->clinic_id);
                    
                    // Process incoming results
                    $processedResults = $this->processIncomingResults($labService, $setting->clinic_id);
                    
                    $totalProcessed += $sentRequests + count($processedResults);
                    
                    Log::info("Clinic {$setting->clinic_id}: Sent {$sentRequests} requests, processed " . count($processedResults) . " results");
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    Log::error("Error processing TDL for clinic {$setting->clinic_id}: " . $e->getMessage());
                    continue;
                }
            }

            Log::info("TDL Labs processing completed. Total processed: {$totalProcessed}, Errors: {$totalErrors}");

        } catch (Exception $e) {
            Log::error('TDL Labs processing job failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process pending lab requests
     */
    private function processPendingRequests(
        TdlLabService $labService,
        LabTestRequestRepositoryInterface $requestRepository,
        int $clinicId
    ): int {
        try {
            $pendingRequests = $requestRepository->getAllByClinic($clinicId)
                ->where('status', 'pending');

            $sentCount = 0;

            foreach ($pendingRequests as $request) {
                try {
                    $success = $labService->sendRequestToTDL($request->id);
                    if ($success) {
                        $sentCount++;
                        Log::info("Sent lab request {$request->order_number} to TDL");
                    }
                } catch (Exception $e) {
                    Log::error("Failed to send lab request {$request->order_number}: " . $e->getMessage());
                    continue;
                }
            }

            return $sentCount;

        } catch (Exception $e) {
            Log::error("Error processing pending requests for clinic {$clinicId}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Process incoming lab results
     */
    private function processIncomingResults(TdlLabService $labService, int $clinicId): array
    {
        try {
            $processedResults = $labService->processResults($clinicId);
            
            foreach ($processedResults as $result) {
                Log::info("Processed lab result {$result->lab_reference_id} for clinic {$clinicId}");
            }

            return $processedResults;

        } catch (Exception $e) {
            Log::error("Error processing results for clinic {$clinicId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Handle job failure
     */
    public function failed(Exception $exception): void
    {
        Log::error('TDL Labs processing job failed permanently: ' . $exception->getMessage());
        
        // Could send notification to administrators here
        // NotificationService::notifyAdmins('TDL Labs processing failed', $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['tdl-labs', 'background-processing'];
    }
}
