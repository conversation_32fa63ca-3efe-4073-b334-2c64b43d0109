<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TreatmentPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'plan_type',
        'title',
        'description',
        'instructions',
        'start_date',
        'end_date',
        'priority',
        'status',
        'outcome',
        'review_date',
        'goals',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'review_date' => 'date',
        'goals' => 'array',
    ];

    /**
     * Get the consultation that owns the treatment plan.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Scope to filter by plan type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('plan_type', $type);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get planned plans.
     */
    public function scopePlanned($query)
    {
        return $query->where('status', 'planned');
    }

    /**
     * Scope to get completed plans.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get plans due for review.
     */
    public function scopeDueForReview($query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        return $query->where('review_date', '<=', $date)
                    ->whereIn('status', ['planned', 'active']);
    }

    /**
     * Check if plan is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if plan is completed.
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if plan is overdue.
     */
    public function isOverdue()
    {
        return $this->end_date && $this->end_date->isPast() && !$this->isCompleted();
    }

    /**
     * Check if plan is due for review.
     */
    public function isDueForReview()
    {
        return $this->review_date && $this->review_date->isPast() && !$this->isCompleted();
    }

    /**
     * Get the plan type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'treatment' => 'Treatment',
            'investigation' => 'Investigation',
            'referral' => 'Referral',
            'lifestyle' => 'Lifestyle',
        ];

        return $types[$this->plan_type] ?? ucfirst($this->plan_type);
    }

    /**
     * Get the priority display name.
     */
    public function getPriorityDisplayAttribute()
    {
        $priorities = [
            'low' => 'Low Priority',
            'medium' => 'Medium Priority',
            'high' => 'High Priority',
            'urgent' => 'Urgent',
        ];

        return $priorities[$this->priority] ?? ucfirst($this->priority);
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'planned' => 'Planned',
            'active' => 'Active',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'on_hold' => 'On Hold',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the priority color for UI display.
     */
    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
        ];

        return $colors[$this->priority] ?? 'gray';
    }

    /**
     * Get the status color for UI display.
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'planned' => 'blue',
            'active' => 'green',
            'completed' => 'gray',
            'cancelled' => 'red',
            'on_hold' => 'yellow',
        ];

        return $colors[$this->status] ?? 'gray';
    }
}
