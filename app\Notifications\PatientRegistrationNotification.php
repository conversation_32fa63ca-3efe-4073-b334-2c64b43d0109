<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Password;
use App\Models\User;

class PatientRegistrationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $createdBy;
    protected $temporaryPassword;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, $createdBy = null, $temporaryPassword = null)
    {
        $this->user = $user;
        $this->createdBy = $createdBy;
        $this->temporaryPassword = $temporaryPassword;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        // Generate password reset token
        $token = Password::createToken($this->user);
        $resetUrl = url(route('password.reset', [
            'token' => $token,
            'email' => $this->user->email,
        ]));

        $creatorName = $this->createdBy ? $this->createdBy->name : 'Healthcare Administrator';
        $clinicName = 'Medroid Healthcare'; // Default clinic name

        return (new MailMessage)
            ->subject('Welcome to ' . $clinicName . ' - Complete Your Registration')
            ->greeting('Welcome to ' . $clinicName . '!')
            ->line('Your patient account has been created by ' . $creatorName . '.')
            ->line('To complete your registration and access your account, please set up your password by clicking the button below.')
            ->action('Set Up Your Password', $resetUrl)
            ->line('**Account Details:**')
            ->line('Name: ' . $this->user->name)
            ->line('Email: ' . $this->user->email)
            ->line('Phone: ' . ($this->user->phone_number ?? 'Not provided'))
            ->line('')
            ->line('Once you set up your password, you will be able to:')
            ->line('• View and manage your appointments')
            ->line('• Access your medical records')
            ->line('• Communicate with your healthcare providers')
            ->line('• Update your personal information')
            ->line('')
            ->line('If you did not expect this account to be created, please contact ' . $clinicName . ' immediately.')
            ->line('')
            ->line('This password setup link will expire in 60 minutes for security reasons.')
            ->salutation('Best regards,')
            ->salutation($clinicName . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'created_by' => $this->createdBy ? $this->createdBy->id : null,
            'message' => 'Patient account created. Password setup email sent.',
        ];
    }
}