import { ref, computed } from 'vue'

// Global progress state
const activeOperations = ref(new Map())
const globalProgress = ref({
  show: false,
  title: '',
  message: '',
  progress: -1,
  steps: [],
  currentStep: 0,
  canCancel: false,
  cancelCallback: null
})

export function useProgress() {
  
  /**
   * Start a new progress operation
   */
  const startProgress = (options = {}) => {
    const operationId = options.id || Date.now().toString()
    
    const operation = {
      id: operationId,
      title: options.title || 'Processing...',
      message: options.message || 'Please wait...',
      progress: options.progress || -1,
      steps: options.steps || [],
      currentStep: 0,
      canCancel: options.canCancel || false,
      cancelCallback: options.onCancel || null,
      startTime: Date.now(),
      estimatedDuration: options.estimatedDuration || null
    }
    
    activeOperations.value.set(operationId, operation)
    
    // Show global progress if this is the first operation
    if (activeOperations.value.size === 1) {
      updateGlobalProgress(operation)
    }
    
    return operationId
  }

  /**
   * Update progress for an operation
   */
  const updateProgress = (operationId, updates = {}) => {
    const operation = activeOperations.value.get(operationId)
    if (!operation) return

    // Update operation
    Object.assign(operation, updates)
    activeOperations.value.set(operationId, operation)
    
    // Update global progress if this is the current operation
    if (globalProgress.value.show && getCurrentOperation()?.id === operationId) {
      updateGlobalProgress(operation)
    }
  }

  /**
   * Complete an operation
   */
  const completeProgress = (operationId, successMessage = null) => {
    const operation = activeOperations.value.get(operationId)
    if (!operation) return

    activeOperations.value.delete(operationId)
    
    // Show success message if provided
    if (successMessage) {
      // You can integrate with toast notifications here
      console.log('Operation completed:', successMessage)
    }
    
    // Update global progress
    if (activeOperations.value.size === 0) {
      hideGlobalProgress()
    } else {
      // Show next operation
      const nextOperation = getCurrentOperation()
      if (nextOperation) {
        updateGlobalProgress(nextOperation)
      }
    }
  }

  /**
   * Cancel an operation
   */
  const cancelProgress = (operationId) => {
    const operation = activeOperations.value.get(operationId)
    if (!operation) return

    // Call cancel callback if provided
    if (operation.cancelCallback) {
      operation.cancelCallback()
    }
    
    activeOperations.value.delete(operationId)
    
    // Update global progress
    if (activeOperations.value.size === 0) {
      hideGlobalProgress()
    } else {
      const nextOperation = getCurrentOperation()
      if (nextOperation) {
        updateGlobalProgress(nextOperation)
      }
    }
  }

  /**
   * Show global progress modal
   */
  const showProgress = (options = {}) => {
    globalProgress.value = {
      show: true,
      title: options.title || 'Processing...',
      message: options.message || 'Please wait...',
      progress: options.progress || -1,
      steps: options.steps || [],
      currentStep: options.currentStep || 0,
      canCancel: options.canCancel || false,
      cancelCallback: options.onCancel || null
    }
  }

  /**
   * Hide global progress modal
   */
  const hideProgress = () => {
    globalProgress.value.show = false
  }

  /**
   * Update global progress display
   */
  const updateGlobalProgress = (operation) => {
    globalProgress.value = {
      show: true,
      title: operation.title,
      message: operation.message,
      progress: operation.progress,
      steps: operation.steps,
      currentStep: operation.currentStep,
      canCancel: operation.canCancel,
      cancelCallback: operation.cancelCallback
    }
  }

  /**
   * Hide global progress
   */
  const hideGlobalProgress = () => {
    globalProgress.value.show = false
  }

  /**
   * Get current operation
   */
  const getCurrentOperation = () => {
    const operations = Array.from(activeOperations.value.values())
    return operations[0] || null
  }

  /**
   * Create step-based progress tracker
   */
  const createStepProgress = (steps, options = {}) => {
    const operationId = startProgress({
      ...options,
      steps: steps.map((step, index) => ({
        label: step,
        status: index === 0 ? 'active' : 'pending'
      })),
      progress: 0
    })

    let currentStepIndex = 0

    const nextStep = (message = null) => {
      if (currentStepIndex < steps.length - 1) {
        // Complete current step
        const updatedSteps = [...globalProgress.value.steps]
        updatedSteps[currentStepIndex].status = 'completed'
        
        // Move to next step
        currentStepIndex++
        updatedSteps[currentStepIndex].status = 'active'
        
        const progress = ((currentStepIndex) / steps.length) * 100

        updateProgress(operationId, {
          steps: updatedSteps,
          currentStep: currentStepIndex,
          progress,
          message: message || `Step ${currentStepIndex + 1} of ${steps.length}`
        })
      }
    }

    const complete = (successMessage = null) => {
      // Mark all steps as completed
      const updatedSteps = globalProgress.value.steps.map(step => ({
        ...step,
        status: 'completed'
      }))

      updateProgress(operationId, {
        steps: updatedSteps,
        progress: 100,
        message: 'Completed!'
      })

      setTimeout(() => {
        completeProgress(operationId, successMessage)
      }, 1000)
    }

    return {
      operationId,
      nextStep,
      complete,
      cancel: () => cancelProgress(operationId)
    }
  }

  /**
   * Create percentage-based progress tracker
   */
  const createPercentageProgress = (options = {}) => {
    const operationId = startProgress({
      ...options,
      progress: 0
    })

    const setProgress = (percentage, message = null) => {
      updateProgress(operationId, {
        progress: Math.min(100, Math.max(0, percentage)),
        message: message || globalProgress.value.message
      })
    }

    const complete = (successMessage = null) => {
      setProgress(100, 'Completed!')
      setTimeout(() => {
        completeProgress(operationId, successMessage)
      }, 1000)
    }

    return {
      operationId,
      setProgress,
      complete,
      cancel: () => cancelProgress(operationId)
    }
  }

  /**
   * Wrap async function with progress tracking
   */
  const withProgress = async (asyncFn, options = {}) => {
    const operationId = startProgress(options)
    
    try {
      const result = await asyncFn((updates) => updateProgress(operationId, updates))
      completeProgress(operationId, options.successMessage)
      return result
    } catch (error) {
      cancelProgress(operationId)
      throw error
    }
  }

  // Computed properties
  const hasActiveOperations = computed(() => activeOperations.value.size > 0)
  const operationCount = computed(() => activeOperations.value.size)

  return {
    // State
    globalProgress,
    hasActiveOperations,
    operationCount,
    
    // Methods
    startProgress,
    updateProgress,
    completeProgress,
    cancelProgress,
    showProgress,
    hideProgress,
    createStepProgress,
    createPercentageProgress,
    withProgress,
    
    // Utilities
    getCurrentOperation
  }
}
