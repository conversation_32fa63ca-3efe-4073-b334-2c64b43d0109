<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescription_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prescription_id')->constrained()->onDelete('cascade');
            $table->foreignId('medication_id')->nullable()->constrained()->onDelete('set null');
            $table->string('medication_name'); // Store name even if medication record is deleted
            $table->string('strength'); // e.g., "500mg"
            $table->string('form'); // tablet, capsule, liquid, etc.
            $table->string('dosage'); // e.g., "1 tablet"
            $table->string('frequency'); // e.g., "twice daily", "every 6 hours"
            $table->string('route')->default('oral'); // oral, topical, injection, etc.
            $table->integer('quantity'); // Number of units to dispense
            $table->string('quantity_unit')->default('tablets'); // tablets, ml, tubes, etc.
            $table->integer('duration_days')->nullable(); // Treatment duration
            $table->text('directions_for_use'); // Patient instructions
            $table->text('additional_instructions')->nullable(); // Extra instructions
            $table->boolean('take_with_food')->nullable();
            $table->boolean('avoid_alcohol')->nullable();
            $table->text('warnings')->nullable(); // Item-specific warnings
            $table->decimal('unit_cost', 8, 2)->nullable(); // Cost per unit
            $table->decimal('total_cost', 10, 2)->nullable(); // Total cost for this item
            $table->enum('status', ['pending', 'dispensed', 'partially_dispensed', 'cancelled'])->default('pending');
            $table->integer('quantity_dispensed')->default(0);
            $table->datetime('dispensed_at')->nullable();
            $table->text('dispensing_notes')->nullable();
            $table->boolean('is_repeat_eligible')->default(false); // Can be repeated
            $table->integer('repeats_allowed')->default(0); // Number of repeats allowed
            $table->integer('repeats_used')->default(0); // Number of repeats used
            $table->timestamps();

            // Indexes
            $table->index(['prescription_id', 'status']);
            $table->index('medication_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescription_items');
    }
};
