<?php

namespace App\Services\Migration;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Exception;

/**
 * File Migration Service
 * 
 * Handles downloading and migrating files from WordPress to Laravel storage
 */
class FileMigrationService
{
    /**
     * Download file from WordPress and store in Laravel storage
     */
    public function downloadAndStoreFile(array $fileInfo, string $category = 'consultation_documents'): array
    {
        $wpFilePath = $fileInfo['file_path'] ?? null;
        $wpFileUrl = $fileInfo['document_url'] ?? $fileInfo['full_file_url'] ?? null;
        $originalName = $fileInfo['name'] ?? 'unknown_file';
        $mimeType = $fileInfo['file_mime_type'] ?? 'application/octet-stream';
        $fileSize = $fileInfo['file_size'] ?? 0;
        $extension = $fileInfo['file_extension'] ?? $this->getExtensionFromMimeType($mimeType);

        // Validate file info
        if (!$wpFileUrl && !$wpFilePath) {
            throw new Exception('No file URL or path provided');
        }

        // Use URL if available, otherwise construct from path
        $downloadUrl = $wpFileUrl;
        if (!$downloadUrl && $wpFilePath) {
            // Convert Windows path to URL
            $relativePath = str_replace('\\', '/', $wpFilePath);
            $relativePath = str_replace('C:/laragon/www/medroid/', '', $relativePath);
            $downloadUrl = 'http://medroid.local/' . $relativePath;
        }

        // Generate unique filename
        $sanitizedName = $this->sanitizeFilename($originalName);
        $uniqueFilename = $this->generateUniqueFilename($sanitizedName, $extension);
        
        // Generate organized storage path
        $storagePath = $this->generateStoragePath($category, $uniqueFilename);

        try {
            // Download file from WordPress
            $response = Http::timeout(60)->get($downloadUrl);
            
            if (!$response->successful()) {
                throw new Exception("Failed to download file from {$downloadUrl}. HTTP Status: " . $response->status());
            }

            $fileContent = $response->body();
            $actualSize = strlen($fileContent);

            // Validate file content
            if (empty($fileContent)) {
                throw new Exception('Downloaded file is empty');
            }

            // Basic security validation
            if (!$this->isFileSecure($fileContent, $mimeType, $extension)) {
                throw new Exception('File failed security validation');
            }

            // Store file in Laravel storage
            Storage::disk('public')->put($storagePath, $fileContent);

            // Verify file was stored correctly
            if (!Storage::disk('public')->exists($storagePath)) {
                throw new Exception('File was not stored correctly');
            }

            return [
                'file_path' => $storagePath,
                'file_name' => $uniqueFilename,
                'original_name' => $originalName,
                'file_size' => $actualSize,
                'mime_type' => $mimeType,
                'extension' => $extension,
                'download_url' => $downloadUrl,
                'wp_file_path' => $wpFilePath,
            ];

        } catch (Exception $e) {
            // Clean up any partially stored file
            if (Storage::disk('public')->exists($storagePath)) {
                Storage::disk('public')->delete($storagePath);
            }
            
            throw new Exception("File migration failed: " . $e->getMessage());
        }
    }

    /**
     * Generate organized storage path
     */
    private function generateStoragePath(string $category, string $filename): string
    {
        $date = now()->format('Y/m');
        return "{$category}/{$date}/{$filename}";
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename(string $originalName, string $extension): string
    {
        $name = pathinfo($originalName, PATHINFO_FILENAME);
        $name = Str::slug($name);
        $uuid = Str::uuid();
        
        return "{$name}_{$uuid}.{$extension}";
    }

    /**
     * Sanitize filename for security
     */
    private function sanitizeFilename(string $filename): string
    {
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
        
        // Ensure reasonable length
        if (strlen($filename) > 200) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = substr(pathinfo($filename, PATHINFO_FILENAME), 0, 190);
            $filename = $name . '.' . $extension;
        }
        
        return $filename ?: 'file_' . time();
    }

    /**
     * Get file extension from mime type
     */
    private function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExt = [
            'application/pdf' => 'pdf',
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'text/plain' => 'txt',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
        ];

        return $mimeToExt[$mimeType] ?? 'bin';
    }

    /**
     * Basic file security validation
     */
    private function isFileSecure(string $content, string $mimeType, string $extension): bool
    {
        // Check for executable file extensions
        $dangerousExtensions = ['php', 'exe', 'bat', 'sh', 'cmd', 'scr', 'vbs', 'js'];
        if (in_array(strtolower($extension), $dangerousExtensions)) {
            return false;
        }

        // Check for PHP code in content
        if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
            return false;
        }

        // Basic mime type validation for PDFs
        if ($mimeType === 'application/pdf') {
            // Check for PDF header - can be at the beginning or after some whitespace/comments
            if (strpos($content, '%PDF-') !== false) {
                return true;
            }

            // Some PDFs might have different headers or be corrupted but still valid
            // Allow PDFs that have typical PDF content markers
            if (strpos($content, '/Type') !== false &&
                (strpos($content, '/Catalog') !== false || strpos($content, '/Page') !== false)) {
                return true;
            }

            // Check if content looks like HTML (common issue with download errors)
            if (strpos($content, '<html') !== false || strpos($content, '<!DOCTYPE') !== false) {
                \Log::warning('PDF file appears to be HTML content', [
                    'mime_type' => $mimeType,
                    'extension' => $extension,
                    'content_start' => substr($content, 0, 200)
                ]);
                return false;
            }

            // Check if content is too small to be a valid PDF
            if (strlen($content) < 100) {
                \Log::warning('PDF file content too small', [
                    'mime_type' => $mimeType,
                    'extension' => $extension,
                    'content_length' => strlen($content),
                    'content' => $content
                ]);
                return false;
            }

            // For migration purposes, be more lenient with PDFs
            // If it's supposed to be a PDF and passes basic checks, allow it
            \Log::info('PDF file passed lenient validation', [
                'mime_type' => $mimeType,
                'extension' => $extension,
                'content_length' => strlen($content)
            ]);
            return true;
        }

        if (strpos($mimeType, 'image/') === 0) {
            // Basic image validation - check for image headers
            $imageHeaders = [
                'image/jpeg' => ["\xFF\xD8\xFF"],
                'image/png' => ["\x89PNG\r\n\x1a\n"],
                'image/gif' => ["GIF87a", "GIF89a"],
            ];

            if (isset($imageHeaders[$mimeType])) {
                foreach ($imageHeaders[$mimeType] as $header) {
                    if (strpos($content, $header) === 0) {
                        return true;
                    }
                }
                return false;
            }
        }

        return true;
    }

    /**
     * Get file info from storage
     */
    public function getStoredFileInfo(string $filePath): array
    {
        if (!Storage::disk('public')->exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        return [
            'exists' => true,
            'size' => Storage::disk('public')->size($filePath),
            'last_modified' => Storage::disk('public')->lastModified($filePath),
            'url' => Storage::disk('public')->url($filePath),
        ];
    }

    /**
     * Delete file from storage
     */
    public function deleteStoredFile(string $filePath): bool
    {
        if (Storage::disk('public')->exists($filePath)) {
            return Storage::disk('public')->delete($filePath);
        }
        
        return true; // Already deleted
    }
}
