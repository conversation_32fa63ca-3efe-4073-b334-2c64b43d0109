<?php

namespace App\Services;

use App\Models\Bill;
use App\Models\Clinic;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class InvoiceService
{
    /**
     * Generate PDF invoice for a bill.
     */
    public function generateInvoicePdf(Bill $bill): string
    {
        // Load bill with all necessary relationships
        $bill->load([
            'clinic',
            'patient.user',
            'provider.user',
            'billItems.service',
            'consultation',
            'appointment'
        ]);

        // Get clinic letterhead settings
        $letterhead = $this->getClinicLetterhead($bill->clinic);

        // Prepare invoice data
        $invoiceData = [
            'bill' => $bill,
            'clinic' => $bill->clinic,
            'patient' => $bill->patient,
            'provider' => $bill->provider,
            'letterhead' => $letterhead,
            'invoice_number' => $bill->bill_number,
            'invoice_date' => $bill->bill_date->format('d/m/Y'),
            'due_date' => $bill->due_date ? $bill->due_date->format('d/m/Y') : null,
            'items' => $bill->billItems,
            'subtotal' => $bill->subtotal,
            'tax_data' => $bill->tax_data,
            'tax_amount' => $bill->tax_amount,
            'discount' => $bill->discount,
            'total_amount' => $bill->total_amount,
            'payment_status' => $bill->payment_status,
            'generated_at' => now()->format('d/m/Y H:i'),
        ];

        // Generate PDF
        $pdf = Pdf::loadView('invoices.template', $invoiceData);
        $pdf->setPaper('A4', 'portrait');

        // Generate filename
        $filename = "invoice-{$bill->bill_number}-" . now()->format('Y-m-d') . '.pdf';

        // Store PDF in storage
        $pdfContent = $pdf->output();
        $path = "invoices/{$bill->clinic_id}/{$filename}";
        Storage::disk('local')->put($path, $pdfContent);

        return $path;
    }

    /**
     * Get clinic letterhead settings.
     */
    private function getClinicLetterhead(Clinic $clinic): array
    {
        // Check if clinic has custom letterhead
        $letterheadSettings = $clinic->letterhead_settings ?? [];

        return [
            'clinic_name' => $letterheadSettings['clinic_name'] ?? $clinic->name,
            'address' => $letterheadSettings['address'] ?? $clinic->address,
            'phone' => $letterheadSettings['phone'] ?? $clinic->phone,
            'email' => $letterheadSettings['email'] ?? $clinic->email,
            'website' => $letterheadSettings['website'] ?? null,
            'logo_url' => $letterheadSettings['logo_url'] ?? null,
            'header_text' => $letterheadSettings['header_text'] ?? null,
            'footer_text' => $letterheadSettings['footer_text'] ?? 'Thank you for choosing our services.',
            'colors' => [
                'primary' => $letterheadSettings['primary_color'] ?? '#2563eb',
                'secondary' => $letterheadSettings['secondary_color'] ?? '#64748b',
                'accent' => $letterheadSettings['accent_color'] ?? '#059669',
            ]
        ];
    }

    /**
     * Generate and download invoice PDF.
     */
    public function downloadInvoice(Bill $bill): \Symfony\Component\HttpFoundation\Response
    {
        $bill->load([
            'clinic',
            'patient.user',
            'provider.user',
            'billItems.service'
        ]);

        $letterhead = $this->getClinicLetterhead($bill->clinic);

        $invoiceData = [
            'bill' => $bill,
            'clinic' => $bill->clinic,
            'patient' => $bill->patient,
            'provider' => $bill->provider,
            'letterhead' => $letterhead,
            'invoice_number' => $bill->bill_number,
            'invoice_date' => $bill->bill_date->format('d/m/Y'),
            'due_date' => $bill->due_date ? $bill->due_date->format('d/m/Y') : null,
            'items' => $bill->billItems,
            'subtotal' => $bill->subtotal,
            'tax_data' => $bill->tax_data,
            'tax_amount' => $bill->tax_amount,
            'discount' => $bill->discount,
            'total_amount' => $bill->total_amount,
            'payment_status' => $bill->payment_status,
            'generated_at' => now()->format('d/m/Y H:i'),
        ];

        $pdf = Pdf::loadView('invoices.template', $invoiceData);
        $pdf->setPaper('A4', 'portrait');

        $filename = "invoice-{$bill->bill_number}.pdf";

        return $pdf->download($filename);
    }

    /**
     * Generate invoice preview (for browser viewing).
     */
    public function previewInvoice(Bill $bill): string
    {
        $bill->load([
            'clinic',
            'patient.user',
            'provider.user',
            'billItems.service'
        ]);

        $letterhead = $this->getClinicLetterhead($bill->clinic);

        $invoiceData = [
            'bill' => $bill,
            'clinic' => $bill->clinic,
            'patient' => $bill->patient,
            'provider' => $bill->provider,
            'letterhead' => $letterhead,
            'invoice_number' => $bill->bill_number,
            'invoice_date' => $bill->bill_date->format('d/m/Y'),
            'due_date' => $bill->due_date ? $bill->due_date->format('d/m/Y') : null,
            'items' => $bill->billItems,
            'subtotal' => $bill->subtotal,
            'tax_data' => $bill->tax_data,
            'tax_amount' => $bill->tax_amount,
            'discount' => $bill->discount,
            'total_amount' => $bill->total_amount,
            'payment_status' => $bill->payment_status,
            'generated_at' => now()->format('d/m/Y H:i'),
            'is_preview' => true,
        ];

        return View::make('invoices.template', $invoiceData)->render();
    }

    /**
     * Get stored invoice PDF path.
     */
    public function getInvoicePath(Bill $bill): ?string
    {
        $filename = "invoice-{$bill->bill_number}-" . $bill->updated_at->format('Y-m-d') . '.pdf';
        $path = "invoices/{$bill->clinic_id}/{$filename}";

        if (Storage::disk('local')->exists($path)) {
            return $path;
        }

        return null;
    }

    /**
     * Check if invoice PDF exists.
     */
    public function invoiceExists(Bill $bill): bool
    {
        return $this->getInvoicePath($bill) !== null;
    }

    /**
     * Delete invoice PDF.
     */
    public function deleteInvoice(Bill $bill): bool
    {
        $path = $this->getInvoicePath($bill);
        
        if ($path && Storage::disk('local')->exists($path)) {
            return Storage::disk('local')->delete($path);
        }

        return false;
    }

    /**
     * Get invoice file size.
     */
    public function getInvoiceSize(Bill $bill): ?int
    {
        $path = $this->getInvoicePath($bill);
        
        if ($path && Storage::disk('local')->exists($path)) {
            return Storage::disk('local')->size($path);
        }

        return null;
    }

    /**
     * Generate receipt PDF for payment.
     */
    public function generateReceiptPdf(Bill $bill): string
    {
        $bill->load([
            'clinic',
            'patient.user',
            'provider.user',
            'billItems.service',
            'payments'
        ]);

        $letterhead = $this->getClinicLetterhead($bill->clinic);

        $receiptData = [
            'bill' => $bill,
            'clinic' => $bill->clinic,
            'patient' => $bill->patient,
            'provider' => $bill->provider,
            'letterhead' => $letterhead,
            'receipt_number' => 'RCP-' . $bill->bill_number,
            'payment_date' => $bill->paid_at ? $bill->paid_at->format('d/m/Y') : now()->format('d/m/Y'),
            'payments' => $bill->payments,
            'total_paid' => $bill->payments->sum('amount'),
            'generated_at' => now()->format('d/m/Y H:i'),
        ];

        $pdf = Pdf::loadView('invoices.receipt', $receiptData);
        $pdf->setPaper('A4', 'portrait');

        $filename = "receipt-{$bill->bill_number}-" . now()->format('Y-m-d') . '.pdf';
        $path = "receipts/{$bill->clinic_id}/{$filename}";

        // Ensure directory exists
        $directory = "receipts/{$bill->clinic_id}";
        if (!Storage::disk('local')->exists($directory)) {
            Storage::disk('local')->makeDirectory($directory);
        }

        $pdfContent = $pdf->output();
        $success = Storage::disk('local')->put($path, $pdfContent);

        if (!$success) {
            throw new \Exception('Failed to save receipt PDF to storage');
        }

        // Verify file was created
        if (!Storage::disk('local')->exists($path)) {
            throw new \Exception('Receipt PDF was not created successfully');
        }

        return $path;
    }
}
