<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultation_tabs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->string('type'); // concerns, history, examination, plan, notes, etc.
            $table->text('content')->nullable();
            $table->json('metadata')->nullable(); // For storing additional form data
            $table->json('templates')->nullable(); // For storing template data
            $table->string('title')->nullable();
            $table->integer('order')->default(0);
            $table->boolean('is_from_template')->default(false);
            $table->boolean('is_ai_generated')->default(false);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['consultation_id', 'type']);
            $table->index(['patient_id', 'type']);
            $table->index(['consultation_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultation_tabs');
    }
};