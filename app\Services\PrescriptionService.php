<?php

namespace App\Services;

use App\Models\Prescription;
use App\Repositories\PrescriptionRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PrescriptionService
{
    protected PrescriptionRepository $prescriptionRepository;

    public function __construct(PrescriptionRepository $prescriptionRepository)
    {
        $this->prescriptionRepository = $prescriptionRepository;
    }

    /**
     * Create a new prescription with business logic
     */
    public function createPrescription(array $data): Prescription
    {
        return DB::transaction(function () use ($data) {
            // Business logic: Set defaults
            $data['status'] = $data['status'] ?? 'active';
            $data['prescribed_date'] = $data['prescribed_date'] ?? now();
            $data['prescribed_by'] = $data['prescribed_by'] ?? Auth::id();

            // Business logic: Generate prescription number
            if (empty($data['prescription_number'])) {
                $data['prescription_number'] = $this->generatePrescriptionNumber();
            }

            // Extract items data
            $items = $data['items'] ?? [];
            unset($data['items']);

            // Set total items count
            $data['total_items'] = count($items);

            // Create prescription
            $prescription = $this->prescriptionRepository->create($data);

            // Create prescription items
            foreach ($items as $itemData) {
                $this->createPrescriptionItem($prescription->id, $itemData);
            }

            // Load relationships and return
            return $prescription->load(['patient.user', 'items.medication']);
        });
    }

    /**
     * Create prescription item
     */
    private function createPrescriptionItem(int $prescriptionId, array $itemData): void
    {
        $medication = \App\Models\Medication::find($itemData['medication_id']);

        \App\Models\PrescriptionItem::create([
            'prescription_id' => $prescriptionId,
            'medication_id' => $medication->id,
            'medication_name' => $medication->display_name,
            'strength' => $medication->strength,
            'form' => $medication->form,
            'dosage' => $itemData['dosage'],
            'frequency' => $itemData['frequency'],
            'route' => $medication->route ?? 'oral',
            'quantity' => $itemData['quantity'],
            'quantity_unit' => $itemData['quantity_unit'],
            'duration_days' => $itemData['duration_days'],
            'directions_for_use' => $itemData['directions_for_use'],
            'additional_instructions' => $itemData['additional_instructions'],
            'take_with_food' => $itemData['take_with_food'] ?? false,
            'avoid_alcohol' => $itemData['avoid_alcohol'] ?? false,
            'warnings' => $itemData['warnings'],
            'is_repeat_eligible' => $itemData['is_repeat_eligible'] ?? false,
            'repeats_allowed' => $itemData['repeats_allowed'] ?? 0,
        ]);
    }

    /**
     * Update prescription with business logic
     */
    public function updatePrescription(int $prescriptionId, array $data): ?Prescription
    {
        // Business logic: Recalculate end date if duration changed
        if (isset($data['duration_days']) && !isset($data['end_date'])) {
            $prescription = $this->prescriptionRepository->find($prescriptionId);
            if ($prescription) {
                $startDate = $prescription->start_date;
                $data['end_date'] = $startDate->addDays($data['duration_days']);
            }
        }

        return $this->prescriptionRepository->update($prescriptionId, $data);
    }

    /**
     * Get prescription details
     */
    public function getPrescriptionDetails(int $prescriptionId): ?Prescription
    {
        $prescription = $this->prescriptionRepository->find($prescriptionId);
        
        if (!$prescription) {
            return null;
        }

        // Load relationships
        $prescription->load([
            'patient.user',
            'provider.user',
            'consultation',
            'items',
            'refillRequests'
        ]);

        return $prescription;
    }

    /**
     * Get patient prescriptions
     */
    public function getPatientPrescriptions(int $patientId): Collection
    {
        return $this->prescriptionRepository->findByPatient($patientId);
    }

    /**
     * Get active prescriptions for patient
     */
    public function getActivePatientPrescriptions(int $patientId): Collection
    {
        return $this->prescriptionRepository->findActive($patientId);
    }

    /**
     * Get provider prescriptions
     */
    public function getProviderPrescriptions(int $providerId): Collection
    {
        return $this->prescriptionRepository->findByProvider($providerId);
    }

    /**
     * Get prescriptions by consultation
     */
    public function getConsultationPrescriptions(int $consultationId): Collection
    {
        return $this->prescriptionRepository->findByConsultation($consultationId);
    }

    /**
     * Get prescriptions with filters and pagination
     */
    public function getPrescriptionsWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->prescriptionRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Complete prescription
     */
    public function completePrescription(int $prescriptionId, ?string $notes = null): bool
    {
        $data = [
            'status' => 'completed',
            'completed_at' => now(),
            'completion_notes' => $notes
        ];

        $prescription = $this->prescriptionRepository->update($prescriptionId, $data);
        
        // Business logic: Handle completion side effects
        if ($prescription) {
            // Could send notifications, update patient records, etc.
        }

        return $prescription !== null;
    }

    /**
     * Cancel prescription
     */
    public function cancelPrescription(int $prescriptionId, string $reason): bool
    {
        $data = [
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason
        ];

        $prescription = $this->prescriptionRepository->update($prescriptionId, $data);
        
        // Business logic: Handle cancellation side effects
        if ($prescription) {
            // Could send notifications, update related records, etc.
        }

        return $prescription !== null;
    }

    /**
     * Renew prescription
     */
    public function renewPrescription(int $prescriptionId, array $renewalData = []): Prescription
    {
        $originalPrescription = $this->prescriptionRepository->find($prescriptionId);
        
        if (!$originalPrescription) {
            throw new \Exception('Original prescription not found');
        }

        // Business logic: Create new prescription based on original
        $newPrescriptionData = array_merge([
            'patient_id' => $originalPrescription->patient_id,
            'provider_id' => $originalPrescription->provider_id,
            'consultation_id' => $originalPrescription->consultation_id,
            'medication_name' => $originalPrescription->medication_name,
            'dosage' => $originalPrescription->dosage,
            'frequency' => $originalPrescription->frequency,
            'instructions' => $originalPrescription->instructions,
            'duration_days' => $originalPrescription->duration_days,
            'original_prescription_id' => $prescriptionId,
            'is_renewal' => true
        ], $renewalData);

        return $this->createPrescription($newPrescriptionData);
    }

    /**
     * Get prescriptions expiring soon
     */
    public function getPrescriptionsExpiringSoon(int $days = 7, ?int $patientId = null): Collection
    {
        return $this->prescriptionRepository->findExpiringSoon($days, $patientId);
    }

    /**
     * Search prescriptions by medication
     */
    public function searchByMedication(string $medication, ?int $patientId = null): Collection
    {
        return $this->prescriptionRepository->findByMedication($medication, $patientId);
    }

    /**
     * Get prescription statistics
     */
    public function getPrescriptionStatistics(?int $providerId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        return $this->prescriptionRepository->getStatistics($providerId, $startDate, $endDate);
    }

    /**
     * Check for drug interactions
     */
    public function checkDrugInteractions(int $patientId, string $newMedication): array
    {
        // Business logic: Check for drug interactions
        $activePrescriptions = $this->getActivePatientPrescriptions($patientId);
        $interactions = [];

        foreach ($activePrescriptions as $prescription) {
            // This would integrate with a drug interaction database
            $interaction = $this->checkInteractionBetweenMedications(
                $prescription->medication_name,
                $newMedication
            );
            
            if ($interaction) {
                $interactions[] = $interaction;
            }
        }

        return $interactions;
    }

    /**
     * Generate unique prescription number
     */
    private function generatePrescriptionNumber(): string
    {
        do {
            $number = 'RX' . date('Y') . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while ($this->prescriptionRepository->findOneBy(['prescription_number' => $number]));

        return $number;
    }

    /**
     * Check interaction between two medications
     */
    private function checkInteractionBetweenMedications(string $medication1, string $medication2): ?array
    {
        // Placeholder - would integrate with drug interaction API/database
        return null;
    }
}
