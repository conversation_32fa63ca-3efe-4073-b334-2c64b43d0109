<template>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Services & Procedures</h3>
                <div class="flex space-x-2">
                    <button @click="showAddServiceModal = true" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                        Add Service
                    </button>
                    <button v-if="unbilledServices.length > 0" 
                            @click="showGenerateBillModal = true"
                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">
                        Generate Bill
                    </button>
                </div>
            </div>
        </div>

        <div class="p-4">
            <!-- Services List -->
            <div v-if="services.length > 0" class="space-y-3">
                <div v-for="service in services" :key="service.id" 
                     class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h4 class="font-medium text-gray-900">{{ service.service_name }}</h4>
                            <span v-if="service.is_billed" 
                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Billed
                            </span>
                            <span v-else-if="service.is_billable" 
                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Billable
                            </span>
                            <span v-else 
                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                Non-billable
                            </span>
                        </div>
                        <p v-if="service.description" class="text-sm text-gray-600 mt-1">{{ service.description }}</p>
                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>Qty: {{ service.quantity }}</span>
                            <span>Unit Price: £{{ service.unit_price }}</span>
                            <span class="font-medium">Total: £{{ service.total_price }}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button v-if="!service.is_billed" 
                                @click="editService(service)" 
                                class="text-blue-600 hover:text-blue-800 text-sm">
                            Edit
                        </button>
                        <button v-if="!service.is_billed" 
                                @click="deleteService(service.id)" 
                                class="text-red-600 hover:text-red-800 text-sm">
                            Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No services added</h3>
                <p class="mt-1 text-sm text-gray-500">Add services and procedures performed during this consultation.</p>
            </div>

            <!-- Summary -->
            <div v-if="services.length > 0" class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Total Billable Amount:</span>
                    <span class="text-lg font-bold text-gray-900">£{{ totalBillableAmount.toFixed(2) }}</span>
                </div>
                <div v-if="unbilledServices.length > 0" class="flex justify-between items-center mt-2">
                    <span class="text-sm text-gray-600">Unbilled Amount:</span>
                    <span class="text-sm font-medium text-yellow-600">£{{ unbilledAmount.toFixed(2) }}</span>
                </div>
            </div>
        </div>

        <!-- Add Service Modal -->
        <div v-if="showAddServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        {{ editingService ? 'Edit Service' : 'Add Service' }}
                    </h3>
                    <form @submit.prevent="saveService">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Service</label>
                            <select v-model="serviceForm.service_id" 
                                    @change="updateServiceFromSelection"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select a service</option>
                                <option v-for="service in availableServices" :key="service.id" :value="service.id">
                                    {{ service.name }} (£{{ service.price }})
                                </option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Service Name *</label>
                            <input v-model="serviceForm.service_name" 
                                   type="text" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea v-model="serviceForm.description" 
                                      rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Unit Price *</label>
                                <input v-model="serviceForm.unit_price" 
                                       type="number" 
                                       step="0.01"
                                       min="0"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quantity *</label>
                                <input v-model="serviceForm.quantity" 
                                       type="number" 
                                       min="1"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="flex items-center">
                                <input v-model="serviceForm.is_billable" 
                                       type="checkbox" 
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Billable</span>
                            </label>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" 
                                    @click="closeServiceModal"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                                Cancel
                            </button>
                            <button type="submit" 
                                    :disabled="loading"
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50">
                                {{ loading ? 'Saving...' : 'Save' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Generate Bill Modal -->
        <div v-if="showGenerateBillModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Generate Bill</h3>
                    <form @submit.prevent="generateBill">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bill Title</label>
                            <input v-model="billForm.title" 
                                   type="text" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                            <input v-model="billForm.due_date" 
                                   type="date" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Discount</label>
                            <input v-model="billForm.discount" 
                                   type="number" 
                                   step="0.01"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                            <textarea v-model="billForm.notes" 
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex justify-between text-sm">
                                <span>Services to bill:</span>
                                <span>{{ unbilledServices.length }}</span>
                            </div>
                            <div class="flex justify-between text-sm font-medium">
                                <span>Total amount:</span>
                                <span>£{{ unbilledAmount.toFixed(2) }}</span>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" 
                                    @click="showGenerateBillModal = false"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                                Cancel
                            </button>
                            <button type="submit" 
                                    :disabled="loading"
                                    class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors disabled:opacity-50">
                                {{ loading ? 'Generating...' : 'Generate Bill' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import axios from 'axios'

const props = defineProps({
    consultationId: {
        type: [String, Number],
        required: true
    }
})

const emit = defineEmits(['billGenerated'])

const services = ref([])
const availableServices = ref([])
const loading = ref(false)
const showAddServiceModal = ref(false)
const showGenerateBillModal = ref(false)
const editingService = ref(null)

const serviceForm = reactive({
    service_id: '',
    service_name: '',
    description: '',
    unit_price: 0,
    quantity: 1,
    is_billable: true
})

const billForm = reactive({
    title: '',
    due_date: '',
    discount: 0,
    notes: ''
})

const unbilledServices = computed(() => {
    return services.value.filter(service => service.is_billable && !service.is_billed)
})

const totalBillableAmount = computed(() => {
    return services.value
        .filter(service => service.is_billable)
        .reduce((sum, service) => sum + parseFloat(service.total_price), 0)
})

const unbilledAmount = computed(() => {
    return unbilledServices.value
        .reduce((sum, service) => sum + parseFloat(service.total_price), 0)
})

const loadServices = async () => {
    try {
        const response = await axios.get(`/consultations/${props.consultationId}/services`)
        services.value = response.data.data
    } catch (error) {
        console.error('Error loading consultation services:', error)
    }
}

const loadAvailableServices = async () => {
    try {
        const response = await axios.get('/bills/create')
        availableServices.value = response.data.data.services || []
    } catch (error) {
        console.error('Error loading available services:', error)
    }
}

const updateServiceFromSelection = () => {
    const selectedService = availableServices.value.find(s => s.id == serviceForm.service_id)
    if (selectedService) {
        serviceForm.service_name = selectedService.name
        serviceForm.unit_price = selectedService.price
        serviceForm.description = selectedService.description || ''
    }
}

const saveService = async () => {
    loading.value = true
    try {
        if (editingService.value) {
            await axios.put(`/consultations/${props.consultationId}/services/${editingService.value.id}`, serviceForm)
        } else {
            await axios.post(`/consultations/${props.consultationId}/services`, serviceForm)
        }
        closeServiceModal()
        await loadServices()
    } catch (error) {
        console.error('Error saving service:', error)
    } finally {
        loading.value = false
    }
}

const editService = (service) => {
    editingService.value = service
    Object.assign(serviceForm, {
        service_id: service.service_id || '',
        service_name: service.service_name,
        description: service.description || '',
        unit_price: service.unit_price,
        quantity: service.quantity,
        is_billable: service.is_billable
    })
    showAddServiceModal.value = true
}

const deleteService = async (serviceId) => {
    if (confirm('Are you sure you want to delete this service?')) {
        try {
            await axios.delete(`/consultations/${props.consultationId}/services/${serviceId}`)
            await loadServices()
        } catch (error) {
            console.error('Error deleting service:', error)
        }
    }
}

const generateBill = async () => {
    loading.value = true
    try {
        const response = await axios.post(`/consultations/${props.consultationId}/services/generate-bill`, billForm)
        showGenerateBillModal.value = false
        await loadServices()
        emit('billGenerated', response.data.data)
        alert('Bill generated successfully!')
    } catch (error) {
        console.error('Error generating bill:', error)
        alert('Failed to generate bill. Please try again.')
    } finally {
        loading.value = false
    }
}

const closeServiceModal = () => {
    showAddServiceModal.value = false
    editingService.value = null
    Object.assign(serviceForm, {
        service_id: '',
        service_name: '',
        description: '',
        unit_price: 0,
        quantity: 1,
        is_billable: true
    })
}

onMounted(() => {
    loadServices()
    loadAvailableServices()
})
</script>
