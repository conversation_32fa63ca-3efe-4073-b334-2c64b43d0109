<?php

namespace Database\Factories;

use App\Models\Prescription;
use App\Models\Patient;
use App\Models\User;
use App\Models\Clinic;
use App\Models\Consultation;
use Illuminate\Database\Eloquent\Factories\Factory;

class PrescriptionFactory extends Factory
{
    protected $model = Prescription::class;

    public function definition(): array
    {
        $prescribedDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $validUntil = $this->faker->dateTimeBetween($prescribedDate, '+6 months');

        return [
            'prescription_number' => 'RX' . date('Ym', $prescribedDate->getTimestamp()) . str_pad($this->faker->unique()->numberBetween(1, 999999), 6, '0', STR_PAD_LEFT),
            'consultation_id' => null,
            'patient_id' => Patient::factory(),
            'prescriber_id' => User::factory(),
            'clinic_id' => Clinic::factory(),
            'status' => $this->faker->randomElement(['draft', 'active', 'dispensed', 'completed', 'cancelled', 'expired']),
            'type' => $this->faker->randomElement(['new', 'repeat', 'acute', 'chronic']),
            'prescribed_date' => $prescribedDate,
            'valid_until' => $validUntil,
            'clinical_indication' => $this->faker->sentence(),
            'additional_instructions' => $this->faker->paragraph(),
            'warnings' => $this->faker->sentence(),
            'is_private' => $this->faker->boolean(10),
            'is_electronic' => true,
            'pharmacy_name' => $this->faker->company() . ' Pharmacy',
            'pharmacy_address' => $this->faker->address(),
            'total_items' => $this->faker->numberBetween(1, 5),
            'total_cost' => $this->faker->randomFloat(2, 10, 200),
            'dispensed_at' => function (array $attributes) {
                return in_array($attributes['status'], ['dispensed', 'completed']) ? 
                    $this->faker->dateTimeBetween($attributes['prescribed_date'], 'now') : null;
            },
            'dispensed_by' => function (array $attributes) {
                return $attributes['dispensed_at'] ? $this->faker->name() : null;
            },
            'dispensing_notes' => function (array $attributes) {
                return $attributes['dispensed_at'] ? $this->faker->sentence() : null;
            },
            'attachments' => null,
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'dispensed_at' => null,
            'dispensed_by' => null,
            'dispensing_notes' => null,
        ]);
    }

    public function dispensed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'dispensed',
            'dispensed_at' => $this->faker->dateTimeBetween($attributes['prescribed_date'], 'now'),
            'dispensed_by' => $this->faker->name(),
            'dispensing_notes' => $this->faker->sentence(),
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'valid_until' => $this->faker->dateTimeBetween('-6 months', '-1 day'),
        ]);
    }

    public function withConsultation(): static
    {
        return $this->state(fn (array $attributes) => [
            'consultation_id' => Consultation::factory(),
        ]);
    }

    public function repeat(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'repeat',
        ]);
    }

    public function chronic(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'chronic',
        ]);
    }
}
