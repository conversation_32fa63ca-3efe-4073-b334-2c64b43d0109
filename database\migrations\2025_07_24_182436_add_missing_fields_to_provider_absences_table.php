<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add missing fields to provider_absences table for clinic schedules migration
     */
    public function up(): void
    {
        Schema::table('provider_absences', function (Blueprint $table) {
            // Add missing fields that the ProviderAbsence model expects
            if (!Schema::hasColumn('provider_absences', 'start_time')) {
                $table->time('start_time')->nullable()->after('start_date');
            }
            if (!Schema::hasColumn('provider_absences', 'end_time')) {
                $table->time('end_time')->nullable()->after('end_date');
            }
            if (!Schema::hasColumn('provider_absences', 'all_day')) {
                $table->boolean('all_day')->default(false)->after('end_time');
            }
            if (!Schema::hasColumn('provider_absences', 'module_type')) {
                $table->string('module_type')->nullable()->after('reason')->comment('Type of module (doctor, clinic, etc.)');
            }
            if (!Schema::hasColumn('provider_absences', 'module_id')) {
                $table->unsignedBigInteger('module_id')->nullable()->after('module_type')->comment('ID of the module');
            }
            if (!Schema::hasColumn('provider_absences', 'status')) {
                $table->boolean('status')->default(true)->after('module_id')->comment('Active status');
            }
            if (!Schema::hasColumn('provider_absences', 'wp_schedule_data')) {
                $table->json('wp_schedule_data')->nullable()->after('wp_clinic_schedule_id')->comment('WordPress schedule data for migration tracking');
            }

            // Add indexes for performance
            $table->index('module_type');
            $table->index('module_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provider_absences', function (Blueprint $table) {
            // Drop the added columns in reverse order
            $columnsToCheck = ['wp_schedule_data', 'status', 'module_id', 'module_type', 'all_day', 'end_time', 'start_time'];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('provider_absences', $column)) {
                    // Drop indexes first if they exist
                    if (in_array($column, ['module_type', 'module_id', 'status'])) {
                        try {
                            $table->dropIndex([$column]);
                        } catch (Exception $e) {
                            // Index might not exist, continue
                        }
                    }

                    $table->dropColumn($column);
                }
            }
        });
    }
};
