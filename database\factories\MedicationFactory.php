<?php

namespace Database\Factories;

use App\Models\Medication;
use Illuminate\Database\Eloquent\Factories\Factory;

class MedicationFactory extends Factory
{
    protected $model = Medication::class;

    public function definition(): array
    {
        $medications = [
            ['name' => 'Paracetamol', 'brand' => 'Tylenol', 'ingredient' => 'Acetaminophen', 'strength' => '500mg', 'form' => 'tablet', 'class' => 'Analgesic'],
            ['name' => 'Ibuprofen', 'brand' => 'Advil', 'ingredient' => 'Ibuprofen', 'strength' => '200mg', 'form' => 'tablet', 'class' => 'NSAID'],
            ['name' => 'Amoxicillin', 'brand' => 'Amoxil', 'ingredient' => 'Amoxicillin', 'strength' => '250mg', 'form' => 'capsule', 'class' => 'Antibiotic'],
            ['name' => 'Lisinopril', 'brand' => 'Prinivil', 'ingredient' => 'Lisinopril', 'strength' => '10mg', 'form' => 'tablet', 'class' => 'ACE Inhibitor'],
            ['name' => 'Metformin', 'brand' => 'Glucophage', 'ingredient' => 'Metformin HCl', 'strength' => '500mg', 'form' => 'tablet', 'class' => 'Antidiabetic'],
        ];

        $medication = $this->faker->randomElement($medications);

        return [
            'name' => $medication['name'],
            'brand_name' => $medication['brand'],
            'active_ingredient' => $medication['ingredient'],
            'strength' => $medication['strength'],
            'form' => $medication['form'],
            'route' => $this->faker->randomElement(['oral', 'topical', 'injection', 'inhalation']),
            'description' => $this->faker->paragraph(),
            'indications' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
            'contraindications' => [
                $this->faker->sentence(),
            ],
            'side_effects' => [
                $this->faker->word(),
                $this->faker->word(),
                $this->faker->word(),
            ],
            'interactions' => [
                [
                    'medication_id' => $this->faker->numberBetween(1, 100),
                    'interaction_type' => 'moderate',
                    'description' => $this->faker->sentence(),
                ]
            ],
            'dosage_guidelines' => $this->faker->paragraph(),
            'drug_class' => $medication['class'],
            'controlled_substance_schedule' => $this->faker->randomElement([null, 'II', 'III', 'IV', 'V']),
            'requires_prescription' => $this->faker->boolean(80),
            'is_active' => true,
            'regulatory_code' => $this->faker->regexify('[A-Z]{2}[0-9]{4}'),
        ];
    }

    public function controlled(): static
    {
        return $this->state(fn (array $attributes) => [
            'controlled_substance_schedule' => $this->faker->randomElement(['II', 'III', 'IV', 'V']),
            'requires_prescription' => true,
        ]);
    }

    public function overTheCounter(): static
    {
        return $this->state(fn (array $attributes) => [
            'controlled_substance_schedule' => null,
            'requires_prescription' => false,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
