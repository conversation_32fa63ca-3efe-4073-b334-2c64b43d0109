<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;

class MistralService
{
    private string $apiKey;
    private string $apiUrl;
    private string $model;
    private string $systemMessage;

    public function __construct()
    {
        $this->apiKey = config('services.mistral.api_key');
        $this->apiUrl = config('services.mistral.api_url');
        $this->model = config('services.mistral.model');
        $this->systemMessage = $this->getSystemMessage();

        if (empty($this->apiKey)) {
            throw new Exception('Mistral API key is not configured. Please set MISTRAL_API_KEY in your environment.');
        }
    }

    /**
     * Analyze transcript and extract medical information using Mistral AI
     */
    public function analyzeTranscript(string $transcript): array
    {
        try {
            Log::info('Starting Mistral transcript analysis', [
                'transcript_length' => strlen($transcript),
                'word_count' => str_word_count($transcript)
            ]);

            $wordCount = str_word_count($transcript);
            $shouldChunk = $wordCount > 2000;
            $extractedData = [];
            
            if ($shouldChunk) {
                Log::info("Large transcript detected ($wordCount words). Using chunking approach.");
                $extractedData = $this->analyzeTranscriptInChunks($transcript);
            } else {
                // For smaller transcripts, use the regular approach
                $userPrompt = "I am a doctor performing a consultation. Please analyze the following transcript and extract relevant medical information exactly as instructed in the system prompt. Extract only information explicitly mentioned in the transcript and organize it into the specified sections. Use the exact format outlined in the system prompt.

                Transcript:
                $transcript";
                
                $extractedData = $this->callMistralAPI($userPrompt);
            }
            
            if (!$extractedData) {
                throw new Exception("Failed to parse structured data from analysis");
            }

            Log::info('Mistral analysis completed successfully');

            return [
                'success' => true,
                'data' => $extractedData
            ];

        } catch (Exception $e) {
            Log::error('Mistral analysis failed', [
                'error' => $e->getMessage(),
                'transcript_length' => strlen($transcript)
            ]);

            return [
                'success' => false,
                'error' => 'Analysis failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process large transcript by breaking it into chunks for analysis (matching plugin implementation)
     */
    private function analyzeTranscriptInChunks(string $transcript): array
    {
        // Split transcript into chunks by speaker turns
        $chunks = $this->splitTranscriptIntoChunks($transcript, 1500); // ~1500 words per chunk
        Log::info("Split transcript into " . count($chunks) . " chunks");

        $allResults = [];

        foreach ($chunks as $index => $chunk) {
            Log::info("Processing chunk " . ($index + 1) . " of " . count($chunks));

            $userPrompt = "I am a doctor performing a consultation. Please analyze the following PART of a medical consultation transcript and extract relevant medical information exactly as instructed in the system prompt. This is part " . ($index + 1) . " of " . count($chunks) . ".

            Extract only information explicitly mentioned in this part of the transcript and organize it into the specified sections. Use the exact format outlined in the system prompt.

            Transcript Part " . ($index + 1) . ":
            $chunk";

            try {
                // Call API for this chunk
                $chunkResult = $this->callMistralAPI($userPrompt);

                if ($chunkResult) {
                    $allResults[] = $chunkResult;
                }

                // Small delay between chunks to avoid rate limiting (matching plugin)
                usleep(500000); // 0.5 second delay

            } catch (Exception $e) {
                Log::error("Failed to analyze chunk " . ($index + 1), [
                    'error' => $e->getMessage(),
                    'chunk_length' => strlen($chunk)
                ]);
                // Continue with other chunks even if one fails
            }
        }

        // Combine results from all chunks
        return $this->combineChunkResults($allResults);
    }

    /**
     * Call the Mistral API with the given prompt
     */
    private function callMistralAPI(string $userPrompt): array
    {
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 180, // 3 minutes timeout
            CURLOPT_CONNECTTIMEOUT => 30, // 30 second connection timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode([
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $this->systemMessage
                    ],
                    [
                        'role' => 'user',
                        'content' => $userPrompt
                    ]
                ],
                'temperature' => 0.1,
                'max_tokens' => 4000
            ]),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json'
            ],
        ]);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        curl_close($curl);
        
        if ($err) {
            Log::error("Mistral API error: $err");
            throw new Exception("cURL Error: " . $err);
        }
        
        if ($httpCode !== 200) {
            Log::error("Mistral API returned HTTP code $httpCode: $response");
            throw new Exception("Mistral API error: HTTP $httpCode");
        }
        
        $analysisResult = json_decode($response, true);
        
        if (!isset($analysisResult['choices'][0]['message']['content'])) {
            Log::error("Unexpected response format from Mistral API");
            throw new Exception("Failed to analyze transcript: unexpected response format");
        }
        
        // Parse the response and extract structured medical information
        $content = $analysisResult['choices'][0]['message']['content'];
        
        return $this->parseStructuredResponse($content);
    }

    /**
     * Parse structured response from Mistral API
     */
    private function parseStructuredResponse(string $content): array
    {
        $extractedData = [];
        
        // Parse sections from the response
        $sections = [
            'vital_signs' => 'Vital Signs',
            'concerns' => 'Present Concerns',
            'history' => 'Present History',
            'examination' => 'Examination',
            'systems_review' => 'Systems Review',
            'allergies' => 'Allergies',
            'family_history' => 'Family History',
            'past_medical_history' => 'Past Medical History',
            'medications' => 'Medications',
            'social_history' => 'Social History',
            'mental_health' => 'Mental Health',
            'lifestyle' => 'Lifestyle',
            'safeguarding' => 'Safeguarding',
            'notes' => 'Notes',
            'comments' => 'Comments',
            'safety_netting' => 'Safety Netting',
            'preventative_care' => 'Preventative Care',
            'plan' => 'Plan'
        ];
        
        foreach ($sections as $key => $sectionTitle) {
            $pattern = '/## ' . preg_quote($sectionTitle, '/') . '\s*\n(.*?)(?=\n## |\n```|\Z)/s';
            if (preg_match($pattern, $content, $matches)) {
                $extractedData[$key] = trim($matches[1]);
            }
        }
        
        return $extractedData;
    }

    /**
     * Split transcript into manageable chunks
     */
    private function splitTranscriptIntoChunks(string $transcript, int $maxWords): array
    {
        $words = explode(' ', $transcript);
        $chunks = [];
        $currentChunk = [];
        
        foreach ($words as $word) {
            $currentChunk[] = $word;
            
            if (count($currentChunk) >= $maxWords) {
                $chunks[] = implode(' ', $currentChunk);
                $currentChunk = [];
            }
        }
        
        // Add remaining words as final chunk
        if (!empty($currentChunk)) {
            $chunks[] = implode(' ', $currentChunk);
        }
        
        return $chunks;
    }

    /**
     * Combine results from multiple chunks
     */
    private function combineChunkResults(array $allResults): array
    {
        $combined = [];
        
        foreach ($allResults as $result) {
            foreach ($result as $key => $value) {
                if (!isset($combined[$key])) {
                    $combined[$key] = $value;
                } else {
                    // Combine text from multiple chunks
                    $combined[$key] .= "\n\n" . $value;
                }
            }
        }
        
        return $combined;
    }

    /**
     * Get system message for Mistral AI (matching plugin implementation)
     */
    private function getSystemMessage(): string
    {
        return "# Medical Transcript Analysis Prompt

Analyze the provided medical transcript and extract only the information that is explicitly mentioned. Organize the data into the following sections:

## Instructions
1. Extract only information that is explicitly stated in the transcript
2. Do not infer, assume, or add any information not directly mentioned
3. Use the exact wording from the transcript when possible
4. If a section has no information in the transcript, omit that section entirely
5. Do not include placeholders or notes about missing information
6. Include only factual information from the transcript

## Output Format

```
## Vital Signs
Temperature: [exact value if mentioned]
Pulse: [exact value if mentioned]
Blood Pressure: [exact value if mentioned]
Respiratory Rate: [exact value if mentioned]
Saturation: [exact value if mentioned]

## Present Concerns
[List exactly as mentioned in transcript]

## Present History
[Exact details from transcript]

## Examination
[Exact findings mentioned in transcript]

## Systems Review
[Any system reviews mentioned in transcript]

## Allergies
[Any allergies mentioned in transcript]

## Family History
[Any family history mentioned in transcript]

## Past Medical History
[Any past medical history mentioned in transcript]

## Medications
[Any medications mentioned in transcript]

## Social History
[Any social history mentioned in transcript]

## Mental Health
[Any mental health information mentioned in transcript]

## Lifestyle
[Any lifestyle information mentioned in transcript]

## Safeguarding
[Any safeguarding concerns or issues mentioned in transcript]

## Notes
[Any general notes from the consultation mentioned in transcript]

## Comments
[Any comments or remarks made by the clinician in transcript]

## Safety Netting
[Any safety netting advice or guidance mentioned in transcript]

## Preventative Care
[Any preventative care measures or advice mentioned in transcript]

## Plan
[Any management plan, follow-ups, or recommendations mentioned in transcript]
```

Remember to only include sections that have information explicitly mentioned in the transcript. If there is no information for a particular section, omit that section completely.";
    }

    /**
     * Check if Mistral API is configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }
}
