<?php

namespace App\Observers;

use App\Models\Appointment;
use App\Models\PatientProvider;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Log;

class AppointmentObserver
{
    /**
     * Handle the Appointment "created" event.
     * Automatically create patient-provider relationship when appointment is booked.
     */
    public function created(Appointment $appointment): void
    {
        try {
            // Create patient-provider relationship when appointment is created
            PatientProvider::createRelationship(
                $appointment->patient_id,
                $appointment->provider_id,
                'appointment',
                null, // No specific user assigned this (system-generated)
                'Automatically created when appointment was booked'
            );

            // Log for HIPAA/GDPR compliance using audit system
            AuditLog::logPatientProviderRelationship(
                $appointment->patient_id,
                $appointment->provider_id,
                'created',
                'appointment',
                'Automatically created when appointment was booked'
            );

            // Also log the appointment creation
            AuditLog::logActivity([
                'action' => 'create',
                'resource_type' => 'appointment',
                'resource_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'description' => 'Appointment created',
                'is_sensitive' => true,
                'severity' => 'medium',
                'new_values' => $appointment->toArray(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create patient-provider relationship for appointment', [
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'provider_id' => $appointment->provider_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle the Appointment "updated" event.
     */
    public function updated(Appointment $appointment): void
    {
        $changes = $appointment->getChanges();
        $original = $appointment->getOriginal();

        AuditLog::logActivity([
            'action' => 'update',
            'resource_type' => 'appointment',
            'resource_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'description' => 'Appointment updated: ' . implode(', ', array_keys($changes)),
            'is_sensitive' => true,
            'severity' => 'medium',
            'old_values' => array_intersect_key($original, $changes),
            'new_values' => $changes,
        ]);
    }

    /**
     * Handle the Appointment "deleted" event.
     */
    public function deleted(Appointment $appointment): void
    {
        AuditLog::logActivity([
            'action' => 'delete',
            'resource_type' => 'appointment',
            'resource_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'description' => 'Appointment deleted',
            'is_sensitive' => true,
            'severity' => 'high',
            'old_values' => $appointment->toArray(),
        ]);
    }

    /**
     * Handle the Appointment "restored" event.
     */
    public function restored(Appointment $appointment): void
    {
        // Log appointment restoration for HIPAA/GDPR compliance
        Log::info('Appointment restored', [
            'appointment_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'provider_id' => $appointment->provider_id,
            'restored_by' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Handle the Appointment "force deleted" event.
     */
    public function forceDeleted(Appointment $appointment): void
    {
        // Log permanent appointment deletion for HIPAA/GDPR compliance
        Log::warning('Appointment permanently deleted', [
            'appointment_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'provider_id' => $appointment->provider_id,
            'deleted_by' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
