<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->string('country_code', 2)->nullable()->after('credit_applied');
            $table->string('currency', 3)->nullable()->after('country_code');
            $table->string('checkout_flow_type')->nullable()->after('currency');
            $table->string('primary_gateway')->nullable()->after('checkout_flow_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn(['country_code', 'currency', 'checkout_flow_type', 'primary_gateway']);
        });
    }
};
