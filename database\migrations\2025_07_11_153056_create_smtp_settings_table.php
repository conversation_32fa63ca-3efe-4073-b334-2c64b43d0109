<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('smtp_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->string('email_provider')->default('smtp'); // smtp, ses, postmark, etc.
            $table->json('details');
            $table->json('additional_details')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);

            // Indexes
            $table->index('clinic_id');
            $table->index('email_provider');
            $table->index('is_verified');
            $table->index('is_active');

            // Unique constraint: one setting per provider per clinic
            $table->unique(['clinic_id', 'email_provider']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('smtp_settings');
    }
};
