<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt {{ $receipt_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .receipt-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid {{ $letterhead['colors']['primary'] }};
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 24px;
            font-weight: bold;
            color: {{ $letterhead['colors']['primary'] }};
            margin-bottom: 10px;
        }
        
        .clinic-details {
            color: {{ $letterhead['colors']['secondary'] }};
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .receipt-title {
            font-size: 28px;
            font-weight: bold;
            color: {{ $letterhead['colors']['accent'] }};
            margin-bottom: 10px;
        }
        
        .receipt-meta {
            color: {{ $letterhead['colors']['secondary'] }};
            font-size: 14px;
        }
        
        .payment-info {
            background: #f0f9ff;
            border: 2px solid {{ $letterhead['colors']['accent'] }};
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .payment-amount {
            font-size: 32px;
            font-weight: bold;
            color: {{ $letterhead['colors']['accent'] }};
            margin-bottom: 10px;
        }
        
        .payment-status {
            background: #d1fae5;
            color: #065f46;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .details-section {
            margin: 30px 0;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table td {
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .details-table .label {
            font-weight: bold;
            color: {{ $letterhead['colors']['secondary'] }};
            width: 40%;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .payments-table th {
            background: {{ $letterhead['colors']['primary'] }};
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
        }
        
        .payments-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: {{ $letterhead['colors']['secondary'] }};
            font-size: 11px;
        }
        
        .thank-you {
            background: #f8f9fa;
            border-left: 4px solid {{ $letterhead['colors']['accent'] }};
            padding: 15px;
            margin: 30px 0;
            text-align: center;
            font-style: italic;
            color: {{ $letterhead['colors']['primary'] }};
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="header">
            <div class="clinic-name">{{ $letterhead['clinic_name'] }}</div>
            <div class="clinic-details">
                @if($letterhead['address'])
                    {{ $letterhead['address'] }}<br>
                @endif
                @if($letterhead['phone'])
                    Phone: {{ $letterhead['phone'] }}
                @endif
                @if($letterhead['email'])
                    | Email: {{ $letterhead['email'] }}
                @endif
            </div>
            <div class="receipt-title">PAYMENT RECEIPT</div>
            <div class="receipt-meta">
                Receipt #: {{ $receipt_number }}<br>
                Payment Date: {{ $payment_date }}
            </div>
        </div>

        <!-- Payment Info -->
        <div class="payment-info">
            <div class="payment-status">✓ PAYMENT RECEIVED</div>
            <div class="payment-amount">£{{ number_format($total_paid, 2) }}</div>
            <div>Total Amount Paid</div>
        </div>

        <!-- Details -->
        <div class="details-section">
            <table class="details-table">
                <tr>
                    <td class="label">Patient:</td>
                    <td>{{ $patient->user->name }}</td>
                </tr>
                <tr>
                    <td class="label">Provider:</td>
                    <td>{{ $provider->user->name }}</td>
                </tr>
                <tr>
                    <td class="label">Invoice Number:</td>
                    <td>{{ $bill->bill_number }}</td>
                </tr>
                <tr>
                    <td class="label">Invoice Date:</td>
                    <td>{{ $bill->bill_date->format('d/m/Y') }}</td>
                </tr>
                <tr>
                    <td class="label">Invoice Total:</td>
                    <td><strong>£{{ number_format($bill->total_amount, 2) }}</strong></td>
                </tr>
                <tr>
                    <td class="label">Amount Paid:</td>
                    <td><strong style="color: {{ $letterhead['colors']['accent'] }};">£{{ number_format($total_paid, 2) }}</strong></td>
                </tr>
            </table>
        </div>

        <!-- Payment Details -->
        @if($payments->count() > 0)
        <div class="details-section">
            <h3 style="color: {{ $letterhead['colors']['primary'] }}; margin-bottom: 15px;">Payment Details</h3>
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Date</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($payments as $payment)
                    <tr>
                        <td>
                            {{ ucfirst(str_replace('_', ' ', $payment->payment_method_type)) }}
                            @if($payment->payment_id)
                                <br><small style="color: #6b7280;">ID: {{ $payment->payment_id }}</small>
                            @endif
                        </td>
                        <td>{{ $payment->paid_at ? $payment->paid_at->format('d/m/Y H:i') : 'N/A' }}</td>
                        <td class="text-right">£{{ number_format($payment->amount, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        <!-- Thank You Message -->
        <div class="thank-you">
            Thank you for your payment!<br>
            {{ $letterhead['footer_text'] ?? 'We appreciate your business.' }}
        </div>

        <!-- Footer -->
        <div class="footer">
            This receipt was generated on {{ $generated_at }}<br>
            For questions about this payment, please contact {{ $letterhead['email'] ?? $letterhead['phone'] }}<br>
            <strong>Please keep this receipt for your records</strong>
        </div>
    </div>
</body>
</html>
