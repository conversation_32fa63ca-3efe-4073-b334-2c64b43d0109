<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use App\Models\User;
use App\Models\Clinic;
use App\Models\Appointment;
use App\Models\HealthRecord;
use App\Models\ConsultationVital;
use App\Models\Consultation;
use App\Models\AuditLog;
use App\Services\PatientService;
use App\Traits\ClinicFilterable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\Patient\StorePatientRequest;
use App\Http\Requests\Patient\UpdatePatientRequest;
use App\Http\Requests\Patient\StorePatientVitalsRequest;

class PatientController extends Controller
{
    use ClinicFilterable;

    protected PatientService $patientService;

    public function __construct(PatientService $patientService = null)
    {
        $this->patientService = $patientService ?? app(PatientService::class);
    }
    /**
     * Display a listing of the patients.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // Check if user has permission to view patients
            if (!$request->user()->can('view patients')) {
                return $this->forbiddenResponse('You do not have permission to view patients');
            }

            // Prepare filters from request
            $filters = [
                'search' => $request->get('search'),
                'clinic_id' => $request->get('clinic_id'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_dir' => $request->get('sort_dir', 'desc'),
            ];

            // Apply clinic filtering for non-admin users
            $user = $request->user();
            if ($user && !$user->hasRole('admin')) {
                $filters['clinic_id'] = $user->clinic_id;
            }

            $perPage = $request->input('per_page', 15);
            $patients = $this->patientService->getPatientsWithFilters($filters, $perPage);

            return $this->paginatedResponse($patients, 'Patients retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving patients: ' . $e->getMessage(), [
                'user_id' => $request->user()->id ?? null,
                'filters' => $filters ?? [],
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to retrieve patients. Please try again.');
        }
    }

    /**
     * Get patients list with proper pagination for main patients page
     */
    public function getPatientsList(Request $request)
    {
        try {
            // Check if user has permission to view patients
            if (!$request->user()->can('view patients')) {
                return $this->forbiddenResponse('You do not have permission to view patients');
            }

            // Prepare filters from request
            $filters = [
                'search' => $request->get('search'),
                'clinic_id' => $request->get('clinic_id'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_dir' => $request->get('sort_dir', 'desc'),
            ];

            // Apply clinic filtering for non-admin users
            $user = $request->user();
            if ($user && !$user->hasRole('admin')) {
                $filters['clinic_id'] = $user->clinic_id;
            }

            $perPage = $request->input('per_page', 15);
            $patients = $this->patientService->getPatientsWithFilters($filters, $perPage);

            return $this->paginatedResponse($patients, 'Patients retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving patients list: ' . $e->getMessage(), [
                'user_id' => $request->user()->id ?? null,
                'filters' => $filters ?? [],
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to retrieve patients. Please try again.');
        }
    }

    /**
     * Get patients list for dropdown (simplified)
     */
    public function getPatientsListDropdown(Request $request)
    {
        try {
            $user = $request->user();
            $query = Patient::select('id', 'first_name', 'last_name', 'email', 'clinic_id');

            // Apply clinic filtering for non-admin users
            if ($user && !$user->hasRole('admin')) {
                $clinicId = $user->clinic_id;
                if ($clinicId) {
                    $query->where('clinic_id', $clinicId);
                } else {
                    // If user has no clinic association, return no results
                    $query->whereRaw('1 = 0');
                }
            }

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            $patients = $query->orderBy('first_name')->limit(50)->get()->map(function($patient) {
                return [
                    'id' => $patient->id,
                    'name' => trim($patient->first_name . ' ' . $patient->last_name) ?: 'N/A',
                    'email' => $patient->email ?? ''
                ];
            });

            return $this->successResponse($patients, 'Patients dropdown list retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving patients dropdown: ' . $e->getMessage(), [
                'user_id' => $request->user()->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to retrieve patients dropdown. Please try again.');
        }
    }

    /**
     * Display the specified patient.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // Check if user has permission to view patients
            if (!$request->user()->can('view patients')) {
                return $this->forbiddenResponse('You do not have permission to view patients');
            }

            $patient = $this->patientService->getPatientDetails($id);

            if (!$patient) {
                return $this->notFoundResponse('Patient not found');
            }

            // Check clinic access for non-admin users
            $user = $request->user();
            if ($user && !$user->hasRole('admin')) {
                if ($patient->clinic_id !== $user->clinic_id) {
                    return $this->forbiddenResponse('You do not have access to this patient');
                }
            }

            return $this->successResponse($patient, 'Patient retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving patient: ' . $e->getMessage(), [
                'patient_id' => $id,
                'user_id' => $request->user()->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to retrieve patient. Please try again.');
        }
    }

    /**
     * Store a newly created patient in storage.
     *
     * @param  \App\Http\Requests\Patient\StorePatientRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StorePatientRequest $request)
    {
        try {
            // Prepare user data
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => $request->password,
                'role' => 'patient',
                'email_verified_at' => now(), // Auto-verify for admin-created accounts
            ];

            // Prepare patient data - all data stored in patients table
            $patientData = [
                'clinic_id' => $this->getClinicIdForPatient($request),
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'nhs_number' => $request->nhs_number,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'emergency_contact_relationship' => $request->emergency_contact_relationship,
                'emergency_contact' => $request->emergency_contact,
                'medical_history' => $request->medical_history,
                'current_medications' => $request->current_medications,
                'registered_gp_name' => $request->registered_gp_name,
                'registered_gp_address' => $request->registered_gp_address,
                'health_history' => [],
                'allergies' => [],
                'preferences' => [
                    'feed_topics' => [],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_gender' => null,
                    'preferred_language' => null
                ],
                'registration_date' => now(),
            ];

            $patient = $this->patientService->createPatient($userData, $patientData);

            return $this->createdResponse($patient, 'Patient created successfully');

        } catch (\Exception $e) {
            Log::error('Error creating patient: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'user_id' => $request->user()->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to create patient. Please try again.');
        }
    }

    /**
     * Get clinic ID for patient creation
     */
    private function getClinicIdForPatient(StorePatientRequest $request): ?int
    {
        $currentUser = $request->user();
        $clinicId = $request->clinic_id;

        // For non-admin users, use their clinic
        if (!$currentUser->hasRole('admin')) {
            return $currentUser->clinic_id;
        }

        // Admin users can specify clinic or use default
        if (!$clinicId) {
            $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
            if (!$defaultClinic) {
                $defaultClinic = Clinic::first();
            }
            $clinicId = $defaultClinic ? $defaultClinic->id : null;
        }

        return $clinicId;
    }

    /**
     * Update the specified patient in storage.
     *
     * @param  \App\Http\Requests\Patient\UpdatePatientRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdatePatientRequest $request, $id)
    {
        // Authorization is handled by the UpdatePatientRequest class
        // Validation is handled by the UpdatePatientRequest class
        $validatedData = $request->validated();

        $user = $request->user();
        $query = Patient::query();

        // Apply clinic filtering for non-admin users
        if ($user && !$user->hasRole('admin')) {
            $clinicId = $user->clinic_id;
            if ($clinicId) {
                $query->where('clinic_id', $clinicId);
            } else {
                // If user has no clinic association, return no results
                $query->whereRaw('1 = 0');
            }
        }

        $patient = $query->findOrFail($id);

        // Update user information if provided
        $userModel = User::findOrFail($patient->user_id);
        $userData = [];

        // Handle user fields
        if ($request->has('email')) $userData['email'] = $request->email;
        if ($request->has('phone_number')) $userData['phone_number'] = $request->phone_number;
        if ($request->has('country_code')) $userData['country_code'] = $request->country_code;
        if ($request->has('address')) $userData['address'] = $request->address;
        if ($request->has('city')) $userData['city'] = $request->city;
        if ($request->has('country')) $userData['country'] = $request->country;
        if ($request->has('postal_code')) $userData['postal_code'] = $request->postal_code;
        if ($request->has('post_code')) $userData['postal_code'] = $request->post_code;

        // Handle legacy name field or construct from first_name + last_name
        if ($request->has('name')) {
            $userData['name'] = $request->name;
        } elseif ($request->has('first_name') || $request->has('last_name')) {
            $firstName = $request->first_name ?: '';
            $lastName = $request->last_name ?: '';
            $userData['name'] = trim($firstName . ' ' . $lastName);
        }

        if (!empty($userData)) {
            $userModel->update($userData);
        }

        // Update patient profile
        $patientData = [];
        $patientFields = [
            // New patient fields
            'patient_unique_id', 'first_name', 'last_name', 'nhs_number',
            'registered_gp_name', 'registered_gp_address', 'gender', 'date_of_birth',
            'emergency_contact', 'medical_history', 'current_medications', 'clinic_id',
            // Legacy fields for backward compatibility
            'phone', 'address', 'city', 'state', 'zip_code', 'country',
            'emergency_contact_name', 'emergency_contact_phone', 'preferred_language',
            'preferred_provider_gender', 'preferred_location'
        ];

        foreach ($patientFields as $field) {
            if ($request->has($field)) {
                $patientData[$field] = $request->$field;
            }
        }

        if (!empty($patientData)) {
            $patient->update($patientData);
        }

        // Reload the patient with user relationship
        $patient = Patient::with('user')->findOrFail($id);

        return response()->json($patient);
    }

    /**
     * Get the patient's health records.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHealthRecords(Request $request, $id)
    {
        // Check if user has permission to view patients
        if (!$request->user()->can('view patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::findOrFail($id);

        // Get health records with pagination
        $perPage = $request->input('per_page', 10);
        $healthRecords = HealthRecord::where('patient_id', $id)
            ->orderBy('date', 'desc')
            ->paginate($perPage);

        return response()->json($healthRecords);
    }

    /**
     * Get the patient's appointment history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppointmentHistory(Request $request, $id)
    {
        // Check if user has permission to view patients and appointments
        if (!$request->user()->can('view patients') || !$request->user()->can('view appointments')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $patient = Patient::findOrFail($id);

        // Get appointments with pagination
        $perPage = $request->input('per_page', 10);
        $appointments = Appointment::where('patient_id', $id)
            ->with(['provider.user', 'service'])
            ->orderBy('scheduled_at', 'desc')
            ->paginate($perPage);

        return response()->json($appointments);
    }

    /**
     * Get the patient's recent consultations (last 3).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecentConsultations(Request $request, $id)
    {
        // Check if user has permission to view patients
        if (!$request->user()->can('view patients')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = $request->user();

        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can view consultations.'
            ], 403);
        }

        // Get last 3 consultations for this patient
        $consultations = Consultation::where('patient_id', $id)
            ->with(['provider.user'])
            ->orderBy('consultation_date', 'desc')
            ->limit(3)
            ->get();

        return response()->json([
            'data' => $consultations
        ]);
    }

    /**
     * Get all consultations for a patient with pagination.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPatientConsultations(Request $request, $id)
    {
        try {
            // Check if user has permission to view patients
            if (!$request->user()->can('view patients')) {
                return $this->forbiddenResponse('You do not have permission to view patients');
            }

            $user = $request->user();

            // Check if patient exists and user has access
            $patient = Patient::find($id);
            if (!$patient) {
                return $this->notFoundResponse('Patient not found');
            }

            // Check clinic access for non-admin users
            if ($user && !$user->hasRole('admin')) {
                if ($patient->clinic_id !== $user->clinic_id) {
                    return $this->forbiddenResponse('You do not have access to this patient');
                }
            }

            $perPage = $request->input('per_page', 15);

            // Get all consultations for this patient with pagination
            $consultations = Consultation::where('patient_id', $id)
                ->with(['provider.user', 'clinic', 'patient'])
                ->orderBy('consultation_date', 'desc')
                ->paginate($perPage);

            return $this->paginatedResponse($consultations, 'Patient consultations retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Error retrieving patient consultations: ' . $e->getMessage(), [
                'patient_id' => $id,
                'user_id' => $request->user()->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('Failed to retrieve patient consultations. Please try again.');
        }
    }

    /**
     * Remove the specified patient from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // Check if user has permission to delete patients
            if (!$request->user() || !$request->user()->can('delete patients')) {
                return response()->json(['message' => 'Unauthorized to delete patients'], 403);
            }

            try {
                $patient = Patient::findOrFail($id);
                $user = User::findOrFail($patient->user_id);
                $currentUser = $request->user();

                // Prevent deleting your own account
                if ($currentUser->id === $patient->user_id) {
                    return response()->json(['message' => 'You cannot delete your own account'], 400);
                }

                // Check clinic access for non-admin users
                if (!$currentUser->hasRole('admin')) {
                    if ($patient->clinic_id !== $currentUser->clinic_id) {
                        return response()->json(['message' => 'You do not have access to delete patients from other clinics'], 403);
                    }
                }

                // Begin transaction to ensure both patient and user are deleted
                DB::beginTransaction();

                try {
                    // Create audit log BEFORE deleting the patient to avoid foreign key constraint issues
                    AuditLog::logActivity([
                        'action' => 'delete',
                        'resource_type' => 'patient',
                        'resource_id' => $patient->id,
                        'patient_id' => $patient->id,
                        'description' => 'Patient record deleted',
                        'is_sensitive' => true,
                        'severity' => 'critical',
                        'old_values' => $patient->toArray(),
                    ]);

                    // Delete related appointments
                    $appointmentsDeleted = Appointment::where('patient_id', $id)->delete();

                    // Delete related health records
                    $healthRecordsDeleted = HealthRecord::where('patient_id', $id)->delete();

                    // Delete the patient (disable observer to prevent duplicate audit log)
                    Patient::withoutEvents(function () use ($patient) {
                        $patient->delete();
                    });
                    $patientDeleted = true;

                    // Remove roles from user before deleting to avoid model_has_roles issues
                    $user->roles()->detach();

                    // Delete the user
                    $userDeleted = $user->delete();

                    DB::commit();

                    // Log the deletion for debugging
                    Log::info('Patient deleted successfully', [
                        'patient_id' => $id,
                        'user_id' => $patient->user_id,
                        'appointments_deleted' => $appointmentsDeleted,
                        'health_records_deleted' => $healthRecordsDeleted,
                        'patient_deleted' => $patientDeleted,
                        'user_deleted' => $userDeleted,
                        'deleted_by' => $request->user()->id
                    ]);

                    return response()->json(['message' => 'Patient deleted successfully']);
                } catch (\Exception $e) {
                    DB::rollBack();

                    Log::error('Failed to delete patient in transaction', [
                        'patient_id' => $id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    return response()->json([
                        'message' => 'Failed to delete patient: ' . $e->getMessage()
                    ], 500);
                }
            } catch (\Exception $e) {
                Log::error('Failed to find patient for deletion', [
                    'patient_id' => $id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'message' => 'Patient not found or could not be deleted: ' . $e->getMessage()
                ], 404);
            }
        } catch (\Exception $e) {
            // Catch any authentication or permission errors
            Log::error('Authentication or permission error in patient deletion', [
                'patient_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user() ? $request->user()->id : 'none'
            ]);

            return response()->json([
                'message' => 'Authentication error: ' . $e->getMessage()
            ], 401);
        }
    }

    /**
     * Get patient vital signs
     */
    public function getVitals(Request $request, $patientId)
    {
        try {
            $limit = $request->get('limit', 10);
            $encounterId = $request->get('encounter_id');

            $query = ConsultationVital::where('patient_id', $patientId)
                ->orderBy('created_at', 'desc');

            if ($encounterId) {
                $query->where('consultation_id', $encounterId);
            }

            $vitals = $query->limit($limit)->get()->map(function ($vital) {
                return [
                    'id' => $vital->id,
                    'temperature' => $vital->temperature,
                    'temperature_unit' => $vital->temperature_unit,
                    'pulse' => $vital->pulse,
                    'systolic_bp' => $vital->systolic_bp,
                    'diastolic_bp' => $vital->diastolic_bp,
                    'respiratory_rate' => $vital->respiratory_rate,
                    'oxygen_saturation' => $vital->oxygen_saturation,
                    'weight' => $vital->weight,
                    'weight_unit' => $vital->weight_unit,
                    'height' => $vital->height,
                    'height_unit' => $vital->height_unit,
                    'bmi' => $vital->bmi,
                    'pain_level' => $vital->pain_level,
                    'recorded_at' => $vital->created_at->toISOString(),
                    'recorded_by' => $vital->recordedBy->name ?? 'Unknown',
                    'notes' => $vital->notes
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $vitals
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load vitals: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store patient vital signs
     *
     * @param  \App\Http\Requests\Patient\StorePatientVitalsRequest  $request
     * @param  int  $patientId
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeVitals(StorePatientVitalsRequest $request, $patientId)
    {
        // Authorization is handled by the StorePatientVitalsRequest class
        // Validation is handled by the StorePatientVitalsRequest class
        $validatedData = $request->validated();

        try {
            $savedVitals = [];

            foreach ($request->vitals as $vitalData) {
                $vital = VitalSign::create([
                    'patient_id' => $patientId,
                    'encounter_id' => $request->encounter_id,
                    'type' => $vitalData['type'],
                    'value' => $vitalData['value'],
                    'systolic' => $vitalData['systolic'] ?? null,
                    'diastolic' => $vitalData['diastolic'] ?? null,
                    'unit' => $vitalData['unit'],
                    'recorded_at' => now(),
                    'recorded_by' => auth()->id(),
                    'notes' => $vitalData['notes'] ?? null
                ]);

                $savedVitals[] = [
                    'id' => $vital->id,
                    'type' => $vital->type,
                    'value' => $vital->value,
                    'systolic' => $vital->systolic,
                    'diastolic' => $vital->diastolic,
                    'unit' => $vital->unit,
                    'recorded_at' => $vital->recorded_at->toISOString(),
                    'recorded_by' => $vital->recordedBy->name ?? 'Unknown',
                    'notes' => $vital->notes
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $savedVitals,
                'message' => count($savedVitals) . ' vital sign(s) saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save vitals: ' . $e->getMessage()
            ], 500);
        }
    }
}
