<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Refund Processed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2d5a87;
            margin-bottom: 10px;
        }
        .title {
            font-size: 20px;
            color: #333;
            margin: 0;
        }
        .content {
            margin-bottom: 30px;
        }
        .refund-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .reason-section {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        .reason-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .reason-text {
            color: #856404;
            font-style: italic;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 6px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ config('app.name') }}</div>
            <h1 class="title">Payment Refund Processed</h1>
        </div>

        <div class="content">
            <p>Dear {{ $patient_name }},</p>
            
            <p>We are writing to inform you that your payment refund has been successfully processed. The refunded amount will appear in your original payment method within 5-10 business days.</p>

            <div class="refund-details">
                <h3 style="margin-top: 0; color: #28a745;">Refund Details</h3>
                
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">{{ $transaction_id }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Refund Amount:</span>
                    <span class="detail-value">
                        @if($currency === 'USD')
                            ${{ $amount }}
                        @elseif($currency === 'GBP')
                            £{{ $amount }}
                        @elseif($currency === 'EUR')
                            €{{ $amount }}
                        @elseif($currency === 'INR')
                            ₹{{ $amount }}
                        @else
                            {{ $currency }} {{ $amount }}
                        @endif
                    </span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Refund Date:</span>
                    <span class="detail-value">{{ $refund_date }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Provider:</span>
                    <span class="detail-value">{{ $provider_name }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Appointment Date:</span>
                    <span class="detail-value">{{ $appointment_date }}</span>
                </div>
            </div>

            <div class="reason-section">
                <div class="reason-title">Refund Reason:</div>
                <div class="reason-text">"{{ $refund_reason }}"</div>
            </div>

            <p><strong>What happens next?</strong></p>
            <ul>
                <li>The refund will be processed back to your original payment method</li>
                <li>You should see the refund in your account within 5-10 business days</li>
                <li>If you don't see the refund after 10 business days, please contact your bank or card issuer</li>
                <li>You will receive a separate confirmation from your payment provider</li>
            </ul>

            <div class="contact-info">
                <p><strong>Need Help?</strong></p>
                <p>If you have any questions about this refund or need assistance, please don't hesitate to contact our support team. We're here to help!</p>
            </div>

            <p>Thank you for choosing {{ config('app.name') }}. We appreciate your understanding and look forward to serving you again in the future.</p>
        </div>

        <div class="footer">
            <p>Best regards,<br>
            The {{ config('app.name') }} Team</p>
            
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                This is an automated email. Please do not reply to this message.
                <br>
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
