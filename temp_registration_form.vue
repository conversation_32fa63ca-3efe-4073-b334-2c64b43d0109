                <!-- Simple Registration Form -->
                <form v-if="!isLoginMode" class="space-y-5" @submit.prevent="submit">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-medroid-navy mb-2">Create Your Account</h3>
                        <p class="text-medroid-slate">Join <PERSON>dr<PERSON> and start your wellness journey</p>
                        
                        <!-- Selected Plan Info -->
                        <div v-if="selectedPlan" class="mt-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                             :class="form.selected_plan === 'free' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'">
                            {{ selectedPlan.name }} Plan Selected
                        </div>
                    </div>

                    <!-- Name Field -->
                    <div>
                        <input
                            id="name"
                            v-model="form.name"
                            type="text"
                            placeholder="Full Name"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder"
                            required
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <!-- Email Field -->
                    <div>
                        <input
                            id="email"
                            v-model="form.email"
                            type="email"
                            placeholder="Email Address"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder"
                            required
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <!-- Password Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password"
                                v-model="form.password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="Password"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder pr-10"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <button
                                type="button"
                                @click="togglePasswordVisibility"
                                class="absolute inset-y-0 right-3 flex items-center text-medroid-slate hover:text-medroid-navy"
                            >
                                <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L12 12m-2.122-2.122L7.758 7.758M12 12l2.122 2.122m-2.122-2.122L16.242 16.242M12 12l4.242 4.242M9.878 9.878l-2.12-2.121m4.242 4.242L14.121 14.121"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Password Confirmation Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                :type="showPasswordConfirmation ? 'text' : 'password'"
                                placeholder="Confirm Password"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder pr-10"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <button
                                type="button"
                                @click="togglePasswordConfirmationVisibility"
                                class="absolute inset-y-0 right-3 flex items-center text-medroid-slate hover:text-medroid-navy"
                            >
                                <svg v-if="showPasswordConfirmation" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L12 12m-2.122-2.122L7.758 7.758M12 12l2.122 2.122m-2.122-2.122L16.242 16.242M12 12l4.242 4.242M9.878 9.878l-2.12-2.121m4.242 4.242L14.121 14.121"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>

                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Gender Field -->
                        <div>
                            <select
                                v-model="form.gender"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent appearance-none bg-no-repeat bg-right pr-10"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            >
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                                <option value="prefer_not_to_say">Prefer not to say</option>
                            </select>
                            <InputError class="mt-2" :message="form.errors.gender" />
                        </div>

                        <!-- Date of Birth Field -->
                        <div>
                            <input
                                v-model="form.date_of_birth"
                                type="date"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <InputError class="mt-2" :message="form.errors.date_of_birth" />
                        </div>
                    </div>

                    <!-- Referral Code Field -->
                    <div>
                        <input
                            v-model="form.referral_code"
                            type="text"
                            placeholder="Referral Code (Optional)"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder"
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.referral_code" />
                    </div>

                    <!-- Submit Button -->
                    <button
                        type="submit"
                        :disabled="isLoading || (waitlistStatus.enabled && !invitationValid)"
                        :class="[
                            'w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200',
                            isLoading || (waitlistStatus.enabled && !invitationValid) ? 'opacity-50 cursor-not-allowed' : ''
                        ]"
                    >
                        <span v-if="isLoading" class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Creating Account...
                        </span>
                        <span v-else>
                            {{ form.selected_plan === 'free' ? 'Create Free Account' : 'Create Account & Continue to Payment' }}
                        </span>
                    </button>

                    <!-- Footer Information -->
                    <div class="pt-4 text-center">
                        <p class="text-sm text-medroid-slate">
                            Already have an account?
                            <button
                                type="button"
                                @click="toggleMode"
                                class="font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline ml-1"
                            >
                                Sign in
                            </button>
                        </p>
                    </div>
                </form>