<?php

namespace App\Repositories;

use App\Models\Appointment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class AppointmentRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Appointment $model
     * @return void
     */
    public function __construct(Appointment $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find appointments by patient.
     *
     * @param int $patientId
     * @return Collection
     */
    public function findByPatient(int $patientId): Collection
    {
        return $this->findBy(['patient_id' => $patientId]);
    }
    
    /**
     * Find appointments by provider.
     *
     * @param int $providerId
     * @return Collection
     */
    public function findByProvider(int $providerId): Collection
    {
        return $this->findBy(['provider_id' => $providerId]);
    }
    
    /**
     * Find appointments by status.
     *
     * @param string $status
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByStatus(string $status, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('status', $status);
        
        if ($clinicId) {
            $query->whereHas('provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->get();
    }
    
    /**
     * Find appointments by date range.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $clinicId
     * @return Collection
     */
    public function findByDateRange(Carbon $startDate, Carbon $endDate, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->whereBetween('scheduled_at', [$startDate, $endDate]);
        
        if ($clinicId) {
            $query->whereHas('provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->get();
    }
    
    /**
     * Find upcoming appointments.
     *
     * @param int|null $patientId
     * @param int|null $providerId
     * @return Collection
     */
    public function findUpcoming(?int $patientId = null, ?int $providerId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->where('scheduled_at', '>', now())
              ->whereIn('status', ['scheduled', 'confirmed']);
        
        if ($patientId) {
            $query->where('patient_id', $patientId);
        }
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        return $query->orderBy('scheduled_at')->get();
    }
    
    /**
     * Find today's appointments.
     *
     * @param int|null $providerId
     * @param int|null $clinicId
     * @return Collection
     */
    public function findToday(?int $providerId = null, ?int $clinicId = null): Collection
    {
        $query = $this->model->newQuery();
        $query->whereDate('scheduled_at', today());
        
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }
        
        if ($clinicId) {
            $query->whereHas('provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        return $query->orderBy('scheduled_at')->get();
    }
    
    /**
     * Check for scheduling conflicts.
     *
     * @param int $providerId
     * @param Carbon $scheduledAt
     * @param int $duration
     * @param int|null $excludeAppointmentId
     * @return bool
     */
    public function hasConflict(int $providerId, Carbon $scheduledAt, int $duration, ?int $excludeAppointmentId = null): bool
    {
        $endTime = $scheduledAt->copy()->addMinutes($duration);
        
        $query = $this->model->newQuery();
        $query->where('provider_id', $providerId)
              ->where('status', '!=', 'cancelled')
              ->where(function ($q) use ($scheduledAt, $endTime) {
                  $q->whereBetween('scheduled_at', [$scheduledAt, $endTime])
                    ->orWhere(function ($subQ) use ($scheduledAt, $endTime) {
                        $subQ->where('scheduled_at', '<=', $scheduledAt)
                             ->whereRaw('DATE_ADD(scheduled_at, INTERVAL duration_minutes MINUTE) > ?', [$scheduledAt]);
                    });
              });
        
        if ($excludeAppointmentId) {
            $query->where('id', '!=', $excludeAppointmentId);
        }
        
        return $query->exists();
    }
    
    /**
     * Get appointments with pagination and filters.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        $query->with(['patient.user', 'provider.user', 'service']);
        
        // Apply filters
        if (!empty($filters['patient_id'])) {
            $query->where('patient_id', $filters['patient_id']);
        }
        
        if (!empty($filters['provider_id'])) {
            $query->where('provider_id', $filters['provider_id']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->whereDate('scheduled_at', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->whereDate('scheduled_at', '<=', $filters['date_to']);
        }
        
        if (isset($filters['is_telemedicine'])) {
            $query->where('is_telemedicine', $filters['is_telemedicine']);
        }
        
        return $query->orderBy('scheduled_at', 'desc')->paginate($perPage);
    }
    
    /**
     * Get appointment statistics.
     *
     * @param int|null $clinicId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getStatistics(?int $clinicId = null, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = $this->model->newQuery();
        
        if ($clinicId) {
            $query->whereHas('provider', function ($q) use ($clinicId) {
                $q->where('clinic_id', $clinicId);
            });
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('scheduled_at', [$startDate, $endDate]);
        }
        
        $total = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $cancelled = $query->where('status', 'cancelled')->count();
        $upcoming = $query->where('scheduled_at', '>', now())->whereIn('status', ['scheduled', 'confirmed'])->count();
        
        return [
            'total' => $total,
            'completed' => $completed,
            'cancelled' => $cancelled,
            'upcoming' => $upcoming,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'cancellation_rate' => $total > 0 ? round(($cancelled / $total) * 100, 2) : 0,
        ];
    }
    
    /**
     * Update appointment status.
     *
     * @param int $appointmentId
     * @param string $status
     * @return bool
     */
    public function updateStatus(int $appointmentId, string $status): bool
    {
        $appointment = $this->update($appointmentId, ['status' => $status]);
        return $appointment !== null;
    }
}
