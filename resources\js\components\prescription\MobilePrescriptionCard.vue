<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <!-- Swipe Actions Container -->
    <div 
      class="relative"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      :style="{ transform: `translateX(${swipeOffset}px)` }"
    >
      <!-- Left Swipe Actions (Quick PDF) -->
      <div 
        class="absolute left-0 top-0 h-full w-20 bg-green-500 flex items-center justify-center"
        :class="{ 'opacity-100': swipeOffset > 40, 'opacity-0': swipeOffset <= 40 }"
      >
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>

      <!-- Right Swipe Actions (Quick Email) -->
      <div 
        class="absolute right-0 top-0 h-full w-20 bg-purple-500 flex items-center justify-center"
        :class="{ 'opacity-100': swipeOffset < -40, 'opacity-0': swipeOffset >= -40 }"
      >
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
      </div>

      <!-- Main Card Content -->
      <div class="p-4 bg-white">
        <div class="flex items-start justify-between">
          <!-- Prescription Info -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-2">
              <!-- Selection Checkbox -->
              <input 
                type="checkbox" 
                :value="prescription.id"
                :checked="isSelected"
                @change="$emit('toggle-selection', prescription.id)"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              
              <h3 class="text-base font-medium text-gray-800 truncate">
                {{ prescription.medicationName }}
              </h3>
            </div>
            
            <div class="space-y-1 text-sm text-gray-600">
              <div class="flex items-center gap-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {{ prescription.frequency }}
                </span>
                <span class="text-gray-400">•</span>
                <span>{{ formatDuration(prescription.duration) }}</span>
              </div>
              
              <div v-if="prescription.dosage" class="text-gray-500">
                Dosage: {{ prescription.dosage }}
              </div>
              
              <div v-if="prescription.instructions" class="text-gray-500 line-clamp-2">
                {{ prescription.instructions }}
              </div>
            </div>
          </div>

          <!-- Quick Actions Menu -->
          <div class="ml-3 flex-shrink-0">
            <button 
              @click="showActions = !showActions"
              class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Expanded Actions -->
        <div v-if="showActions" class="mt-3 pt-3 border-t border-gray-100">
          <div class="grid grid-cols-2 gap-2">
            <button 
              @click="$emit('view', prescription.id)"
              class="flex items-center justify-center gap-2 p-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              View
            </button>
            
            <button 
              @click="$emit('quick-pdf', prescription.id)"
              :disabled="pdfLoading"
              class="flex items-center justify-center gap-2 p-2 text-sm font-medium text-green-600 bg-green-50 rounded-md hover:bg-green-100 disabled:opacity-50"
            >
              <svg v-if="pdfLoading" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              PDF
            </button>
            
            <button 
              @click="$emit('quick-email', prescription.id)"
              :disabled="emailLoading"
              class="flex items-center justify-center gap-2 p-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-md hover:bg-purple-100 disabled:opacity-50"
            >
              <svg v-if="emailLoading" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              Email
            </button>
            
            <button 
              @click="$emit('edit', prescription)"
              class="flex items-center justify-center gap-2 p-2 text-sm font-medium text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit
            </button>
          </div>
        </div>

        <!-- Swipe Hint -->
        <div v-if="showSwipeHint" class="mt-2 text-xs text-gray-400 text-center">
          ← Swipe for PDF • Swipe for Email →
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  prescription: Object,
  isSelected: Boolean,
  pdfLoading: Boolean,
  emailLoading: Boolean,
  showSwipeHint: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'toggle-selection',
  'view',
  'quick-pdf',
  'quick-email',
  'edit',
  'delete'
])

// State
const showActions = ref(false)
const swipeOffset = ref(0)
const startX = ref(0)
const isSwiping = ref(false)

// Touch handlers for swipe actions
const handleTouchStart = (e) => {
  startX.value = e.touches[0].clientX
  isSwiping.value = true
}

const handleTouchMove = (e) => {
  if (!isSwiping.value) return
  
  const currentX = e.touches[0].clientX
  const diff = currentX - startX.value
  
  // Limit swipe distance
  swipeOffset.value = Math.max(-80, Math.min(80, diff))
}

const handleTouchEnd = () => {
  if (!isSwiping.value) return
  
  // Trigger actions based on swipe distance
  if (swipeOffset.value > 60) {
    // Swipe right - Quick PDF
    emit('quick-pdf', props.prescription.id)
  } else if (swipeOffset.value < -60) {
    // Swipe left - Quick Email
    emit('quick-email', props.prescription.id)
  }
  
  // Reset swipe
  swipeOffset.value = 0
  isSwiping.value = false
}

// Helper methods
const formatDuration = (duration) => {
  if (!duration) return 'Not specified'
  
  // Handle different duration formats
  if (typeof duration === 'string') {
    return duration
  }
  
  if (typeof duration === 'number') {
    return `${duration} days`
  }
  
  return duration.toString()
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions for swipe */
.relative {
  transition: transform 0.2s ease-out;
}
</style>
