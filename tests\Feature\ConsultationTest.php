<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use App\Models\Consultation;
use App\Models\Appointment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class ConsultationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $clinician;
    protected $patient;
    protected $provider;
    protected $clinic;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the roles and permissions seeder
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
        
        // Create test data
        $this->clinic = Clinic::factory()->create();
        
        $this->clinician = User::factory()->create([
            'is_clinician' => true
        ]);
        $this->clinician->assignRole('provider');
        
        $this->provider = Provider::factory()->create([
            'user_id' => $this->clinician->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        $patientUser = User::factory()->create([
            'is_clinician' => false
        ]);
        $patientUser->assignRole('patient');
        
        $this->patient = Patient::factory()->create([
            'user_id' => $patientUser->id
        ]);
    }

    /** @test */
    public function clinician_can_create_consultation()
    {
        Sanctum::actingAs($this->clinician);
        
        $consultationData = [
            'patient_id' => $this->patient->id,
            'consultation_type' => 'general',
            'consultation_date' => now()->toDateString(),
            'consultation_mode' => 'in_person',
            'chief_complaint' => 'Headache and fever',
            'history_of_present_illness' => 'Patient reports headache for 2 days',
            'assessment' => 'Possible viral infection',
            'diagnosis' => 'Viral syndrome',
            'treatment_plan' => 'Rest and fluids',
            'is_telemedicine' => false,
        ];
        
        $response = $this->postJson('/api/consultations', $consultationData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'patient_id',
                        'provider_id',
                        'clinic_id',
                        'consultation_type',
                        'status',
                        'consultation_date',
                        'chief_complaint',
                        'diagnosis',
                        'treatment_plan'
                    ]
                ]);
        
        $this->assertDatabaseHas('consultations', [
            'patient_id' => $this->patient->id,
            'provider_id' => $this->provider->id,
            'chief_complaint' => 'Headache and fever',
            'status' => 'draft'
        ]);
    }

    /** @test */
    public function clinician_can_view_their_consultations()
    {
        Sanctum::actingAs($this->clinician);
        
        // Create some consultations
        Consultation::factory()->count(3)->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        $response = $this->getJson('/api/consultations');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'patient_id',
                                'provider_id',
                                'consultation_type',
                                'status',
                                'consultation_date'
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function clinician_can_update_consultation()
    {
        Sanctum::actingAs($this->clinician);
        
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'draft'
        ]);
        
        $updateData = [
            'status' => 'completed',
            'diagnosis' => 'Updated diagnosis',
            'treatment_plan' => 'Updated treatment plan'
        ];
        
        $response = $this->putJson("/api/consultations/{$consultation->id}", $updateData);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('consultations', [
            'id' => $consultation->id,
            'status' => 'completed',
            'diagnosis' => 'Updated diagnosis',
            'treatment_plan' => 'Updated treatment plan'
        ]);
    }

    /** @test */
    public function clinician_cannot_access_other_providers_consultations()
    {
        // Create another clinician and provider
        $otherClinician = User::factory()->create(['is_clinician' => true]);
        $otherProvider = Provider::factory()->create([
            'user_id' => $otherClinician->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        $otherConsultation = Consultation::factory()->create([
            'provider_id' => $otherProvider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        Sanctum::actingAs($this->clinician);
        
        $response = $this->getJson("/api/consultations/{$otherConsultation->id}");
        
        $response->assertStatus(403);
    }

    /** @test */
    public function clinician_can_create_consultation_from_appointment()
    {
        Sanctum::actingAs($this->clinician);
        
        $appointment = Appointment::factory()->create([
            'patient_id' => $this->patient->id,
            'provider_id' => $this->provider->id,
            'reason' => 'Regular checkup',
            'is_telemedicine' => false
        ]);
        
        $response = $this->postJson("/api/consultations/from-appointment/{$appointment->id}");
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'appointment_id',
                        'patient_id',
                        'provider_id',
                        'chief_complaint'
                    ]
                ]);
        
        $this->assertDatabaseHas('consultations', [
            'appointment_id' => $appointment->id,
            'patient_id' => $this->patient->id,
            'provider_id' => $this->provider->id,
            'chief_complaint' => 'Regular checkup',
            'status' => 'in_progress'
        ]);
    }

    /** @test */
    public function consultation_validation_works_correctly()
    {
        Sanctum::actingAs($this->clinician);
        
        $invalidData = [
            'patient_id' => 999, // Non-existent patient
            'consultation_type' => 'invalid_type',
            'consultation_date' => 'invalid_date',
            'consultation_mode' => 'invalid_mode'
        ];
        
        $response = $this->postJson('/api/consultations', $invalidData);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'patient_id',
                    'consultation_type',
                    'consultation_date',
                    'consultation_mode'
                ]);
    }

    /** @test */
    public function clinician_can_delete_draft_consultation()
    {
        Sanctum::actingAs($this->clinician);
        
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'draft'
        ]);
        
        $response = $this->deleteJson("/api/consultations/{$consultation->id}");
        
        $response->assertStatus(200);
        
        $this->assertDatabaseMissing('consultations', [
            'id' => $consultation->id
        ]);
    }

    /** @test */
    public function clinician_cannot_delete_completed_consultation()
    {
        Sanctum::actingAs($this->clinician);
        
        $consultation = Consultation::factory()->create([
            'provider_id' => $this->provider->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'completed'
        ]);
        
        $response = $this->deleteJson("/api/consultations/{$consultation->id}");
        
        $response->assertStatus(400)
                ->assertJson(['message' => 'Only draft consultations can be deleted.']);
        
        $this->assertDatabaseHas('consultations', [
            'id' => $consultation->id
        ]);
    }
}
