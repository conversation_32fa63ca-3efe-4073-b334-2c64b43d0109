<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class SyncUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:sync-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync user role field with Spatie roles and permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing user roles with <PERSON>tie roles...');

        $users = User::whereNotNull('role')->get();
        $synced = 0;
        $errors = 0;

        foreach ($users as $user) {
            if ($user->role && !$user->hasRole($user->role)) {
                $role = Role::where('name', $user->role)->first();
                if ($role) {
                    $user->assignRole($user->role);
                    $this->line("✓ Assigned '{$user->role}' role to {$user->name} (ID: {$user->id})");
                    $synced++;
                } else {
                    $this->error("✗ Role '{$user->role}' not found for user {$user->name} (ID: {$user->id})");
                    $errors++;
                }
            } else {
                $this->line("- User {$user->name} already has correct role assignment");
            }
        }

        $this->info("Sync completed:");
        $this->info("- Synced: {$synced} users");
        $this->info("- Errors: {$errors} users");
        $this->info("- Total processed: " . $users->count() . " users");

        return 0;
    }
}
