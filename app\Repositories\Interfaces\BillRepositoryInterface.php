<?php

namespace App\Repositories\Interfaces;

use App\Models\Bill;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface BillRepositoryInterface
{
    public function getAllByClinic(int $clinicId, int $perPage = 20): LengthAwarePaginator;
    public function create(array $data): Bill;
    public function update(Bill $bill, array $data): Bill;
    public function delete(Bill $bill): bool;
    public function findById(int $id): ?Bill;
    public function findByIdWithRelations(int $id, array $relations = []): ?Bill;
    public function getUnpaidByClinic(int $clinicId): Collection;
    public function getByPatient(int $patientId): Collection;
}
