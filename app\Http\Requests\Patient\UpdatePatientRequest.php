<?php

namespace App\Http\Requests\Patient;

use App\Http\Requests\BaseFormRequest;

class UpdatePatientRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        $user = auth()->user();
        $patientId = $this->route('id');

        // Check if user has edit patients permission
        if (!$user->can('edit patients')) {
            return false;
        }

        // Admin can edit any patient
        if ($user->hasRole('admin')) {
            return true;
        }

        // Get the patient to check clinic access
        $patient = \App\Models\Patient::find($patientId);
        if (!$patient) {
            return false;
        }

        // Clinic admin and provider can only edit patients from their clinic
        if ($user->hasRole(['clinic_admin', 'provider'])) {
            return $patient->clinic_id === $user->clinic_id;
        }

        // Patient can edit their own record
        if ($user->hasRole('patient') && $user->patient) {
            return $user->patient->id === $patient->id;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $patientId = $this->route('id');
        $patient = \App\Models\Patient::find($patientId);
        $userId = $patient ? $patient->user_id : null;

        return [
            // Basic details
            'patient_unique_id' => 'nullable|string|max:50|unique:patients,patient_unique_id,' . $patientId,
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users,email,' . $userId,
            'phone_number' => 'nullable|string|max:20',
            'country_code' => 'nullable|string|max:10',
            'date_of_birth' => 'nullable|date|before:today',
            'nhs_number' => 'nullable|string|max:20',
            'registered_gp_name' => 'nullable|string|max:255',
            'registered_gp_address' => 'nullable|string|max:500',
            'gender' => 'nullable|string|in:male,female,other',
            'clinic_id' => 'nullable|exists:clinics,id',

            // Other details
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'post_code' => 'nullable|string|max:20',
            'emergency_contact' => 'nullable|string|max:255',

            // Medical information
            'medical_history' => 'nullable|string',
            'current_medications' => 'nullable|string',

            // Legacy fields for backward compatibility
            'name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'state' => 'nullable|string|max:100',
            'zip_code' => 'nullable|string|max:20',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email is already registered.',
            'patient_unique_id.unique' => 'This patient unique ID is already in use.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
            'gender.in' => 'Gender must be one of: male, female, other.',
            'clinic_id.exists' => 'The selected clinic is not valid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'patient_unique_id' => 'patient unique ID',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'date_of_birth' => 'date of birth',
            'phone_number' => 'phone number',
            'nhs_number' => 'NHS number',
            'registered_gp_name' => 'registered GP name',
            'registered_gp_address' => 'registered GP address',
            'postal_code' => 'postal code',
            'emergency_contact' => 'emergency contact',
            'medical_history' => 'medical history',
            'current_medications' => 'current medications',
            'zip_code' => 'ZIP/postal code',
        ];
    }
}
