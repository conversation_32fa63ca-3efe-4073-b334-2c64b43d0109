/**
 * Doctor Settings API Composable
 * 
 * This composable provides methods for managing doctor settings,
 * digital signatures, and letter templates.
 */

import { ref, type Ref } from 'vue'
import { useApi } from './useApi'

export interface DoctorSettings {
  id?: number
  user_id?: number
  professional_prefix?: string
  registration_number?: string
  registration_body?: string
  digital_signature?: string
  signature_format: string
  letter_templates?: Record<string, any>
  consultation_templates?: Record<string, any>
  practice_name?: string
  practice_address?: string
  practice_phone?: string
  practice_email?: string
  include_signature_in_letters: boolean
  include_registration_in_letters: boolean
  default_letter_settings?: Record<string, any>
  created_at?: string
  updated_at?: string
}

export interface LetterTemplate {
  name: string
  content: string
}

export interface MedicalLetter {
  id: number
  consultation_id?: number
  patient_id: number
  created_by: number
  clinic_id?: number
  letter_type: string
  template_name?: string
  recipient_name?: string
  recipient_title?: string
  recipient_address?: string
  subject: string
  content: string
  additional_notes?: string
  letter_data?: Record<string, any>
  status: 'draft' | 'final' | 'sent' | 'archived'
  letter_date: string
  sent_date?: string
  sent_method?: string
  include_signature: boolean
  include_letterhead: boolean
  file_path?: string
  attachments?: string[]
  created_at: string
  updated_at: string
  patient?: any
  consultation?: any
  clinic?: any
  creator?: any
}

export function useDoctorSettingsApi() {
  const api = useApi()
  const settings: Ref<DoctorSettings | null> = ref(null)
  const letterTemplates: Ref<Record<string, LetterTemplate>> = ref({})
  const medicalLetters: Ref<MedicalLetter[]> = ref([])
  const currentLetter: Ref<MedicalLetter | null> = ref(null)

  // Doctor Settings methods
  const getSettings = async () => {
    const response = await api.get('/doctor-settings')
    if (response?.data) {
      settings.value = response.data
    }
    return response
  }

  const updateSettings = async (data: Partial<DoctorSettings>) => {
    const response = await api.put('/doctor-settings', data)
    if (response?.data) {
      settings.value = response.data
    }
    return response
  }

  const uploadSignature = async (file: File) => {
    const formData = new FormData()
    formData.append('signature', file)
    
    const response = await api.post('/api/doctor-settings/signature', formData)
    if (response?.data && settings.value) {
      // Update the settings with signature info
      await getSettings()
    }
    return response
  }

  const removeSignature = async () => {
    const response = await api.delete('/api/doctor-settings/signature')
    if (response && settings.value) {
      settings.value.digital_signature = undefined
    }
    return response
  }

  // Letter Templates methods
  const getLetterTemplates = async () => {
    const response = await api.get('/api/doctor-settings/letter-templates')
    if (response?.data) {
      letterTemplates.value = {
        ...response.data.default_templates,
        ...response.data.custom_templates
      }
    }
    return response
  }

  const saveLetterTemplate = async (templateKey: string, templateName: string, templateContent: string) => {
    const response = await api.post('/api/doctor-settings/letter-templates', {
      template_key: templateKey,
      template_name: templateName,
      template_content: templateContent
    })
    if (response?.data) {
      letterTemplates.value = response.data
    }
    return response
  }

  // Medical Letters methods
  const getMedicalLetters = async (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    const response = await api.get(`/medical-letters${queryString}`)
    if (response?.data) {
      medicalLetters.value = response.data.data || response.data
    }
    return response
  }

  const getMedicalLetter = async (id: number) => {
    const response = await api.get(`/medical-letters/${id}`)
    if (response?.data) {
      currentLetter.value = response.data
    }
    return response
  }

  const createMedicalLetter = async (data: Partial<MedicalLetter>) => {
    const response = await api.post('/api/medical-letters', data)
    if (response?.data) {
      medicalLetters.value.unshift(response.data)
      currentLetter.value = response.data
    }
    return response
  }

  const updateMedicalLetter = async (id: number, data: Partial<MedicalLetter>) => {
    const response = await api.put(`/api/medical-letters/${id}`, data)
    if (response?.data) {
      const index = medicalLetters.value.findIndex(l => l.id === id)
      if (index !== -1) {
        medicalLetters.value[index] = response.data
      }
      if (currentLetter.value?.id === id) {
        currentLetter.value = response.data
      }
    }
    return response
  }

  const deleteMedicalLetter = async (id: number) => {
    const success = await api.delete(`/medical-letters/${id}`)
    if (success) {
      medicalLetters.value = medicalLetters.value.filter(l => l.id !== id)
      if (currentLetter.value?.id === id) {
        currentLetter.value = null
      }
    }
    return success
  }

  const generateLetterPdf = async (id: number) => {
    const response = await api.post(`/medical-letters/${id}/generate-pdf`)
    if (response?.data && currentLetter.value?.id === id) {
      currentLetter.value.file_path = response.data.file_path
      currentLetter.value.status = 'final'
    }
    return response
  }

  const downloadLetter = async (id: number) => {
    // This will trigger a download
    window.open(`/medical-letters/${id}/download`, '_blank')
  }

  const markLetterAsSent = async (id: number, sentMethod: string, sentDate?: string) => {
    const response = await api.post(`/api/medical-letters/${id}/mark-sent`, {
      sent_method: sentMethod,
      sent_date: sentDate
    })
    if (response?.data) {
      const index = medicalLetters.value.findIndex(l => l.id === id)
      if (index !== -1) {
        medicalLetters.value[index] = response.data
      }
      if (currentLetter.value?.id === id) {
        currentLetter.value = response.data
      }
    }
    return response
  }

  // Helper methods
  const hasDigitalSignature = () => {
    return settings.value?.digital_signature ? true : false
  }

  const getSignatureDataUrl = () => {
    if (!settings.value?.digital_signature) return null
    const mimeType = `image/${settings.value.signature_format}`
    return `data:${mimeType};base64,${settings.value.digital_signature}`
  }

  const getFullProfessionalName = () => {
    if (!settings.value) return ''
    const prefix = settings.value.professional_prefix ? `${settings.value.professional_prefix} ` : ''
    return prefix + (settings.value.user_id ? 'Doctor Name' : '') // Would need user name from context
  }

  const getRegistrationDisplay = () => {
    if (!settings.value?.registration_number || !settings.value?.registration_body) return null
    return `${settings.value.registration_body}: ${settings.value.registration_number}`
  }

  return {
    // State
    settings,
    letterTemplates,
    medicalLetters,
    currentLetter,
    loading: api.loading,
    error: api.error,

    // Settings methods
    getSettings,
    updateSettings,
    uploadSignature,
    removeSignature,

    // Template methods
    getLetterTemplates,
    saveLetterTemplate,

    // Medical letter methods
    getMedicalLetters,
    getMedicalLetter,
    createMedicalLetter,
    updateMedicalLetter,
    deleteMedicalLetter,
    generateLetterPdf,
    downloadLetter,
    markLetterAsSent,

    // Helper methods
    hasDigitalSignature,
    getSignatureDataUrl,
    getFullProfessionalName,
    getRegistrationDisplay,

    // Utility methods
    reset: api.reset,
    clearError: api.clearError
  }
}
