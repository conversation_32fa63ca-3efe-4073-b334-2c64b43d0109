<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Order Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .order-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .item { border-bottom: 1px solid #eee; padding: 10px 0; }
        .item:last-child { border-bottom: none; }
        .total { font-weight: bold; font-size: 18px; color: #3B82F6; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmation</h1>
            <p>Thank you for your order!</p>
        </div>
        
        <div class="content">
            <h2>Order #{{ $order->order_number }}</h2>
            <p>Hi {{ $order->user->name }},</p>
            <p>We've received your order and it's being processed. Here are the details:</p>
            
            <div class="order-details">
                <h3>Order Items:</h3>
                @foreach($orderItems as $item)
                <div class="item">
                    <strong>{{ $item->product->name }}</strong><br>
                    Quantity: {{ $item->quantity }}<br>
                    Price: £{{ number_format($item->price, 2) }}<br>
                    Subtotal: £{{ number_format($item->quantity * $item->price, 2) }}
                </div>
                @endforeach
                
                <div class="item total">
                    Total: £{{ number_format($total, 2) }}
                </div>
            </div>
            
            <div class="order-details">
                <h3>Shipping Information:</h3>
                <p>
                    {{ $order->shipping_name }}<br>
                    {{ $order->shipping_address }}<br>
                    {{ $order->shipping_city }}, {{ $order->shipping_postal_code }}<br>
                    {{ $order->shipping_country }}
                </p>
            </div>
            
            <p>We'll send you another email when your order ships.</p>
        </div>
        
        <div class="footer">
            <p>Thank you for choosing {{ config('app.name') }}!</p>
            <p>If you have any questions, please contact us.</p>
        </div>
    </div>
</body>
</html>
