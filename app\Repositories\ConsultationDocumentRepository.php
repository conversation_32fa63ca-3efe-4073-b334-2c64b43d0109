<?php

namespace App\Repositories;

use App\Models\ConsultationDocument;
use Illuminate\Database\Eloquent\Collection;

class ConsultationDocumentRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param ConsultationDocument $model
     * @return void
     */
    public function __construct(ConsultationDocument $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find documents by consultation.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findByConsultation(int $consultationId): Collection
    {
        $query = $this->model->newQuery();
        $query->where('consultation_id', $consultationId)
              ->with('uploader')
              ->orderBy('created_at', 'desc');
        
        return $query->get();
    }
    
    /**
     * Find documents by type.
     *
     * @param int $consultationId
     * @param string $documentType
     * @return Collection
     */
    public function findByType(int $consultationId, string $documentType): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'document_type' => $documentType
        ]);
    }
    
    /**
     * Find documents by uploader.
     *
     * @param int $consultationId
     * @param int $uploaderId
     * @return Collection
     */
    public function findByUploader(int $consultationId, int $uploaderId): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'uploaded_by' => $uploaderId
        ]);
    }
}
