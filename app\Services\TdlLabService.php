<?php

namespace App\Services;

use App\Repositories\Interfaces\ClinicTdlSettingRepositoryInterface;
use App\Repositories\Interfaces\LabTestRequestRepositoryInterface;
use App\Repositories\Interfaces\LabTestResultRepositoryInterface;
use App\Models\LabTestRequest;
use App\Models\LabTestResult;
use App\Models\ClinicTdlSetting;
use Exception;

class TdlLabService
{
    public function __construct(
        private ClinicTdlSettingRepositoryInterface $tdlSettingsRepository,
        private LabTestRequestRepositoryInterface $requestRepository,
        private LabTestResultRepositoryInterface $resultRepository,
        private TdlTestCatalogService $catalogService,
        private HL7MessageService $hl7Service,
        private AzureBlobService $azureService
    ) {}

    /**
     * Create a new lab test request
     */
    public function createLabRequest(array $data): LabTestRequest
    {
        try {
            // Validate clinic TDL configuration
            $tdlSettings = $this->tdlSettingsRepository->findByClinic($data['clinic_id']);
            if (!$tdlSettings || !$tdlSettings->isConfigured()) {
                throw new Exception('TDL integration not configured for this clinic');
            }

            // Validate selected tests
            $testValidation = $this->catalogService->validateTestSelection($data['test_codes']);
            if (!$testValidation['is_valid']) {
                throw new Exception('Invalid test codes: ' . implode(', ', $testValidation['invalid_codes']));
            }

            // Format tests for storage
            $tests = $this->catalogService->formatTestsForRequest($data['test_codes']);
            
            // Prepare request data
            $requestData = [
                'clinic_id' => $data['clinic_id'],
                'patient_id' => $data['patient_id'],
                'provider_id' => $data['provider_id'],
                'consultation_id' => $data['consultation_id'] ?? null,
                'tests' => $tests,
                'request_data' => [
                    'clinical_notes' => $data['clinical_notes'] ?? '',
                    'urgent' => $data['urgent'] ?? false,
                    'fasting_status' => $data['fasting_status'] ?? false,
                    'collection_date' => $data['collection_date'] ?? now()->toDateString(),
                    'total_cost' => $this->catalogService->calculateTotalCost($data['test_codes']),
                    'requirements' => $this->catalogService->getTestRequirements($data['test_codes'])
                ],
                'status' => 'pending'
            ];

            // Create the request
            $request = $this->requestRepository->create($requestData);

            // Generate HL7 message
            $hl7Message = $this->hl7Service->generateOrderMessage($request);
            
            // Update request with HL7 message
            $this->requestRepository->update($request, ['hl7_message' => $hl7Message]);

            // Auto-send if configured
            if ($tdlSettings->getSetting('auto_send_requests', true)) {
                $this->sendRequestToTDL($request->id);
            }

            return $request->fresh();

        } catch (Exception $e) {
            throw new Exception('Failed to create lab request: ' . $e->getMessage());
        }
    }

    /**
     * Send lab request to TDL via Azure
     */
    public function sendRequestToTDL(int $requestId): bool
    {
        try {
            $request = $this->requestRepository->findByIdWithRelations($requestId, ['clinic']);
            if (!$request) {
                throw new Exception('Lab request not found');
            }

            if ($request->status !== 'pending') {
                throw new Exception('Request is not in pending status');
            }

            // Get TDL settings
            $tdlSettings = $this->tdlSettingsRepository->findByClinic($request->clinic_id);
            if (!$tdlSettings || !$tdlSettings->isConfigured()) {
                throw new Exception('TDL integration not configured for this clinic');
            }

            // Upload to Azure
            $azureFilePath = $this->azureService->uploadRequest(
                $tdlSettings,
                (string)$request->id,
                $request->hl7_message
            );

            // Update request status
            $this->requestRepository->update($request, [
                'status' => 'sent',
                'azure_file_path' => $azureFilePath,
                'sent_at' => now()
            ]);

            return true;

        } catch (Exception $e) {
            // Mark request as failed
            if (isset($request)) {
                $this->requestRepository->update($request, ['status' => 'failed']);
            }
            throw new Exception('Failed to send request to TDL: ' . $e->getMessage());
        }
    }

    /**
     * Process incoming lab results
     */
    public function processResults(int $clinicId): array
    {
        try {
            $tdlSettings = $this->tdlSettingsRepository->findByClinic($clinicId);
            if (!$tdlSettings || !$tdlSettings->isConfigured()) {
                throw new Exception('TDL integration not configured for this clinic');
            }

            // Poll Azure for new results
            $resultFiles = $this->azureService->pollForResults($tdlSettings);
            $processedResults = [];

            foreach ($resultFiles as $file) {
                try {
                    $result = $this->processResultFile($file, $clinicId);
                    if ($result) {
                        $processedResults[] = $result;
                        
                        // Delete processed file if configured
                        if ($tdlSettings->getSetting('auto_cleanup_files', true)) {
                            $this->azureService->deleteFile($tdlSettings, $file['file_name']);
                        }
                    }
                } catch (Exception $e) {
                    // Log error but continue processing other files
                    error_log("Error processing result file {$file['file_name']}: " . $e->getMessage());
                    continue;
                }
            }

            return $processedResults;

        } catch (Exception $e) {
            throw new Exception('Failed to process results: ' . $e->getMessage());
        }
    }

    /**
     * Process individual result file
     */
    private function processResultFile(array $file, int $clinicId): ?LabTestResult
    {
        try {
            // Parse result content based on format
            if ($file['format'] === 'hl7') {
                $resultData = $this->hl7Service->parseResultMessage($file['content']);
            } else {
                $resultData = json_decode($file['content'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception('Invalid JSON format in result file');
                }
            }

            // Extract order number or lab reference to find matching request
            $orderNumber = $this->extractOrderNumber($file['file_name'], $resultData);
            $request = null;
            
            if ($orderNumber) {
                $request = $this->requestRepository->findByOrderNumber($orderNumber);
            }

            // Check if result already exists
            $labReference = $resultData['lab_reference'] ?? $file['file_name'];
            $existingResult = $this->resultRepository->findByLabReference($labReference);
            
            if ($existingResult) {
                return null; // Skip duplicate
            }

            // Create result record
            $resultRecord = [
                'request_id' => $request?->id,
                'clinic_id' => $clinicId,
                'patient_id' => $request?->patient_id ?? $this->findPatientByResult($resultData),
                'order_number' => $orderNumber,
                'lab_reference_id' => $labReference,
                'status' => 'received',
                'result_data' => $resultData,
                'azure_file_path' => $file['file_name'],
                'received_at' => now()
            ];

            $result = $this->resultRepository->create($resultRecord);

            // Auto-process if configured
            $tdlSettings = $this->tdlSettingsRepository->findByClinic($clinicId);
            if ($tdlSettings && $tdlSettings->getSetting('auto_process_results', true)) {
                $this->markResultAsProcessed($result->id);
            }

            // Update request status if found
            if ($request) {
                $this->requestRepository->update($request, [
                    'status' => 'completed',
                    'completed_at' => now()
                ]);
            }

            return $result;

        } catch (Exception $e) {
            throw new Exception('Failed to process result file: ' . $e->getMessage());
        }
    }

    /**
     * Mark result as processed
     */
    public function markResultAsProcessed(int $resultId): LabTestResult
    {
        $result = $this->resultRepository->findById($resultId);
        if (!$result) {
            throw new Exception('Lab result not found');
        }

        return $this->resultRepository->update($result, [
            'status' => 'completed',
            'processed_at' => now()
        ]);
    }

    /**
     * Mark result as reviewed by physician
     */
    public function markResultAsReviewed(int $resultId, int $reviewerId, string $notes = null): LabTestResult
    {
        $result = $this->resultRepository->findById($resultId);
        if (!$result) {
            throw new Exception('Lab result not found');
        }

        return $this->resultRepository->update($result, [
            'status' => 'reviewed',
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'physician_notes' => $notes
        ]);
    }

    /**
     * Get lab requests for clinic
     */
    public function getLabRequests(int $clinicId, int $perPage = 20)
    {
        return $this->requestRepository->getAllByClinic($clinicId, $perPage);
    }

    /**
     * Get lab results for clinic
     */
    public function getLabResults(int $clinicId, int $perPage = 20)
    {
        return $this->resultRepository->getAllByClinic($clinicId, $perPage);
    }

    /**
     * Get requests by patient
     */
    public function getPatientRequests(int $patientId)
    {
        return $this->requestRepository->getByPatient($patientId);
    }

    /**
     * Get results by patient
     */
    public function getPatientResults(int $patientId)
    {
        return $this->resultRepository->getByPatient($patientId);
    }

    /**
     * Get TDL settings for clinic
     */
    public function getTdlSettings(int $clinicId): ?ClinicTdlSetting
    {
        return $this->tdlSettingsRepository->findByClinic($clinicId);
    }

    /**
     * Update TDL settings for clinic
     */
    public function updateTdlSettings(int $clinicId, array $data): ClinicTdlSetting
    {
        $settings = $this->tdlSettingsRepository->findByClinic($clinicId);
        
        if ($settings) {
            return $this->tdlSettingsRepository->update($settings, $data);
        } else {
            $data['clinic_id'] = $clinicId;
            return $this->tdlSettingsRepository->create($data);
        }
    }

    /**
     * Test TDL connection for clinic
     */
    public function testConnection(int $clinicId): bool
    {
        $settings = $this->tdlSettingsRepository->findByClinic($clinicId);
        if (!$settings || !$settings->azure_connection_string) {
            return false;
        }

        return $this->azureService->testConnection($settings->azure_connection_string);
    }

    /**
     * Helper methods
     */
    private function extractOrderNumber(string $fileName, array $resultData): ?string
    {
        // Try to extract from filename first
        if (preg_match('/REQ_CLINIC_\d+_(\w+)_/', $fileName, $matches)) {
            return $matches[1];
        }

        // Try to extract from result data
        return $resultData['order_number'] ?? null;
    }

    private function findPatientByResult(array $resultData): ?int
    {
        // This would need to be implemented based on how patient matching is done
        // For now, return null and handle manually
        return null;
    }
}
