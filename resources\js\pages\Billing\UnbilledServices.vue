<template>
    <AppLayout title="Unbilled Services">
        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">Unbilled Services</h2>
                                <p class="text-gray-600 mt-1">Services from consultations that haven't been billed yet</p>
                            </div>
                            <Link :href="route('bills.index')" 
                                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Back to Bills
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Summary Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Services</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ unbilledServices.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Total Value</p>
                                    <p class="text-2xl font-bold text-gray-900">£{{ totalValue.toFixed(2) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500">Consultations</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ uniqueConsultations.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Table -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Unbilled Services</h3>
                            <button v-if="selectedServices.length > 0" 
                                    @click="showBulkBillModal = true"
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Create Bill for Selected ({{ selectedServices.length }})
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left">
                                            <input type="checkbox" 
                                                   @change="toggleSelectAll"
                                                   :checked="allSelected"
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consultation Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="service in unbilledServices" :key="service.id">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" 
                                                   v-model="selectedServices"
                                                   :value="service.id"
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ service.service_name }}</div>
                                                <div v-if="service.description" class="text-sm text-gray-500">{{ service.description }}</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ service.consultation?.patient?.user?.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ service.consultation?.provider?.user?.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ formatDate(service.consultation?.consultation_date) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ service.quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            £{{ service.unit_price }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            £{{ service.total_price }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="createBillForConsultation(service.consultation_id)"
                                                    class="text-blue-600 hover:text-blue-900">
                                                Bill Consultation
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Empty State -->
                        <div v-if="unbilledServices.length === 0" class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No unbilled services</h3>
                            <p class="mt-1 text-sm text-gray-500">All consultation services have been billed.</p>
                        </div>
                    </div>
                </div>

                <!-- Bulk Bill Modal -->
                <div v-if="showBulkBillModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Create Bill for Selected Services</h3>
                            <form @submit.prevent="createBulkBill">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Bill Title</label>
                                    <input v-model="bulkBillForm.title" 
                                           type="text" 
                                           required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Patient</label>
                                    <select v-model="bulkBillForm.patient_id" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Select Patient</option>
                                        <option v-for="patient in uniquePatients" :key="patient.id" :value="patient.id">
                                            {{ patient.name }}
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                                    <select v-model="bulkBillForm.provider_id" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Select Provider</option>
                                        <option v-for="provider in uniqueProviders" :key="provider.id" :value="provider.id">
                                            {{ provider.name }}
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                                    <input v-model="bulkBillForm.due_date" 
                                           type="date" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div class="mb-6 p-3 bg-gray-50 rounded-lg">
                                    <div class="flex justify-between text-sm">
                                        <span>Selected services:</span>
                                        <span>{{ selectedServices.length }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm font-medium">
                                        <span>Total amount:</span>
                                        <span>£{{ selectedTotal.toFixed(2) }}</span>
                                    </div>
                                </div>

                                <div class="flex justify-end space-x-3">
                                    <button type="button" 
                                            @click="showBulkBillModal = false"
                                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" 
                                            :disabled="loading"
                                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors disabled:opacity-50">
                                        {{ loading ? 'Creating...' : 'Create Bill' }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import axios from 'axios'

const unbilledServices = ref([])
const selectedServices = ref([])
const loading = ref(false)
const showBulkBillModal = ref(false)

const bulkBillForm = reactive({
    title: 'Consultation Services Bill',
    patient_id: '',
    provider_id: '',
    due_date: '',
    notes: ''
})

const totalValue = computed(() => {
    return unbilledServices.value.reduce((sum, service) => sum + parseFloat(service.total_price), 0)
})

const uniqueConsultations = computed(() => {
    const consultationIds = [...new Set(unbilledServices.value.map(s => s.consultation_id))]
    return consultationIds
})

const uniquePatients = computed(() => {
    const patients = unbilledServices.value
        .filter(s => selectedServices.value.includes(s.id))
        .map(s => ({
            id: s.consultation?.patient?.id,
            name: s.consultation?.patient?.user?.name
        }))
    return [...new Map(patients.map(p => [p.id, p])).values()]
})

const uniqueProviders = computed(() => {
    const providers = unbilledServices.value
        .filter(s => selectedServices.value.includes(s.id))
        .map(s => ({
            id: s.consultation?.provider?.id,
            name: s.consultation?.provider?.user?.name
        }))
    return [...new Map(providers.map(p => [p.id, p])).values()]
})

const selectedTotal = computed(() => {
    return unbilledServices.value
        .filter(s => selectedServices.value.includes(s.id))
        .reduce((sum, service) => sum + parseFloat(service.total_price), 0)
})

const allSelected = computed(() => {
    return unbilledServices.value.length > 0 && selectedServices.value.length === unbilledServices.value.length
})

const loadUnbilledServices = async () => {
    try {
        const response = await axios.get('/bills/unbilled-services')
        unbilledServices.value = response.data.data
    } catch (error) {
        console.error('Error loading unbilled services:', error)
    }
}

const toggleSelectAll = () => {
    if (allSelected.value) {
        selectedServices.value = []
    } else {
        selectedServices.value = unbilledServices.value.map(s => s.id)
    }
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const createBillForConsultation = async (consultationId) => {
    try {
        const response = await axios.post('/bills/create-from-consultation', {
            consultation_id: consultationId
        })
        
        alert('Bill created successfully!')
        router.visit(route('bills.show', response.data.data.bill.id))
    } catch (error) {
        console.error('Error creating bill:', error)
        alert('Failed to create bill. Please try again.')
    }
}

const createBulkBill = async () => {
    loading.value = true
    try {
        // For now, we'll create individual bills per consultation
        // In a more advanced implementation, you could create a single bill with multiple consultation services
        alert('Bulk billing feature coming soon! Please use "Bill Consultation" for individual consultations.')
        showBulkBillModal.value = false
    } catch (error) {
        console.error('Error creating bulk bill:', error)
        alert('Failed to create bill. Please try again.')
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    loadUnbilledServices()
})
</script>
