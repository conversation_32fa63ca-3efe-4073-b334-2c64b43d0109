<template>
  <Dialog :open="true" @update:open="$emit('close')">
    <DialogContent class="max-w-2xl">
      <DialogHeader>
        <DialogTitle class="flex items-center">
          <FileText class="w-5 h-5 mr-2" />
          Upload Document
        </DialogTitle>
        <DialogDescription>
          Upload documents, images, or files related to this consultation.
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Document Type Selection -->
        <div class="space-y-2">
          <Label>Document Type</Label>
          <Select v-model="documentType">
            <SelectTrigger>
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lab_report">Lab Report</SelectItem>
              <SelectItem value="image">Image/Photo</SelectItem>
              <SelectItem value="prescription">Prescription</SelectItem>
              <SelectItem value="letter">Medical Letter</SelectItem>
              <SelectItem value="scan">Scan/X-ray</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- File Upload Area -->
        <div class="space-y-4">
          <Label>Upload Files</Label>
          
          <!-- Drag and Drop Area -->
          <div
            @drop="handleDrop"
            @dragover.prevent
            @dragenter.prevent
            :class="[
              'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
              isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            ]"
            @dragenter="isDragging = true"
            @dragleave="isDragging = false"
          >
            <Upload class="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <div class="text-lg font-medium text-gray-900 mb-2">
              Drop files here or click to browse
            </div>
            <div class="text-sm text-gray-500 mb-4">
              Supports: PDF, DOC, DOCX, JPG, PNG, GIF (Max 10MB each)
            </div>
            <Button type="button" variant="outline" @click="triggerFileInput">
              <Plus class="w-4 h-4 mr-2" />
              Choose Files
            </Button>
            <input
              ref="fileInput"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              @change="handleFileSelect"
              class="hidden"
            />
          </div>
        </div>

        <!-- Selected Files List -->
        <div v-if="selectedFiles.length" class="space-y-2">
          <Label>Selected Files ({{ selectedFiles.length }})</Label>
          <div class="space-y-2 max-h-40 overflow-y-auto">
            <div
              v-for="(file, index) in selectedFiles"
              :key="index"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <component :is="getFileIcon(file.type)" class="w-5 h-5 text-gray-500" />
                <div>
                  <div class="font-medium text-sm">{{ file.name }}</div>
                  <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                @click="removeFile(index)"
              >
                <X class="w-4 h-4 text-red-500" />
              </Button>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="space-y-2">
          <Label for="description">Description (Optional)</Label>
          <Textarea
            id="description"
            v-model="description"
            placeholder="Add a description for these documents..."
            rows="3"
          />
        </div>

        <!-- Upload Progress -->
        <div v-if="uploading" class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Uploading...</span>
            <span class="text-sm text-gray-500">{{ uploadProgress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" @click="$emit('close')" :disabled="uploading">
            Cancel
          </Button>
          <Button
            type="button"
            @click="uploadFiles"
            :disabled="!selectedFiles.length || uploading"
          >
            <Loader2 v-if="uploading" class="w-4 h-4 mr-2 animate-spin" />
            <Upload v-else class="w-4 h-4 mr-2" />
            Upload {{ selectedFiles.length }} File{{ selectedFiles.length !== 1 ? 's' : '' }}
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FileText, Upload, Plus, X, Loader2, File, Image, FileType } from 'lucide-vue-next'
import axios from 'axios'

interface Props {
  consultationId: string | number
}

interface Emits {
  (e: 'close'): void
  (e: 'uploaded'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Component state
const documentType = ref('other')
const selectedFiles = ref<File[]>([])
const description = ref('')
const uploading = ref(false)
const uploadProgress = ref(0)
const isDragging = ref(false)
const fileInput = ref<HTMLInputElement>()

// Methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    addFiles(Array.from(target.files))
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragging.value = false
  
  if (event.dataTransfer?.files) {
    addFiles(Array.from(event.dataTransfer.files))
  }
}

const addFiles = (files: File[]) => {
  const validFiles = files.filter(file => {
    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert(`File ${file.name} is too large. Maximum size is 10MB.`)
      return false
    }
    
    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      alert(`File ${file.name} is not a supported format.`)
      return false
    }
    
    return true
  })
  
  selectedFiles.value.push(...validFiles)
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) {
    return Image
  } else if (fileType.includes('pdf')) {
    return FileType
  } else {
    return File
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const uploadFiles = async () => {
  if (!selectedFiles.value.length) return

  try {
    uploading.value = true
    uploadProgress.value = 0

    const formData = new FormData()
    formData.append('consultation_id', props.consultationId)
    formData.append('document_type', documentType.value)
    formData.append('description', description.value)

    selectedFiles.value.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })

    await axios.post('/consultation-documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })

    emit('uploaded')
    emit('close')
  } catch (error) {
    console.error('Error uploading files:', error)
    alert('Failed to upload files. Please try again.')
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}
</script>
