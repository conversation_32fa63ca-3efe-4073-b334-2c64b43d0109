<template>
    <div class="country-selector">


        <!-- Country Selection -->
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Country & Currency</h3>
            </div>

            <!-- Current Country Display -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-base font-semibold text-gray-900">{{ currentCountryInfo.name }}</h4>
                            <p class="text-sm text-gray-600">{{ currentCountryInfo.currency_symbol }} {{ currentCountryInfo.currency_code }} - {{ currentCountryInfo.currency_name }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-900">{{ currentCountryInfo.code }}</div>
                        <div class="text-xs text-gray-500">{{ currentCountryInfo.phone_code }}</div>
                    </div>
                </div>
            </div>

            <!-- Country Dropdown -->
            <div v-if="allowEditing" class="space-y-3">
                <label for="country-select" class="block text-sm font-medium text-gray-700">
                    Change Country
                </label>
                
                <div class="relative">
                    <select
                        id="country-select"
                        v-model="selectedCountryCode"
                        @change="handleCountryChange"
                        :disabled="isLoading"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 disabled:bg-gray-100 disabled:cursor-not-allowed appearance-none bg-no-repeat bg-right pr-10"
                        style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                    >
                        <option value="" disabled>Select a country...</option>
                        <option
                            v-for="country in availableCountries"
                            :key="country.code"
                            :value="country.code"
                        >
                            {{ country.name }} ({{ country.currency_symbol }}{{ country.currency_code }})
                        </option>
                    </select>
                    
                    <!-- Loading Spinner -->
                    <div v-if="isLoading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Payment Gateway Preview -->
                <div v-if="selectedCountryCode && gatewayPreview && showGatewayPreview" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-900 mb-2">Payment Gateway for {{ currentCountryInfo.name }}</h5>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ gatewayPreview.name }}</div>
                            <div class="text-xs text-gray-600">{{ gatewayPreview.description }}</div>
                        </div>
                        <div v-if="gatewayPreview.is_primary" class="ml-auto">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                                Primary
                            </span>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Read-only Notice -->
            <div v-else class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm font-medium text-blue-800">Country Information</span>
                </div>
                <p class="text-sm text-blue-700 mt-1">
                    This was set during registration and cannot be changed. Contact support if you need to update your country.
                </p>
            </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-red-800">Error</span>
            </div>
            <p class="text-sm text-red-700 mt-1">{{ error }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePage } from '@inertiajs/vue3'
import axios from 'axios'

// Props
const props = defineProps({
    allowEditing: {
        type: Boolean,
        default: false // Default to false, must be explicitly enabled
    },
    showGatewayPreview: {
        type: Boolean,
        default: false
    }
})

// Emits
const emit = defineEmits(['country-changed', 'error'])

// State
const isLoading = ref(false)
const error = ref(null)
const availableCountries = ref([])
const selectedCountryCode = ref('')
const gatewayPreview = ref(null)

// Page data
const page = usePage()
const currentUser = computed(() => page.props.auth?.user || null)

// Use allowEditing prop directly
const allowEditing = computed(() => props.allowEditing)
const isTestingMode = computed(() => props.allowEditing)

// Get current country info from session storage or user data
const getCurrentCountryCode = () => {
    if (allowEditing.value) {
        // In editing mode, check session storage first
        const sessionCountry = sessionStorage.getItem('test_country_code')
        if (sessionCountry) {
            return sessionCountry
        }
    }

    // Fallback to user's actual country or default
    return currentUser.value?.country_code || 'GB'
}

// Current country information
const currentCountryInfo = computed(() => {
    const countryCode = getCurrentCountryCode()
    const country = availableCountries.value.find(c => c.code === countryCode)
    
    if (country) {
        return country
    }
    
    // Fallback country info
    return {
        name: 'United Kingdom',
        code: 'GB',
        currency_code: 'GBP',
        currency_symbol: '£',
        currency_name: 'British Pound',
        phone_code: '+44'
    }
})

// Load available countries
const loadCountries = async () => {
    try {
        isLoading.value = true
        error.value = null
        
        const response = await axios.get('/countries-list')
        if (response.data.success) {
            availableCountries.value = response.data.data
            selectedCountryCode.value = getCurrentCountryCode()
        } else {
            throw new Error(response.data.message || 'Failed to load countries')
        }
    } catch (err) {
        error.value = err.response?.data?.message || err.message || 'Failed to load countries'
        emit('error', err)
    } finally {
        isLoading.value = false
    }
}

// Load gateway preview for selected country
const loadGatewayPreview = async (countryCode) => {
    if (!props.showGatewayPreview || !countryCode) return
    
    try {
        const response = await axios.get(`/api/payment-gateways/available?country_code=${countryCode}`)
        if (response.data.success && response.data.data.available_gateways.length > 0) {
            const primaryGateway = response.data.data.available_gateways.find(g => g.is_primary)
            gatewayPreview.value = primaryGateway || response.data.data.available_gateways[0]
        } else {
            gatewayPreview.value = null
        }
    } catch (err) {
        console.error('Failed to load gateway preview:', err)
        gatewayPreview.value = null
    }
}

// Handle country change
const handleCountryChange = async () => {
    if (!allowEditing.value || !selectedCountryCode.value) return
    
    try {
        isLoading.value = true
        error.value = null
        
        // Store in session storage for testing
        sessionStorage.setItem('test_country_code', selectedCountryCode.value)
        
        // Update user's country in the database
        try {
            const response = await axios.post('/api/user/country', {
                country_code: selectedCountryCode.value
            })
            
            if (response.data.success) {
                console.log('Country updated successfully in database')
                
                // Emit change event with updated data
                emit('country-changed', {
                    countryCode: selectedCountryCode.value,
                    countryInfo: response.data.data.country,
                    gatewayPreview: {
                        available_gateways: response.data.data.available_gateways,
                        primary_gateway: response.data.data.primary_gateway,
                        currency_info: response.data.data.currency_info
                    }
                })
                
                // Reload the page to reflect changes across the app
                setTimeout(() => {
                    window.location.reload()
                }, 1000)
                
            } else {
                throw new Error(response.data.message || 'Failed to update country')
            }
        } catch (apiError) {
            console.error('API Error:', apiError)
            // Fallback to old behavior for testing
            await loadGatewayPreview(selectedCountryCode.value)
            
            emit('country-changed', {
                countryCode: selectedCountryCode.value,
                countryInfo: currentCountryInfo.value,
                gatewayPreview: gatewayPreview.value
            })
        }
        
    } catch (err) {
        error.value = err.message || 'Failed to change country'
        emit('error', err)
    } finally {
        isLoading.value = false
    }
}

// Watch for changes in selected country
watch(selectedCountryCode, (newCode) => {
    if (newCode && isTestingMode.value) {
        loadGatewayPreview(newCode)
    }
})

// Initialize on mount
onMounted(async () => {
    await loadCountries()
    
    // Load initial gateway preview
    if (props.showGatewayPreview) {
        await loadGatewayPreview(getCurrentCountryCode())
    }
})
</script>

<style scoped>
.country-selector {
    max-width: 600px;
}

/* Custom select styling */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Animation for smooth transitions */
.country-selector > * {
    transition: all 0.2s ease-in-out;
}
</style>
