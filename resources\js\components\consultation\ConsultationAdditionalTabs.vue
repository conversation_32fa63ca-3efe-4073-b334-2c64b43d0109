<template>
  <div class="space-y-6">
    <!-- Theme-colored Additional Tabs Toggle Buttons -->
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 mb-4">
      <div v-for="(field, key) in availableAdditionalTabs" :key="key">
        <button @click="toggleTab(key)"
          class="w-full inline-flex items-center justify-center gap-1 px-2 py-2 text-xs font-medium rounded-lg transition-colors" :class="[
            isTabActive(key)
              ? 'bg-teal-600 text-white shadow-sm'
              : 'bg-teal-50 hover:bg-teal-100 text-teal-700 border border-teal-200',
          ]">
          <span class="text-center leading-tight">{{ field.label }}</span>
          <span v-if="!isTabActive(key)" class="text-xs">+</span>
        </button>
      </div>
    </div>

    <!-- Active Additional Tabs -->
    <div v-if="Object.keys(additionalTabs).length" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <div v-for="(entries, tabKey) in additionalTabs" :key="tabKey" class="border border-teal-200 rounded-lg overflow-hidden">
        <!-- Theme-colored Tab Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between px-3 py-2 bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200 gap-2 sm:gap-0">
          <h4 class="text-sm font-semibold text-teal-900">{{ defaultAdditionalTabs[tabKey]?.label || tabKey }}</h4>
          <div class="flex items-center gap-1">
            <button @click="addEntry(tabKey)" class="text-teal-600 hover:text-teal-700 text-xs font-medium px-2 py-1 hover:bg-teal-200 rounded transition-colors">
              + Add
            </button>
            <button @click="removeTab(tabKey)" class="text-red-600 hover:text-red-700 ml-1 p-1 hover:bg-red-50 rounded transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6L6 18"></path>
                <path d="M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Theme-colored Tab Content -->
        <div class="p-3 bg-white">
          <!-- Existing Entries -->
          <div v-if="entries?.length" class="space-y-2">
            <div v-for="entry in entries" :key="entry.id" class="flex gap-2">
              <textarea :value="entry.content" @input="updateEntry(tabKey, entry.id, ($event.target as HTMLTextAreaElement).value)"
                class="flex-1 px-3 py-2 border border-teal-200 rounded-lg text-sm resize-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-teal-50/30"
                rows="4"
                :placeholder="defaultAdditionalTabs[tabKey]?.placeholder || 'Enter ' + (defaultAdditionalTabs[tabKey]?.label || tabKey).toLowerCase() + '...'"></textarea>
              <button @click="removeEntry(tabKey, entry.id)" class="text-slate-400 hover:text-coral-600 hover:bg-coral-50 p-1 rounded transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else>
            <textarea @blur="handleNewEntry(tabKey, ($event.target as HTMLTextAreaElement).value, $event)"
              @keydown.enter="handleNewEntry(tabKey, ($event.target as HTMLTextAreaElement).value, $event)"
              class="w-full px-3 py-2 border border-teal-200 rounded-lg text-sm resize-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-teal-50/30"
              rows="3"
              :placeholder="defaultAdditionalTabs[tabKey]?.placeholder || 'Enter ' + (defaultAdditionalTabs[tabKey]?.label || tabKey).toLowerCase() + '...'"></textarea>
          </div>

          <!-- Prefill Button -->
          <div class="flex justify-between items-center pt-3 border-t border-teal-100 mt-3">
            <button @click="prefillTemplate(tabKey)" class="text-sm text-teal-600 hover:text-teal-700 font-medium px-3 py-1.5 hover:bg-teal-50 rounded-lg transition-colors">
              Prefill
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Theme-colored Empty State for Additional Tabs -->
    <div v-if="!Object.keys(additionalTabs).length" class="text-center py-8 bg-gradient-to-br from-teal-50 to-mint-50 rounded-lg border border-teal-200">
      <div class="text-teal-400 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </div>
      <h4 class="text-base font-semibold text-teal-900 mb-2">No Additional Sections Added</h4>
      <p class="text-sm text-teal-700 mb-3">Click on any section above to add it to your consultation</p>
      <div class="text-xs text-teal-600 bg-teal-100 px-3 py-1 rounded-full inline-block">
        Available: Allergies, Family History, Social History, Past Medical History, and more
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface TabEntry {
  id: number
  content: string
  created_at: string
}

interface Props {
  additionalTabs: Record<string, TabEntry[]>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', additionalTabs: Record<string, TabEntry[]>): void
  (e: 'add-tab', tabKey: string): void
  (e: 'remove-tab', tabKey: string): void
  (e: 'add-entry', tabKey: string, content: string): void
  (e: 'remove-entry', tabKey: string, entryId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Component state
const showDropdown = ref(false)



// Default additional tabs - always available
const defaultAdditionalTabs = {






  safeguarding: {
    key: 'safeguarding',
    type: 'textarea',
    label: 'Safeguarding',
    placeholder: 'Enter safeguarding assessment...',
    required: false,
    multiple: true,
    icon: 'shield',
    prefill: `* Vulnerability assessment:
* Risk factors identified:
* Support systems in place:
* Previous safeguarding concerns:
* Actions taken:
* Follow-up plan:`
  },
  notes: {
    key: 'notes',
    type: 'textarea',
    label: 'Notes',
    placeholder: 'Enter additional notes...',
    required: false,
    multiple: true,
    icon: 'file-text',
    prefill: `* Key observations:
* Special considerations:
* Follow-up requirements:
* Communication needs:
* Care coordination:`
  },
  allergies: {
    key: 'allergies',
    type: 'textarea',
    label: 'Allergies',
    placeholder: 'Enter known allergies and reactions...',
    required: false,
    multiple: true,
    icon: 'alert-triangle',
    prefill: `* Known allergies:
* Type of reaction:
* Severity:
* Date identified:
* Management plan:`
  },
  family_history: {
    key: 'family_history',
    type: 'textarea',
    label: 'Family History',
    placeholder: 'Enter relevant family medical history...',
    required: false,
    multiple: true,
    icon: 'users',
    prefill: `* Relevant family conditions:
* First-degree relatives:
* Age of onset:
* Current status:
* Genetic testing:`
  },
  safety_netting: {
    key: 'safety_netting',
    type: 'textarea',
    label: 'Safety Netting',
    placeholder: 'Enter safety netting advice...',
    required: false,
    multiple: true,
    icon: 'shield-check',
    prefill: `* Warning signs to watch for:
* When to seek urgent care:
* Follow-up plan:
* Contact information:
* Resources provided:`
  },
  past_medical_history: {
    key: 'past_medical_history',
    type: 'textarea',
    label: 'Past Medical History',
    placeholder: 'Enter previous medical conditions and treatments...',
    required: false,
    multiple: true,
    icon: 'file-text',
    prefill: `* Chronic conditions:
* Previous surgeries:
* Hospitalizations:
* Significant illnesses:
* Current status:`
  },
  medications: {
    key: 'medications',
    type: 'textarea',
    label: 'Medications',
    placeholder: 'Enter current medications and dosages...',
    required: false,
    multiple: true,
    icon: 'pill',
    prefill: `* Current medications:
* Dosage:
* Frequency:
* Start date:
* Side effects:
* Compliance:`
  },
  social_history: {
    key: 'social_history',
    type: 'textarea',
    label: 'Social History',
    placeholder: 'Enter social history including lifestyle factors...',
    required: false,
    multiple: true,
    icon: 'home',
    prefill: `* Living situation:
* Occupation:
* Lifestyle habits:
* Support system:
* Substance use:
* Exercise:`
  },
  systems_review: {
    key: 'systems_review',
    type: 'textarea',
    label: 'Systems Review',
    placeholder: 'Enter systematic review of symptoms...',
    required: false,
    multiple: true,
    icon: 'search',
    prefill: `Cardiovascular:
* Chest pain:
* Palpitations:
* Dyspnea:

Respiratory:
* Cough:
* Wheezing:
* Sputum:

Gastrointestinal:
* Appetite:
* Bowel habits:
* Abdominal pain:`
  },
  preventative_care: {
    key: 'preventative_care',
    type: 'textarea',
    label: 'Preventative Care',
    placeholder: 'Enter preventative care measures...',
    required: false,
    multiple: true,
    icon: 'shield-plus',
    prefill: `* Immunizations:
* Screenings due:
* Health promotion:
* Risk factors:
* Prevention strategies:`
  },
  mental_health: {
    key: 'mental_health',
    type: 'textarea',
    label: 'Mental Health',
    placeholder: 'Enter mental health assessment...',
    required: false,
    multiple: true,
    icon: 'brain',
    prefill: `* Current mental state:
* Previous diagnoses:
* Treatment history:
* Risk assessment:
* Support systems:
* Coping strategies:`
  },
  lifestyle: {
    key: 'lifestyle',
    type: 'textarea',
    label: 'Lifestyle',
    placeholder: 'Enter lifestyle factors...',
    required: false,
    multiple: true,
    icon: 'activity',
    prefill: `* Diet:
* Exercise routine:
* Sleep pattern:
* Stress levels:
* Work-life balance:
* Habits/Dependencies:`
  },
  plan: {
    key: 'plan',
    type: 'textarea',
    label: 'Plan',
    placeholder: 'Enter management plan...',
    required: false,
    multiple: true,
    icon: 'clipboard-list',
    prefill: `* Diagnosis:
* Treatment plan:
* Medications prescribed:
* Follow-up arrangements:
* Patient education:
* Referrals:`
  }
}

// Computed properties
const availableAdditionalTabs = computed(() => {
  // Always use the default tabs, regardless of template
  return defaultAdditionalTabs
})

// Methods
const isTabActive = (tabKey: string) => {
  return !!props.additionalTabs[tabKey]
}

const toggleTab = (tabKey: string) => {
  if (isTabActive(tabKey)) {
    removeTab(tabKey)
  } else {
    addTab(tabKey)
  }
  showDropdown.value = false
}

// Methods
const addTab = (tabKey: string) => {
  emit('add-tab', tabKey)
  showDropdown.value = false
}

const removeTab = (tabKey: string) => {
  emit('remove-tab', tabKey)
}

const addEntry = (tabKey: string, content: string = '') => {
  emit('add-entry', tabKey, content)
}

const removeEntry = (tabKey: string, entryId: number) => {
  emit('remove-entry', tabKey, entryId)
}

const updateEntry = (tabKey: string, entryId: number, content: string) => {
  const updatedAdditionalTabs = { ...props.additionalTabs }
  if (updatedAdditionalTabs[tabKey]) {
    const entry = updatedAdditionalTabs[tabKey].find(e => e.id === entryId)
    if (entry) {
      entry.content = content
      emit('update', updatedAdditionalTabs)
    }
  }
}

const handleNewEntry = (tabKey: string, content: string, event?: Event) => {
  if (content.trim()) {
    addEntry(tabKey, content.trim())
    // Clear the textarea after adding entry
    const textarea = event?.target as HTMLTextAreaElement
    if (textarea) {
      textarea.value = ''
    }
  }
}

const getTextareaRows = (content: string) => {
  if (!content) return 3
  const lines = content.split('\n').length
  return Math.max(3, Math.min(lines + 1, 10))
}

const prefillTemplate = (tabKey: string) => {
  const template = defaultAdditionalTabs[tabKey]
  if (template?.prefill) {
    addEntry(tabKey, template.prefill)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
