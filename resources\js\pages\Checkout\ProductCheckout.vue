<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Checkout - Product Purchase" />

        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Checkout</h1>
                <p class="text-lg text-gray-600 mt-2">Complete your purchase</p>
                
                <!-- Country & Flow Type Badge -->
                <div class="inline-flex items-center mt-4 px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {{ checkoutFlow.country_name }} - {{ formatFlowType(checkoutFlow.flow_type) }}
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Checkout Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <!-- Progress Steps -->
                        <div class="border-b border-gray-200 px-6 py-4">
                            <div class="flex items-center justify-between">
                                <div 
                                    v-for="(step, index) in checkoutFlow.steps" 
                                    :key="step"
                                    class="flex items-center"
                                    :class="{ 'opacity-50': index > currentStep }"
                                >
                                    <div 
                                        class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                                        :class="index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'"
                                    >
                                        {{ index + 1 }}
                                    </div>
                                    <span class="ml-2 text-sm font-medium text-gray-900 hidden sm:block">
                                        {{ formatStepName(step) }}
                                    </span>
                                    <svg 
                                        v-if="index < checkoutFlow.steps.length - 1"
                                        class="w-5 h-5 text-gray-400 mx-4"
                                        fill="none" 
                                        stroke="currentColor" 
                                        viewBox="0 0 24 24"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Step Content -->
                        <div class="p-6">
                            <!-- Shipping Address Step -->
                            <div v-if="currentStepName === 'shipping_address'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Shipping Address</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                        <input
                                            v-model="formData.shipping_address.full_name"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Enter full name"
                                            required
                                        />
                                    </div>
                                    
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 1</label>
                                        <input
                                            v-model="formData.shipping_address.address_line_1"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Street address"
                                            required
                                        />
                                    </div>
                                    
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 2 (Optional)</label>
                                        <input
                                            v-model="formData.shipping_address.address_line_2"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Apartment, suite, etc."
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                        <input
                                            v-model="formData.shipping_address.city"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="City"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            {{ checkoutFlow.country_code === 'GB' ? 'Postcode' : 'ZIP Code' }}
                                        </label>
                                        <input
                                            v-model="formData.shipping_address.postal_code"
                                            type="text"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            :placeholder="checkoutFlow.country_code === 'GB' ? 'Postcode' : 'ZIP Code'"
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input
                                            v-model="formData.shipping_address.phone"
                                            type="tel"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Phone number"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Prescription Upload Step (India specific) -->
                            <div v-if="currentStepName === 'prescription_upload' && requiresPrescription" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Prescription Upload</h3>
                                
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-yellow-800">Prescription Required</h4>
                                            <p class="text-sm text-yellow-700 mt-1">
                                                Some items in your cart require a valid prescription. Please upload your prescription to proceed.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Prescription</label>
                                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                                            <input
                                                type="file"
                                                @change="handlePrescriptionUpload"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                class="hidden"
                                                ref="prescriptionInput"
                                            />
                                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                            </svg>
                                            <button
                                                @click="$refs.prescriptionInput.click()"
                                                class="text-blue-600 hover:text-blue-700 font-medium"
                                            >
                                                Click to upload prescription
                                            </button>
                                            <p class="text-sm text-gray-500 mt-2">PDF, JPG, JPEG, PNG up to 10MB</p>
                                        </div>
                                        
                                        <div v-if="formData.prescription_file" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                                <span class="text-sm text-green-800">{{ formData.prescription_file.name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Step -->
                            <div v-if="currentStepName === 'payment_details'" class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900">Payment Information</h3>
                                
                                <!-- Payment Gateway Selection -->
                                <PaymentGatewaySelector 
                                    @gateway-selected="onGatewaySelected"
                                    @error="onPaymentError"
                                />

                                <!-- Razorpay Checkout -->
                                <div v-if="selectedGateway?.id === 'razorpay'">
                                    <RazorpayCheckout
                                        :amount="totalAmount"
                                        :currency="checkoutFlow.currency"
                                        :description="`Product Purchase - ${cartItems.length} items`"
                                        @success="onPaymentSuccess"
                                        @error="onPaymentError"
                                    />
                                </div>

                                <!-- Stripe Checkout -->
                                <div v-if="selectedGateway?.id === 'stripe'">
                                    <StripeCheckout
                                        :amount="totalAmount"
                                        :currency="checkoutFlow.currency"
                                        :description="`Product Purchase - ${cartItems.length} items`"
                                        @success="onPaymentSuccess"
                                        @error="onPaymentError"
                                    />
                                </div>
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                                <button
                                    v-if="currentStep > 0"
                                    @click="previousStep"
                                    class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                                >
                                    Previous
                                </button>
                                <div v-else></div>

                                <button
                                    v-if="currentStep < checkoutFlow.steps.length - 1"
                                    @click="nextStep"
                                    :disabled="!canProceedToNextStep"
                                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                                >
                                    Next
                                </button>
                                <button
                                    v-else
                                    @click="submitOrder"
                                    :disabled="isSubmitting || !paymentCompleted"
                                    class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                                >
                                    <span v-if="isSubmitting">Processing...</span>
                                    <span v-else>Place Order</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Order Summary -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                        
                        <div class="space-y-4 mb-4">
                            <div v-for="item in cartItems" :key="item.product_id" class="flex items-center space-x-3">
                                <img 
                                    :src="getProduct(item.product_id)?.image || '/images/product-placeholder.png'" 
                                    :alt="getProduct(item.product_id)?.name"
                                    class="w-12 h-12 object-cover rounded-lg"
                                />
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ getProduct(item.product_id)?.name }}</h4>
                                    <p class="text-sm text-gray-600">Qty: {{ item.quantity }}</p>
                                </div>
                                <span class="font-medium">{{ formatAmount(item.quantity * item.price) }}</span>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4 space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="font-medium">{{ formatAmount(subtotal) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-medium">{{ formatAmount(shippingCost) }}</span>
                            </div>
                            
                            <div v-if="checkoutFlow.regulations?.requires_gst" class="flex justify-between text-sm">
                                <span class="text-gray-600">GST (18%)</span>
                                <span class="font-medium">{{ formatAmount(gstAmount) }}</span>
                            </div>
                            
                            <div class="border-t pt-2 flex justify-between font-semibold text-lg">
                                <span>Total</span>
                                <span>{{ formatAmount(totalAmount) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Country-specific Information -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-2">{{ checkoutFlow.country_name }} Regulations</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li v-if="checkoutFlow.regulations?.prescription_requirements">
                                ✓ Prescription verification required
                            </li>
                            <li v-if="checkoutFlow.regulations?.gdpr_compliance">
                                ✓ GDPR compliant
                            </li>
                            <li v-if="checkoutFlow.regulations?.data_localization">
                                ✓ Data stored locally
                            </li>
                            <li v-if="checkoutFlow.regulations?.requires_gst">
                                ✓ GST included in pricing
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import PaymentGatewaySelector from '@/components/PaymentGatewaySelector.vue'
import RazorpayCheckout from '@/components/RazorpayCheckout.vue'
import StripeCheckout from '@/components/StripeCheckout.vue'
import { useCountryManager } from '@/composables/useCountryManager'
import axios from 'axios'

// Props
const props = defineProps({
    checkoutFlow: Object,
    cartItems: Array,
    products: Array,
    user: Object,
    breadcrumbs: Array
})

// Country manager
const { formatAmount } = useCountryManager()

// State
const currentStep = ref(0)
const selectedGateway = ref(null)
const paymentCompleted = ref(false)
const isSubmitting = ref(false)

// Form data
const formData = ref({
    shipping_address: {
        full_name: props.user?.name || '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        postal_code: '',
        phone: ''
    },
    billing_address: null, // Will copy from shipping if not provided
    prescription_file: null,
    items: props.cartItems
})

// Computed
const currentStepName = computed(() => props.checkoutFlow.steps[currentStep.value])

const requiresPrescription = computed(() => {
    return props.cartItems.some(item => {
        const product = getProduct(item.product_id)
        return product?.requires_prescription
    })
})

const subtotal = computed(() => {
    return props.cartItems.reduce((sum, item) => sum + (item.quantity * item.price), 0)
})

const shippingCost = computed(() => {
    // Simple shipping calculation - can be made more sophisticated
    return subtotal.value > 50 ? 0 : 10
})

const gstAmount = computed(() => {
    return props.checkoutFlow.regulations?.requires_gst ? subtotal.value * 0.18 : 0
})

const totalAmount = computed(() => {
    return subtotal.value + shippingCost.value + gstAmount.value
})

const canProceedToNextStep = computed(() => {
    switch (currentStepName.value) {
        case 'shipping_address':
            const addr = formData.value.shipping_address
            return addr.full_name && addr.address_line_1 && addr.city && addr.postal_code && addr.phone
        case 'prescription_upload':
            return !requiresPrescription.value || formData.value.prescription_file
        case 'payment_details':
            return paymentCompleted.value
        default:
            return true
    }
})

// Methods
const getProduct = (productId) => {
    return props.products.find(p => p.id === productId)
}

const formatStepName = (step) => {
    return step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatFlowType = (flowType) => {
    return flowType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const nextStep = () => {
    if (canProceedToNextStep.value && currentStep.value < props.checkoutFlow.steps.length - 1) {
        currentStep.value++
    }
}

const previousStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--
    }
}

const handlePrescriptionUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
        formData.value.prescription_file = file
    }
}

const onGatewaySelected = (gateway) => {
    selectedGateway.value = gateway
    console.log('Gateway selected:', gateway)
}

const onPaymentSuccess = (result) => {
    paymentCompleted.value = true
    formData.value.payment_result = result
    console.log('Payment successful:', result)
}

const onPaymentError = (error) => {
    console.error('Payment error:', error)
}

const submitOrder = async () => {
    if (!paymentCompleted.value) return

    isSubmitting.value = true

    try {
        const response = await axios.post('/checkout/process/product', {
            ...formData.value,
            total_amount: totalAmount.value
        })

        if (response.data.success) {
            router.visit(response.data.data.redirect_url)
        } else {
            throw new Error(response.data.message)
        }
    } catch (error) {
        console.error('Order submission failed:', error)
        alert('Failed to place order. Please try again.')
    } finally {
        isSubmitting.value = false
    }
}

onMounted(() => {
    console.log('Product checkout flow:', props.checkoutFlow)
})
</script>
