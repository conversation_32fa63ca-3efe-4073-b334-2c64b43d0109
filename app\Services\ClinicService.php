<?php

namespace App\Services;

use App\Models\Clinic;
use App\Repositories\ClinicRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class ClinicService
{
    protected ClinicRepository $clinicRepository;

    public function __construct(ClinicRepository $clinicRepository)
    {
        $this->clinicRepository = $clinicRepository;
    }

    /**
     * Create a new clinic with business logic
     */
    public function createClinic(array $data): Clinic
    {
        // Business logic: Generate slug, set defaults
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? true;
        $data['accepts_new_patients'] = $data['accepts_new_patients'] ?? true;
        $data['telemedicine_enabled'] = $data['telemedicine_enabled'] ?? false;

        return $this->clinicRepository->create($data);
    }

    /**
     * Update clinic with business logic
     */
    public function updateClinic(int $clinicId, array $data): ?Clinic
    {
        // Business logic: Don't allow certain fields to be updated directly
        unset($data['slug']); // Slug shouldn't be changed after creation

        return $this->clinicRepository->update($clinicId, $data);
    }

    /**
     * Get active clinics
     */
    public function getActiveClinics(): Collection
    {
        return $this->clinicRepository->findActive();
    }

    /**
     * Search clinics with business logic
     */
    public function searchClinics(string $search, array $filters = []): Collection
    {
        $clinics = $this->clinicRepository->search($search);
        
        // Business logic: Apply additional filters
        if (!empty($filters['accepts_new_patients'])) {
            $clinics = $clinics->where('accepts_new_patients', true);
        }

        if (!empty($filters['telemedicine_enabled'])) {
            $clinics = $clinics->where('telemedicine_enabled', true);
        }

        return $clinics;
    }

    /**
     * Activate clinic
     */
    public function activateClinic(int $clinicId): bool
    {
        $clinic = $this->clinicRepository->update($clinicId, ['is_active' => true]);
        
        // Business logic: Could send notifications, update related records, etc.
        if ($clinic) {
            // Notify clinic staff, update provider statuses, etc.
        }

        return $clinic !== null;
    }

    /**
     * Deactivate clinic
     */
    public function deactivateClinic(int $clinicId): bool
    {
        $clinic = $this->clinicRepository->update($clinicId, ['is_active' => false]);
        
        // Business logic: Cancel appointments, notify patients, etc.
        if ($clinic) {
            // Handle cleanup processes
        }

        return $clinic !== null;
    }

    /**
     * Generate unique slug for clinic
     */
    private function generateUniqueSlug(string $name): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->clinicRepository->findBySlug($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
