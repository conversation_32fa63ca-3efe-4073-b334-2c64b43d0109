<?php

namespace App\Providers;

use App\Repositories\Interfaces\TaxRepositoryInterface;
use App\Repositories\Interfaces\BillRepositoryInterface;
use App\Repositories\Interfaces\BillItemRepositoryInterface;
use App\Repositories\Interfaces\BillingRepositoryInterface;
use App\Repositories\Interfaces\ConsultationServiceRepositoryInterface;
use App\Repositories\TaxRepository;
use App\Repositories\BillRepository;
use App\Repositories\BillItemRepository;
use App\Repositories\BillingRepository;
use App\Repositories\ConsultationServiceRepository;
use Illuminate\Support\ServiceProvider;

class BillingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(TaxRepositoryInterface::class, TaxRepository::class);
        $this->app->bind(BillRepositoryInterface::class, BillRepository::class);
        $this->app->bind(BillItemRepositoryInterface::class, BillItemRepository::class);
        $this->app->bind(BillingRepositoryInterface::class, BillingRepository::class);
        $this->app->bind(ConsultationServiceRepositoryInterface::class, ConsultationServiceRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
