<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Specialty extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'label',
        'description',
        'is_active',
        'wp_specialty_id',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'wp_specialty_id' => 'integer',
    ];

    /**
     * Get the specializations for this specialty.
     */
    public function specializations()
    {
        return $this->hasMany(Specialization::class);
    }

    /**
     * Get the providers for this specialty.
     */
    public function providers()
    {
        return $this->hasMany(Provider::class, 'specialty', 'name');
    }

    /**
     * Scope to get only active specialties
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get specialties ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
