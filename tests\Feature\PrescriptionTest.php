<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use App\Models\Prescription;
use App\Models\Medication;
use App\Models\PrescriptionRefillRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class PrescriptionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $clinician;
    protected $patient;
    protected $provider;
    protected $clinic;
    protected $medication;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the roles and permissions seeder
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
        
        // Create test data
        $this->clinic = Clinic::factory()->create();
        
        $this->clinician = User::factory()->create([
            'is_clinician' => true
        ]);
        $this->clinician->assignRole('provider');
        
        $this->provider = Provider::factory()->create([
            'user_id' => $this->clinician->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        $patientUser = User::factory()->create([
            'is_clinician' => false
        ]);
        $patientUser->assignRole('patient');
        
        $this->patient = Patient::factory()->create([
            'user_id' => $patientUser->id
        ]);
        
        $this->medication = Medication::factory()->create([
            'name' => 'Paracetamol',
            'strength' => '500mg',
            'form' => 'tablet',
            'route' => 'oral'
        ]);
    }

    /** @test */
    public function clinician_can_create_prescription()
    {
        Sanctum::actingAs($this->clinician);
        
        $prescriptionData = [
            'patient_id' => $this->patient->id,
            'type' => 'new',
            'prescribed_date' => now()->toDateString(),
            'clinical_indication' => 'Pain relief',
            'items' => [
                [
                    'medication_id' => $this->medication->id,
                    'dosage' => '1 tablet',
                    'frequency' => 'twice daily',
                    'quantity' => 30,
                    'quantity_unit' => 'tablets',
                    'duration_days' => 7,
                    'directions_for_use' => 'Take with food',
                    'take_with_food' => true,
                    'avoid_alcohol' => false,
                    'is_repeat_eligible' => true,
                    'repeats_allowed' => 2
                ]
            ]
        ];
        
        $response = $this->postJson('/api/prescriptions', $prescriptionData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'prescription_number',
                        'patient_id',
                        'prescriber_id',
                        'status',
                        'type',
                        'prescribed_date',
                        'clinical_indication',
                        'total_items'
                    ]
                ]);
        
        $this->assertDatabaseHas('prescriptions', [
            'patient_id' => $this->patient->id,
            'prescriber_id' => $this->clinician->id,
            'clinical_indication' => 'Pain relief',
            'status' => 'active',
            'total_items' => 1
        ]);
        
        $this->assertDatabaseHas('prescription_items', [
            'medication_id' => $this->medication->id,
            'dosage' => '1 tablet',
            'frequency' => 'twice daily',
            'quantity' => 30,
            'directions_for_use' => 'Take with food'
        ]);
    }

    /** @test */
    public function prescription_number_is_generated_automatically()
    {
        Sanctum::actingAs($this->clinician);
        
        $prescriptionData = [
            'patient_id' => $this->patient->id,
            'type' => 'new',
            'prescribed_date' => now()->toDateString(),
            'items' => [
                [
                    'medication_id' => $this->medication->id,
                    'dosage' => '1 tablet',
                    'frequency' => 'twice daily',
                    'quantity' => 30,
                    'quantity_unit' => 'tablets',
                    'directions_for_use' => 'Take with food'
                ]
            ]
        ];
        
        $response = $this->postJson('/api/prescriptions', $prescriptionData);
        
        $response->assertStatus(201);
        
        $prescription = Prescription::first();
        $this->assertNotNull($prescription->prescription_number);
        $this->assertStringStartsWith('RX', $prescription->prescription_number);
    }

    /** @test */
    public function clinician_can_view_their_prescriptions()
    {
        Sanctum::actingAs($this->clinician);
        
        Prescription::factory()->count(3)->create([
            'prescriber_id' => $this->clinician->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id
        ]);
        
        $response = $this->getJson('/api/prescriptions');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'prescription_number',
                                'patient_id',
                                'prescriber_id',
                                'status',
                                'type',
                                'prescribed_date'
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function clinician_can_cancel_prescription()
    {
        Sanctum::actingAs($this->clinician);
        
        $prescription = Prescription::factory()->create([
            'prescriber_id' => $this->clinician->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'active'
        ]);
        
        $response = $this->postJson("/api/prescriptions/{$prescription->id}/cancel", [
            'reason' => 'Patient allergic reaction'
        ]);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('prescriptions', [
            'id' => $prescription->id,
            'status' => 'cancelled'
        ]);
    }

    /** @test */
    public function patient_can_request_prescription_refill()
    {
        $patientUser = $this->patient->user;
        Sanctum::actingAs($patientUser);
        
        $prescription = Prescription::factory()->create([
            'prescriber_id' => $this->clinician->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'active'
        ]);
        
        $refillData = [
            'original_prescription_id' => $prescription->id,
            'request_reason' => 'Running low on medication',
            'patient_notes' => 'Need refill for ongoing treatment'
        ];
        
        $response = $this->postJson('/api/prescription-refills', $refillData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'original_prescription_id',
                        'patient_id',
                        'requested_by',
                        'status',
                        'request_reason'
                    ]
                ]);
        
        $this->assertDatabaseHas('prescription_refill_requests', [
            'original_prescription_id' => $prescription->id,
            'patient_id' => $this->patient->id,
            'requested_by' => $patientUser->id,
            'status' => 'pending',
            'request_reason' => 'Running low on medication'
        ]);
    }

    /** @test */
    public function clinician_can_approve_refill_request()
    {
        Sanctum::actingAs($this->clinician);
        
        $prescription = Prescription::factory()->create([
            'prescriber_id' => $this->clinician->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'active'
        ]);
        
        $refillRequest = PrescriptionRefillRequest::factory()->create([
            'original_prescription_id' => $prescription->id,
            'patient_id' => $this->patient->id,
            'requested_by' => $this->patient->user->id,
            'status' => 'pending'
        ]);
        
        $response = $this->postJson("/api/prescription-refills/{$refillRequest->id}/approve", [
            'review_notes' => 'Approved for continued treatment',
            'create_new_prescription' => false
        ]);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('prescription_refill_requests', [
            'id' => $refillRequest->id,
            'status' => 'approved',
            'reviewed_by' => $this->clinician->id
        ]);
    }

    /** @test */
    public function clinician_can_reject_refill_request()
    {
        Sanctum::actingAs($this->clinician);
        
        $prescription = Prescription::factory()->create([
            'prescriber_id' => $this->clinician->id,
            'patient_id' => $this->patient->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'active'
        ]);
        
        $refillRequest = PrescriptionRefillRequest::factory()->create([
            'original_prescription_id' => $prescription->id,
            'patient_id' => $this->patient->id,
            'requested_by' => $this->patient->user->id,
            'status' => 'pending'
        ]);
        
        $response = $this->postJson("/api/prescription-refills/{$refillRequest->id}/reject", [
            'rejection_reason' => 'Requires consultation first',
            'review_notes' => 'Patient needs to be seen before refill'
        ]);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('prescription_refill_requests', [
            'id' => $refillRequest->id,
            'status' => 'rejected',
            'reviewed_by' => $this->clinician->id,
            'rejection_reason' => 'Requires consultation first'
        ]);
    }

    /** @test */
    public function prescription_validation_works_correctly()
    {
        Sanctum::actingAs($this->clinician);
        
        $invalidData = [
            'patient_id' => 999, // Non-existent patient
            'type' => 'invalid_type',
            'prescribed_date' => 'invalid_date',
            'items' => [] // Empty items array
        ];
        
        $response = $this->postJson('/api/prescriptions', $invalidData);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'patient_id',
                    'type',
                    'prescribed_date',
                    'items'
                ]);
    }
}
