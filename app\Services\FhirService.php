<?php

namespace App\Services;

use App\Models\Patient;
use App\Models\Consultation;
use App\Models\Prescription;
use App\Models\Diagnosis;
use Carbon\Carbon;

/**
 * FHIR (Fast Healthcare Interoperability Resources) Service
 * Converts medical data to FHIR R4 standard format
 */
class FhirService
{
    /**
     * Convert Patient to FHIR Patient resource
     */
    public function patientToFhir(Patient $patient): array
    {
        $user = $patient->user;
        
        return [
            'resourceType' => 'Patient',
            'id' => (string) $patient->id,
            'meta' => [
                'versionId' => '1',
                'lastUpdated' => $patient->updated_at->toISOString(),
                'profile' => ['http://hl7.org/fhir/StructureDefinition/Patient']
            ],
            'identifier' => [
                [
                    'use' => 'usual',
                    'type' => [
                        'coding' => [
                            [
                                'system' => 'http://terminology.hl7.org/CodeSystem/v2-0203',
                                'code' => 'MR',
                                'display' => 'Medical Record Number'
                            ]
                        ]
                    ],
                    'system' => 'http://medroid.ai/patient-id',
                    'value' => (string) $patient->id
                ]
            ],
            'active' => true,
            'name' => [
                [
                    'use' => 'official',
                    'text' => $user->name,
                    'family' => $this->getLastName($user->name),
                    'given' => [$this->getFirstName($user->name)]
                ]
            ],
            'telecom' => [
                [
                    'system' => 'email',
                    'value' => $user->email,
                    'use' => 'home'
                ],
                [
                    'system' => 'phone',
                    'value' => $patient->phone ?? '',
                    'use' => 'mobile'
                ]
            ],
            'gender' => $this->mapGender($patient->gender),
            'birthDate' => $patient->date_of_birth ? Carbon::parse($patient->date_of_birth)->format('Y-m-d') : null,
            'address' => [
                [
                    'use' => 'home',
                    'type' => 'physical',
                    'text' => $patient->address ?? '',
                    'city' => $patient->city ?? '',
                    'postalCode' => $patient->postal_code ?? '',
                    'country' => $patient->country ?? 'UK'
                ]
            ]
        ];
    }

    /**
     * Convert Consultation to FHIR Encounter resource
     */
    public function consultationToFhir(Consultation $consultation): array
    {
        return [
            'resourceType' => 'Encounter',
            'id' => (string) $consultation->id,
            'meta' => [
                'versionId' => '1',
                'lastUpdated' => $consultation->updated_at->toISOString(),
                'profile' => ['http://hl7.org/fhir/StructureDefinition/Encounter']
            ],
            'status' => $this->mapConsultationStatus($consultation->status),
            'class' => [
                'system' => 'http://terminology.hl7.org/CodeSystem/v3-ActCode',
                'code' => $consultation->consultation_mode === 'video' ? 'VR' : 'AMB',
                'display' => $consultation->consultation_mode === 'video' ? 'Virtual' : 'Ambulatory'
            ],
            'type' => [
                [
                    'coding' => [
                        [
                            'system' => 'http://snomed.info/sct',
                            'code' => '11429006',
                            'display' => 'Consultation'
                        ]
                    ]
                ]
            ],
            'subject' => [
                'reference' => "Patient/{$consultation->patient_id}",
                'display' => $consultation->patient->user->name
            ],
            'participant' => [
                [
                    'type' => [
                        [
                            'coding' => [
                                [
                                    'system' => 'http://terminology.hl7.org/CodeSystem/v3-ParticipationType',
                                    'code' => 'PPRF',
                                    'display' => 'Primary Performer'
                                ]
                            ]
                        ]
                    ],
                    'individual' => [
                        'reference' => "Practitioner/{$consultation->provider_id}",
                        'display' => $consultation->provider->user->name ?? 'Unknown Provider'
                    ]
                ]
            ],
            'period' => [
                'start' => $consultation->consultation_date->toISOString(),
                'end' => $consultation->end_time ? $consultation->end_time->toISOString() : null
            ],
            'reasonCode' => [
                [
                    'text' => $consultation->chief_complaint ?? 'General consultation'
                ]
            ]
        ];
    }

    /**
     * Convert Diagnosis to FHIR Condition resource
     */
    public function diagnosisToFhir(Diagnosis $diagnosis): array
    {
        return [
            'resourceType' => 'Condition',
            'id' => (string) $diagnosis->id,
            'meta' => [
                'versionId' => '1',
                'lastUpdated' => $diagnosis->updated_at->toISOString(),
                'profile' => ['http://hl7.org/fhir/StructureDefinition/Condition']
            ],
            'clinicalStatus' => [
                'coding' => [
                    [
                        'system' => 'http://terminology.hl7.org/CodeSystem/condition-clinical',
                        'code' => 'active',
                        'display' => 'Active'
                    ]
                ]
            ],
            'verificationStatus' => [
                'coding' => [
                    [
                        'system' => 'http://terminology.hl7.org/CodeSystem/condition-ver-status',
                        'code' => 'confirmed',
                        'display' => 'Confirmed'
                    ]
                ]
            ],
            'category' => [
                [
                    'coding' => [
                        [
                            'system' => 'http://terminology.hl7.org/CodeSystem/condition-category',
                            'code' => 'encounter-diagnosis',
                            'display' => 'Encounter Diagnosis'
                        ]
                    ]
                ]
            ],
            'code' => [
                'coding' => [
                    [
                        'system' => 'http://hl7.org/fhir/sid/icd-10',
                        'code' => $diagnosis->icd10_code ?? '',
                        'display' => $diagnosis->diagnosis_name
                    ]
                ],
                'text' => $diagnosis->diagnosis_name
            ],
            'subject' => [
                'reference' => "Patient/{$diagnosis->consultation->patient_id}",
                'display' => $diagnosis->consultation->patient->user->name
            ],
            'encounter' => [
                'reference' => "Encounter/{$diagnosis->consultation_id}"
            ],
            'recordedDate' => $diagnosis->created_at->toISOString(),
            'note' => [
                [
                    'text' => $diagnosis->notes ?? ''
                ]
            ]
        ];
    }

    /**
     * Map gender to FHIR standard
     */
    private function mapGender(?string $gender): string
    {
        return match(strtolower($gender ?? '')) {
            'male', 'm' => 'male',
            'female', 'f' => 'female',
            'other' => 'other',
            default => 'unknown'
        };
    }

    /**
     * Map consultation status to FHIR encounter status
     */
    private function mapConsultationStatus(string $status): string
    {
        return match($status) {
            'scheduled' => 'planned',
            'in_progress' => 'in-progress',
            'completed' => 'finished',
            'cancelled' => 'cancelled',
            default => 'unknown'
        };
    }

    /**
     * Extract first name from full name
     */
    private function getFirstName(string $fullName): string
    {
        $parts = explode(' ', trim($fullName));
        return $parts[0] ?? '';
    }

    /**
     * Extract last name from full name
     */
    private function getLastName(string $fullName): string
    {
        $parts = explode(' ', trim($fullName));
        return count($parts) > 1 ? end($parts) : '';
    }
}
