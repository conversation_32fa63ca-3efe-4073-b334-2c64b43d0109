<?php

namespace App\Services\Migration;

use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use App\Models\Service;
use App\Models\Clinic;
use Illuminate\Support\Str;

class DataTransformer
{
    /**
     * Transform WordPress clinic data to Laravel format
     */
    public function transformClinic($wpClinic)
    {
        return [
            'name' => $this->sanitizeString($wpClinic['name'] ?? ''),
            'email' => $this->sanitizeEmail($wpClinic['email'] ?? ''),
            'phone' => $this->sanitizePhone($wpClinic['telephone_no'] ?? ''),
            'website' => $this->sanitizeString($wpClinic['website'] ?? ''),
            'address' => $this->sanitizeString($wpClinic['address'] ?? ''),
            'city' => $this->sanitizeString($wpClinic['city'] ?? ''),
            'state' => $this->sanitizeString($wpClinic['state'] ?? ''),
            'country' => $this->sanitizeString($wpClinic['country'] ?? ''),
            'country_code' => $this->sanitizeString($wpClinic['country_code'] ?? ''),
            'country_calling_code' => $this->sanitizeString($wpClinic['country_calling_code'] ?? ''),
            'postal_code' => $this->sanitizeString($wpClinic['postal_code'] ?? ''),
            'specialties' => $this->transformSpecialties($wpClinic['specialties'] ?? []),
            'is_active' => $this->convertBoolean($wpClinic['status'] ?? 0),
            'logo' => $this->sanitizeString($wpClinic['clinic_logo'] ?? ''),
            'profile_image' => $this->sanitizeString($wpClinic['profile_image'] ?? ''),
            'clinic_admin_id' => $this->sanitizeInteger($wpClinic['clinic_admin_id'] ?? null),
            'clinic_admin_email' => $this->sanitizeEmail($wpClinic['clinic_admin_email'] ?? ''),
            'allow_no_of_doc' => $this->sanitizeString($wpClinic['allow_no_of_doc'] ?? ''),
            'settings' => $this->transformSettings($wpClinic['extra'] ?? []),
            'wp_clinic_id' => (int) $wpClinic['id'],
            'created_at' => $this->convertDateTime($wpClinic['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress user data to Laravel format
     */
    public function transformUser($wpUser, $clinicId = null)
    {
        // Extract role from WordPress user data
        $wpRole = $this->extractUserRole($wpUser);
        $role = $this->convertRole($wpRole);

        // Parse basic_data JSON field
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');

        // Extract first_name and last_name from WordPress fields or basic_data
        $firstName = $this->sanitizeString($wpUser['first_name'] ?? $basicData['first_name'] ?? '');
        $lastName = $this->sanitizeString($wpUser['last_name'] ?? $basicData['last_name'] ?? '');

        return [
            'name' => $this->sanitizeString($wpUser['display_name'] ?? ''),
            'email' => $this->sanitizeEmail($wpUser['user_email'] ?? ''),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone_number' => $this->sanitizePhone($basicData['mobile_number'] ?? ''),
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'date_of_birth' => $this->convertDate($basicData['dob'] ?? null),
            'address' => $this->sanitizeString($basicData['address'] ?? ''),
            'role' => $role,
            'clinic_id' => $clinicId,
            'is_active' => true,
            'wp_user_id' => (int) $wpUser['ID'],
            'wp_basic_data' => $basicData, // Store original data for reference
            'email_verified_at' => null,
            'password' => $this->generateTemporaryPassword($wpUser['ID']),
            'password_change_required' => true,
            'signup_source' => 'system_created',
            'created_at' => $this->convertDateTime($wpUser['user_registered'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress user data for Patient record
     */
    public function transformPatientData($wpUser, $userId, $clinicId)
    {
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');

        return [
            'user_id' => $userId,
            'clinic_id' => $clinicId,
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'date_of_birth' => $this->convertDate($basicData['dob'] ?? null),

            // Insurance information
            'insurance_provider' => $this->sanitizeString($basicData['insurance_provider'] ?? ''),
            'insurance_policy_number' => $this->sanitizeString($basicData['insurance_no'] ?? ''),

            // Emergency contact (using GP details as emergency contact)
            'emergency_contact_name' => $this->sanitizeString($basicData['registered_gp_name'] ?? ''),
            'emergency_contact_phone' => $this->sanitizeString($basicData['mobile_number'] ?? ''),
            'emergency_contact_relationship' => 'GP',

            // Additional patient-specific data from WordPress
            'emergency_contact' => $this->buildEmergencyContactData($basicData),
            'medical_history' => $this->buildMedicalHistory($basicData),
            'current_medications' => [],

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Build emergency contact data from WordPress basic_data
     */
    protected function buildEmergencyContactData($basicData)
    {
        $emergencyContact = [];

        if (!empty($basicData['registered_gp_name'])) {
            $emergencyContact['gp_name'] = $basicData['registered_gp_name'];
        }

        if (!empty($basicData['registered_gp_address'])) {
            $emergencyContact['gp_address'] = $basicData['registered_gp_address'];
        }

        return $emergencyContact;
    }

    /**
     * Build medical history from WordPress basic_data
     */
    protected function buildMedicalHistory($basicData)
    {
        $medicalHistory = [];

        if (!empty($basicData['nhs'])) {
            $medicalHistory['nhs_number'] = $basicData['nhs'];
        }

        if (!empty($basicData['blood_group'])) {
            $medicalHistory['blood_group'] = $basicData['blood_group'];
        }

        return $medicalHistory;
    }

    /**
     * Transform WordPress user data for Provider record
     */
    public function transformProviderData($wpUser, $userId)
    {
        $basicData = $this->parseBasicData($wpUser['basic_data'] ?? '');
        $specialties = $this->transformSpecialties($basicData['specialties'] ?? []);

        return [
            'user_id' => $userId,
            'specialization' => !empty($specialties) ? $specialties[0]['label'] ?? 'General Practice' : 'General Practice',
            'license_number' => $this->buildLicenseNumber($basicData),
            'verification_status' => 'pending',
            'gender' => $this->convertGender($basicData['gender'] ?? ''),
            'pricing' => $this->transformPricing($basicData),

            // Additional provider data from WordPress
            'bio' => $this->buildProviderBio($basicData),
            'education' => $this->buildEducationString($basicData['qualifications'] ?? []),
            'languages' => ['English'], // Default, can be enhanced later
            'accepts_insurance' => !empty($basicData['insurance_provider']),
            'insurance_providers' => !empty($basicData['insurance_provider']) ? [$basicData['insurance_provider']] : [],

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Build license number with prefix
     */
    protected function buildLicenseNumber($basicData)
    {
        $gmcNo = $this->sanitizeString($basicData['gmc_no'] ?? '');
        $prefix = $this->sanitizeString($basicData['registration_prefix'] ?? '');

        if (!empty($prefix) && !empty($gmcNo)) {
            return strtoupper($prefix) . ': ' . $gmcNo;
        }

        return $gmcNo;
    }

    /**
     * Build provider bio from WordPress data
     */
    protected function buildProviderBio($basicData)
    {
        $bio = [];

        if (!empty($basicData['no_of_experience'])) {
            $bio[] = "Experience: {$basicData['no_of_experience']} years";
        }

        if (!empty($basicData['specialties'])) {
            $specialties = $this->transformSpecialties($basicData['specialties']);
            if (!empty($specialties)) {
                $specialtyNames = array_column($specialties, 'label');
                $bio[] = "Specialties: " . implode(', ', $specialtyNames);
            }
        }

        return implode('. ', $bio);
    }

    /**
     * Build education string from qualifications array
     */
    protected function buildEducationString($qualifications)
    {
        if (empty($qualifications) || !is_array($qualifications)) {
            return '';
        }

        $educationParts = [];
        foreach ($qualifications as $qualification) {
            if (is_array($qualification)) {
                $parts = [];
                if (!empty($qualification['degree'])) {
                    $parts[] = $qualification['degree'];
                }
                if (!empty($qualification['university'])) {
                    $parts[] = $qualification['university'];
                }
                if (!empty($qualification['year'])) {
                    $parts[] = $qualification['year'];
                }
                if (!empty($parts)) {
                    $educationParts[] = implode(' - ', $parts);
                }
            }
        }

        return implode('; ', $educationParts);
    }

    /**
     * Transform pricing data for providers
     */
    protected function transformPricing($basicData)
    {
        $pricing = [];

        if (!empty($basicData['price_type']) && !empty($basicData['price'])) {
            if ($basicData['price_type'] === 'range') {
                $priceRange = explode('-', $basicData['price']);
                $pricing['consultation_fee'] = [
                    'min' => (float) ($priceRange[0] ?? 0),
                    'max' => (float) ($priceRange[1] ?? 0),
                ];
            } else {
                $pricing['consultation_fee'] = (float) $basicData['price'];
            }
        }

        if (!empty($basicData['video_price'])) {
            $pricing['video_consultation_fee'] = (float) $basicData['video_price'];
        }

        return $pricing;
    }

    /**
     * Extract user role from WordPress user data (handles different formats)
     */
    protected function extractUserRole($wpUser)
    {
        // First check for user_type field from the API response
        if (isset($wpUser['user_type']) && !empty($wpUser['user_type'])) {
            return $wpUser['user_type'];
        }

        // Try different possible role formats from WordPress
        if (isset($wpUser['roles']) && is_array($wpUser['roles']) && !empty($wpUser['roles'])) {
            return $wpUser['roles'][0];
        }

        if (isset($wpUser['role']) && !empty($wpUser['role'])) {
            return $wpUser['role'];
        }

        if (isset($wpUser['user_role']) && !empty($wpUser['user_role'])) {
            return $wpUser['user_role'];
        }

        if (isset($wpUser['meta']['wp_capabilities']) && is_array($wpUser['meta']['wp_capabilities'])) {
            $capabilities = array_keys($wpUser['meta']['wp_capabilities']);
            return $capabilities[0] ?? 'patient';
        }

        // Default fallback
        return 'patient';
    }

    /**
     * Transform WordPress service data to Laravel format
     */
    public function transformService($wpService)
    {
        return [
            'name' => $this->sanitizeString($wpService['name'] ?? ''),
            'description' => $this->sanitizeString($wpService['description'] ?? ''),
            'type' => $this->sanitizeString($wpService['type'] ?? 'consultation'),
            'price' => $this->convertDecimal($wpService['price'] ?? 0),
            'duration' => (int) ($wpService['duration'] ?? 30),
            'active' => $this->convertBoolean($wpService['status'] ?? 0),
            'clinic_id' => (int) $wpService['clinic_id'],
            'provider_id' => $this->mapDoctorToProvider($wpService['doctor_id'] ?? null),
            'wp_service_id' => (int) $wpService['id'],
            'created_at' => $this->convertDateTime($wpService['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Map WordPress doctor_id to Laravel provider_id
     */
    protected function mapDoctorToProvider($wpDoctorId)
    {
        if (!$wpDoctorId) {
            return null;
        }

        // Find user by wp_user_id, then find their provider record
        $user = \App\Models\User::where('wp_user_id', $wpDoctorId)->first();

        if (!$user) {
            // User not migrated yet, return null
            return null;
        }

        // Find provider record for this user
        $provider = \App\Models\Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            // User exists but no provider record, return null
            return null;
        }

        return $provider->id;
    }

    /**
     * Transform WordPress category data to Laravel format
     */
    public function transformCategory($wpCategory)
    {
        // Handle both array and object formats
        $data = is_array($wpCategory) ? $wpCategory : (array) $wpCategory;

        return [
            'name' => $this->sanitizeString($data['name'] ?? ''),
            'description' => $this->sanitizeString($data['description'] ?? ''),
            'is_active' => true, // Categories from static data are active
            'clinic_id' => null, // Global categories, not clinic-specific
            'wp_category_id' => (int) ($data['id'] ?? 0),
            'created_at' => $this->convertDateTime($data['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress specialty data to Laravel format
     */
    public function transformSpecialty($wpSpecialty)
    {
        // Handle both array and object formats
        $data = is_array($wpSpecialty) ? $wpSpecialty : (array) $wpSpecialty;

        $label = $this->sanitizeString($data['label'] ?? '');
        $name = strtolower(str_replace([' ', '&', 'And'], ['_', 'and', 'and'], $label));

        return [
            'name' => $name,
            'label' => $label,
            'description' => $this->sanitizeString($data['description'] ?? ''),
            'is_active' => $this->convertBoolean($data['status'] ?? 1),
            'wp_specialty_id' => (int) ($data['id'] ?? 0),
            'sort_order' => (int) ($data['sort_order'] ?? 0),
            'created_at' => $this->convertDateTime($data['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress clinic session data to Laravel provider availability format
     */
    public function transformClinicSession($wpSession, $providerId, $clinicId, $serviceId = null)
    {
        return [
            'provider_id' => $providerId,
            'clinic_id' => $clinicId,
            'service_id' => $serviceId,
            'day_of_week' => $this->convertDayNameToNumber($wpSession['day'] ?? ''),
            'start_time' => $this->convertTime($wpSession['start_time'] ?? ''),
            'end_time' => $this->convertTime($wpSession['end_time'] ?? ''),
            'time_slot' => (int) ($wpSession['time_slot'] ?? 30),
            'parent_id' => $wpSession['parent_id'] ?? null,
            'is_active' => true,
            'wp_clinic_session_id' => (int) $wpSession['id'],
            'wp_session_data' => $wpSession,
            'created_at' => $this->convertDateTime($wpSession['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress clinic schedule data to Laravel provider absence format
     */
    public function transformClinicSchedule($wpSchedule, $providerId = null, $clinicId)
    {
        return [
            'provider_id' => $providerId,
            'clinic_id' => $clinicId,
            'start_date' => $this->convertDate($wpSchedule['start_date'] ?? null),
            'end_date' => $this->convertDate($wpSchedule['end_date'] ?? null),
            'start_time' => $this->convertTime($wpSchedule['start_time'] ?? null),
            'end_time' => $this->convertTime($wpSchedule['end_time'] ?? null),
            'all_day' => $this->convertBoolean($wpSchedule['all_day'] ?? 1),
            'reason' => $this->sanitizeString($wpSchedule['description'] ?? 'Scheduled absence'),
            'module_type' => $this->sanitizeString($wpSchedule['module_type'] ?? ''),
            'module_id' => (int) ($wpSchedule['module_id'] ?? 0),
            'status' => $this->convertBoolean($wpSchedule['status'] ?? 1),
            'wp_clinic_schedule_id' => (int) $wpSchedule['id'],
            'wp_schedule_data' => $wpSchedule,
            'created_at' => $this->convertDateTime($wpSchedule['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Convert day name to day_of_week number
     */
    protected function convertDayNameToNumber($dayName)
    {
        $days = [
            'Sunday' => 0,
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6,
        ];

        return $days[$dayName] ?? 1; // Default to Monday
    }

    /**
     * Convert time string to proper format
     */
    protected function convertTime($time)
    {
        if (empty($time)) {
            return null;
        }

        try {
            return \Carbon\Carbon::createFromFormat('H:i:s', $time)->format('H:i:s');
        } catch (\Exception $e) {
            try {
                return \Carbon\Carbon::createFromFormat('H:i', $time)->format('H:i:s');
            } catch (\Exception $e) {
                return null;
            }
        }
    }

    /**
     * Transform WordPress specialization data to Laravel format
     */
    public function transformSpecialization($wpSpecialization)
    {
        // Handle both array and object formats
        $data = is_array($wpSpecialization) ? $wpSpecialization : (array) $wpSpecialization;

        $label = $this->sanitizeString($data['name'] ?? ''); // WordPress uses 'name' field
        $name = $data['value'] ?? strtolower(str_replace([' ', '&', 'And'], ['_', 'and', 'and'], $label));

        return [
            'name' => $name,
            'label' => $label,
            'description' => $this->sanitizeString($data['description'] ?? ''),
            'specialty_id' => null, // Keep null for now, no complex mapping
            'is_active' => $this->convertBoolean($data['status'] ?? 1),
            'wp_specialization_id' => (int) ($data['id'] ?? 0), // Keep WordPress ID as-is
            'sort_order' => (int) ($data['sort_order'] ?? 0),
            'created_at' => $this->convertDateTime($data['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress appointment data to Laravel format
     */
    public function transformAppointment($wpAppointment, $patientId, $providerId)
    {
        $scheduledAt = $this->combineDateTime(
            $wpAppointment['appointment_start_date'] ?? null,
            $wpAppointment['appointment_start_time'] ?? null
        );

        $endedAt = $this->combineDateTime(
            $wpAppointment['appointment_end_date'] ?? null,
            $wpAppointment['appointment_end_time'] ?? null
        );

        return [
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'date' => $this->convertDate($wpAppointment['appointment_start_date'] ?? null),
            'time_slot' => $wpAppointment['appointment_start_time'] ?? null,
            'scheduled_at' => $scheduledAt,
            'ended_at' => $endedAt,
            'duration_minutes' => $this->calculateDuration($scheduledAt, $endedAt),
            'reason' => $this->sanitizeString($wpAppointment['description'] ?? ''),
            'status' => $this->convertAppointmentStatus($wpAppointment['status'] ?? 0),
            'consultation_type' => $this->sanitizeString($wpAppointment['visit_type'] ?? 'in_person'),
            'is_telemedicine' => $this->isTelemedicine($wpAppointment['visit_type'] ?? ''),
            'payment_status' => 'pending',
            'wp_appointment_id' => (int) $wpAppointment['id'],
            'created_at' => $this->convertDateTime($wpAppointment['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress encounter data to Laravel consultation format
     */
    public function transformConsultation($wpEncounter, $appointmentId, $patientId, $providerId)
    {
        return [
            'appointment_id' => $appointmentId,
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'clinic_id' => (int) $wpEncounter['clinic_id'],
            'consultation_type' => 'in_person',
            'status' => $this->convertConsultationStatus($wpEncounter['status'] ?? 0),
            'consultation_date' => $this->convertDateTime($wpEncounter['encounter_date'] ?? null),
            'consultation_mode' => 'in_person',
            'is_telemedicine' => false,
            'vital_signs' => $this->transformVitalSigns($wpEncounter['vitals'] ?? []),
            'main_tabs' => $this->transformConsultationTabs($wpEncounter['tabs'] ?? []),
            'additional_tabs' => [],
            'wp_encounter_id' => (int) $wpEncounter['id'],
            'created_at' => $this->convertDateTime($wpEncounter['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress prescription data to Laravel format
     */
    public function transformPrescription($wpPrescription, $consultationId, $patientId, $providerId)
    {
        return [
            'consultation_id' => $consultationId,
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'medication_name' => $this->sanitizeString($wpPrescription['name'] ?? ''),
            'dosage' => $this->extractDosage($wpPrescription['name'] ?? ''),
            'frequency' => $this->sanitizeString($wpPrescription['frequency'] ?? ''),
            'duration' => $this->sanitizeString($wpPrescription['duration'] ?? ''),
            'instructions' => $this->sanitizeString($wpPrescription['instruction'] ?? ''),
            'status' => 'active',
            'wp_prescription_id' => (int) $wpPrescription['id'],
            'created_at' => $this->convertDateTime($wpPrescription['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    // Helper methods for data conversion

    /**
     * Convert WordPress role to Laravel role
     */
    public function convertRole($wpRole)
    {
        $mapping = config('migration.role_mapping', []);
        return $mapping[$wpRole] ?? 'patient';
    }

    /**
     * Convert appointment status
     */
    public function convertAppointmentStatus($status)
    {
        $mapping = config('migration.appointment_status_mapping', []);
        return $mapping[$status] ?? 'scheduled';
    }

    /**
     * Convert consultation status
     */
    public function convertConsultationStatus($status)
    {
        $mapping = config('migration.consultation_status_mapping', []);
        return $mapping[$status] ?? 'draft';
    }

    /**
     * Convert boolean value
     */
    public function convertBoolean($value)
    {
        return $value == 1 || $value === true || $value === 'true';
    }

    /**
     * Convert date string to Carbon instance
     */
    public function convertDate($date)
    {
        if (!$date || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return null;
        }

        try {
            return Carbon::parse($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Convert datetime string to Carbon instance
     */
    public function convertDateTime($datetime)
    {
        if (!$datetime || $datetime === '0000-00-00 00:00:00') {
            return null;
        }

        try {
            return Carbon::parse($datetime);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Combine date and time into datetime
     */
    public function combineDateTime($date, $time)
    {
        if (!$date || !$time) {
            return null;
        }

        try {
            return Carbon::parse($date . ' ' . $time);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Calculate duration between two datetime objects
     */
    public function calculateDuration($start, $end)
    {
        if (!$start || !$end) {
            return 30; // Default 30 minutes
        }

        try {
            return $start->diffInMinutes($end);
        } catch (\Exception $e) {
            return 30;
        }
    }

    /**
     * Check if appointment is telemedicine
     */
    public function isTelemedicine($visitType)
    {
        $telemedicineTypes = ['video', 'phone', 'online', 'virtual', 'telemedicine'];
        return in_array(strtolower($visitType), $telemedicineTypes);
    }

    /**
     * Sanitize string input
     */
    public function sanitizeString($value)
    {
        if (is_array($value)) {
            // If it's an array, join the values or take the first one
            return trim(strip_tags(implode(', ', $value)));
        }

        return trim(strip_tags($value ?? ''));
    }

    /**
     * Sanitize email input
     */
    public function sanitizeEmail($email)
    {
        $email = trim(strtolower($email ?? ''));
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : null;
    }

    /**
     * Sanitize phone number
     */
    public function sanitizePhone($phone)
    {
        if (is_array($phone)) {
            $phone = reset($phone); // Get first element
        }
        return preg_replace('/[^0-9+\-\s\(\)]/', '', $phone ?? '');
    }

    /**
     * Sanitize integer input
     */
    public function sanitizeInteger($value)
    {
        if (is_array($value)) {
            $value = reset($value); // Get first element
        }

        if ($value === null || $value === '' || $value === '0') {
            return null;
        }
        return (int) $value;
    }

    /**
     * Convert decimal value
     */
    public function convertDecimal($value)
    {
        return round(floatval($value ?? 0), 2);
    }

    /**
     * Convert gender value
     */
    public function convertGender($gender)
    {
        $gender = strtolower(trim($gender ?? ''));
        $validGenders = ['male', 'female', 'other'];
        return in_array($gender, $validGenders) ? $gender : null;
    }

    /**
     * Generate temporary password
     */
    public function generateTemporaryPassword($userId)
    {
        return Hash::make('temporary_password_' . $userId);
    }

    /**
     * Transform specialties array
     */
    public function transformSpecialties($specialties)
    {
        if (is_string($specialties)) {
            return json_decode($specialties, true) ?? [];
        }
        return is_array($specialties) ? $specialties : [];
    }

    /**
     * Transform qualifications array
     */
    public function transformQualifications($qualifications)
    {
        if (is_string($qualifications)) {
            return json_decode($qualifications, true) ?? [];
        }
        return is_array($qualifications) ? $qualifications : [];
    }

    /**
     * Parse basic_data JSON field from WordPress
     */
    public function parseBasicData($basicDataJson)
    {
        if (empty($basicDataJson)) {
            return [];
        }

        if (is_string($basicDataJson)) {
            $decoded = json_decode($basicDataJson, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($basicDataJson) ? $basicDataJson : [];
    }

    /**
     * Transform settings/extra data
     */
    public function transformSettings($extra)
    {
        if (is_string($extra)) {
            return json_decode($extra, true) ?? [];
        }
        return is_array($extra) ? $extra : [];
    }

    /**
     * Transform vital signs
     */
    public function transformVitalSigns($vitals)
    {
        return is_array($vitals) ? $vitals : [];
    }

    /**
     * Transform consultation tabs
     */
    public function transformConsultationTabs($tabs)
    {
        $transformed = [];
        
        if (is_array($tabs)) {
            foreach ($tabs as $tab) {
                $tabName = Str::slug($tab['tab_name'] ?? 'general');
                $transformed[$tabName] = [
                    [
                        'id' => 1,
                        'content' => $tab['content'] ?? '',
                        'created_at' => now()->toISOString(),
                    ]
                ];
            }
        }
        
        return $transformed;
    }

    /**
     * Transform WordPress bill data to Laravel format
     */
    public function transformBill($wpBill, $consultation, $appointment, $patientId, $providerId)
    {
        return [
            'patient_id' => $patientId,
            'provider_id' => $providerId,
            'clinic_id' => $consultation ? $consultation->clinic_id : ($appointment ? $appointment->clinic_id : null),
            'consultation_id' => $consultation ? $consultation->id : null,
            'appointment_id' => $appointment ? $appointment->id : null,
            'bill_number' => $this->generateBillNumber($wpBill['id']),
            'description' => $this->sanitizeString($wpBill['title'] ?? ''),
            'subtotal_amount' => $this->convertDecimal($wpBill['total_amount'] ?? 0),
            'discount_amount' => $this->convertDecimal($wpBill['discount'] ?? 0),
            'tax_amount' => 0, // WordPress doesn't seem to have separate tax
            'total_amount' => $this->convertDecimal($wpBill['total_amount'] ?? 0),
            'final_amount' => $this->convertDecimal($wpBill['actual_amount'] ?? 0),
            'status' => $this->convertBillStatus($wpBill['status'] ?? 0),
            'payment_status' => $this->sanitizeString($wpBill['payment_status'] ?? 'pending'),
            'due_date' => now()->addDays(30), // Default 30 days
            'notes' => '',
            'wp_bill_id' => (int) $wpBill['id'],
            'created_at' => $this->convertDateTime($wpBill['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Convert bill status
     */
    public function convertBillStatus($status)
    {
        $mapping = config('migration.bill_status_mapping', [
            0 => 'draft',
            1 => 'sent',
            2 => 'paid',
            3 => 'overdue',
        ]);
        return $mapping[$status] ?? 'draft';
    }

    /**
     * Generate bill number from WordPress ID
     */
    public function generateBillNumber($wpBillId)
    {
        return 'WP-' . str_pad($wpBillId, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Extract dosage from medication name
     */
    public function extractDosage($medicationName)
    {
        // Simple regex to extract dosage like "Aspirin 81mg" -> "81mg"
        if (preg_match('/(\d+(?:\.\d+)?\s*(?:mg|g|ml|mcg|units?))/i', $medicationName, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Convert duration string to days
     */
    public function convertDurationToDays($duration)
    {
        if (empty($duration)) {
            return null;
        }

        $duration = strtolower(trim($duration));

        // Extract number from duration string
        if (preg_match('/(\d+)/', $duration, $matches)) {
            $number = (int) $matches[1];

            // Check for time units
            if (strpos($duration, 'week') !== false) {
                return $number * 7;
            } elseif (strpos($duration, 'month') !== false) {
                return $number * 30;
            } elseif (strpos($duration, 'year') !== false) {
                return $number * 365;
            } else {
                // Assume days if no unit specified
                return $number;
            }
        }

        return null;
    }

    /**
     * Simple transform methods for clinical migration (without pre-resolved IDs)
     */

    /**
     * Transform WordPress appointment data (simple version)
     */
    public function transformAppointmentSimple($wpAppointment)
    {
        $scheduledAt = $this->combineDateTime(
            $wpAppointment['appointment_start_date'] ?? null,
            $wpAppointment['appointment_start_time'] ?? null
        );

        $endedAt = $this->combineDateTime(
            $wpAppointment['appointment_end_date'] ?? null,
            $wpAppointment['appointment_end_time'] ?? null
        );

        // Create proper time_slot JSON structure
        $timeSlot = [
            'start_time' => $wpAppointment['appointment_start_time'] ?? null,
            'end_time' => $wpAppointment['appointment_end_time'] ?? null,
        ];

        return [
            'date' => $this->convertDate($wpAppointment['appointment_start_date'] ?? null),
            'time_slot' => $timeSlot,
            'scheduled_at' => $scheduledAt,
            'ended_at' => $endedAt,
            'duration_minutes' => $this->calculateDuration($scheduledAt, $endedAt),
            'reason' => $this->sanitizeString($wpAppointment['description'] ?? ''),
            'status' => $this->convertAppointmentStatus($wpAppointment['status'] ?? 0),
            'consultation_type' => $this->sanitizeString($wpAppointment['visit_type'] ?? 'in_person'),
            'is_telemedicine' => $this->isTelemedicine($wpAppointment['visit_type'] ?? ''),
            'payment_status' => 'pending',
            'created_at' => $this->convertDateTime($wpAppointment['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress consultation data (simple version)
     */
    public function transformConsultationSimple($wpEncounter)
    {
        $description = $this->sanitizeString($wpEncounter['description'] ?? '');
        $consultationMode = $this->determineConsultationMode($description);

        return [
            'consultation_type' => $this->determineConsultationType($wpEncounter),
            'status' => $this->convertConsultationStatus($wpEncounter['status'] ?? 0),
            'consultation_date' => $this->convertDateTime($wpEncounter['encounter_date'] ?? null),
            'consultation_mode' => $consultationMode,
            'description' => $description,
            'added_by_wp_id' => (int) ($wpEncounter['added_by'] ?? null),
            'is_telemedicine' => $consultationMode === 'phone' || $consultationMode === 'video',
            'vital_signs' => $this->transformVitalSigns($wpEncounter['vitals'] ?? []),
            'main_tabs' => $this->transformConsultationTabs($wpEncounter['tabs'] ?? []),
            'additional_tabs' => [],
            'created_at' => $this->convertDateTime($wpEncounter['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Determine consultation mode from description
     */
    protected function determineConsultationMode($description)
    {
        $description = strtolower($description);

        if (str_contains($description, 'telephone') || str_contains($description, 'phone') || str_contains($description, 'call')) {
            return 'phone';
        }

        if (str_contains($description, 'video') || str_contains($description, 'online') || str_contains($description, 'virtual')) {
            return 'video';
        }

        return 'in_person';
    }

    /**
     * Determine consultation type from WordPress encounter data
     */
    protected function determineConsultationType($wpEncounter)
    {
        $description = strtolower($wpEncounter['description'] ?? '');

        // Check for follow-up indicators
        if (str_contains($description, 'follow') || str_contains($description, 'review') || str_contains($description, 'results')) {
            return 'follow_up';
        }

        // Check for emergency indicators
        if (str_contains($description, 'urgent') || str_contains($description, 'emergency') || str_contains($description, 'acute')) {
            return 'emergency';
        }

        // Check if it's linked to an appointment
        if (!empty($wpEncounter['appointment_id'])) {
            return 'scheduled';
        }

        return 'general';
    }

    /**
     * Transform WordPress prescription data (simple version)
     * Creates prescription container for encounter
     */
    public function transformPrescriptionSimple($wpEncounter)
    {
        // Handle encounter data structure
        $encounterDate = $wpEncounter['encounter_date'] ?? null;
        $createdAt = null;

        // Get created_at from first prescription item if available
        if (isset($wpEncounter['prescriptions']) && !empty($wpEncounter['prescriptions'])) {
            $createdAt = $wpEncounter['prescriptions'][0]['created_at'] ?? null;
        }

        return [
            'prescription_number' => 'RX-' . uniqid(),
            'status' => 'active',
            'prescribed_date' => $this->convertDate($encounterDate ?? $createdAt) ?: now()->format('Y-m-d'),
            'clinical_indication' => null, // Will be set from consultation if available
            'additional_instructions' => null, // General prescription instructions
            'total_items' => 1, // Will be updated when items are added
            'created_at' => $this->convertDateTime($createdAt) ?: now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress prescription item data
     * Creates individual medicine record
     */
    public function transformPrescriptionItem($wpPrescription)
    {
        return [
            'medication_name' => $this->sanitizeString($wpPrescription['medicine_name'] ?? ''),
            'strength' => 'Not specified', // Default value since not available in WordPress data
            'form' => 'Not specified', // Default value since not available in WordPress data
            'dosage' => 'As directed', // Default value since not available in WordPress data
            'frequency' => $this->sanitizeString($wpPrescription['frequency'] ?? ''),
            'route' => 'oral', // Default to oral route
            'quantity' => 1, // Default quantity
            'quantity_unit' => 'units', // Default unit
            'duration_days' => $this->convertDurationToDays($wpPrescription['duration'] ?? null),
            'directions_for_use' => $this->sanitizeString($wpPrescription['instruction'] ?? ''),
            'additional_instructions' => $this->sanitizeString($wpPrescription['instruction'] ?? ''),
            'status' => 'pending',
            'created_at' => $this->convertDateTime($wpPrescription['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress medical history data (simple version)
     */
    public function transformMedicalHistory($wpHistory)
    {
        return [
            'type' => $this->sanitizeString($wpHistory['type'] ?? 'general'),
            'condition' => $this->sanitizeString($wpHistory['condition'] ?? ''),
            'description' => $this->sanitizeString($wpHistory['description'] ?? ''),
            'diagnosis_date' => $this->convertDate($wpHistory['diagnosis_date'] ?? null),
            'status' => $this->sanitizeString($wpHistory['status'] ?? 'active'),
            'severity' => $this->sanitizeString($wpHistory['severity'] ?? 'mild'),
            'notes' => $this->sanitizeString($wpHistory['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpHistory['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress bill data from new API structure
     */
    public function transformBillFromWordPress($wpBill, $encounterData)
    {
        return [
            'bill_number' => 'BILL-' . ($wpBill['id'] ?? uniqid()),
            'title' => $this->sanitizeString($wpBill['title'] ?? ''),
            'bill_date' => $this->convertDateTime($wpBill['created_at'] ?? null) ?? now(),
            'due_date' => now()->addDays(30), // Default 30 days
            'subtotal' => (float) ($wpBill['total_amount'] ?? 0),
            'tax_amount' => 0, // WordPress doesn't seem to have separate tax
            'discount' => (float) ($wpBill['discount'] ?? 0),
            'total_amount' => (float) ($wpBill['total_amount'] ?? 0),
            'status' => $this->convertBillStatus($wpBill['status'] ?? 0),
            'payment_status' => $this->sanitizeString($wpBill['payment_status'] ?? 'unpaid'),
            'notes' => '',
            'created_at' => $this->convertDateTime($wpBill['created_at'] ?? null) ?? now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress bill data (simple version)
     */
    public function transformBillSimple($wpBill)
    {
        return [
            'bill_number' => $wpBill['bill_number'] ?? 'BILL-' . uniqid(),
            'bill_date' => $this->convertDate($wpBill['bill_date'] ?? null),
            'due_date' => $this->convertDate($wpBill['due_date'] ?? null),
            'subtotal' => (float) ($wpBill['subtotal'] ?? 0),
            'tax_amount' => (float) ($wpBill['tax_amount'] ?? 0),
            'discount_amount' => (float) ($wpBill['discount_amount'] ?? 0),
            'total_amount' => (float) ($wpBill['total_amount'] ?? 0),
            'paid_amount' => (float) ($wpBill['paid_amount'] ?? 0),
            'status' => $this->convertBillStatus($wpBill['status'] ?? 0), // Use existing method
            'payment_status' => $this->sanitizeString($wpBill['payment_status'] ?? 'pending'),
            'payment_method' => $this->sanitizeString($wpBill['payment_method'] ?? 'cash'),
            'notes' => $this->sanitizeString($wpBill['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpBill['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress medical problem data (simple version)
     */
    public function transformMedicalProblem($wpProblem)
    {
        return [
            'problem_type' => $this->sanitizeString($wpProblem['problem_type'] ?? ''),
            'description' => $this->sanitizeString($wpProblem['description'] ?? ''),
            'start_date' => $this->convertDate($wpProblem['start_date'] ?? null),
            'end_date' => $this->convertDate($wpProblem['end_date'] ?? null),
            'status' => $this->sanitizeString($wpProblem['status'] ?? 'active'),
            'severity' => $this->sanitizeString($wpProblem['severity'] ?? 'mild'),
            'outcome' => $this->sanitizeString($wpProblem['outcome'] ?? ''),
            'notes' => $this->sanitizeString($wpProblem['notes'] ?? ''),
            'created_at' => $this->convertDateTime($wpProblem['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress patient document data (simple version)
     */
    public function transformPatientDocument($wpDocument)
    {
        return [
            'document_name' => $this->sanitizeString($wpDocument['document_name'] ?? ''),
            'document_type' => $this->sanitizeString($wpDocument['document_type'] ?? 'general'),
            'file_path' => $this->sanitizeString($wpDocument['file_path'] ?? ''),
            'file_url' => $this->sanitizeString($wpDocument['file_url'] ?? ''),
            'file_size' => (int) ($wpDocument['file_size'] ?? 0),
            'mime_type' => $this->sanitizeString($wpDocument['mime_type'] ?? ''),
            'description' => $this->sanitizeString($wpDocument['description'] ?? ''),
            'is_private' => (bool) ($wpDocument['is_private'] ?? false),
            'uploaded_by' => $this->sanitizeString($wpDocument['uploaded_by'] ?? ''),
            'created_at' => $this->convertDateTime($wpDocument['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform WordPress patient document data from API response
     */
    public function transformPatientDocumentFromApi($wpDocument, $patientData)
    {
        $fileInfo = $wpDocument['file_info'] ?? [];

        return [
            'document_type' => $this->mapDocumentType($wpDocument['type'] ?? 'other'),
            'file_path' => $this->sanitizeString($fileInfo['file_path'] ?? ''),
            'file_name' => $this->sanitizeString($wpDocument['name'] ?? 'unknown'),
            'original_name' => $this->sanitizeString($fileInfo['attachment_title'] ?? $wpDocument['name'] ?? 'unknown'),
            'file_size' => (int) ($fileInfo['file_size'] ?? 0),
            'mime_type' => $this->sanitizeString($fileInfo['file_mime_type'] ?? 'application/octet-stream'),
            'description' => $this->sanitizeString($wpDocument['description'] ?? ''),
            'wp_created_date' => $this->convertDateTime($wpDocument['created_at'] ?? null),
        ];
    }

    /**
     * Map WordPress document types to Laravel document types
     */
    private function mapDocumentType($wpType)
    {
        $typeMapping = [
            'medical_report' => 'medical_report',
            'lab_report' => 'lab_report',
            'scan' => 'scan',
            'other' => 'other',
            'prescription' => 'prescription',
            'letter' => 'letter',
            'image' => 'image',
            'pdf' => 'pdf',
        ];

        return $typeMapping[$wpType] ?? 'other';
    }

    /**
     * Find service ID by name and provider
     */
    public function findServiceByName($serviceName, $providerId = null, $clinicId = null)
    {
        if (empty($serviceName)) {
            return null;
        }

        $query = Service::where('name', 'like', '%' . trim($serviceName) . '%')
                       ->where('active', true);

        // Prefer exact matches first
        $exactMatch = clone $query;
        $exactMatch->where('name', trim($serviceName));

        if ($providerId) {
            $exactMatch->where('provider_id', $providerId);
        }

        if ($clinicId) {
            $exactMatch->where('clinic_id', $clinicId);
        }

        $service = $exactMatch->first();

        if ($service) {
            return $service->id;
        }

        // If no exact match, try partial match
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }

        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }

        $service = $query->first();

        return $service ? $service->id : null;
    }

    /**
     * Find clinic ID by WordPress clinic ID
     */
    public function findClinicByWpId($wpClinicId)
    {
        if (empty($wpClinicId)) {
            return null;
        }

        $clinic = Clinic::where('wp_clinic_id', $wpClinicId)->first();

        return $clinic ? $clinic->id : null;
    }

    /**
     * Transform WordPress encounter tabs data to Laravel consultation format
     * Processes the new API response structure with tabs_by_type
     */
    public function transformEncounterTabsData($wpEncounterData)
    {
        $result = [
            'vital_signs' => [],
            'main_tabs' => [],
            'additional_tabs' => []
        ];

        try {
            if (empty($wpEncounterData) || !is_array($wpEncounterData)) {
                \Log::warning('Empty or invalid encounter data provided to transformer', [
                    'data_type' => gettype($wpEncounterData),
                    'data_empty' => empty($wpEncounterData)
                ]);
                return $result;
            }

            // Process each patient's encounter data
            foreach ($wpEncounterData as $patientEmail => $patientData) {
                if (!$this->validatePatientData($patientData, $patientEmail)) {
                    continue;
                }

                // Process each encounter for this patient
                foreach ($patientData['encounters'] as $encounterId => $encounterData) {
                    if (!$this->validateEncounterData($encounterData, $encounterId)) {
                        continue;
                    }

                    // Process tabs by type
                    foreach ($encounterData['tabs_by_type'] as $tabType => $tabs) {
                        if (!is_array($tabs)) {
                            \Log::warning('Invalid tabs data for tab type', [
                                'tab_type' => $tabType,
                                'encounter_id' => $encounterId,
                                'data_type' => gettype($tabs)
                            ]);
                            continue;
                        }

                        // Normalize tab type
                        $normalizedTabType = $this->normalizeTabType($tabType);

                        // Process each tab entry
                        foreach ($tabs as $tabIndex => $tab) {
                            try {
                                if (!$this->validateTabData($tab, $normalizedTabType, $encounterId, $tabIndex)) {
                                    continue;
                                }

                                $transformedTab = $this->transformSingleEncounterTab($tab, $normalizedTabType, $encounterId);

                                if ($normalizedTabType === 'vitals') {
                                    // Extract and merge vital signs
                                    $vitalSigns = $this->extractVitalSigns($tab['content'] ?? '');
                                    if (!empty($vitalSigns)) {
                                        $result['vital_signs'] = array_merge($result['vital_signs'], $vitalSigns);
                                    }
                                } elseif ($this->isMainTab($normalizedTabType)) {
                                    // Add to main tabs
                                    if (!isset($result['main_tabs'][$normalizedTabType])) {
                                        $result['main_tabs'][$normalizedTabType] = [];
                                    }
                                    $result['main_tabs'][$normalizedTabType][] = $transformedTab;
                                } else {
                                    // Add to additional tabs
                                    if (!isset($result['additional_tabs'][$normalizedTabType])) {
                                        $result['additional_tabs'][$normalizedTabType] = [];
                                    }
                                    $result['additional_tabs'][$normalizedTabType][] = $transformedTab;
                                }
                            } catch (\Exception $e) {
                                \Log::error('Failed to transform individual tab', [
                                    'tab_type' => $normalizedTabType,
                                    'encounter_id' => $encounterId,
                                    'tab_index' => $tabIndex,
                                    'error' => $e->getMessage(),
                                    'tab_data' => $tab
                                ]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('Critical error in encounter tabs transformation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * Transform a single encounter tab with comprehensive error handling
     */
    public function transformSingleEncounterTab($wpTab, $tabType, $encounterId)
    {
        try {
            // Validate input data
            if (!is_array($wpTab)) {
                \Log::warning('Invalid tab data provided to transformSingleEncounterTab', [
                    'tab_type' => $tabType,
                    'encounter_id' => $encounterId,
                    'data_type' => gettype($wpTab)
                ]);
                return null;
            }

            // Process content with enhanced sanitization
            $content = $this->processTabContent($wpTab['content'] ?? '');

            // Process metadata with error handling
            $metadata = $this->processTabMetadata($wpTab['metadata'] ?? '{}');

            // Validate and process timestamps
            $createdAt = $this->safeConvertDateTime($wpTab['created_at'] ?? null);
            $updatedAt = $this->safeConvertDateTime($wpTab['updated_at'] ?? null);

            // Ensure we have valid IDs
            $tabId = $this->sanitizeInteger($wpTab['id'] ?? null);
            $addedBy = $this->sanitizeInteger($wpTab['added_by'] ?? null);

            return [
                'id' => $tabId,
                'content' => $content,
                'metadata' => $metadata,
                'is_from_template' => $this->convertBoolean($wpTab['is_from_template'] ?? '0'),
                'added_by' => $addedBy,
                'created_at' => $createdAt,
                'updated_at' => $updatedAt ?: now(),
                'wp_encounter_tab_id' => $tabId,
                'wp_encounter_id' => $encounterId,
                'tab_type' => $tabType,
                'content_length' => strlen($content),
                'has_metadata' => !empty($metadata)
            ];
        } catch (\Exception $e) {
            \Log::error('Failed to transform single encounter tab', [
                'tab_type' => $tabType,
                'encounter_id' => $encounterId,
                'error' => $e->getMessage(),
                'tab_data' => $wpTab
            ]);
            return null;
        }
    }

    /**
     * Process tab content with enhanced sanitization
     */
    protected function processTabContent($content)
    {
        if (empty($content)) {
            return '';
        }

        // Handle different content types
        if (is_array($content)) {
            $content = json_encode($content);
        } elseif (!is_string($content)) {
            $content = (string)$content;
        }

        // Clean HTML but preserve line breaks
        $content = str_replace(['<br>', '<br/>', '<br />'], "\n", $content);
        $content = strip_tags($content);

        // Normalize whitespace but preserve paragraphs
        $content = preg_replace('/\n\s*\n/', "\n\n", $content);
        $content = preg_replace('/[ \t]+/', ' ', $content);

        // Remove excessive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        // Trim and handle encoding issues
        $content = trim($content);

        // Ensure UTF-8 encoding
        if (!mb_check_encoding($content, 'UTF-8')) {
            $content = mb_convert_encoding($content, 'UTF-8', 'auto');
        }

        return $content;
    }

    /**
     * Process tab metadata with error handling
     */
    protected function processTabMetadata($metadata)
    {
        if (empty($metadata)) {
            return [];
        }

        // Handle different metadata formats
        if (is_string($metadata)) {
            try {
                $decoded = json_decode($metadata, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return is_array($decoded) ? $decoded : [];
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to decode metadata JSON', [
                    'metadata' => substr($metadata, 0, 100),
                    'error' => $e->getMessage()
                ]);
            }
            return [];
        }

        if (is_array($metadata)) {
            return $metadata;
        }

        return [];
    }

    /**
     * Safe datetime conversion with error handling
     */
    protected function safeConvertDateTime($dateTime)
    {
        try {
            return $this->convertDateTime($dateTime);
        } catch (\Exception $e) {
            \Log::warning('Failed to convert datetime', [
                'datetime' => $dateTime,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if a tab type is a main tab according to consultation template
     */
    public function isMainTab($tabType)
    {
        $mainTabs = ['concerns', 'history', 'examination', 'plan'];
        return in_array($tabType, $mainTabs);
    }

    /**
     * Extract vital signs from content text with comprehensive edge case handling
     */
    public function extractVitalSigns($content)
    {
        $vitalSigns = [];

        if (empty($content) || !is_string($content)) {
            return $vitalSigns;
        }

        // Clean content for better parsing
        $content = $this->cleanContentForVitalSigns($content);

        // Enhanced patterns for extracting vital signs with more variations
        $patterns = [
            'blood_pressure' => [
                '/(?:blood\s*pressure|bp)[\s:=\-]*(\d{2,3})[\s]*[\/\\\][\s]*(\d{2,3})/i',
                '/(\d{2,3})[\s]*[\/\\\][\s]*(\d{2,3})[\s]*(?:mmhg|mm\s*hg)?/i'
            ],
            'heart_rate' => [
                '/(?:heart\s*rate|hr|pulse|bpm)[\s:=\-]*(\d{2,3})(?:\s*bpm)?/i',
                '/(\d{2,3})[\s]*(?:bpm|beats)/i'
            ],
            'respiratory_rate' => [
                '/(?:respiratory\s*rate|rr|resp|breathing\s*rate)[\s:=\-]*(\d{1,2})/i',
                '/(\d{1,2})[\s]*(?:breaths|resp)/i'
            ],
            'oxygen_saturation' => [
                '/(?:oxygen\s*saturation|o2\s*sat|spo2|sat)[\s:=\-]*(\d{2,3})%?/i',
                '/(\d{2,3})%[\s]*(?:o2|oxygen|sat)/i'
            ],
            'temperature' => [
                '/(?:temperature|temp|t)[\s:=\-]*(\d{2,3}(?:\.\d{1,2})?)[\s]*[°]?[fc]?/i',
                '/(\d{2,3}(?:\.\d{1,2})?)[\s]*[°][fc]/i'
            ],
            'weight' => [
                '/(?:weight|wt|mass)[\s:=\-]*(\d{1,3}(?:\.\d{1,2})?)[\s]*(?:kg|kgs|lbs?|pounds?)?/i',
                '/(\d{1,3}(?:\.\d{1,2})?)[\s]*(?:kg|kgs|lbs?|pounds)/i'
            ],
            'height' => [
                '/(?:height|ht|tall)[\s:=\-]*(\d{2,3}(?:\.\d{1,2})?)[\s]*(?:cm|cms|m|meters?|ft|feet|in|inches?)?/i',
                '/(\d{2,3}(?:\.\d{1,2})?)[\s]*(?:cm|cms|m|meters?|ft|feet|in|inches)/i'
            ]
        ];

        foreach ($patterns as $type => $patternArray) {
            foreach ($patternArray as $pattern) {
                try {
                    if (preg_match($pattern, $content, $matches)) {
                        $value = $this->processVitalSignMatch($type, $matches);
                        if ($value !== null) {
                            if ($type === 'blood_pressure' && is_array($value)) {
                                $vitalSigns = array_merge($vitalSigns, $value);
                            } else {
                                $vitalSigns[$type] = $value;
                            }
                            break; // Use first match for each type
                        }
                    }
                } catch (\Exception $e) {
                    \Log::warning('Regex error in vital signs extraction', [
                        'pattern' => $pattern,
                        'content' => substr($content, 0, 100),
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        return $this->validateAndCleanVitalSigns($vitalSigns);
    }

    /**
     * Clean content for better vital signs parsing
     */
    protected function cleanContentForVitalSigns($content)
    {
        // Remove HTML tags
        $content = strip_tags($content);

        // Normalize whitespace
        $content = preg_replace('/\s+/', ' ', $content);

        // Remove common noise characters but keep vital punctuation
        $content = preg_replace('/[^\w\s\.\-\/\\\%°:=]/', ' ', $content);

        // Normalize common separators
        $content = str_replace(['–', '—', '−'], '-', $content);

        return trim($content);
    }

    /**
     * Process vital sign match and return appropriate value
     */
    protected function processVitalSignMatch($type, $matches)
    {
        switch ($type) {
            case 'blood_pressure':
                if (isset($matches[2])) {
                    $systolic = (int)$matches[1];
                    $diastolic = (int)$matches[2];

                    // Validate BP ranges
                    if ($systolic >= 70 && $systolic <= 250 &&
                        $diastolic >= 40 && $diastolic <= 150) {
                        return [
                            'blood_pressure_systolic' => $systolic,
                            'blood_pressure_diastolic' => $diastolic
                        ];
                    }
                }
                break;

            case 'heart_rate':
                $value = (int)$matches[1];
                return ($value >= 30 && $value <= 200) ? $value : null;

            case 'respiratory_rate':
                $value = (int)$matches[1];
                return ($value >= 8 && $value <= 40) ? $value : null;

            case 'oxygen_saturation':
                $value = (int)$matches[1];
                return ($value >= 70 && $value <= 100) ? $value : null;

            case 'temperature':
                $value = (float)$matches[1];
                return ($value >= 90 && $value <= 110) ? $value : null;

            case 'weight':
                $value = (float)$matches[1];
                return ($value >= 1 && $value <= 300) ? $value : null;

            case 'height':
                $value = (float)$matches[1];
                return ($value >= 30 && $value <= 250) ? $value : null;
        }

        return null;
    }

    /**
     * Validate and clean vital signs data
     */
    protected function validateAndCleanVitalSigns($vitalSigns)
    {
        $cleaned = [];

        foreach ($vitalSigns as $key => $value) {
            if (is_numeric($value) && $value > 0) {
                $cleaned[$key] = $value;
            }
        }

        return $cleaned;
    }

    /**
     * Legacy method for backward compatibility
     * Transform WordPress encounter tab data to Laravel format
     */
    public function transformEncounterTab($wpTab)
    {
        return [
            'type' => $this->sanitizeString($wpTab['type'] ?? ''),
            'content' => $this->sanitizeString($wpTab['content'] ?? ''),
            'metadata' => $this->transformMetadata($wpTab['metadata'] ?? []),
            'created_at' => $this->convertDateTime($wpTab['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Validate and normalize tab type names
     */
    public function normalizeTabType($tabType)
    {
        if (empty($tabType)) {
            return 'notes'; // Default fallback
        }

        // Normalize common variations
        $normalizations = [
            'problem' => 'concerns',
            'problems' => 'concerns',
            'present_concerns' => 'concerns',
            'chief_complaint' => 'concerns',
            'cc' => 'concerns',

            'hpi' => 'history',
            'history_of_present_illness' => 'history',
            'present_history' => 'history',

            'physical_examination' => 'examination',
            'exam' => 'examination',
            'pe' => 'examination',
            'observations' => 'examination',

            'assessment_and_plan' => 'plan',
            'assessment' => 'plan',
            'treatment_plan' => 'plan',

            'past_medical_history' => 'medical_history',
            'pmh' => 'medical_history',
            'medical_hx' => 'medical_history',

            'family_hx' => 'family_history',
            'fh' => 'family_history',

            'social_hx' => 'social_history',
            'sh' => 'social_history',

            'ros' => 'systems_review',
            'review_of_systems' => 'systems_review',

            'meds' => 'medications',
            'drugs' => 'medications',
            'current_medications' => 'medications',

            'note' => 'notes',
            'additional_notes' => 'notes',
            'comments' => 'notes',

            'safeguarding_concerns' => 'safeguarding',
            'safegaurding' => 'safeguarding', // Handle typo

            'vital_signs' => 'vitals',
            'vs' => 'vitals'
        ];

        $normalized = strtolower(trim($tabType));
        return $normalizations[$normalized] ?? $normalized;
    }

    /**
     * Validate vital signs data
     */
    public function validateVitalSigns($vitalSigns)
    {
        $validated = [];

        if (!is_array($vitalSigns)) {
            return $validated;
        }

        // Validation ranges
        $ranges = [
            'temperature' => ['min' => 90, 'max' => 110],
            'blood_pressure_systolic' => ['min' => 70, 'max' => 250],
            'blood_pressure_diastolic' => ['min' => 40, 'max' => 150],
            'heart_rate' => ['min' => 30, 'max' => 200],
            'respiratory_rate' => ['min' => 8, 'max' => 40],
            'oxygen_saturation' => ['min' => 70, 'max' => 100],
            'weight' => ['min' => 1, 'max' => 300],
            'height' => ['min' => 30, 'max' => 250]
        ];

        foreach ($vitalSigns as $key => $value) {
            if (isset($ranges[$key])) {
                $numValue = is_numeric($value) ? (float)$value : null;
                if ($numValue !== null &&
                    $numValue >= $ranges[$key]['min'] &&
                    $numValue <= $ranges[$key]['max']) {
                    $validated[$key] = $numValue;
                }
            }
        }

        return $validated;
    }



    /**
     * Validate patient data structure
     */
    protected function validatePatientData($patientData, $patientEmail)
    {
        if (!is_array($patientData)) {
            \Log::warning('Invalid patient data structure', [
                'patient_email' => $patientEmail,
                'data_type' => gettype($patientData)
            ]);
            return false;
        }

        if (!isset($patientData['encounters']) || !is_array($patientData['encounters'])) {
            \Log::warning('Patient data missing encounters', [
                'patient_email' => $patientEmail,
                'has_encounters' => isset($patientData['encounters']),
                'encounters_type' => isset($patientData['encounters']) ? gettype($patientData['encounters']) : 'missing'
            ]);
            return false;
        }

        return true;
    }

    /**
     * Validate encounter data structure
     */
    protected function validateEncounterData($encounterData, $encounterId)
    {
        if (!is_array($encounterData)) {
            \Log::warning('Invalid encounter data structure', [
                'encounter_id' => $encounterId,
                'data_type' => gettype($encounterData)
            ]);
            return false;
        }

        if (!isset($encounterData['tabs_by_type']) || !is_array($encounterData['tabs_by_type'])) {
            \Log::warning('Encounter data missing tabs_by_type', [
                'encounter_id' => $encounterId,
                'has_tabs_by_type' => isset($encounterData['tabs_by_type']),
                'tabs_type' => isset($encounterData['tabs_by_type']) ? gettype($encounterData['tabs_by_type']) : 'missing'
            ]);
            return false;
        }

        return true;
    }

    /**
     * Validate individual tab data
     */
    protected function validateTabData($tab, $tabType, $encounterId, $tabIndex)
    {
        if (!is_array($tab)) {
            \Log::warning('Invalid tab data structure', [
                'tab_type' => $tabType,
                'encounter_id' => $encounterId,
                'tab_index' => $tabIndex,
                'data_type' => gettype($tab)
            ]);
            return false;
        }

        // Check for required fields
        if (!isset($tab['id']) && !isset($tab['content'])) {
            \Log::warning('Tab missing required fields', [
                'tab_type' => $tabType,
                'encounter_id' => $encounterId,
                'tab_index' => $tabIndex,
                'has_id' => isset($tab['id']),
                'has_content' => isset($tab['content'])
            ]);
            return false;
        }

        // Validate content is not empty for important tabs
        $importantTabs = ['concerns', 'history', 'examination', 'plan'];
        if (in_array($tabType, $importantTabs) && empty(trim($tab['content'] ?? ''))) {
            \Log::info('Important tab has empty content', [
                'tab_type' => $tabType,
                'encounter_id' => $encounterId,
                'tab_id' => $tab['id'] ?? 'unknown'
            ]);
            // Don't return false, just log - empty content might be valid
        }

        return true;
    }

    /**
     * Transform WordPress encounter vitals data to Laravel consultation format
     * Processes the new API response structure with vitals_by_type
     */
    public function transformEncounterVitalsData($wpEncounterData)
    {
        $result = [
            'vital_signs' => []
        ];

        try {
            if (empty($wpEncounterData) || !is_array($wpEncounterData)) {
                \Log::warning('Empty or invalid encounter vitals data provided to transformer', [
                    'data_type' => gettype($wpEncounterData),
                    'data_empty' => empty($wpEncounterData)
                ]);
                return $result;
            }

            // Process each patient's encounter data
            foreach ($wpEncounterData as $patientEmail => $patientData) {
                if (!$this->validatePatientData($patientData, $patientEmail)) {
                    continue;
                }

                // Process each encounter for this patient
                foreach ($patientData['encounters'] as $encounterId => $encounterData) {
                    if (!$this->validateEncounterData($encounterData, $encounterId)) {
                        continue;
                    }

                    // Check if encounter has vitals data
                    if (isset($encounterData['vitals_by_type']) && is_array($encounterData['vitals_by_type'])) {
                        // Process vitals by type (new structure)
                        foreach ($encounterData['vitals_by_type'] as $vitalType => $vitals) {
                            if (!is_array($vitals)) {
                                \Log::warning('Invalid vitals data for vital type', [
                                    'vital_type' => $vitalType,
                                    'encounter_id' => $encounterId,
                                    'data_type' => gettype($vitals)
                                ]);
                                continue;
                            }

                            foreach ($vitals as $vitalIndex => $vital) {
                                try {
                                    if (!$this->validateVitalData($vital, $vitalType, $encounterId, $vitalIndex)) {
                                        continue;
                                    }

                                    $transformedVital = $this->transformSingleEncounterVital($vital, $vitalType, $encounterId);
                                    if ($transformedVital) {
                                        $result['vital_signs'][] = $transformedVital;
                                    }
                                } catch (\Exception $e) {
                                    \Log::error('Failed to transform individual vital', [
                                        'vital_type' => $vitalType,
                                        'encounter_id' => $encounterId,
                                        'vital_index' => $vitalIndex,
                                        'error' => $e->getMessage(),
                                        'vital_data' => $vital
                                    ]);
                                }
                            }
                        }
                    } elseif (isset($encounterData['vitals']) && is_array($encounterData['vitals'])) {
                        // Process flat vitals array (legacy structure)
                        foreach ($encounterData['vitals'] as $vitalIndex => $vital) {
                            try {
                                $vitalType = $vital['vital_type'] ?? 'unknown';
                                if (!$this->validateVitalData($vital, $vitalType, $encounterId, $vitalIndex)) {
                                    continue;
                                }

                                $transformedVital = $this->transformSingleEncounterVital($vital, $vitalType, $encounterId);
                                if ($transformedVital) {
                                    $result['vital_signs'][] = $transformedVital;
                                }
                            } catch (\Exception $e) {
                                \Log::error('Failed to transform legacy vital', [
                                    'encounter_id' => $encounterId,
                                    'vital_index' => $vitalIndex,
                                    'error' => $e->getMessage(),
                                    'vital_data' => $vital
                                ]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('Critical error in encounter vitals transformation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * Transform a single encounter vital with comprehensive error handling
     */
    public function transformSingleEncounterVital($wpVital, $vitalType, $encounterId)
    {
        try {
            // Validate input data
            if (!is_array($wpVital)) {
                \Log::warning('Invalid vital data provided to transformSingleEncounterVital', [
                    'vital_type' => $vitalType,
                    'encounter_id' => $encounterId,
                    'data_type' => gettype($wpVital)
                ]);
                return null;
            }

            // Process vital value with validation
            $vitalValue = $this->processVitalValue($wpVital['vital_value'] ?? '', $vitalType);

            // Process unit with normalization
            $unit = $this->normalizeVitalUnit($wpVital['unit'] ?? '', $vitalType);

            // Validate and process timestamps
            $recordedAt = $this->safeConvertDateTime($wpVital['recorded_at'] ?? $wpVital['created_at'] ?? null);
            $createdAt = $this->safeConvertDateTime($wpVital['created_at'] ?? null);

            // Ensure we have valid IDs
            $vitalId = $this->sanitizeInteger($wpVital['id'] ?? null);
            $patientId = $this->sanitizeInteger($wpVital['patient_id'] ?? null);

            // Normalize vital type
            $normalizedVitalType = $this->normalizeVitalType($vitalType);

            return [
                'id' => $vitalId,
                'vital_type' => $normalizedVitalType,
                'vital_value' => $vitalValue,
                'unit' => $unit,
                'recorded_at' => $recordedAt ?: now(),
                'created_at' => $createdAt ?: now(),
                'updated_at' => now(),
                'wp_encounter_vital_id' => $vitalId,
                'wp_encounter_id' => $encounterId,
                'wp_patient_id' => $patientId,
                'is_valid' => $this->isValidVitalValue($vitalValue, $normalizedVitalType),
                'metadata' => $this->extractVitalMetadata($wpVital)
            ];
        } catch (\Exception $e) {
            \Log::error('Failed to transform single encounter vital', [
                'vital_type' => $vitalType,
                'encounter_id' => $encounterId,
                'error' => $e->getMessage(),
                'vital_data' => $wpVital
            ]);
            return null;
        }
    }

    /**
     * Legacy method for backward compatibility
     * Transform WordPress encounter vital data to Laravel format
     */
    public function transformEncounterVital($wpVital)
    {
        return [
            'vital_type' => $this->sanitizeString($wpVital['vital_type'] ?? ''),
            'vital_value' => $this->sanitizeString($wpVital['vital_value'] ?? ''),
            'unit' => $this->sanitizeString($wpVital['unit'] ?? ''),
            'recorded_at' => $this->convertDateTime($wpVital['created_at'] ?? null),
            'created_at' => $this->convertDateTime($wpVital['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Validate individual vital data
     */
    protected function validateVitalData($vital, $vitalType, $encounterId, $vitalIndex)
    {
        if (!is_array($vital)) {
            \Log::warning('Invalid vital data structure', [
                'vital_type' => $vitalType,
                'encounter_id' => $encounterId,
                'vital_index' => $vitalIndex,
                'data_type' => gettype($vital)
            ]);
            return false;
        }

        // Check for required fields
        if (!isset($vital['vital_value']) && !isset($vital['value'])) {
            \Log::warning('Vital missing value field', [
                'vital_type' => $vitalType,
                'encounter_id' => $encounterId,
                'vital_index' => $vitalIndex,
                'has_vital_value' => isset($vital['vital_value']),
                'has_value' => isset($vital['value'])
            ]);
            return false;
        }

        return true;
    }

    /**
     * Process vital value with type-specific validation
     */
    protected function processVitalValue($value, $vitalType)
    {
        if (empty($value)) {
            return '';
        }

        // Handle different value formats
        if (is_array($value)) {
            $value = json_encode($value);
        } elseif (!is_string($value) && !is_numeric($value)) {
            $value = (string)$value;
        }

        // Clean and validate based on vital type
        $normalizedType = $this->normalizeVitalType($vitalType);

        switch ($normalizedType) {
            case 'blood_pressure':
                return $this->processBloodPressureValue($value);
            case 'temperature':
            case 'pulse':
            case 'heart_rate':
            case 'respiratory_rate':
            case 'oxygen_saturation':
            case 'weight':
            case 'height':
                return $this->processNumericVitalValue($value, $normalizedType);
            default:
                return trim((string)$value);
        }
    }

    /**
     * Process blood pressure value (handles formats like "120/80")
     */
    protected function processBloodPressureValue($value)
    {
        $value = trim((string)$value);

        // Check if it's already in systolic/diastolic format
        if (preg_match('/(\d{2,3})[\s]*[\/\\\][\s]*(\d{2,3})/', $value, $matches)) {
            $systolic = (int)$matches[1];
            $diastolic = (int)$matches[2];

            // Validate ranges
            if ($systolic >= 70 && $systolic <= 250 && $diastolic >= 40 && $diastolic <= 150) {
                return $value; // Keep original format
            }
        }

        return $value; // Return as-is if not in expected format
    }

    /**
     * Process numeric vital value with range validation
     */
    protected function processNumericVitalValue($value, $vitalType)
    {
        $value = trim((string)$value);

        // Extract numeric value
        if (preg_match('/(\d+(?:\.\d+)?)/', $value, $matches)) {
            $numericValue = (float)$matches[1];

            // Validate against expected ranges
            if ($this->isValidVitalValue($numericValue, $vitalType)) {
                return (string)$numericValue;
            }
        }

        return $value; // Return original if validation fails
    }

    /**
     * Normalize vital type names
     */
    protected function normalizeVitalType($vitalType)
    {
        if (empty($vitalType)) {
            return 'unknown';
        }

        $normalizations = [
            'bp' => 'blood_pressure',
            'blood_pressure' => 'blood_pressure',
            'systolic_bp' => 'blood_pressure',
            'diastolic_bp' => 'blood_pressure',

            'temp' => 'temperature',
            'temperature' => 'temperature',

            'hr' => 'heart_rate',
            'heart_rate' => 'heart_rate',
            'pulse' => 'heart_rate',
            'bpm' => 'heart_rate',

            'rr' => 'respiratory_rate',
            'respiratory_rate' => 'respiratory_rate',
            'resp_rate' => 'respiratory_rate',
            'breathing_rate' => 'respiratory_rate',

            'spo2' => 'oxygen_saturation',
            'o2_sat' => 'oxygen_saturation',
            'oxygen_saturation' => 'oxygen_saturation',
            'saturation' => 'oxygen_saturation',

            'wt' => 'weight',
            'weight' => 'weight',

            'ht' => 'height',
            'height' => 'height'
        ];

        $normalized = strtolower(trim($vitalType));
        return $normalizations[$normalized] ?? $normalized;
    }

    /**
     * Normalize vital unit names
     */
    protected function normalizeVitalUnit($unit, $vitalType)
    {
        if (empty($unit)) {
            return $this->getDefaultUnit($vitalType);
        }

        $unit = strtolower(trim($unit));

        $normalizations = [
            // Temperature
            'c' => '°C',
            '°c' => '°C',
            'celsius' => '°C',
            'f' => '°F',
            '°f' => '°F',
            'fahrenheit' => '°F',

            // Pressure
            'mmhg' => 'mmHg',
            'mm hg' => 'mmHg',

            // Rate
            'bpm' => 'bpm',
            'beats/min' => 'bpm',
            'beats per minute' => 'bpm',
            '/min' => '/min',
            'per minute' => '/min',

            // Percentage
            '%' => '%',
            'percent' => '%',
            'percentage' => '%',

            // Weight
            'kg' => 'kg',
            'kgs' => 'kg',
            'lbs' => 'lbs',
            'lb' => 'lbs',
            'pounds' => 'lbs',

            // Height
            'cm' => 'cm',
            'cms' => 'cm',
            'm' => 'm',
            'meters' => 'm',
            'ft' => 'ft',
            'feet' => 'ft',
            'in' => 'in',
            'inches' => 'in'
        ];

        return $normalizations[$unit] ?? $unit;
    }

    /**
     * Get default unit for vital type
     */
    protected function getDefaultUnit($vitalType)
    {
        $defaults = [
            'temperature' => '°C',
            'blood_pressure' => 'mmHg',
            'heart_rate' => 'bpm',
            'pulse' => 'bpm',
            'respiratory_rate' => '/min',
            'oxygen_saturation' => '%',
            'weight' => 'kg',
            'height' => 'cm'
        ];

        return $defaults[$vitalType] ?? '';
    }

    /**
     * Check if vital value is within valid range
     */
    protected function isValidVitalValue($value, $vitalType)
    {
        if (!is_numeric($value)) {
            return false;
        }

        $value = (float)$value;

        $ranges = [
            'temperature' => ['min' => 30, 'max' => 45], // Celsius
            'heart_rate' => ['min' => 30, 'max' => 200],
            'pulse' => ['min' => 30, 'max' => 200],
            'respiratory_rate' => ['min' => 8, 'max' => 40],
            'oxygen_saturation' => ['min' => 70, 'max' => 100],
            'weight' => ['min' => 1, 'max' => 300],
            'height' => ['min' => 30, 'max' => 250]
        ];

        if (isset($ranges[$vitalType])) {
            return $value >= $ranges[$vitalType]['min'] && $value <= $ranges[$vitalType]['max'];
        }

        return true; // Allow unknown types
    }

    /**
     * Extract metadata from vital data
     */
    protected function extractVitalMetadata($vital)
    {
        $metadata = [];

        // Extract additional fields that might be useful
        $metadataFields = ['notes', 'comments', 'source', 'device', 'method', 'location'];

        foreach ($metadataFields as $field) {
            if (isset($vital[$field]) && !empty($vital[$field])) {
                $metadata[$field] = $this->sanitizeString($vital[$field]);
            }
        }

        return $metadata;
    }

    /**
     * Transform WordPress encounter summary document data to Laravel ConsultationDocument format
     */
    public function transformEncounterSummaryDocument($wpDocument, $consultationId, $uploadedBy, $migratedFileInfo = null)
    {
        $fileInfo = $wpDocument['file_info'] ?? [];

        return [
            'consultation_id' => $consultationId,
            'document_type' => $this->determineDocumentType($wpDocument['name'] ?? ''),
            'file_path' => $migratedFileInfo['file_path'] ?? '',
            'file_name' => $migratedFileInfo['file_name'] ?? ($wpDocument['name'] ?? 'unknown'),
            'original_name' => $this->sanitizeString($wpDocument['name'] ?? 'unknown'),
            'file_size' => $migratedFileInfo['file_size'] ?? ($fileInfo['file_size'] ?? 0),
            'mime_type' => $migratedFileInfo['mime_type'] ?? ($fileInfo['file_mime_type'] ?? 'application/octet-stream'),
            'description' => $this->sanitizeString($wpDocument['description'] ?? ''),
            'uploaded_by' => $uploadedBy,
            'wp_document_id' => $wpDocument['id'] ?? null,
            'wp_attachment_id' => $wpDocument['attachment_id'] ?? null,
            'wp_encounter_id' => null, // Will be set by the migration command
            'wp_created_date' => $this->convertDateTime($wpDocument['created_date'] ?? null),
            'created_at' => $this->convertDateTime($wpDocument['created_date'] ?? null) ?: now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Determine document type from filename
     */
    private function determineDocumentType($filename)
    {
        $filename = strtolower($filename);

        if (strpos($filename, 'consultation') !== false) {
            return 'consultation_notes';
        }
        if (strpos($filename, 'referral') !== false) {
            return 'referral_letter';
        }
        if (strpos($filename, 'gp letter') !== false || strpos($filename, 'letter') !== false) {
            return 'medical_letter';
        }
        if (strpos($filename, 'prescription') !== false) {
            return 'prescription';
        }
        if (strpos($filename, 'lab') !== false || strpos($filename, 'test') !== false) {
            return 'lab_report';
        }
        if (strpos($filename, 'image') !== false || strpos($filename, 'photo') !== false) {
            return 'image';
        }

        // Default based on file extension
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        switch ($extension) {
            case 'pdf':
                return 'pdf_document';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                return 'image';
            default:
                return 'general_document';
        }
    }

    /**
     * Transform WordPress patient report data to Laravel format
     */
    public function transformPatientReport($wpReport)
    {
        return [
            'title' => $this->sanitizeString($wpReport['title'] ?? ''),
            'report_type' => $this->sanitizeString($wpReport['report_type'] ?? 'general'),
            'content' => $this->sanitizeString($wpReport['content'] ?? ''),
            'file_path' => $this->sanitizeString($wpReport['file_path'] ?? ''),
            'file_url' => $this->sanitizeString($wpReport['file_url'] ?? ''),
            'file_size' => (int) ($wpReport['file_size'] ?? 0),
            'mime_type' => $this->sanitizeString($wpReport['mime_type'] ?? ''),
            'description' => $this->sanitizeString($wpReport['description'] ?? ''),
            'generated_by' => $this->sanitizeString($wpReport['generated_by'] ?? ''),
            'is_private' => (bool) ($wpReport['is_private'] ?? false),
            'created_at' => $this->convertDateTime($wpReport['created_at'] ?? null),
            'updated_at' => now(),
        ];
    }

    /**
     * Transform metadata array
     */
    protected function transformMetadata($metadata)
    {
        if (is_string($metadata)) {
            $decoded = json_decode($metadata, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($metadata) ? $metadata : [];
    }
}
