<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Service Details</h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <Icon name="x" class="w-5 h-5" />
        </button>
      </div>

      <!-- Content -->
      <div class="mt-6 space-y-6 max-h-96 overflow-y-auto">
        <!-- Basic Information -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Service Name</label>
            <p class="mt-1 text-sm text-gray-900">{{ service.name }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Duration</label>
            <p class="mt-1 text-sm text-gray-900">{{ service.duration }} minutes</p>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Description</label>
          <p class="mt-1 text-sm text-gray-900">{{ service.description }}</p>
        </div>

        <!-- Default Pricing -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Default Price</label>
            <p class="mt-1 text-sm text-gray-900">£{{ service.price }}</p>
          </div>
          <div v-if="service.discount_percentage">
            <label class="block text-sm font-medium text-gray-700">Discount</label>
            <p class="mt-1 text-sm text-gray-900">{{ service.discount_percentage }}% off</p>
          </div>
        </div>

        <!-- Country-Specific Pricing -->
        <div v-if="service.countries && service.countries.length > 0">
          <label class="block text-sm font-medium text-gray-700 mb-3">Country-Specific Pricing</label>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="grid gap-3">
              <div
                v-for="country in service.countries"
                :key="country.code"
                class="flex items-center justify-between p-3 bg-white rounded-md border border-gray-200"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span class="text-xs font-semibold text-blue-600">
                      {{ country.code }}
                    </span>
                  </div>
                  <div>
                    <h5 class="text-sm font-medium text-gray-900">{{ country.name }}</h5>
                    <p class="text-xs text-gray-500">{{ country.currency_name }}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm font-semibold text-gray-900">
                    {{ country.currency_symbol }}{{ country.pivot?.price_override || service.price }}
                  </p>
                  <p class="text-xs" :class="country.pivot?.is_available ? 'text-green-600' : 'text-red-600'">
                    {{ country.pivot?.is_available ? 'Available' : 'Not Available' }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Service Features -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Type</label>
            <p class="mt-1 text-sm text-gray-900">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="service.is_telemedicine ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
                {{ service.is_telemedicine ? 'Telemedicine' : 'In-Person' }}
              </span>
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Status</label>
            <p class="mt-1 text-sm text-gray-900">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="service.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ service.active ? 'Active' : 'Inactive' }}
              </span>
            </p>
          </div>
        </div>

        <!-- Provider Information -->
        <div v-if="service.provider">
          <label class="block text-sm font-medium text-gray-700">Provider</label>
          <p class="mt-1 text-sm text-gray-900">{{ service.provider.user?.name || 'Unknown Provider' }}</p>
        </div>

        <!-- Category -->
        <div v-if="service.category">
          <label class="block text-sm font-medium text-gray-700">Category</label>
          <p class="mt-1 text-sm text-gray-900">{{ service.category.name }}</p>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end pt-4 border-t border-gray-200 mt-6">
        <button
          @click="$emit('close')"
          class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import Icon from '@/components/Icon.vue';

const props = defineProps({
  service: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])
</script>

<style scoped>
/* Component-specific styles */
</style>
