<?php

namespace App\Services;

use App\Models\User;
use App\Models\Country;
use App\Models\Service;
use App\Models\Product;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Log;

class CheckoutFlowService
{
    private PaymentGatewayService $paymentGatewayService;
    private LocationService $locationService;

    public function __construct(
        PaymentGatewayService $paymentGatewayService,
        LocationService $locationService
    ) {
        $this->paymentGatewayService = $paymentGatewayService;
        $this->locationService = $locationService;
    }

    /**
     * Get the appropriate checkout flow for a user and purchase type
     */
    public function getCheckoutFlow(User $user, string $purchaseType, array $items = []): array
    {
        $countryCode = $this->getEffectiveCountryCode($user);
        $country = Country::where('code', $countryCode)->first();

        if (!$country) {
            return $this->getDefaultCheckoutFlow($purchaseType);
        }

        // Get country-specific checkout configuration
        $checkoutConfig = $this->getCountryCheckoutConfig($country, $purchaseType);
        
        // Get available payment gateways
        $paymentGateways = $this->paymentGatewayService->getSupportedGateways($countryCode);
        $primaryGateway = $this->paymentGatewayService->getPrimaryGateway($countryCode);

        return [
            'flow_type' => $checkoutConfig['flow_type'],
            'country_code' => $countryCode,
            'country_name' => $country->name,
            'currency' => $country->currency_code,
            'currency_symbol' => $country->currency_symbol,
            'purchase_type' => $purchaseType,
            'steps' => $checkoutConfig['steps'],
            'validation_rules' => $checkoutConfig['validation_rules'],
            'required_fields' => $checkoutConfig['required_fields'],
            'payment_gateways' => $paymentGateways,
            'primary_gateway' => $primaryGateway,
            'regulations' => $checkoutConfig['regulations'],
            'features' => $checkoutConfig['features'],
            'ui_config' => $checkoutConfig['ui_config']
        ];
    }

    /**
     * Get country-specific checkout configuration
     */
    private function getCountryCheckoutConfig(Country $country, string $purchaseType): array
    {
        $countryCode = $country->code;

        // Define country-specific configurations
        $configurations = [
            'IN' => $this->getIndiaCheckoutConfig($purchaseType),
            'GB' => $this->getUKCheckoutConfig($purchaseType),
            'US' => $this->getUSCheckoutConfig($purchaseType),
            'DE' => $this->getGermanyCheckoutConfig($purchaseType),
            'FR' => $this->getFranceCheckoutConfig($purchaseType),
            'CA' => $this->getCanadaCheckoutConfig($purchaseType),
            'AU' => $this->getAustraliaCheckoutConfig($purchaseType),
            'NG' => $this->getNigeriaCheckoutConfig($purchaseType),
            'ZA' => $this->getSouthAfricaCheckoutConfig($purchaseType),
        ];

        return $configurations[$countryCode] ?? $this->getDefaultCheckoutConfig($purchaseType);
    }

    /**
     * India-specific checkout configuration
     */
    private function getIndiaCheckoutConfig(string $purchaseType): array
    {
        $baseConfig = [
            'flow_type' => 'india_compliant',
            'regulations' => [
                'requires_gst' => true,
                'requires_pan_for_high_value' => true,
                'telemedicine_regulations' => true,
                'prescription_requirements' => 'strict',
                'data_localization' => true
            ],
            'features' => [
                'upi_payments' => true,
                'netbanking' => true,
                'digital_wallets' => true,
                'emi_options' => true,
                'cash_on_delivery' => false, // Not applicable for medical services
                'instant_refunds' => true
            ],
            'ui_config' => [
                'show_gst_breakdown' => true,
                'show_payment_methods_prominently' => true,
                'language_options' => ['en', 'hi'],
                'currency_format' => 'indian'
            ]
        ];

        switch ($purchaseType) {
            case 'appointment':
                return array_merge($baseConfig, [
                    'steps' => [
                        'patient_details',
                        'medical_history',
                        'appointment_preferences',
                        'doctor_selection',
                        'payment_details',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'patient_age_verification' => 'required',
                        'medical_history' => 'required_for_new_patients',
                        'emergency_contact' => 'required',
                        'consent_forms' => 'required'
                    ],
                    'required_fields' => [
                        'full_name', 'age', 'gender', 'phone', 'emergency_contact',
                        'medical_history', 'current_medications', 'allergies'
                    ]
                ]);

            case 'product':
                return array_merge($baseConfig, [
                    'steps' => [
                        'shipping_address',
                        'billing_address',
                        'prescription_upload',
                        'payment_details',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'prescription_required' => 'for_prescription_drugs',
                        'age_verification' => 'for_restricted_items',
                        'address_verification' => 'required'
                    ],
                    'required_fields' => [
                        'shipping_address', 'phone', 'prescription_file'
                    ]
                ]);

            case 'subscription':
                return array_merge($baseConfig, [
                    'steps' => [
                        'plan_selection',
                        'personal_details',
                        'payment_setup',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'recurring_payment_consent' => 'required',
                        'terms_acceptance' => 'required'
                    ],
                    'required_fields' => [
                        'full_name', 'phone', 'email'
                    ]
                ]);

            default:
                return $baseConfig;
        }
    }

    /**
     * UK-specific checkout configuration
     */
    private function getUKCheckoutConfig(string $purchaseType): array
    {
        $baseConfig = [
            'flow_type' => 'uk_compliant',
            'regulations' => [
                'gdpr_compliance' => true,
                'nhs_integration' => true,
                'mhra_compliance' => true,
                'prescription_requirements' => 'gmc_regulated',
                'data_protection' => 'gdpr_strict'
            ],
            'features' => [
                'card_payments' => true,
                'bank_transfers' => true,
                'digital_wallets' => true,
                'nhs_exemptions' => true,
                'prescription_prepayment' => true
            ],
            'ui_config' => [
                'show_vat_breakdown' => true,
                'nhs_branding_compliance' => true,
                'language_options' => ['en'],
                'currency_format' => 'british'
            ]
        ];

        switch ($purchaseType) {
            case 'appointment':
                return array_merge($baseConfig, [
                    'steps' => [
                        'nhs_number_verification',
                        'gp_details',
                        'appointment_booking',
                        'payment_or_exemption',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'nhs_number' => 'optional_but_preferred',
                        'gp_registration' => 'required',
                        'medical_history_consent' => 'required'
                    ],
                    'required_fields' => [
                        'full_name', 'date_of_birth', 'postcode', 'gp_practice'
                    ]
                ]);

            case 'product':
                return array_merge($baseConfig, [
                    'steps' => [
                        'prescription_verification',
                        'delivery_address',
                        'payment_or_exemption',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'prescription_validity' => 'required',
                        'mhra_compliance' => 'required',
                        'age_verification' => 'for_restricted_items'
                    ],
                    'required_fields' => [
                        'prescription_number', 'delivery_address', 'postcode'
                    ]
                ]);

            default:
                return $baseConfig;
        }
    }

    /**
     * US-specific checkout configuration
     */
    private function getUSCheckoutConfig(string $purchaseType): array
    {
        $baseConfig = [
            'flow_type' => 'us_compliant',
            'regulations' => [
                'hipaa_compliance' => true,
                'fda_compliance' => true,
                'state_licensing' => true,
                'prescription_requirements' => 'dea_regulated',
                'telemedicine_state_laws' => true
            ],
            'features' => [
                'card_payments' => true,
                'ach_transfers' => true,
                'hsa_fsa_payments' => true,
                'insurance_billing' => true,
                'copay_assistance' => true
            ],
            'ui_config' => [
                'show_tax_breakdown' => true,
                'insurance_integration' => true,
                'language_options' => ['en', 'es'],
                'currency_format' => 'us'
            ]
        ];

        switch ($purchaseType) {
            case 'appointment':
                return array_merge($baseConfig, [
                    'steps' => [
                        'insurance_verification',
                        'patient_intake',
                        'provider_selection',
                        'appointment_scheduling',
                        'payment_copay',
                        'confirmation'
                    ],
                    'validation_rules' => [
                        'insurance_eligibility' => 'required',
                        'state_licensing_check' => 'required',
                        'hipaa_consent' => 'required'
                    ],
                    'required_fields' => [
                        'insurance_id', 'ssn_last_four', 'state_of_residence'
                    ]
                ]);

            default:
                return $baseConfig;
        }
    }

    /**
     * Get effective country code (prioritizing user's actual country)
     */
    private function getEffectiveCountryCode(User $user): string
    {
        // Always use the user's actual country_code from database first
        // This ensures that when users change their country in settings, it's respected
        if ($user->country_code) {
            return $user->country_code;
        }

        // Only check for test country override if user has no country set
        if (session()->has('test_country_code')) {
            return session('test_country_code');
        }

        // No default fallback - user must have a country set
        throw new \Exception('User must have a country code set');
    }

    /**
     * Default checkout configuration
     */
    private function getDefaultCheckoutConfig(string $purchaseType): array
    {
        return [
            'flow_type' => 'standard',
            'steps' => ['details', 'payment', 'confirmation'],
            'validation_rules' => ['basic_validation' => true],
            'required_fields' => ['name', 'email'],
            'regulations' => ['standard_compliance' => true],
            'features' => ['card_payments' => true],
            'ui_config' => ['language_options' => ['en']]
        ];
    }

    /**
     * Get default checkout flow
     */
    private function getDefaultCheckoutFlow(string $purchaseType): array
    {
        return [
            'flow_type' => 'standard',
            'country_code' => 'GB',
            'currency' => 'GBP',
            'purchase_type' => $purchaseType,
            'steps' => ['details', 'payment', 'confirmation'],
            'payment_gateways' => ['stripe'],
            'primary_gateway' => 'stripe'
        ];
    }

    // Placeholder methods for other countries
    private function getGermanyCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
    private function getFranceCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
    private function getCanadaCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
    private function getAustraliaCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
    private function getNigeriaCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
    private function getSouthAfricaCheckoutConfig(string $purchaseType): array { return $this->getDefaultCheckoutConfig($purchaseType); }
}
