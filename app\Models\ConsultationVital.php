<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationVital extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'patient_id',
        'instance_id',
        'temperature',
        'temperature_unit',
        'pulse',
        'systolic_bp',
        'diastolic_bp',
        'respiratory_rate',
        'oxygen_saturation',
        'weight',
        'weight_unit',
        'height',
        'height_unit',
        'bmi',
        'pain_level',
        'notes',
        'additional_data',
        'is_ai_populated',
        'recorded_by',
    ];

    protected $casts = [
        'additional_data' => 'array',
        'is_ai_populated' => 'boolean',
        'temperature' => 'decimal:2',
        'weight' => 'decimal:2',
        'height' => 'decimal:2',
        'bmi' => 'decimal:2',
    ];

    /**
     * Get the consultation that owns the vital signs.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the patient that owns the vital signs.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who recorded the vital signs.
     */
    public function recordedBy()
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Get the blood pressure as a formatted string.
     */
    public function getBloodPressureAttribute()
    {
        if ($this->systolic_bp && $this->diastolic_bp) {
            return $this->systolic_bp . '/' . $this->diastolic_bp;
        }
        return null;
    }

    /**
     * Get the BMI category.
     */
    public function getBmiCategoryAttribute()
    {
        if (!$this->bmi) {
            return null;
        }

        if ($this->bmi < 18.5) {
            return 'Underweight';
        } elseif ($this->bmi < 25) {
            return 'Normal';
        } elseif ($this->bmi < 30) {
            return 'Overweight';
        } else {
            return 'Obese';
        }
    }

    /**
     * Calculate BMI if weight and height are provided.
     */
    public function calculateBmi()
    {
        if ($this->weight && $this->height) {
            $weight_kg = $this->weight_unit === 'lbs' ? $this->weight * 0.453592 : $this->weight;
            $height_m = $this->height_unit === 'inches' ? $this->height * 0.0254 : $this->height / 100;
            
            return round($weight_kg / ($height_m * $height_m), 2);
        }
        return null;
    }

    /**
     * Auto-calculate BMI before saving.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($vital) {
            if ($vital->weight && $vital->height) {
                $vital->bmi = $vital->calculateBmi();
            }
        });
    }

    /**
     * Get the most recent vital signs for a patient.
     */
    public static function getRecentVitals($patientId, $limit = 5)
    {
        return static::where('patient_id', $patientId)
            ->with(['consultation', 'recordedBy'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get vital signs for a specific consultation.
     */
    public static function getByConsultation($consultationId)
    {
        return static::where('consultation_id', $consultationId)
            ->with('recordedBy')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if vital signs are within normal ranges.
     */
    public function isNormalVitals()
    {
        $abnormal = [];

        // Check temperature (normal: 97-99°F or 36.1-37.2°C)
        if ($this->temperature) {
            $temp_f = $this->temperature_unit === 'C' ? ($this->temperature * 9/5) + 32 : $this->temperature;
            if ($temp_f < 97 || $temp_f > 99) {
                $abnormal[] = 'temperature';
            }
        }

        // Check pulse (normal: 60-100 bpm)
        if ($this->pulse && ($this->pulse < 60 || $this->pulse > 100)) {
            $abnormal[] = 'pulse';
        }

        // Check blood pressure (normal: systolic 90-140, diastolic 60-90)
        if ($this->systolic_bp && ($this->systolic_bp < 90 || $this->systolic_bp > 140)) {
            $abnormal[] = 'systolic_bp';
        }
        if ($this->diastolic_bp && ($this->diastolic_bp < 60 || $this->diastolic_bp > 90)) {
            $abnormal[] = 'diastolic_bp';
        }

        // Check respiratory rate (normal: 12-20 breaths/min)
        if ($this->respiratory_rate && ($this->respiratory_rate < 12 || $this->respiratory_rate > 20)) {
            $abnormal[] = 'respiratory_rate';
        }

        // Check oxygen saturation (normal: 95-100%)
        if ($this->oxygen_saturation && $this->oxygen_saturation < 95) {
            $abnormal[] = 'oxygen_saturation';
        }

        return empty($abnormal) ? true : $abnormal;
    }
}