<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'code_3',
        'currency_code',
        'currency_symbol',
        'currency_name',
        'phone_code',
        'timezone',
        'supported_payment_gateways',
        'supported_languages',
        'tax_rate',
        'is_active',
        'is_supported',
        'sort_order',
    ];

    protected $casts = [
        'supported_payment_gateways' => 'array',
        'supported_languages' => 'array',
        'tax_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'is_supported' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope to get only active and supported countries
     */
    public function scopeActiveAndSupported($query)
    {
        return $query->where('is_active', true)->where('is_supported', true);
    }

    /**
     * Scope to get countries ordered by sort order and name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if a payment gateway is supported in this country
     */
    public function supportsPaymentGateway(string $gateway): bool
    {
        return in_array($gateway, $this->supported_payment_gateways ?? []);
    }

    /**
     * Check if a language is supported in this country
     */
    public function supportsLanguage(string $language): bool
    {
        return in_array($language, $this->supported_languages ?? []);
    }

    /**
     * Get formatted currency display
     */
    public function getFormattedCurrencyAttribute(): string
    {
        return "{$this->currency_symbol} ({$this->currency_code})";
    }

    /**
     * Get formatted phone code
     */
    public function getFormattedPhoneCodeAttribute(): string
    {
        return $this->phone_code;
    }

    /**
     * Relationships
     */
    public function users()
    {
        return $this->hasMany(User::class, 'country_code', 'code');
    }

    public function providers()
    {
        return $this->hasMany(Provider::class, 'country_code', 'code');
    }

    public function clinics()
    {
        return $this->hasMany(Clinic::class, 'country', 'code');
    }
}
