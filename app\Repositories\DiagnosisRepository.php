<?php

namespace App\Repositories;

use App\Models\Diagnosis;
use Illuminate\Database\Eloquent\Collection;

class DiagnosisRepository extends BaseRepository
{
    /**
     * Create a new repository instance.
     *
     * @param Diagnosis $model
     * @return void
     */
    public function __construct(Diagnosis $model)
    {
        $this->model = $model;
    }
    
    /**
     * Find diagnoses by consultation.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findByConsultation(int $consultationId): Collection
    {
        $query = $this->model->newQuery();
        $query->where('consultation_id', $consultationId)
              ->orderBy('type')
              ->orderBy('created_at');
        
        return $query->get();
    }
    
    /**
     * Find diagnoses by type.
     *
     * @param int $consultationId
     * @param string $type
     * @return Collection
     */
    public function findByType(int $consultationId, string $type): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'type' => $type
        ]);
    }
    
    /**
     * Find primary diagnoses.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findPrimary(int $consultationId): Collection
    {
        return $this->findByType($consultationId, 'primary');
    }
    
    /**
     * Find active diagnoses.
     *
     * @param int $consultationId
     * @return Collection
     */
    public function findActive(int $consultationId): Collection
    {
        return $this->findBy([
            'consultation_id' => $consultationId,
            'status' => 'active'
        ]);
    }
    
    /**
     * Search diagnoses by name or code.
     *
     * @param string $search
     * @return Collection
     */
    public function search(string $search): Collection
    {
        $query = $this->model->newQuery();
        $query->where(function ($q) use ($search) {
            $q->where('diagnosis_name', 'like', "%{$search}%")
              ->orWhere('diagnosis_code', 'like', "%{$search}%")
              ->orWhere('icd10_code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
        
        return $query->get();
    }
}
