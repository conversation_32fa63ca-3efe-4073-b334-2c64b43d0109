<?php

namespace App\Repositories\Interfaces;

use App\Models\ClinicTdlSetting;

interface ClinicTdlSettingRepositoryInterface
{
    public function findByClinic(int $clinicId): ?ClinicTdlSetting;
    public function create(array $data): ClinicTdlSetting;
    public function update(ClinicTdlSetting $setting, array $data): ClinicTdlSetting;
    public function delete(ClinicTdlSetting $setting): bool;
    public function getActiveSettings(): \Illuminate\Database\Eloquent\Collection;
    public function isConfiguredForClinic(int $clinicId): bool;
}
