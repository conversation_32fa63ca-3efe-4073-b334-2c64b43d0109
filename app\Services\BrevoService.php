<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BrevoService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.brevo.com/v3';

    public function __construct($apiKey = null)
    {
        $this->apiKey = $apiKey ?: config('services.brevo.api_key');
    }

    /**
     * Get headers for Brevo API requests
     */
    protected function getHeaders()
    {
        return [
            'accept' => 'application/json',
            'api-key' => $this->apiKey,
            'content-type' => 'application/json'
        ];
    }

    /**
     * Add a domain to Brevo and get DNS records
     */
    public function addDomain($domain)
    {
        // Validate domain format
        if (!$this->isValidDomain($domain)) {
            return [
                'success' => false,
                'error' => 'Invalid domain format'
            ];
        }

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30) // Add timeout
                ->post("{$this->baseUrl}/senders/domains", [
                    'name' => $domain
                ]);

            if ($response->successful()) {
                $data = $response->json();

                // Validate response structure
                if (!isset($data['dns_records'])) {
                    return [
                        'success' => false,
                        'error' => 'Invalid response from email service - missing DNS records'
                    ];
                }

                return [
                    'success' => true,
                    'data' => $data
                ];
            }

            $errorData = $response->json();
            $errorMessage = $errorData['message'] ?? 'Failed to add domain';

            // Handle specific error codes
            if ($response->status() === 400 && isset($errorData['code'])) {
                switch ($errorData['code']) {
                    case 'duplicate_parameter':
                        $errorMessage = 'Domain already exists in email service';
                        break;
                    case 'invalid_parameter':
                        $errorMessage = 'Invalid domain format';
                        break;
                }
            }

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('Brevo connection error', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Connection to email service failed. Please try again later.'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo add domain error', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate domain format
     */
    private function isValidDomain($domain)
    {
        return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) !== false;
    }

    /**
     * Get domain information and DNS records
     */
    public function getDomainInfo($domain)
    {
        if (!$domain || !$this->isValidDomain($domain)) {
            return [
                'success' => false,
                'error' => 'Invalid domain'
            ];
        }

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->get("{$this->baseUrl}/senders/domains/{$domain}");

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            $errorData = $response->json();
            $errorMessage = $errorData['message'] ?? 'Failed to get domain info';

            if ($response->status() === 404) {
                $errorMessage = 'Domain not found in email service';
            }

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('Brevo connection error', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Connection to email service failed. Please try again later.'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo get domain info error', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Authenticate/verify a domain
     */
    public function authenticateDomain($domain)
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->put("{$this->baseUrl}/senders/domains/{$domain}/authenticate");

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to authenticate domain'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo authenticate domain error', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a sender for a clinic
     */
    public function createSender($senderName, $senderEmail)
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post("{$this->baseUrl}/senders", [
                    'name' => $senderName,
                    'email' => $senderEmail
                ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to create sender'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo create sender error', [
                'sender_name' => $senderName,
                'sender_email' => $senderEmail,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all senders
     */
    public function getSenders()
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get("{$this->baseUrl}/senders");

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get senders'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo get senders error', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a sender
     */
    public function updateSender($senderId, $senderName, $senderEmail)
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->put("{$this->baseUrl}/senders/{$senderId}", [
                    'name' => $senderName,
                    'email' => $senderEmail
                ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update sender'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo update sender error', [
                'sender_id' => $senderId,
                'sender_name' => $senderName,
                'sender_email' => $senderEmail,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send transactional email with specific sender
     */
    public function sendEmail($senderName, $senderEmail, $recipientEmail, $subject, $content)
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post("{$this->baseUrl}/smtp/email", [
                    'sender' => [
                        'name' => $senderName,
                        'email' => $senderEmail
                    ],
                    'to' => [
                        ['email' => $recipientEmail]
                    ],
                    'subject' => $subject,
                    'htmlContent' => $content
                ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to send email'
            ];
        } catch (\Exception $e) {
            Log::error('Brevo send email error', [
                'sender_email' => $senderEmail,
                'recipient_email' => $recipientEmail,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format DNS records for display
     */
    public function formatDnsRecords($dnsRecords)
    {
        $formatted = [];

        if (isset($dnsRecords['dkim_record'])) {
            $formatted[] = [
                'type' => 'DKIM Authentication',
                'record_type' => $dnsRecords['dkim_record']['type'],
                'host' => $dnsRecords['dkim_record']['host_name'],
                'value' => $dnsRecords['dkim_record']['value'],
                'status' => $dnsRecords['dkim_record']['status'] ?? false,
                'description' => 'Authenticates emails sent from your domain'
            ];
        }

        if (isset($dnsRecords['brevo_code'])) {
            $formatted[] = [
                'type' => 'Domain Verification',
                'record_type' => $dnsRecords['brevo_code']['type'],
                'host' => $dnsRecords['brevo_code']['host_name'],
                'value' => $dnsRecords['brevo_code']['value'],
                'status' => $dnsRecords['brevo_code']['status'] ?? false,
                'description' => 'Verifies domain ownership'
            ];
        }

        if (isset($dnsRecords['dmarc_record'])) {
            $formatted[] = [
                'type' => 'DMARC Policy',
                'record_type' => $dnsRecords['dmarc_record']['type'],
                'host' => $dnsRecords['dmarc_record']['host_name'],
                'value' => $dnsRecords['dmarc_record']['value'],
                'status' => $dnsRecords['dmarc_record']['status'] ?? false,
                'description' => 'Provides email authentication policy'
            ];
        }

        return $formatted;
    }

    /**
     * Get detailed verification status for a domain
     */
    public function getDetailedVerificationStatus($domain)
    {
        $domainInfo = $this->getDomainInfo($domain);

        if (!$domainInfo['success']) {
            return $domainInfo;
        }

        $data = $domainInfo['data'];
        $dnsRecords = $data['dns_records'] ?? [];

        return [
            'success' => true,
            'data' => [
                'domain' => $domain,
                'overall_verified' => $data['verified'] ?? false,
                'overall_authenticated' => $data['authenticated'] ?? false,
                'records_status' => [
                    'dkim' => [
                        'name' => 'DKIM Record',
                        'status' => $dnsRecords['dkim_record']['status'] ?? false,
                        'host' => $dnsRecords['dkim_record']['host_name'] ?? '',
                        'type' => $dnsRecords['dkim_record']['type'] ?? 'TXT'
                    ],
                    'brevo_code' => [
                        'name' => 'Domain Verification',
                        'status' => $dnsRecords['brevo_code']['status'] ?? false,
                        'host' => $dnsRecords['brevo_code']['host_name'] ?? '',
                        'type' => $dnsRecords['brevo_code']['type'] ?? 'TXT'
                    ],
                    'dmarc' => [
                        'name' => 'DMARC Policy',
                        'status' => $dnsRecords['dmarc_record']['status'] ?? false,
                        'host' => $dnsRecords['dmarc_record']['host_name'] ?? '',
                        'type' => $dnsRecords['dmarc_record']['type'] ?? 'TXT'
                    ]
                ],
                'dns_records' => $dnsRecords
            ]
        ];
    }

    /**
     * Generate DNS setup instructions
     */
    public function generateDnsInstructions($dnsRecords)
    {
        $instructions = [
            "1. Log in to your domain provider's control panel (GoDaddy, Namecheap, Cloudflare, etc.)",
            "2. Navigate to DNS Management or DNS Records section",
            "3. Add the following TXT records:"
        ];

        $recordCount = 1;
        foreach (['dkim_record', 'brevo_code', 'dmarc_record'] as $recordType) {
            if (isset($dnsRecords[$recordType])) {
                $record = $dnsRecords[$recordType];
                $name = str_replace('_', ' ', $recordType);
                $name = ucwords(str_replace('record', '', $name));

                $instructions[] = "";
                $instructions[] = "Record {$recordCount}: {$name}";
                $instructions[] = "Type: {$record['type']}";
                $instructions[] = "Host/Name: {$record['host_name']}";
                $instructions[] = "Value: {$record['value']}";
                $recordCount++;
            }
        }

        $instructions = array_merge($instructions, [
            "",
            "4. Save all records",
            "5. Wait 15-30 minutes for initial DNS propagation",
            "6. Click 'Verify Domain' to check authentication status",
            "7. If verification fails, wait longer (DNS can take up to 48 hours) and try again"
        ]);

        return $instructions;
    }
}
