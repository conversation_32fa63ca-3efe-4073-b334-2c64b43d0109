<?php

namespace App\Mail;

use App\Models\Bill;
use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class PaymentConfirmationNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Bill $bill,
        public Payment $payment,
        public ?string $receiptPath = null
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Payment Confirmation - Invoice #{$this->bill->bill_number}",
            from: [
                'address' => config('mail.from.address'),
                'name' => $this->bill->clinic->name,
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.bills.payment-confirmation',
            with: [
                'bill' => $this->bill,
                'payment' => $this->payment,
                'clinic' => $this->bill->clinic,
                'patient' => $this->bill->patient,
                'invoiceNumber' => $this->bill->bill_number,
                'paymentAmount' => $this->payment->amount,
                'paymentDate' => $this->payment->paid_at,
                'paymentMethod' => $this->payment->payment_method_type,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];

        if ($this->receiptPath && Storage::disk('local')->exists($this->receiptPath)) {
            $attachments[] = Attachment::fromStorageDisk('local', $this->receiptPath)
                ->as("receipt-{$this->bill->bill_number}.pdf")
                ->withMime('application/pdf');
        }

        return $attachments;
    }
}
